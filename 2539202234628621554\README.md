# Excel-Word 检索工具

这是一个用于从Excel表格中选择一列数据，然后在Word文档中搜索这些内容，并将结果回填到Excel表格中的工具。

## 功能

1. 自动或手动识别Excel表格的表头行
2. 选择Excel文件并加载其列
3. 选择要检索的列（通过列标号和列名）
4. 选择包含Word文档的文件夹
5. 在所有Word文档中搜索Excel中选定列的内容
6. 将检索结果回填到Excel文件中的新列"检索结果"
7. 显示详细的检索进度和日志

## 使用方法

1. 运行程序
2. 点击"浏览..."选择Excel文件
3. 使用"表头设置"选择表头行：
   - 自动检测：程序会自动尝试识别表头行
   - 手动指定：可以手动输入表头行号
   - 点击"预览Excel"可以查看Excel内容并直接选择表头行
4. 点击"加载列"加载Excel文件的列
5. 从下拉菜单中选择要检索的列
6. 点击"浏览..."选择包含Word文档的文件夹
7. 点击"开始检索"开始搜索过程
8. 等待检索完成，结果将保存为新的Excel文件

## 安装依赖

在使用前，请确保安装了所需的依赖库：

```
pip install -r requirements.txt
```

## 打包成EXE

可以使用PyInstaller将此脚本打包成可执行文件：

```
pip install pyinstaller
pyinstaller --onefile --windowed excel_word_search.py
```

打包后的EXE文件将在dist文件夹中生成。
