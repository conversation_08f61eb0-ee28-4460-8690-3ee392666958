import tkinter as tk
from tkinter import filedialog, messagebox, ttk, simpledialog
import pandas as pd
import docx # python-docx library
import os
import re # For potential advanced search, but using simple count here
from collections import defaultdict
import sys # Used for packaging considerations if needed later
import traceback
try:
    import openpyxl
except ImportError:
    messagebox.showerror("缺少库", "处理 .xlsx 文件需要 'openpyxl' 库。\n请先通过 pip install openpyxl 安装它。")
    sys.exit()

# --- Core Logic ---
# search_word_doc 函数保持不变 (来自 v5)
def search_word_doc(doc_path, search_term):
    """Searches a single Word document for a given term (case-insensitive)."""
    count = 0
    try:
        term_lower = str(search_term).lower().strip()
        if not term_lower:
            return 0
        try:
            doc = docx.Document(doc_path)
        except Exception as doc_err:
            print(f"Error opening docx file {os.path.basename(doc_path)}: {doc_err}")
            return 0

        full_text = []
        for para in doc.paragraphs:
            if para.text:
                full_text.append(para.text)
        # Optional: add table text reading here if needed

        content = "\n".join(full_text).lower()
        count = content.count(term_lower)
    except Exception as e:
        print(f"Warning: Could not read or search {os.path.basename(doc_path)}: {type(e).__name__} - {e}")
        return 0
    return count


# --- **修改后的 process_files 函数 - 完全使用 openpyxl** ---
def process_files(excel_path,
                  target_col_index, # <--- 接收列的 0-based 索引
                  word_folder,
                  result_col_name, # 要写入的结果列标题
                  status_callback,
                  header_row_index # 0-based index of the header row
                 ):
    """Main function using openpyxl. Reads data, searches based on index, adds results back."""
    try:
        if not excel_path.lower().endswith('.xlsx'):
             raise ValueError("此处理流程当前仅支持 .xlsx 文件。")

        status_callback(f"Status: 读取 Excel 文件 '{os.path.basename(excel_path)}' (使用 openpyxl)...")

        try:
            # --- 使用 openpyxl 读取 Excel 文件 ---
            workbook = openpyxl.load_workbook(excel_path, data_only=True)
            sheet = workbook.active
            print(f"调试 (process_files): 成功使用 openpyxl 读取工作簿，活动工作表名称: {sheet.title}")
            
            # 获取工作表的最大行数和列数
            max_row = sheet.max_row
            max_col = sheet.max_column
            print(f"调试 (process_files): 工作表尺寸: {max_row} 行 x {max_col} 列")
            
            # 验证头部行索引是否有效
            if header_row_index >= max_row:
                raise ValueError(f"标题行索引 {header_row_index} (行号 {header_row_index+1}) 超出了工作表的最大行数 ({max_row})。")
            
            # 验证目标列索引是否有效
            if not (0 <= target_col_index < max_col):
                raise ValueError(f"目标列索引 {target_col_index} 超出了工作表的列范围 (0 到 {max_col-1})。")

        except FileNotFoundError:
            raise ValueError(f"Excel 文件未找到: {excel_path}")
        except Exception as e:
            error_type = type(e).__name__
            raise ValueError(f"使用 openpyxl 读取 Excel 数据时出错 ({error_type}): {e}。\n请确保文件有效且未损坏。")

        # --- 确定数据开始行 ---
        first_data_row_index = header_row_index + 1

        # --- 检查数据起始行索引是否有效 ---
        if first_data_row_index > max_row:
             messagebox.showwarning("无数据", f"根据标题行号 {header_row_index+1}，未找到数据行（文件总行数 {max_row}）。无法进行搜索。")
             status_callback("Status: 未找到数据行，处理中止。")
             workbook.close()
             return
        elif first_data_row_index == max_row:
             print("提示: 文件中似乎没有数据行（只有标题行或之前的行）。")
             # 这种情况允许继续，但搜索结果会是空的

        # --- 使用 openpyxl 提取搜索词 ---
        # 将列索引转换为字母列引用 (A, B, C, ...)
        from openpyxl.utils import get_column_letter
        target_col_letter = get_column_letter(target_col_index + 1)  # openpyxl 列是从 1 开始的
        
        # 提取搜索词
        search_terms = []
        for row_idx in range(first_data_row_index, max_row + 1):
            cell_value = sheet[f"{target_col_letter}{row_idx+1}"].value  # openpyxl 行也是从 1 开始的
            search_terms.append(str(cell_value) if cell_value is not None else "")
        
        print(f"调试 (process_files): 从第 {first_data_row_index+1} 行开始，使用第 {target_col_letter} 列提取了 {len(search_terms)} 个搜索词。")

        status_callback(f"Status: 使用第 {target_col_index + 1} 列的数据进行检索。") # 显示 1-based 列号

        # --- 获取 Word 文件 ---
        status_callback("Status: 查找 Word 文档...")
        if not os.path.isdir(word_folder):
             raise ValueError(f"Word 文档文件夹未找到或不是有效目录: {word_folder}")
        word_files = [os.path.join(word_folder, f) for f in os.listdir(word_folder)
                      if f.lower().endswith('.docx') and os.path.isfile(os.path.join(word_folder, f))]
        if not word_files:
            messagebox.showwarning("无文件", f"在选定文件夹中未找到 '.docx' 文件:\n{word_folder}")
            status_callback("Status: 未找到 Word 文件。")
            workbook.close()
            return

        # --- 处理循环 (使用 search_terms 列表) ---
        results_list = []
        total_terms = len(search_terms)
        status_callback(f"Status: 准备在 {len(word_files)} 个 Word 文档中搜索 {total_terms} 个词条...")

        if total_terms == 0:
            print("提示: 没有需要搜索的词条。")
        else:
            for i, search_term in enumerate(search_terms):
                term_display = search_term[:30] + '...' if len(search_term) > 30 else search_term
                status_callback(f"Status: 处理第 {i+1}/{total_terms} 个词条 (搜索 '{term_display}')...")
                if not search_term.strip():
                    results_list.append("跳过 (空搜索词)")
                    continue
                term_total_count = 0
                term_results_by_doc = defaultdict(int)
                for doc_path in word_files:
                    count = search_word_doc(doc_path, search_term)
                    if count > 0:
                        doc_name = os.path.basename(doc_path)
                        term_results_by_doc[doc_name] = count
                        term_total_count += count
                if term_total_count == 0:
                    result_string = "总计出现次数: 0"
                else:
                    sorted_docs = sorted(term_results_by_doc.items())
                    details = ", ".join([f"{doc_name} ({num} 次)" for doc_name, num in sorted_docs])
                    result_string = f"总计出现次数: {term_total_count}, 存在于: {details}"
                results_list.append(result_string)

        # --- 将结果添加到 Excel 工作表 ---
        status_callback("Status: 将结果添加到 Excel 数据中...")

        # 确定结果列的索引和列字母
        result_col_idx = max_col + 1  # 添加到最后一列后面
        result_col_letter = get_column_letter(result_col_idx)
        print(f"调试 (process_files): 结果将添加到第 {result_col_letter} 列。")

        # 写入结果列标题
        header_row_num = header_row_index + 1  # 转换为1-based索引
        sheet[f"{result_col_letter}{header_row_num}"] = result_col_name
        print(f"调试 (process_files): 已在位置 ({result_col_letter}{header_row_num}) 写入结果列标题 '{result_col_name}'。")

        # 写入结果数据
        for i, result in enumerate(results_list):
            row_num = first_data_row_index + i + 1  # 转换为1-based索引
            if row_num <= max_row:  # 确保在有效行范围内
                sheet[f"{result_col_letter}{row_num}"] = result
                print(f"调试 (process_files): 已在位置 ({result_col_letter}{row_num}) 写入结果 '{result[:30]}...'")

        # --- 保存修改后的工作簿 ---
        status_callback(f"Status: 保存更新后的 Excel 文件到 {excel_path}...")
        try:
            workbook.save(excel_path)
            workbook.close()
            print("调试 (process_files): 工作簿已保存并关闭。")
        except PermissionError:
             raise ValueError(f"保存到 {excel_path} 时权限不足。请确保该文件未被其他程序打开。")
        except Exception as e:
             raise ValueError(f"保存更新后的 Excel 文件失败 ({excel_path}): {e}。")

        status_callback(f"Status: 处理完成！结果已回填到 {excel_path} 中。")
        messagebox.showinfo("成功", f"处理完成。\n结果已回填到文件:\n{excel_path} 中。")


    except ImportError as ie:
        messagebox.showerror("缺少库", str(ie))
        status_callback(f"Status: 错误 - {ie}")
    except ValueError as ve:
        messagebox.showerror("输入或处理错误", str(ve))
        status_callback(f"Status: 错误 - {ve}")
    except Exception as e:
        messagebox.showerror("意外错误", f"发生意外错误:\n{type(e).__name__}: {e}")
        status_callback(f"Status: 错误 - {e}")
        print("--- TRACEBACK (process_files) ---")
        traceback.print_exc()
        print("-----------------------------------")
    finally:
        # 确保工作簿被关闭
        try:
            if 'workbook' in locals() and workbook:
                workbook.close()
                print("调试 (process_files): 已在 finally 块中关闭工作簿。")
        except Exception as e:
            print(f"调试 (process_files): 在 finally 块中关闭工作簿时出错: {e}")


# --- GUI Setup ---
class Application(tk.Frame):
    def __init__(self, master=None):
        super().__init__(master)
        self.master = master
        self.master.title("Excel Word 检索工具 (v8 - 全部使用 openpyxl)") # 更新标题
        self.master.geometry("650x350")
        self.pack(padx=10, pady=10, fill=tk.BOTH, expand=True)
        self.excel_columns = [] # 存储 openpyxl 读取的列名
        self.create_widgets()

    def create_widgets(self):
        # (GUI 布局与 v5 相同)
        file_frame = tk.LabelFrame(self, text="输入选项")
        file_frame.pack(pady=5, padx=5, fill=tk.X)

        tk.Label(file_frame, text="Excel 文件 (.xlsx):").grid(row=0, column=0, padx=5, pady=5, sticky="w")
        self.excel_path_entry = tk.Entry(file_frame, width=50)
        self.excel_path_entry.grid(row=0, column=1, columnspan=2, padx=5, pady=5, sticky="ew")
        tk.Button(file_frame, text="浏览...", command=self.select_excel_file).grid(row=0, column=3, padx=5, pady=5)

        tk.Label(file_frame, text="标题行号 (从1开始):").grid(row=1, column=0, padx=5, pady=5, sticky="w")
        self.header_row_entry = tk.Entry(file_frame, width=10)
        self.header_row_entry.insert(0, "1")
        self.header_row_entry.grid(row=1, column=1, padx=5, pady=5, sticky="w")

        self.load_columns_button = tk.Button(file_frame, text="加载列名", command=self.load_excel_columns_manual)
        self.load_columns_button.grid(row=1, column=2, padx=5, pady=5, sticky="w")

        tk.Label(file_frame, text="选择要检索的列:").grid(row=2, column=0, padx=5, pady=5, sticky="w")
        self.column_combobox = ttk.Combobox(file_frame, width=57, state='readonly')
        self.column_combobox.grid(row=2, column=1, columnspan=3, padx=5, pady=5, sticky="ew")
        self.column_combobox.set("输入标题行号后点击 [加载列名]")

        tk.Label(file_frame, text="Word 文档文件夹 (.docx):").grid(row=3, column=0, padx=5, pady=5, sticky="w")
        self.word_folder_entry = tk.Entry(file_frame, width=50)
        self.word_folder_entry.grid(row=3, column=1, columnspan=2, padx=5, pady=5, sticky="ew")
        tk.Button(file_frame, text="浏览...", command=self.select_word_folder).grid(row=3, column=3, padx=5, pady=5)

        tk.Label(file_frame, text="结果列名称:").grid(row=4, column=0, padx=5, pady=5, sticky="w")
        self.result_col_entry = tk.Entry(file_frame, width=50)
        self.result_col_entry.insert(0, "检索结果")
        self.result_col_entry.grid(row=4, column=1, columnspan=3, padx=5, pady=5, sticky="ew")

        file_frame.columnconfigure(1, weight=1)
        file_frame.columnconfigure(2, weight=1)

        self.run_button = tk.Button(self, text="开始检索并更新 Excel", command=self.start_processing, height=2, bg="#D0E0D0", fg="black")
        self.run_button.pack(pady=15, padx=5, fill=tk.X)

        self.status_label = tk.Label(self, text="Status: 空闲", relief=tk.SUNKEN, anchor="w", bd=1)
        self.status_label.pack(side=tk.BOTTOM, fill=tk.X, ipady=2)

    def update_status(self, message):
        self.status_label.config(text=message)
        self.master.update_idletasks()

    def select_excel_file(self):
        filepath = filedialog.askopenfilename(
            title="选择 Excel 文件 (.xlsx)",
            filetypes=(("Excel files", "*.xlsx"), ("All files", "*.*"))
        )
        if filepath:
             file_ext = os.path.splitext(filepath)[1].lower()
             if file_ext != '.xlsx':
                 messagebox.showerror("格式不支持", "此版本当前仅支持 .xlsx 文件。")
                 return

             self.excel_path_entry.delete(0, tk.END)
             self.excel_path_entry.insert(0, filepath)
             self.excel_columns = []
             self.column_combobox['values'] = []
             self.column_combobox.set("输入标题行号后点击 [加载列名]")
             self.column_combobox.config(state='readonly')
             self.update_status(f"Status: 已选择 Excel 文件 (.xlsx). 请输入标题行号并点击 [加载列名]。")

    def select_word_folder(self):
        folderpath = filedialog.askdirectory(title="选择包含 .docx 文件的文件夹")
        if folderpath:
            self.word_folder_entry.delete(0, tk.END)
            self.word_folder_entry.insert(0, folderpath)
            self.update_status(f"Status: 已选择 Word 文件夹: {folderpath}")

    def load_excel_columns_manual(self):
        """使用 openpyxl 直接读取指定行的单元格作为列名"""
        excel_path = self.excel_path_entry.get()
        header_row_str = self.header_row_entry.get()

        if not excel_path or not os.path.isfile(excel_path):
            messagebox.showerror("输入错误", "请先选择一个有效的 Excel 文件。")
            return
        file_ext = os.path.splitext(excel_path)[1].lower()
        if file_ext != '.xlsx':
             messagebox.showerror("格式不支持", f"此加载方法当前仅支持 .xlsx 文件。")
             return

        if not header_row_str:
            messagebox.showerror("输入错误", "请输入标题所在的行号 (从1开始)。")
            return

        try:
            header_row_num_1based = int(header_row_str)
            if header_row_num_1based <= 0:
                raise ValueError("标题行号必须是正整数。")
        except ValueError:
            messagebox.showerror("输入错误", "标题行号必须是一个有效的正整数。")
            return

        self.excel_columns = []
        self.column_combobox['values'] = []
        self.column_combobox.set("正在加载 (openpyxl)...")
        self.column_combobox.config(state='disabled')
        self.update_status(f"Status: 尝试使用 openpyxl 从第 {header_row_num_1based} 行加载列名...")
        self.master.update()

        workbook = None
        try:
            workbook = openpyxl.load_workbook(excel_path, read_only=True, data_only=True)
            sheet = workbook.active
            print(f"调试 (openpyxl): 活动工作表名称: {sheet.title}")

            if header_row_num_1based > sheet.max_row:
                raise IndexError(f"指定的标题行号 {header_row_num_1based} 超出了工作表的最大行数 ({sheet.max_row})。")

            header_row_cells = sheet[header_row_num_1based]
            loaded_columns = [str(cell.value).strip() if cell.value is not None else '' for cell in header_row_cells]
            print(f"调试 (openpyxl): 成功读取第 {header_row_num_1based} 行内容: {loaded_columns}")

            while loaded_columns and loaded_columns[-1] == '':
                loaded_columns.pop()

            if loaded_columns and any(loaded_columns):
                self.excel_columns = loaded_columns # 存储正确列名
                self.column_combobox['values'] = self.excel_columns
                self.column_combobox.current(0)
                self.column_combobox.config(state='readonly')
                self.update_status(f"Status: 已使用 openpyxl 从第 {header_row_num_1based} 行加载列名。")
            else:
                self.column_combobox.set(f"第 {header_row_num_1based} 行未找到有效列名 (openpyxl)")
                self.column_combobox.config(state='disabled')
                messagebox.showwarning("加载列名", f"使用 openpyxl 直接读取时，在 Excel 文件的第 {header_row_num_1based} 行没有找到有效的、非空的列名。")
                self.update_status(f"Status: 警告 - openpyxl 未在第 {header_row_num_1based} 行找到有效列名。")

        except Exception as e: # 简化错误处理，捕获所有异常
            error_msg = f"使用 openpyxl 加载第 {header_row_num_1based} 行时出错:\n{type(e).__name__}: {e}"
            messagebox.showerror("读取 Excel 错误 (openpyxl)", error_msg)
            self.column_combobox.set("加载列名时出错 (openpyxl)")
            self.column_combobox.config(state='disabled')
            self.update_status(f"Status: openpyxl 读取错误: {e}")
            print(f"--- TRACEBACK (load_excel_columns_manual using openpyxl) ---")
            traceback.print_exc()
            print(f"----------------------------------------------------------")
        finally:
            if workbook:
                try:
                    workbook.close()
                    print("调试 (openpyxl): 工作簿已关闭。")
                except Exception as close_err:
                    print(f"调试 (openpyxl): 关闭工作簿时出错: {close_err}")

    def start_processing(self):
        """获取 GUI 输入, 找到选中列名的索引, 并调用核心处理函数"""
        excel_path = self.excel_path_entry.get()
        header_row_str = self.header_row_entry.get()
        selected_column_name = self.column_combobox.get() # 用户选择的列名
        word_folder = self.word_folder_entry.get()
        result_col_name = self.result_col_entry.get()

        # --- 输入验证 ---
        if not excel_path or not os.path.isfile(excel_path):
            messagebox.showerror("输入错误", "请选择一个有效的 Excel 文件。")
            return
        file_ext = os.path.splitext(excel_path)[1].lower()
        if file_ext != '.xlsx':
             messagebox.showerror("格式不支持", f"此版本当前仅支持 .xlsx 文件。")
             return

        if not header_row_str:
            messagebox.showerror("输入错误", "请确保输入了标题行号。")
            return
        try:
            header_row_num_1based = int(header_row_str)
            if header_row_num_1based <= 0:
                raise ValueError("标题行号必须是正整数。")
            header_index_0based = header_row_num_1based - 1
        except ValueError:
            messagebox.showerror("输入错误", "标题行号必须是一个有效的正整数。")
            return

        # --- **关键验证和索引查找** ---
        if not self.excel_columns: # 检查 openpyxl 加载的列名列表是否为空
             messagebox.showerror("输入错误", "请先成功加载列名，然后再选择列。")
             return
        if selected_column_name not in self.excel_columns: # 检查用户选择是否在加载的列表中
            if selected_column_name.startswith("输入标题行号") or selected_column_name.startswith("正在加载") or selected_column_name.startswith("第") or selected_column_name.startswith("加载列名时出错") or selected_column_name.startswith("错误"):
                 messagebox.showerror("输入错误", "请从下拉列表中选择一个有效的列名。")
            else:
                 messagebox.showerror("输入错误", f"选择的列 '{selected_column_name}' 无效。请从加载的列名列表中选择。")
            return

        # 找到选中列名的 0-based 索引
        try:
            target_column_index = self.excel_columns.index(selected_column_name)
            print(f"调试 (start_processing): 用户选择的列 '{selected_column_name}' 对应的索引是 {target_column_index}")
        except ValueError:
            #理论上不应发生，因为前面检查过 selected_column_name in self.excel_columns
            messagebox.showerror("内部错误", f"无法在列名列表 {self.excel_columns} 中找到索引 '{selected_column_name}'。")
            return

        # --- 后续验证不变 ---
        if not word_folder or not os.path.isdir(word_folder):
            messagebox.showerror("输入错误", "请选择一个有效的包含 Word 文档的文件夹。")
            return
        if not result_col_name.strip():
            messagebox.showerror("输入错误", "请输入结果列的名称。")
            return

        self.run_button.config(state=tk.DISABLED, text="正在处理...")
        self.update_status("Status: 开始处理...")

        # --- **调用核心函数，传递列索引** ---
        process_files(
            excel_path,
            target_column_index, # <--- 传递索引
            word_folder,
            result_col_name.strip(),
            self.update_status,
            header_index_0based # 0-based 索引
        )

        self.run_button.config(state=tk.NORMAL, text="开始检索并更新 Excel")


# --- 主程序入口 ---
if __name__ == "__main__":
    root = tk.Tk()
    app = Application(master=root)
    app.mainloop()