import tkinter as tk
from tkinter import filedialog, messagebox, ttk, simpledialog
import os
import re
from collections import defaultdict
import sys
import traceback
import threading
import concurrent.futures
import time
import queue
import zipfile
import xml.etree.ElementTree as ET
from functools import lru_cache

# --- 添加对.doc文件支持的库 ---
DOC_SUPPORT = False
DOCX2TXT_AVAILABLE = False
WIN32COM_AVAILABLE = False
ANTIWORD_AVAILABLE = False

try:
    import docx2txt
    DOCX2TXT_AVAILABLE = True
    DOC_SUPPORT = True
    print("✓ docx2txt 库已加载")
except ImportError:
    print("⚠ docx2txt 库未安装，将尝试使用其他方案")

try:
    import win32com.client
    WIN32COM_AVAILABLE = True
    DOC_SUPPORT = True
    print("✓ win32com 库已加载")
except ImportError:
    print("⚠ win32com 库未安装，无法用这种方式处理 .doc 文件")

# 检查是否可以使用 subprocess 调用 antiword (如果系统中有的话)
try:
    import subprocess
    # 尝试调用 antiword 看是否可用
    result = subprocess.run(['antiword', '-v'], capture_output=True, text=True, timeout=5)
    if result.returncode == 0:
        ANTIWORD_AVAILABLE = True
        DOC_SUPPORT = True
        print("✓ antiword 工具可用")
except:
    pass

if not DOC_SUPPORT:
    print("⚠ 警告: 未安装任何支持 .doc 格式的库。只能处理 .docx 文件。")

# 导入 openpyxl
try:
    import openpyxl
    print("✓ openpyxl 库已加载")
except ImportError:
    messagebox.showerror("缺少库", "处理 .xlsx 文件需要 'openpyxl' 库。\n请先通过 pip install openpyxl 安装它。")
    sys.exit()

# --- 递归查找文件函数 ---
def find_word_files_recursive(folder_path, include_doc=True, include_docx=True, include_subfolders=True):
    """
    递归查找所有Word文档（包括子文件夹中的）
    
    Args:
        folder_path: 根文件夹路径
        include_doc: 是否包含.doc文件
        include_docx: 是否包含.docx文件
        include_subfolders: 是否包含子文件夹中的文件
        
    Returns:
        Word文档完整路径列表
    """
    word_files = []
    
    if include_subfolders:
        # 递归搜索所有子文件夹
        for root, dirs, files in os.walk(folder_path):
            for file in files:
                file_path = os.path.join(root, file)
                file_ext = os.path.splitext(file)[1].lower()
                
                if (include_docx and file_ext == '.docx') or (include_doc and file_ext == '.doc'):
                    word_files.append(file_path)
    else:
        # 只搜索当前文件夹
        for file in os.listdir(folder_path):
            file_path = os.path.join(folder_path, file)
            if os.path.isfile(file_path):
                file_ext = os.path.splitext(file)[1].lower()
                
                if (include_docx and file_ext == '.docx') or (include_doc and file_ext == '.doc'):
                    word_files.append(file_path)
    
    return word_files

# --- 全局变量 ---
CANCEL_PROCESSING = False  # 用于用户取消处理的标志
MAX_WORKERS = min(32, os.cpu_count() * 2)  # 根据CPU核心数设置最大工作线程数
DOCUMENT_CACHE = {}  # 文档内容缓存
FAST_MODE = True  # 使用快速模式（直接解析zip文件而不是使用python-docx）
WORD_APP = None  # 用于存储 Word 应用程序对象
WORD_COM_FAILED = False  # 标记Word COM是否已经失败过

# --- 文档内容提取函数 ---
@lru_cache(maxsize=10)  # 缓存最近的10个文档解析结果
def extract_text_from_docx_fast(docx_path):
    """直接从.docx文件（实际上是zip文件）中提取文本，比python-docx快很多"""
    text = []
    try:
        # 打开docx文件作为zip
        with zipfile.ZipFile(docx_path) as docx_zip:
            # 查找所有包含文档内容的xml文件
            content_files = [f for f in docx_zip.namelist() if f.startswith('word/document.xml') or 
                             f.startswith('word/footer') or 
                             f.startswith('word/header')]
            
            # 处理每个内容文件
            for content_file in content_files:
                try:
                    with docx_zip.open(content_file) as content:
                        # 解析XML
                        tree = ET.parse(content)
                        root = tree.getroot()
                        
                        # 定义namespace
                        ns = {'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'}
                        
                        # 提取所有文本元素
                        for paragraph in root.findall('.//w:p', ns):
                            para_text = []
                            for text_element in paragraph.findall('.//w:t', ns):
                                if text_element.text:
                                    para_text.append(text_element.text)
                            if para_text:
                                text.append(''.join(para_text))
                except Exception as xml_err:
                    print(f"提取文档 {os.path.basename(docx_path)} 中的 {content_file} 内容时出错: {xml_err}")
    except Exception as e:
        print(f"快速解析文档 {os.path.basename(docx_path)} 时出错: {type(e).__name__} - {e}")
        return ""
    
    return '\n'.join(text)

def test_word_com_availability():
    """测试Word COM是否可用"""
    global WORD_COM_FAILED
    
    if WORD_COM_FAILED:
        return False
        
    try:
        import win32com.client
        # 尝试创建Word应用程序实例
        word_app = win32com.client.Dispatch("Word.Application")
        word_app.Visible = False
        word_app.Quit()
        return True
    except Exception as e:
        print(f"Word COM 不可用: {e}")
        WORD_COM_FAILED = True
        return False

def extract_text_from_doc_with_win32com(doc_path):
    """使用 win32com.client 从 .doc 文件中提取文本（需要安装 MS Word）"""
    global WORD_APP, WORD_COM_FAILED
    
    if not WIN32COM_AVAILABLE:
        return f"无法处理 .doc 文件: win32com 库未安装。"
    
    if WORD_COM_FAILED:
        return f"无法处理 .doc 文件: Word COM 组件不可用。"
    
    text = ""
    max_retries = 3
    retry_count = 0
    
    while retry_count < max_retries:
        try:
            # 如果还没初始化 Word 应用程序，则创建一个
            if WORD_APP is None:
                print(f"尝试创建 Word 应用程序实例 (第 {retry_count + 1} 次)...")
                WORD_APP = win32com.client.Dispatch("Word.Application")
                WORD_APP.Visible = False
                WORD_APP.DisplayAlerts = False  # 禁用警告对话框
            
            # 打开文档
            doc = WORD_APP.Documents.Open(doc_path, ReadOnly=True, ConfirmConversions=False)
            
            try:
                # 提取文本
                text = doc.Content.Text
                break  # 成功提取，退出重试循环
            finally:
                # 关闭文档但不退出 Word
                doc.Close(SaveChanges=False)
                
        except Exception as e:
            retry_count += 1
            error_code = getattr(e, 'hresult', None)
            
            print(f"使用 win32com 提取 .doc 文件 {os.path.basename(doc_path)} 时出错 (第 {retry_count} 次): {type(e).__name__} - {e}")
            
            # 特定错误码处理
            if error_code == -2147221005:  # REGDB_E_CLASSNOTREG
                print("错误: Word 应用程序未正确注册或未安装")
                WORD_COM_FAILED = True
                return f"无法处理 .doc 文件: Microsoft Word 未安装或 COM 组件未注册。请安装 Microsoft Word 或使用 docx2txt 库。"
            elif error_code == -2147024894:  # 文件不存在
                return f"无法处理 .doc 文件: 文件不存在或无法访问 - {doc_path}"
            elif "0x800A11FD" in str(e):  # 文件格式错误
                return f"无法处理 .doc 文件: 文件格式可能损坏 - {os.path.basename(doc_path)}"
            
            # 如果 Word 应用程序实例出错，重置它
            if WORD_APP is not None:
                try:
                    WORD_APP.Quit()
                except:
                    pass
                WORD_APP = None
            
            if retry_count >= max_retries:
                WORD_COM_FAILED = True
                return f"无法处理 .doc 文件: 多次尝试失败 - {os.path.basename(doc_path)}"
            else:
                time.sleep(1)  # 等待1秒后重试
                
    return text

def extract_text_from_doc_with_antiword(doc_path):
    """使用 antiword 工具从 .doc 文件中提取文本"""
    if not ANTIWORD_AVAILABLE:
        return f"无法处理 .doc 文件: antiword 工具不可用。"

    try:
        import subprocess
        result = subprocess.run(['antiword', doc_path],
                              capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            return result.stdout
        else:
            return f"antiword 处理失败: {result.stderr}"
    except subprocess.TimeoutExpired:
        return f"antiword 处理超时: {os.path.basename(doc_path)}"
    except Exception as e:
        print(f"使用 antiword 提取 .doc 文件 {os.path.basename(doc_path)} 时出错: {type(e).__name__} - {e}")
        return f"antiword 处理失败: {str(e)}"

def extract_text_from_doc_basic(doc_path):
    """基本的 .doc 文件文本提取（最后的回退方案）"""
    try:
        # 尝试以二进制模式读取文件并提取可见文本
        with open(doc_path, 'rb') as f:
            content = f.read()

        # 简单的文本提取：查找可打印的ASCII字符序列
        import string
        text_parts = []
        current_text = ""

        for byte in content:
            char = chr(byte) if byte < 128 else ''
            if char in string.printable and char not in '\x0b\x0c':
                current_text += char
            else:
                if len(current_text) > 3:  # 只保留长度大于3的文本片段
                    text_parts.append(current_text.strip())
                current_text = ""

        # 添加最后一个文本片段
        if len(current_text) > 3:
            text_parts.append(current_text.strip())

        # 过滤掉明显的垃圾文本
        filtered_parts = []
        for part in text_parts:
            # 过滤掉主要由特殊字符组成的文本
            if len(part) > 5 and sum(c.isalnum() or c.isspace() for c in part) / len(part) > 0.7:
                filtered_parts.append(part)

        result = ' '.join(filtered_parts)
        return result if result else "基本提取未找到可读文本"

    except Exception as e:
        print(f"基本文本提取失败 {os.path.basename(doc_path)}: {type(e).__name__} - {e}")
        return f"基本提取失败: {str(e)}"

def extract_text_from_doc_with_docx2txt(doc_path):
    """使用 docx2txt 从 .doc 文件中提取文本（改进版，更好的错误处理）"""
    if not DOCX2TXT_AVAILABLE:
        return f"无法处理 .doc 文件: docx2txt 库未安装。"

    try:
        # 首先检查文件是否真的是 .doc 文件
        with open(doc_path, 'rb') as f:
            header = f.read(8)
            # .doc 文件的魔术字节
            if not (header.startswith(b'\xd0\xcf\x11\xe0') or header.startswith(b'\x0d\x44\x4f\x43')):
                return f"文件格式错误: {os.path.basename(doc_path)} 不是有效的 .doc 文件"

        text = docx2txt.process(doc_path)
        return text if text else ""
    except zipfile.BadZipFile:
        # docx2txt 把 .doc 当作 zip 文件处理时会出现这个错误
        print(f"docx2txt 无法处理 .doc 文件 {os.path.basename(doc_path)}: 不是 zip 格式")
        return "docx2txt_zip_error"  # 特殊标记，表示需要尝试其他方法
    except Exception as e:
        print(f"使用 docx2txt 提取 .doc 文件 {os.path.basename(doc_path)} 时出错: {type(e).__name__} - {e}")
        return f"docx2txt 处理失败: {str(e)}"

def extract_text_from_document(doc_path):
    """根据文件类型选择合适的文本提取方法（改进版，更好的回退机制）"""
    file_ext = os.path.splitext(doc_path)[1].lower()

    if file_ext == '.docx':
        return extract_text_from_docx_fast(doc_path)
    elif file_ext == '.doc':
        # 按优先级尝试不同的处理方法
        methods_tried = []

        # 方法1: 尝试 win32com (最可靠，但需要 Word)
        if WIN32COM_AVAILABLE and not WORD_COM_FAILED:
            methods_tried.append("win32com")
            result = extract_text_from_doc_with_win32com(doc_path)
            if not result.startswith("无法处理") and not result.startswith("多次尝试失败"):
                return result

        # 方法2: 尝试 docx2txt (可能对某些 .doc 文件有效)
        if DOCX2TXT_AVAILABLE:
            methods_tried.append("docx2txt")
            result = extract_text_from_doc_with_docx2txt(doc_path)
            if result != "docx2txt_zip_error" and not result.startswith("docx2txt 处理失败"):
                return result

        # 方法3: 尝试 antiword (如果可用)
        if ANTIWORD_AVAILABLE:
            methods_tried.append("antiword")
            result = extract_text_from_doc_with_antiword(doc_path)
            if not result.startswith("antiword 处理失败"):
                return result

        # 方法4: 最后的回退方案 - 基本文本提取
        methods_tried.append("基本提取")
        result = extract_text_from_doc_basic(doc_path)
        if not result.startswith("基本提取失败") and result != "基本提取未找到可读文本":
            return result

        # 所有方法都失败了
        if not methods_tried:
            return f"无法处理 .doc 文件: 缺少可用的处理库。请安装 Microsoft Word、docx2txt 库或 antiword 工具。"
        else:
            return f"无法处理 .doc 文件 {os.path.basename(doc_path)}: 已尝试 {', '.join(methods_tried)} 方法均失败。建议转换为 .docx 格式。"
    else:
        return f"不支持的文件格式: {file_ext}"

# --- 预加载所有文档内容到内存 ---
def preload_documents(word_files, status_callback, progress_var=None):
    """预加载所有文档内容到内存中以加速后续搜索"""
    global DOCUMENT_CACHE, CANCEL_PROCESSING
    DOCUMENT_CACHE = {}  # 清空缓存
    
    total_files = len(word_files)
    status_callback(f"Status: 预加载 {total_files} 个Word文档到内存中...")
    
    if progress_var is not None:
        progress_var.set(0)
    
    # 创建进度报告队列
    progress_queue = queue.Queue()
    
    # 监控线程
    def monitor_progress():
        completed = 0
        failed = 0
        while completed + failed < total_files and not CANCEL_PROCESSING:
            try:
                file_idx, file_name, success = progress_queue.get(timeout=0.5)
                if success:
                    completed += 1
                else:
                    failed += 1
                    
                if progress_var is not None:
                    progress_var.set(((completed + failed) / total_files) * 100)
                    
                status_text = f"Status: 预加载文档 ({completed + failed}/{total_files}) {os.path.basename(file_name)}"
                if failed > 0:
                    status_text += f" [成功: {completed}, 失败: {failed}]"
                status_callback(status_text)
                progress_queue.task_done()
            except queue.Empty:
                continue
    
    monitor_thread = threading.Thread(target=monitor_progress, daemon=True)
    monitor_thread.start()
    
    start_time = time.time()
    
    # 使用线程池加载文档
    with concurrent.futures.ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
        future_to_file = {}
        for idx, file_path in enumerate(word_files):
            future = executor.submit(
                extract_text_from_document, 
                file_path
            )
            future_to_file[future] = (idx, file_path)
        
        for future in concurrent.futures.as_completed(future_to_file):
            if CANCEL_PROCESSING:
                executor.shutdown(wait=False, cancel_futures=True)
                break
                
            idx, file_path = future_to_file[future]
            try:
                text_content = future.result()
                success = True
                
                # 检查是否是错误消息
                if text_content.startswith("无法处理") or text_content.startswith("不支持") or "处理失败" in text_content:
                    print(f"预加载文档失败: {file_path} - {text_content}")
                    DOCUMENT_CACHE[file_path] = ""  # 存储空字符串，避免重复尝试
                    success = False
                else:
                    DOCUMENT_CACHE[file_path] = text_content.lower()  # 存储小写版本以便不区分大小写搜索
                    
                progress_queue.put((idx, file_path, success))
            except Exception as e:
                print(f"预加载文档 {os.path.basename(file_path)} 时出错: {e}")
                DOCUMENT_CACHE[file_path] = ""  # 存储空字符串
                progress_queue.put((idx, file_path, False))  # 标记为失败
    
    # 等待监控线程完成
    if monitor_thread.is_alive():
        progress_queue.join()
        monitor_thread.join(timeout=1.0)
    
    # 计算耗时
    end_time = time.time()
    elapsed = end_time - start_time
    
    if CANCEL_PROCESSING:
        status_callback("Status: 预加载过程被取消")
        return False
    else:
        successful_files = len([path for path, content in DOCUMENT_CACHE.items() if content])
        failed_files = total_files - successful_files
        
        status_text = f"Status: 文档预加载完成，共 {total_files} 个文件，用时 {elapsed:.2f} 秒"
        if failed_files > 0:
            status_text += f" [成功: {successful_files}, 失败: {failed_files}]"
        status_callback(status_text)
        return True

# --- 快速搜索文档 ---
def search_word_doc_fast(doc_path, search_term):
    """使用预加载的文档内容进行快速搜索"""
    global DOCUMENT_CACHE
    
    term_lower = str(search_term).lower().strip()
    if not term_lower:
        return 0
    
    if doc_path not in DOCUMENT_CACHE:
        # 如果缓存中没有，临时加载
        doc_content = extract_text_from_document(doc_path).lower()
        DOCUMENT_CACHE[doc_path] = doc_content
    else:
        doc_content = DOCUMENT_CACHE[doc_path]
    
    # 如果文档内容为空（可能是加载失败），返回0
    if not doc_content:
        return 0
    
    # 使用字符串计数方法进行快速计数
    return doc_content.count(term_lower)

# --- 处理单个搜索词 ---
def process_search_term(term, word_files, progress_queue=None, term_idx=None, total_terms=None):
    """处理单个搜索词，返回结果"""
    if not term.strip():
        result = {"term": term, "result": "跳过 (空搜索词)", "is_empty": True}
        if progress_queue:
            progress_queue.put((term_idx, total_terms, term))
        return result
    
    term_total_count = 0
    term_results_by_doc = {}
    
    for doc_path in word_files:
        if CANCEL_PROCESSING:
            break
        count = search_word_doc_fast(doc_path, term)
        if count > 0:
            doc_name = os.path.basename(doc_path)
            term_results_by_doc[doc_name] = count
            term_total_count += count
    
    if term_total_count == 0:
        result_string = "总计出现次数: 0"
    else:
        sorted_docs = sorted(term_results_by_doc.items())
        details = ", ".join([f"{doc_name} ({num} 次)" for doc_name, num in sorted_docs])
        result_string = f"总计出现次数: {term_total_count}, 存在于: {details}"
    
    result = {"term": term, "result": result_string, "is_empty": False}
    if progress_queue:
        progress_queue.put((term_idx, total_terms, term))
    return result

# --- 更新进度的线程函数 ---
def progress_monitor(progress_queue, status_callback, total_terms):
    """从队列中读取进度信息并更新UI"""
    completed = 0
    while completed < total_terms:
        try:
            term_idx, total, term = progress_queue.get(timeout=0.5)
            completed += 1
            term_display = term[:30] + '...' if len(term) > 30 else term
            status_callback(f"Status: 处理第 {term_idx+1}/{total} 个词条 (搜索 '{term_display}')... 已完成: {completed}/{total_terms}")
            progress_queue.task_done()
        except queue.Empty:
            if CANCEL_PROCESSING:
                status_callback("Status: 处理已取消")
                break
            continue

# --- 优化后的主处理函数 ---
def process_files(excel_path,
                  target_col_index,
                  word_folder,
                  result_col_name,
                  status_callback,
                  header_row_index,
                  progress_var=None,  # 进度条变量
                  word_files=None     # 添加参数以接受预先收集的文件列表
                 ):
    """Main function using openpyxl with document preloading. Reads data, searches, adds results back."""
    global CANCEL_PROCESSING, DOCUMENT_CACHE, WORD_APP
    CANCEL_PROCESSING = False
    DOCUMENT_CACHE = {}  # 清空文档缓存
    
    try:
        if not excel_path.lower().endswith('.xlsx'):
             raise ValueError("此处理流程当前仅支持 .xlsx 文件。")

        status_callback(f"Status: 读取 Excel 文件 '{os.path.basename(excel_path)}' (使用 openpyxl)...")

        try:
            # --- 使用 openpyxl 读取 Excel 文件 ---
            workbook = openpyxl.load_workbook(excel_path, data_only=True)
            sheet = workbook.active
            print(f"调试: 成功使用 openpyxl 读取工作簿，活动工作表名称: {sheet.title}")
            
            # 获取工作表的最大行数和列数
            max_row = sheet.max_row
            max_col = sheet.max_column
            print(f"调试: 工作表尺寸: {max_row} 行 x {max_col} 列")
            
            # 验证头部行索引是否有效
            if header_row_index >= max_row:
                raise ValueError(f"标题行索引 {header_row_index} (行号 {header_row_index+1}) 超出了工作表的最大行数 ({max_row})。")
            
            # 验证目标列索引是否有效
            if not (0 <= target_col_index < max_col):
                raise ValueError(f"目标列索引 {target_col_index} 超出了工作表的列范围 (0 到 {max_col-1})。")

        except FileNotFoundError:
            raise ValueError(f"Excel 文件未找到: {excel_path}")
        except Exception as e:
            error_type = type(e).__name__
            raise ValueError(f"使用 openpyxl 读取 Excel 数据时出错 ({error_type}): {e}。\n请确保文件有效且未损坏。")

        # --- 确定数据开始行 ---
        first_data_row_index = header_row_index + 1

        # --- 检查数据起始行索引是否有效 ---
        if first_data_row_index > max_row:
             messagebox.showwarning("无数据", f"根据标题行号 {header_row_index+1}，未找到数据行（文件总行数 {max_row}）。无法进行搜索。")
             status_callback("Status: 未找到数据行，处理中止。")
             workbook.close()
             return
        elif first_data_row_index == max_row:
             print("提示: 文件中似乎没有数据行（只有标题行或之前的行）。")
             # 这种情况允许继续，但搜索结果会是空的

        # --- 使用 openpyxl 提取搜索词 ---
        # 将列索引转换为字母列引用 (A, B, C, ...)
        from openpyxl.utils import get_column_letter
        target_col_letter = get_column_letter(target_col_index + 1)  # openpyxl 列是从 1 开始的
        
        # 提取搜索词
        search_terms = []
        for row_idx in range(first_data_row_index, max_row + 1):
            cell_value = sheet[f"{target_col_letter}{row_idx+1}"].value  # openpyxl 行也是从 1 开始的
            search_terms.append("" if cell_value is None else str(cell_value))
        
        total_terms = len(search_terms)
        print(f"调试: 从第 {first_data_row_index+1} 行开始，使用第 {target_col_letter} 列提取了 {total_terms} 个搜索词。")

        status_callback(f"Status: 使用第 {target_col_index + 1} 列的数据进行检索。") # 显示 1-based 列号

        # --- 获取 Word 文件（包括.doc和.docx）或使用提供的列表 ---
        status_callback("Status: 准备处理 Word 文档...")
        if not os.path.isdir(word_folder):
             raise ValueError(f"Word 文档文件夹未找到或不是有效目录: {word_folder}")
        
        # 如果提供了文件列表，使用它；否则查找文件（但不递归）
        if word_files is None:
            # 同时搜索 .doc 和 .docx 文件（仅当前文件夹）
            word_files = [os.path.join(word_folder, f) for f in os.listdir(word_folder)
                        if (f.lower().endswith('.docx') or f.lower().endswith('.doc')) and 
                        os.path.isfile(os.path.join(word_folder, f))]
            status_callback(f"Status: 在当前文件夹中找到 {len(word_files)} 个 Word 文件。")
        else:
            status_callback(f"Status: 准备处理 {len(word_files)} 个 Word 文件。")
        
        if not word_files:
            messagebox.showwarning("无文件", f"在选定文件夹及其子文件夹中未找到 '.doc' 或 '.docx' 文件:\n{word_folder}")
            status_callback("Status: 未找到 Word 文件。")
            workbook.close()
            return

        # 统计文件类型
        doc_count = sum(1 for f in word_files if f.lower().endswith('.doc'))
        docx_count = sum(1 for f in word_files if f.lower().endswith('.docx'))
        status_callback(f"Status: 找到 {doc_count} 个 .doc 文件和 {docx_count} 个 .docx 文件，共 {len(word_files)} 个。")
        
        # 检查是否有 .doc 文件但没有支持库
        if doc_count > 0 and not DOC_SUPPORT:
            messagebox.showwarning("缺少库", 
                                 f"发现 {doc_count} 个 .doc 文件，但未安装支持库。\n"
                                 f"请安装 'win32com' 或 'docx2txt' 库，否则这些文件将被跳过。\n"
                                 f"可以使用命令: pip install pywin32 或 pip install docx2txt")

        # --- 预加载所有文档 ---
        if not preload_documents(word_files, status_callback, progress_var):
            if CANCEL_PROCESSING:
                workbook.close()
                return
        
        # --- 设置进度条（如果提供） ---
        if progress_var is not None:
            progress_var.set(0)  # 重置进度条
        
        # --- 多线程处理搜索 ---
        status_callback(f"Status: 准备在 {len(word_files)} 个 Word 文档中搜索 {total_terms} 个词条 (使用 {MAX_WORKERS} 个线程)...")
        
        # 设置进度报告队列和监控线程
        progress_queue = queue.Queue()
        monitor_thread = threading.Thread(
            target=progress_monitor, 
            args=(progress_queue, status_callback, total_terms),
            daemon=True
        )
        monitor_thread.start()
        
        results = []
        start_time = time.time()
        
        # 使用 ThreadPoolExecutor 进行并行处理
        with concurrent.futures.ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
            # 准备函数参数
            future_to_term = {
                executor.submit(
                    process_search_term, 
                    term, 
                    word_files,
                    progress_queue,
                    i,
                    total_terms
                ): (i, term) for i, term in enumerate(search_terms)
            }
            
            # 收集结果
            for future in concurrent.futures.as_completed(future_to_term):
                if CANCEL_PROCESSING:
                    executor.shutdown(wait=False, cancel_futures=True)
                    break
                
                idx, term = future_to_term[future]
                try:
                    result = future.result()
                    # 将结果放在正确的位置
                    while len(results) <= idx:
                        results.append(None)
                    results[idx] = result["result"]
                    
                    # 更新进度条
                    if progress_var is not None:
                        completed = len([r for r in results if r is not None])
                        progress_var.set((completed / total_terms) * 100)
                        
                except Exception as e:
                    print(f"处理搜索词 '{term}' 时出错: {e}")
                    traceback.print_exc()
                    while len(results) <= idx:
                        results.append(None)
                    results[idx] = f"处理出错: {str(e)}"
        
        # 等待监控线程完成
        if monitor_thread.is_alive():
            progress_queue.join()
            monitor_thread.join(timeout=1.0)
        
        # 检查是否取消
        if CANCEL_PROCESSING:
            status_callback("Status: 处理已被用户取消")
            messagebox.showinfo("已取消", "处理已被用户取消。")
            workbook.close()
            return
        
        end_time = time.time()
        elapsed = end_time - start_time
        status_callback(f"Status: 所有搜索词处理完成，用时 {elapsed:.2f} 秒。将结果写入 Excel...")

        # --- 将结果添加到 Excel 工作表 ---
        status_callback("Status: 将结果添加到 Excel 数据中...")

        # 确定结果列的索引和列字母
        result_col_idx = max_col + 1  # 添加到最后一列后面
        result_col_letter = get_column_letter(result_col_idx)
        print(f"调试: 结果将添加到第 {result_col_letter} 列。")

        # 写入结果列标题
        header_row_num = header_row_index + 1  # 转换为1-based索引
        sheet[f"{result_col_letter}{header_row_num}"] = result_col_name
        print(f"调试: 已在位置 ({result_col_letter}{header_row_num}) 写入结果列标题 '{result_col_name}'。")

        # 写入结果数据 - 优化：批量处理
        for i, result in enumerate(results):
            row_num = first_data_row_index + i + 1  # 转换为1-based索引
            if row_num <= max_row:  # 确保在有效行范围内
                sheet[f"{result_col_letter}{row_num}"] = result
                if i % 100 == 0:  # 每100行输出一次日志，减少控制台输出量
                    print(f"调试: 已写入第 {row_num} 行结果")

        # --- 保存修改后的工作簿 ---
        status_callback(f"Status: 保存更新后的 Excel 文件到 {excel_path}...")
        try:
            workbook.save(excel_path)
            workbook.close()
            print("调试: 工作簿已保存并关闭。")
        except PermissionError:
             raise ValueError(f"保存到 {excel_path} 时权限不足。请确保该文件未被其他程序打开。")
        except Exception as e:
             raise ValueError(f"保存更新后的 Excel 文件失败 ({excel_path}): {e}。")

        status_callback(f"Status: 处理完成！结果已回填到 {excel_path} 中。用时 {elapsed:.2f} 秒。")
        messagebox.showinfo("成功", f"处理完成。\n结果已回填到文件:\n{excel_path} 中。\n总用时: {elapsed:.2f} 秒")


    except ImportError as ie:
        messagebox.showerror("缺少库", str(ie))
        status_callback(f"Status: 错误 - {ie}")
    except ValueError as ve:
        messagebox.showerror("输入或处理错误", str(ve))
        status_callback(f"Status: 错误 - {ve}")
    except Exception as e:
        messagebox.showerror("意外错误", f"发生意外错误:\n{type(e).__name__}: {e}")
        status_callback(f"Status: 错误 - {e}")
        print("--- TRACEBACK (process_files) ---")
        traceback.print_exc()
        print("-----------------------------------")
    finally:
        # 如果使用了 Word 应用程序，关闭它
        if WORD_APP is not None:
            try:
                WORD_APP.Quit()
                WORD_APP = None
                print("调试: 已关闭 Word 应用程序")
            except:
                print("调试: 关闭 Word 应用程序时出错")
        
        # 清空文档缓存
        DOCUMENT_CACHE.clear()
        # 确保工作簿被关闭
        try:
            if 'workbook' in locals() and workbook:
                workbook.close()
                print("调试: 已在 finally 块中关闭工作簿。")
        except Exception as e:
            print(f"调试: 在 finally 块中关闭工作簿时出错: {e}")

# --- 改进的 GUI 类 ---
class Application(tk.Frame):
    def __init__(self, master=None):
        super().__init__(master)
        self.master = master
        self.master.title("Excel Word 检索工具 (极速版 - 支持 doc/docx)")
        self.master.geometry("700x600")  # 增加窗口高度
        self.pack(padx=10, pady=10, fill=tk.BOTH, expand=True)
        self.excel_columns = [] # 存储 openpyxl 读取的列名
        self.processing_thread = None  # 用于存储处理线程
        self.create_widgets()
        
        # 测试Word COM可用性
        if WIN32COM_AVAILABLE:
            if test_word_com_availability():
                self.update_status("Status: ✓ Word COM 组件可用，支持完整的 .doc 文件处理")
            else:
                self.update_status("Status: ⚠ Word COM 组件不可用，建议安装 docx2txt 库处理 .doc 文件")
        
        # 显示 .doc 支持状态
        if DOC_SUPPORT:
            support_methods = []
            if WIN32COM_AVAILABLE and not WORD_COM_FAILED:
                support_methods.append("Word COM")
            if DOCX2TXT_AVAILABLE:
                support_methods.append("docx2txt")
            if ANTIWORD_AVAILABLE:
                support_methods.append("antiword")
            self.update_status(f"Status: 已加载支持库 {' 和 '.join(support_methods)}，可以处理 .doc 和 .docx 文件")
        else:
            self.update_status("Status: 未找到支持 .doc 格式的库，只能处理 .docx 文件")

    def create_widgets(self):
        # 输入选项框架
        file_frame = tk.LabelFrame(self, text="输入选项")
        file_frame.pack(pady=5, padx=5, fill=tk.X)

        tk.Label(file_frame, text="Excel 文件 (.xlsx):").grid(row=0, column=0, padx=5, pady=5, sticky="w")
        self.excel_path_entry = tk.Entry(file_frame, width=50)
        self.excel_path_entry.grid(row=0, column=1, columnspan=2, padx=5, pady=5, sticky="ew")
        tk.Button(file_frame, text="浏览...", command=self.select_excel_file).grid(row=0, column=3, padx=5, pady=5)

        tk.Label(file_frame, text="标题行号 (从1开始):").grid(row=1, column=0, padx=5, pady=5, sticky="w")
        self.header_row_entry = tk.Entry(file_frame, width=10)
        self.header_row_entry.insert(0, "1")
        self.header_row_entry.grid(row=1, column=1, padx=5, pady=5, sticky="w")

        self.load_columns_button = tk.Button(file_frame, text="加载列名", command=self.load_excel_columns_manual)
        self.load_columns_button.grid(row=1, column=2, padx=5, pady=5, sticky="w")

        tk.Label(file_frame, text="选择要检索的列:").grid(row=2, column=0, padx=5, pady=5, sticky="w")
        self.column_combobox = ttk.Combobox(file_frame, width=57, state='readonly')
        self.column_combobox.grid(row=2, column=1, columnspan=3, padx=5, pady=5, sticky="ew")
        self.column_combobox.set("输入标题行号后点击 [加载列名]")

        tk.Label(file_frame, text="Word 文档文件夹:").grid(row=3, column=0, padx=5, pady=5, sticky="w")
        self.word_folder_entry = tk.Entry(file_frame, width=50)
        self.word_folder_entry.grid(row=3, column=1, columnspan=2, padx=5, pady=5, sticky="ew")
        tk.Button(file_frame, text="浏览...", command=self.select_word_folder).grid(row=3, column=3, padx=5, pady=5)

        tk.Label(file_frame, text="结果列名称:").grid(row=4, column=0, padx=5, pady=5, sticky="w")
        self.result_col_entry = tk.Entry(file_frame, width=50)
        self.result_col_entry.insert(0, "检索结果")
        self.result_col_entry.grid(row=4, column=1, columnspan=3, padx=5, pady=5, sticky="ew")

        # 线程数设置
        tk.Label(file_frame, text=f"线程数 (最大{MAX_WORKERS}):").grid(row=5, column=0, padx=5, pady=5, sticky="w")
        self.thread_slider = ttk.Scale(file_frame, from_=1, to=MAX_WORKERS, orient="horizontal", length=200)
        self.thread_slider.set(min(16, MAX_WORKERS))  # 默认16线程或最大线程数
        self.thread_slider.grid(row=5, column=1, padx=5, pady=5, sticky="w")
        self.thread_label = tk.Label(file_frame, text=f"{min(16, MAX_WORKERS)}")
        self.thread_label.grid(row=5, column=2, padx=5, pady=5, sticky="w")
        self.thread_slider.bind("<Motion>", self.update_thread_label)
        
        # 性能优化选项
        optimization_frame = tk.LabelFrame(self, text="性能优化选项")
        optimization_frame.pack(pady=5, padx=5, fill=tk.X)
        
        # 预加载文档选项
        self.preload_var = tk.BooleanVar(value=True)
        self.preload_check = tk.Checkbutton(optimization_frame, text="预加载所有文档 (推荐，大幅提高搜索速度)", 
                                           variable=self.preload_var)
        self.preload_check.grid(row=0, column=0, columnspan=3, padx=5, pady=5, sticky="w")
        
        # 快速解析模式
        self.fast_parse_var = tk.BooleanVar(value=True)
        self.fast_parse_check = tk.Checkbutton(optimization_frame, text="使用快速解析模式 (推荐，比标准解析快5-10倍)", 
                                              variable=self.fast_parse_var)
        self.fast_parse_check.grid(row=1, column=0, columnspan=3, padx=5, pady=5, sticky="w")
        
        # 添加子文件夹搜索选项
        self.include_subfolders_var = tk.BooleanVar(value=True)
        self.include_subfolders_check = tk.Checkbutton(optimization_frame, text="包含所有子文件夹中的文档 (递归搜索)", 
                                                     variable=self.include_subfolders_var)
        self.include_subfolders_check.grid(row=2, column=0, columnspan=3, padx=5, pady=5, sticky="w")
        
        # 文件类型选择
        file_types_frame = tk.Frame(optimization_frame)
        file_types_frame.grid(row=3, column=0, columnspan=3, padx=5, pady=5, sticky="w")
        
        tk.Label(file_types_frame, text="处理的文件类型:").pack(side=tk.LEFT, padx=(0, 5))
        
        # 复选框用于选择文件类型
        self.docx_var = tk.BooleanVar(value=True)
        self.doc_var = tk.BooleanVar(value=DOC_SUPPORT)  # 仅在支持 .doc 时默认选中
        
        tk.Checkbutton(file_types_frame, text=".docx", variable=self.docx_var).pack(side=tk.LEFT, padx=5)
        self.doc_checkbox = tk.Checkbutton(file_types_frame, text=".doc", variable=self.doc_var)
        self.doc_checkbox.pack(side=tk.LEFT, padx=5)
        
        # 如果不支持 .doc，禁用复选框
        if not DOC_SUPPORT:
            self.doc_checkbox.config(state=tk.DISABLED)
            self.doc_var.set(False)
        
        # 添加库状态显示
        status_info_frame = tk.LabelFrame(self, text="库状态信息", fg="blue")
        status_info_frame.pack(pady=5, padx=5, fill=tk.X)
        
        # 显示各种库的状态
        libs_text = []
        if WIN32COM_AVAILABLE:
            word_status = "可用" if not WORD_COM_FAILED else "不可用 (Word未安装或COM组件未注册)"
            libs_text.append(f"• win32com: {word_status}")
        else:
            libs_text.append("• win32com: 未安装")

        if DOCX2TXT_AVAILABLE:
            libs_text.append("• docx2txt: 可用")
        else:
            libs_text.append("• docx2txt: 未安装")

        if ANTIWORD_AVAILABLE:
            libs_text.append("• antiword: 可用")
        else:
            libs_text.append("• antiword: 未安装")

        libs_text.append("• openpyxl: 可用")
        
        self.libs_label = tk.Label(status_info_frame, text="\n".join(libs_text), 
                                  justify=tk.LEFT, font=("Consolas", 9))
        self.libs_label.pack(padx=5, pady=5, anchor="w")
        
        file_frame.columnconfigure(1, weight=1)
        file_frame.columnconfigure(2, weight=1)

        # 按钮框架
        button_frame = tk.Frame(self)
        button_frame.pack(pady=10, fill=tk.X)
        
        self.run_button = tk.Button(button_frame, text="开始检索并更新 Excel", command=self.start_processing, 
                                    height=2, bg="#D0E0D0", fg="black")
        self.run_button.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        
        self.cancel_button = tk.Button(button_frame, text="取消", command=self.cancel_processing, 
                                       height=2, bg="#E0D0D0", fg="black", state=tk.DISABLED)
        self.cancel_button.pack(side=tk.RIGHT, padx=5, fill=tk.X, expand=True)

        # 进度条
        progress_frame = tk.Frame(self)
        progress_frame.pack(pady=10, fill=tk.X)
        
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.pack(fill=tk.X, padx=5, pady=5)

        # 状态标签
        self.status_label = tk.Label(self, text="Status: 空闲", relief=tk.SUNKEN, anchor="w", bd=1, height=2)
        self.status_label.pack(side=tk.BOTTOM, fill=tk.X, ipady=2, pady=(10, 0))

    def update_thread_label(self, event=None):
        """更新线程数量标签"""
        threads = int(self.thread_slider.get())
        self.thread_label.config(text=f"{threads}")

    def update_status(self, message):
        """更新状态栏文本"""
        self.status_label.config(text=message)
        self.master.update_idletasks()
        
        # 尝试从消息中提取进度信息并更新进度条
        if "已完成" in message:
            try:
                parts = message.split("已完成: ")[1].split("/")
                completed = int(parts[0])
                total = int(parts[1].split(" ")[0])
                progress = (completed / total) * 100
                self.progress_var.set(progress)
            except (IndexError, ValueError):
                pass

    def select_excel_file(self):
        """选择Excel文件"""
        filepath = filedialog.askopenfilename(
            title="选择 Excel 文件 (.xlsx)",
            filetypes=(("Excel files", "*.xlsx"), ("All files", "*.*"))
        )
        if filepath:
             file_ext = os.path.splitext(filepath)[1].lower()
             if file_ext != '.xlsx':
                 messagebox.showerror("格式不支持", "此版本当前仅支持 .xlsx 文件。")
                 return

             self.excel_path_entry.delete(0, tk.END)
             self.excel_path_entry.insert(0, filepath)
             self.excel_columns = []
             self.column_combobox['values'] = []
             self.column_combobox.set("输入标题行号后点击 [加载列名]")
             self.column_combobox.config(state='readonly')
             self.update_status(f"Status: 已选择 Excel 文件 (.xlsx). 请输入标题行号并点击 [加载列名]。")

    def select_word_folder(self):
        """选择Word文件夹"""
        folderpath = filedialog.askdirectory(title="选择包含 Word 文件的文件夹")
        if folderpath:
            self.word_folder_entry.delete(0, tk.END)
            self.word_folder_entry.insert(0, folderpath)
            
            # 统计文件夹中的文档数量（包括所有子文件夹）
            if os.path.isdir(folderpath):
                include_subfolders = self.include_subfolders_var.get()
                doc_files = find_word_files_recursive(folderpath, 
                                                    include_docx=False, 
                                                    include_doc=True,
                                                    include_subfolders=include_subfolders)
                docx_files = find_word_files_recursive(folderpath, 
                                                     include_docx=True, 
                                                     include_doc=False,
                                                     include_subfolders=include_subfolders)
                
                subfolder_text = "含所有子文件夹" if include_subfolders else "仅当前文件夹"
                self.update_status(f"Status: 已选择文件夹: {folderpath} (包含 {len(doc_files)} 个 .doc 文件和 {len(docx_files)} 个 .docx 文件，{subfolder_text})")
                
                # 如果有 .doc 文件但没有支持库，显示警告
                if len(doc_files) > 0 and not DOC_SUPPORT:
                    messagebox.showwarning("缺少库支持", 
                                          f"文件夹中有 {len(doc_files)} 个 .doc 文件，但未安装支持库。\n"
                                          f"请安装 win32com 或 docx2txt 库以支持 .doc 文件处理。\n"
                                          f"可以使用命令: pip install pywin32 或 pip install docx2txt")
            else:
                self.update_status(f"Status: 已选择文件夹: {folderpath}")

    def load_excel_columns_manual(self):
        """使用 openpyxl 读取列名"""
        excel_path = self.excel_path_entry.get()
        header_row_str = self.header_row_entry.get()

        if not excel_path or not os.path.isfile(excel_path):
            messagebox.showerror("输入错误", "请先选择一个有效的 Excel 文件。")
            return
        file_ext = os.path.splitext(excel_path)[1].lower()
        if file_ext != '.xlsx':
             messagebox.showerror("格式不支持", f"此加载方法当前仅支持 .xlsx 文件。")
             return

        if not header_row_str:
            messagebox.showerror("输入错误", "请输入标题所在的行号 (从1开始)。")
            return

        try:
            header_row_num_1based = int(header_row_str)
            if header_row_num_1based <= 0:
                raise ValueError("标题行号必须是正整数。")
        except ValueError:
            messagebox.showerror("输入错误", "标题行号必须是一个有效的正整数。")
            return

        self.excel_columns = []
        self.column_combobox['values'] = []
        self.column_combobox.set("正在加载 (openpyxl)...")
        self.column_combobox.config(state='disabled')
        self.update_status(f"Status: 尝试使用 openpyxl 从第 {header_row_num_1based} 行加载列名...")
        self.master.update()

        workbook = None
        try:
            workbook = openpyxl.load_workbook(excel_path, read_only=True, data_only=True)
            sheet = workbook.active
            print(f"调试: 活动工作表名称: {sheet.title}")

            if header_row_num_1based > sheet.max_row:
                raise IndexError(f"指定的标题行号 {header_row_num_1based} 超出了工作表的最大行数 ({sheet.max_row})。")

            header_row_cells = sheet[header_row_num_1based]
            loaded_columns = [str(cell.value).strip() if cell.value is not None else '' for cell in header_row_cells]
            print(f"调试: 成功读取第 {header_row_num_1based} 行内容: {loaded_columns}")

            while loaded_columns and loaded_columns[-1] == '':
                loaded_columns.pop()

            if loaded_columns and any(loaded_columns):
                self.excel_columns = loaded_columns # 存储正确列名
                self.column_combobox['values'] = self.excel_columns
                self.column_combobox.current(0)
                self.column_combobox.config(state='readonly')
                self.update_status(f"Status: 已使用 openpyxl 从第 {header_row_num_1based} 行加载列名。")
            else:
                self.column_combobox.set(f"第 {header_row_num_1based} 行未找到有效列名 (openpyxl)")
                self.column_combobox.config(state='disabled')
                messagebox.showwarning("加载列名", f"使用 openpyxl 直接读取时，在 Excel 文件的第 {header_row_num_1based} 行没有找到有效的、非空的列名。")
                self.update_status(f"Status: 警告 - openpyxl 未在第 {header_row_num_1based} 行找到有效列名。")

        except Exception as e: # 简化错误处理，捕获所有异常
            error_msg = f"使用 openpyxl 加载第 {header_row_num_1based} 行时出错:\n{type(e).__name__}: {e}"
            messagebox.showerror("读取 Excel 错误 (openpyxl)", error_msg)
            self.column_combobox.set("加载列名时出错 (openpyxl)")
            self.column_combobox.config(state='disabled')
            self.update_status(f"Status: openpyxl 读取错误: {e}")
            print(f"--- TRACEBACK (load_excel_columns_manual) ---")
            traceback.print_exc()
            print(f"----------------------------------------------------------")
        finally:
            if workbook:
                try:
                    workbook.close()
                    print("调试: 工作簿已关闭。")
                except Exception as close_err:
                    print(f"调试: 关闭工作簿时出错: {close_err}")

    def cancel_processing(self):
        """取消处理"""
        global CANCEL_PROCESSING
        if messagebox.askyesno("确认取消", "确定要取消当前处理吗？"):
            CANCEL_PROCESSING = True
            self.update_status("Status: 正在取消处理，请稍候...")
            self.cancel_button.config(state=tk.DISABLED)

    def start_processing(self):
        """在单独线程中执行处理"""
        excel_path = self.excel_path_entry.get()
        header_row_str = self.header_row_entry.get()
        selected_column_name = self.column_combobox.get()
        word_folder = self.word_folder_entry.get()
        result_col_name = self.result_col_entry.get()

        # --- 输入验证 ---
        if not excel_path or not os.path.isfile(excel_path):
            messagebox.showerror("输入错误", "请选择一个有效的 Excel 文件。")
            return
        file_ext = os.path.splitext(excel_path)[1].lower()
        if file_ext != '.xlsx':
             messagebox.showerror("格式不支持", f"此版本当前仅支持 .xlsx 文件。")
             return

        if not header_row_str:
            messagebox.showerror("输入错误", "请确保输入了标题行号。")
            return
        try:
            header_row_num_1based = int(header_row_str)
            if header_row_num_1based <= 0:
                raise ValueError("标题行号必须是正整数。")
            header_index_0based = header_row_num_1based - 1
        except ValueError:
            messagebox.showerror("输入错误", "标题行号必须是一个有效的正整数。")
            return

        # --- 列名和索引验证 ---
        if not self.excel_columns:
             messagebox.showerror("输入错误", "请先成功加载列名，然后再选择列。")
             return
        if selected_column_name not in self.excel_columns:
            if selected_column_name.startswith("输入标题行号") or selected_column_name.startswith("正在加载") or selected_column_name.startswith("第") or selected_column_name.startswith("加载列名时出错") or selected_column_name.startswith("错误"):
                 messagebox.showerror("输入错误", "请从下拉列表中选择一个有效的列名。")
            else:
                 messagebox.showerror("输入错误", f"选择的列 '{selected_column_name}' 无效。请从加载的列名列表中选择。")
            return

        # 找到选中列名的 0-based 索引
        try:
            target_column_index = self.excel_columns.index(selected_column_name)
            print(f"调试: 用户选择的列 '{selected_column_name}' 对应的索引是 {target_column_index}")
        except ValueError:
            messagebox.showerror("内部错误", f"无法在列名列表 {self.excel_columns} 中找到索引 '{selected_column_name}'。")
            return

        # --- 其他验证 ---
        if not word_folder or not os.path.isdir(word_folder):
            messagebox.showerror("输入错误", "请选择一个有效的包含 Word 文档的文件夹。")
            return
        if not result_col_name.strip():
            messagebox.showerror("输入错误", "请输入结果列的名称。")
            return

        # --- 设置线程数和速度优化选项 ---
        global MAX_WORKERS, FAST_MODE
        MAX_WORKERS = int(self.thread_slider.get())
        FAST_MODE = self.fast_parse_var.get()
        print(f"调试: 设置处理线程数为 {MAX_WORKERS}, 快速模式: {FAST_MODE}")

        # --- 检查文件类型选择 ---
        process_docx = self.docx_var.get()
        process_doc = self.doc_var.get()
        
        if not process_docx and not process_doc:
            messagebox.showerror("输入错误", "请至少选择一种文件类型进行处理 (.doc 或 .docx)。")
            return
        
        if process_doc and not DOC_SUPPORT:
            messagebox.showwarning("缺少库支持", 
                                "未安装支持 .doc 文件的库。\n"
                                "请安装 win32com 或 docx2txt 库以支持 .doc 文件处理。\n"
                                "可以使用命令: pip install pywin32 或 pip install docx2txt")
            process_doc = False

        # --- 界面准备 ---
        self.run_button.config(state=tk.DISABLED, text="正在处理...")
        self.cancel_button.config(state=tk.NORMAL)
        self.progress_var.set(0)
        global CANCEL_PROCESSING
        CANCEL_PROCESSING = False

        # --- 在单独线程中执行处理 ---
        self.processing_thread = threading.Thread(
            target=self._process_in_thread,
            args=(excel_path, target_column_index, word_folder, result_col_name, header_index_0based, process_docx, process_doc),
            daemon=True
        )
        self.processing_thread.start()

    def _process_in_thread(self, excel_path, target_column_index, word_folder, result_col_name, header_index_0based, process_docx, process_doc):
        """在单独线程中执行处理，完成后恢复界面状态"""
        try:
            # 根据用户选择递归查找文件
            if os.path.isdir(word_folder):
                include_subfolders = self.include_subfolders_var.get()
                word_files = find_word_files_recursive(word_folder, 
                                                     include_doc=process_doc, 
                                                     include_docx=process_docx,
                                                     include_subfolders=include_subfolders)
                
                subfolder_text = "含子文件夹" if include_subfolders else "仅当前文件夹"
                self.update_status(f"Status: 找到 {len(word_files)} 个符合条件的文件（{subfolder_text}），准备处理...")
                
                # 处理文件
                process_files(
                    excel_path,
                    target_column_index,
                    word_folder,
                    result_col_name.strip(),
                    self.update_status,
                    header_index_0based,
                    self.progress_var,
                    word_files=word_files  # 传递文件列表
                )
            
        except Exception as e:
            # 主要错误应该已经在 process_files 中处理，这里捕获任何遗漏的异常
            print(f"处理线程中发生未捕获的异常: {e}")
            traceback.print_exc()
        finally:
            # 恢复按钮状态
            self.master.after(0, self._reset_ui_after_processing)

    def _reset_ui_after_processing(self):
        """重置UI状态（在主线程中调用）"""
        self.run_button.config(state=tk.NORMAL, text="开始检索并更新 Excel")
        self.cancel_button.config(state=tk.DISABLED)
        self.processing_thread = None  # 清除线程引用


# --- 主程序入口 ---
if __name__ == "__main__":
    root = tk.Tk()
    app = Application(master=root)
    root.protocol("WM_DELETE_WINDOW", app.master.destroy)  # 确保窗口关闭时正确退出
    app.mainloop()