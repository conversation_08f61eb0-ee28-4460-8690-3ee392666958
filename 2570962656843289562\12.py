import re

# "除了个位，每一位的进制都是地球人数字的质数。"
# "十位上是2进制，百位上是3进制，千位上是5进制"
# This means for a Martian number d_n ... d_2 d_1 d_0 (where d_0 is the rightmost/units digit):
# The value is determined by summing contributions:
# d_0 * (weight of units place)
# d_1 * (weight of tens place)
# d_2 * (weight of hundreds place)
# ...
# The weight of the units place (d_0) is 1.
# The weight of the tens place (d_1) is P_0 (where P_0=2, the first prime).
# The weight of the hundreds place (d_2) is P_0 * P_1 (where P_1=3, the second prime).
# The weight of the thousands place (d_3) is P_0 * P_1 * P_2 (where P_2=5, the third prime).
# And so on.

# Primes sequence for determining incremental parts of the weights: 2, 3, 5, 7, 11, 13, 17, 19, ...
# Maximum Martian number length is 9 digits (implied by "十位以下" and "超出九位").
# For a 9-digit number (d_8 d_7 ... d_1 d_0):
# - d_0 uses weight 1.
# - d_1 uses weight P_0.
# - ...
# - d_8 uses weight P_0*P_1*...*P_7.
# So, we need the first 8 prime numbers.
PRIMES = [2, 3, 5, 7, 11, 13, 17, 19] # The first 8 prime numbers

def martian_to_decimal(martian_str: str) -> int:
    """
    Converts a Martian number string (composed of '0's and '1's)
    to its decimal (Earth) equivalent based on the specified mixed radix system.
    """
    decimal_value = 0
    current_digit_weight = 1  # Initial weight for the rightmost digit (d_0)
    
    # Iterate through the Martian string's digits from right to left.
    # e.g., for martian_str "1101", reversed_digits will be ['1', '0', '1', '1']
    # These correspond to d_0, d_1, d_2, d_3 respectively.
    reversed_digits = martian_str[::-1]

    for i, digit_char in enumerate(reversed_digits):
        digit = int(digit_char)
        decimal_value += digit * current_digit_weight
        
        # After processing the digit at index i (from the right, 0-indexed),
        # update current_digit_weight for the *next* digit to its left (i.e., index i+1 from the right).
        # The prime PRIMES[i] is used to scale the weight for the (i+1)-th position.
        if i < len(PRIMES): 
            # This condition ensures we only use available primes and don't go out of bounds.
            # For the rightmost digit (i=0), its weight is 1.
            # The next digit's weight (i=1) will be current_digit_weight * PRIMES[0].
            # For the leftmost digit of a 9-digit number, i will be 8.
            # The weight for this d_8 was calculated using PRIMES[7] in the previous iteration.
            # After processing d_8, i=8. `8 < len(PRIMES)` (8 < 8) is false, so no PRIMES[8] access.
            current_digit_weight *= PRIMES[i]
            
    return decimal_value

def main():
    """
    Main function to get input from the user, validate it,
    convert the Martian number to decimal, and print the result.
    """
    while True:
        martian_input = input("请输入十位以下的火星码: ")
        
        # Validation 1: Check if the string contains only '0's and '1's and is not empty.
        # The regex r"^[01]+$" means:
        # ^     : start of the string
        # [01]  : character class, matches '0' or '1'
        # +     : one or more occurrences of the preceding token
        # $     : end of the string
        if not re.fullmatch(r"^[01]+$", martian_input):
            print("输入错误：火星码只能包含0或1，且不能为空。请重新输入。")
            continue
        
        # Validation 2: Check length. "十位以下" implies max 9 digits.
        # "超出九位" is an error condition.
        if len(martian_input) > 9:
            print("输入错误：火星码长度不能超过9位。请重新输入。")
            continue
        
        # If all validations pass, break the loop.
        break
    
    decimal_result = martian_to_decimal(martian_input)
    # The output format matches the image, e.g., "该火星码对应的十进制值为37"
    print(f"该火星码对应的十进制值为{decimal_result}")

if __name__ == "__main__":
    main()