"""
单段直导线电迁移应力预测器
基于原始代码修改，专门用于单段导线的应力预测
@author: Modified for single segment wire stress prediction
"""

import torch
import numpy as np
import os, sys
import time
from torch import nn
import torch.nn.functional as F
from torch.autograd import Variable, grad
from torch import optim
from scipy.io import loadmat
import re
import csv

import matplotlib as mpl
mpl.use('Agg')  # 使用非交互式后端
from matplotlib import pyplot as plt
import matplotlib.gridspec as gridspec
from matplotlib import cm
from matplotlib.colors import Normalize

import argparse
from datetime import datetime
from shutil import copyfile

import random
import math

def mkdir(path):
    if not os.path.exists(path):
        os.makedirs(path)

def mkdirs(*paths):
    if isinstance(paths, list) or isinstance(paths, tuple):
        for path in paths:
            mkdir(path)
    else:
        raise ValueError

class Parser(argparse.ArgumentParser):
  def __init__(self):
    super(Parser, self).__init__(description='单段直导线电迁移应力预测器')
    self.add_argument('--sample-dir', type=str, default="./Samples_single_segment_stress_predictor", help='输出目录')
    self.add_argument('--data-path', type=str, default="./data/", help='数据路径')

    self.add_argument('--read-csv', type=bool, default=False, help='从CSV文件读取训练和测试数据')
    self.add_argument('--lr', type=float, default=1e-3, help='学习率')
    self.add_argument('--weight-decay', type=float, default=0., help="权重衰减")

    self.add_argument('--w-mse', type=float, default=1, help='MSE损失权重')

    self.add_argument('--n-epochs', type=int, default=80, help='总训练轮数')
    self.add_argument('--n-batches', type=int, default=1000, help='每轮的批次数')
    self.add_argument('--cuda', type=int, default=0, choices=[0, 1, 2, 3], help='CUDA设备索引')
    self.add_argument('--save-freq', type=int, default=25, help='模型保存频率')

    # 新增参数：控制样本数量
    self.add_argument('--num-samples', type=int, default=500, help='训练样本数量（默认500）')
    self.add_argument('--train-ratio', type=float, default=0.8, help='训练集比例（默认0.8，即80%用于训练）')

  def parse(self):
    args = self.parse_args()

    # Create output dir
    dt = datetime.now()
    args.date = dt.strftime("%Y%m%d%H%M%S")
    args.hparams = f'EM1d_Epochs{args.n_epochs}_{args.date}'
    args.run_dir = args.sample_dir + '/' + args.hparams
    args.code_bkup_dir = args.run_dir + '/code_bkup'
    mkdirs(args.run_dir, args.code_bkup_dir)

    # backup the code of current model
    code_path_src = os.path.basename(__file__)
    code_path_dst = args.code_bkup_dir + '/' + code_path_src
    copyfile(code_path_src,code_path_dst)

    # print('Arguments:')
    # pprint(vars(args))
    # with open(args.run_dir + "/args.txt", 'w') as args_file:
    #     json.dump(vars(args), args_file, indent=4)

    return args

args = Parser().parse()

device = torch.device(f"cuda:{args.cuda}" if torch.cuda.is_available() else "cpu")

########################################################################
########################### Parameters #################################
#########################################################################
time_interval = 1.e4
T_n_points = 101
time_length = time_interval * (T_n_points-1)
e = 1.69e-19
Ea=0.84*e
kB=1.3806e-23
T=353.0
D0 = 7.5e-8
Da=D0*math.exp(-Ea/(kB*T))
B = 1e11
Omega=1.182e-29
Z=10
rou = 3.e-08
kappa = Da*B*Omega/(kB*T)

# 单段导线网络结构：输入[x, t, L, W, G] -> 输出[stress] (简化网络)
mynet_mlp = [5, 128, 256, 512, 256, 128, 1]

# 使用命令行参数控制样本数量
total_cases = args.num_samples
training_cases = int(total_cases * args.train_ratio)  # 根据比例计算训练样本数

print(f"样本配置：总样本数={total_cases}, 训练样本数={training_cases}, 测试样本数={total_cases-training_cases}")
print(f"训练集比例={args.train_ratio:.1%}")

# Generate trainig and testing datasets
if args.read_csv:
    # read dataset from csv start
    reader = csv.reader(open(args.sample_dir+'/training.csv', "r"), delimiter=",")
    training_list = list(reader)
    training_list = [int(file[0]) for file in training_list]
    random.shuffle(training_list)

    reader = csv.reader(open(args.sample_dir+'/testing.csv', "r"), delimiter=",")
    testing_list = list(reader)
    testing_list = [int(file[0]) for file in testing_list]
    random.shuffle(testing_list)

    reader = csv.reader(open(args.sample_dir+'/statistics.csv', "r"), delimiter=",")
    statistics_list = list(reader)
    statistics_list = [float(value) for value in statistics_list[1]]
    max_time, max_length, max_G, max_k, stress_max, stress_min = statistics_list
    # read dataset from csv end
else:
    files_list = [i for i in range(total_cases)]
    # random.shuffle(files_list)
    training_list = files_list[:training_cases]
    testing_list = files_list[training_cases:]
    np.savetxt(args.run_dir+"/training.csv", training_list, fmt='%i', delimiter=',')
    np.savetxt(args.run_dir+"/testing.csv", testing_list, fmt='%i', delimiter=',')

    max_G = 0.0
    max_length = 0.0
    max_time = time_length
    stress_max = 0.0
    stress_min = 0.0

    # 只处理单段导线数据
    print(f"开始处理 {len(files_list)} 个文件的统计信息...")
    for i, file in enumerate(files_list):
        if i % 250 == 0:  # 每250个文件显示一次进度
            print(f"统计信息处理进度: {i}/{len(files_list)} ({i/len(files_list)*100:.1f}%)")
        file_path = args.data_path + str(file)

        # Read .geo file for wire geometries - 只取第一个矩形（单段）
        with open(file_path+".geo") as f:
            lines = f.readlines()

        # 找到第一个Rectangle定义
        for line in lines:
            if line[0:9] == 'Rectangle':
                rect_vertices = re.findall('-*[0-9]+\.*[0-9]*', line)
                current_segment_length = max(abs(float(rect_vertices[4]+"e-6")),abs(float(rect_vertices[5]+"e-6")))
                max_length = max(max_length, current_segment_length)
                break  # 只处理第一个段

        # Read .mat file for stress results and current
        data_mat = loadmat(file_path+".mat")
        current = data_mat['J']
        stress = data_mat['sVC']

        # Current density - 只取第一个段的电流密度
        J = current[0][0]  # 第一个段的电流密度
        G = e * Z * rou * J / Omega
        max_G = max(max_G, abs(G))

        # Stress - 只处理第一个段的应力
        truth = stress[0,0]  # shape = [L_n_points, T_n_points]
        stress_max = max(stress_max, truth.max())
        stress_min = min(stress_min, truth.min())

    with open(args.run_dir+"/statistics.csv", "w", newline='') as csv_file:
        writer = csv.writer(csv_file, delimiter=',')
        writer.writerow(["max_time", "max_length", "max_G", "stress_max", "stress_min"])
        writer.writerow([max_time, max_length, max_G, stress_max, stress_min])

# training_list = training_list[:10]

# params for domain
stress_range = stress_max - stress_min

# Read training data and convert to input arrays - 单段导线版本
idx_MSE_list = []
truth_MSE_list = []
idx_MSE = np.empty([0,5])  # [x, t, L, W, G]
truth_MSE = np.empty([0,1])

print(f"开始处理 {len(training_list)} 个训练文件...")
for i, file in enumerate(training_list):
    if i % 200 == 0:  # 每200个文件显示一次进度
        print(f"训练数据处理进度: {i}/{len(training_list)} ({i/len(training_list)*100:.1f}%)")
    file_path = args.data_path + str(file)

    # Read .geo file for wire geometries - 只取第一个矩形
    with open(file_path+".geo") as f:
        lines = f.readlines()

    current_segment_length = None
    for line in lines:
        if line[0:9] == 'Rectangle':
            rect_vertices = re.findall('-*[0-9]+\.*[0-9]*', line)
            current_segment_length = max(abs(float(rect_vertices[4]+"e-6")),abs(float(rect_vertices[5]+"e-6")))
            break  # 只处理第一个段

    L_normalized = current_segment_length / max_length

    # Read .mat file for stress results and current
    data_mat = loadmat(file_path+".mat")
    current = data_mat['J']
    stress = data_mat['sVC']

    # Current density - 只取第一个段
    J = current[0][0]
    G = e * Z * rou * J / Omega
    G_normalized = G / max_G

    # Stress - 只处理第一个段
    truth = stress[0,0]  # shape = [L_n_points, T_n_points]

    # 生成配点索引 [x,t,L,W,G]
    truth = 2*((truth-stress_min) / stress_range)-1  # convert to [-1, 1]
    x = np.linspace(0,1, num=truth.shape[0], endpoint=True)
    t = np.linspace(0,1, num=truth.shape[1], endpoint=True)
    X, T = np.meshgrid(x, t)  # shape=[T_n_points, L_n_points]
    L = np.tile(L_normalized, [X.shape[0],X.shape[1]])
    W = np.tile(1.0, [X.shape[0],X.shape[1]])  # 宽度归一化为1
    G = np.tile(G_normalized, [X.shape[0],X.shape[1]])

    idx = [X,T,L,W,G]

    idx_MSE_single_case = [np.expand_dims(col[:,:].flatten(), axis=-1) for col in idx]
    idx_MSE_single_case = np.hstack(idx_MSE_single_case)
    truth_MSE_single_case = np.expand_dims(truth.T.flatten(), axis=-1)

    idx_MSE_list.append(idx_MSE_single_case)
    truth_MSE_list.append(truth_MSE_single_case)

    # plt.show()
    # mpl.use('Agg')

    # # Plot k values for all segments
    # for i in range(n_segments):
    #     plt.plot(k1_list[i], label = f"k1_{i}")
    #     plt.plot(k2_list[i], label = f"k2_{i}")
    # plt.legend()
    # plt.show()
idx_MSE = np.vstack(idx_MSE_list)
truth_MSE = np.vstack(truth_MSE_list)

print(f"数据处理完成！")
print(f"训练样本数: {len(training_list)}")
print(f"测试样本数: {len(testing_list)}")
print(f"总训练点数: {idx_MSE.shape[0]:,}")
print(f"每个样本点数: {idx_MSE.shape[0]//len(training_list):,}")
print(f"开始训练...")


def to_numpy(input):
    if isinstance(input, torch.Tensor):
        return input.detach().cpu().numpy()
    elif isinstance(input, np.ndarray):
        return input
    else:
        raise TypeError('Unknown type of input, expected torch.Tensor or '\
            'np.ndarray, but got {}'.format(type(input)))

def normal_init(m, mean, std):
    if isinstance(m, nn.ConvTranspose2d) or isinstance(m, nn.Conv2d) or isinstance(m, nn.Linear):
        m.weight.data.normal_(mean, std)
        m.bias.data.zero_()

def create_2d_stress_visualization(truth, prediction, save_dir, file_id, epoch):
    """
    创建二维直导线应力分布可视化图 - 修复版本，显示真实的数据差异
    """
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS']
    plt.rcParams['axes.unicode_minus'] = False

    # 创建三个子图：真实值、预测值、误差图
    fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(24, 6))

    # 计算数据范围用于统一颜色映射
    vmin = min(truth.min(), prediction.min())
    vmax = max(truth.max(), prediction.max())

    # 计算误差
    error = np.abs(truth - prediction)

    # 绘制三个子图：真实值、预测值、误差

    # 子图1：真实应力分布
    im1 = ax1.imshow(truth, cmap='jet', origin='lower', aspect='auto',
                     extent=[-20, 0, -2, 18], vmin=vmin, vmax=vmax)
    ax1.set_xlabel('μm', fontsize=12)
    ax1.set_ylabel('μm', fontsize=12)
    ax1.set_title('真实应力分布', fontsize=12, pad=20)
    ax1.set_xticks([-20, -15, -10, -5, 0])
    ax1.set_yticks([0, 2, 4, 6, 8, 10, 12, 14, 16, 18])
    cbar1 = plt.colorbar(im1, ax=ax1, shrink=0.8, aspect=20)
    cbar1.set_label('应力值', fontsize=10)

    # 子图2：预测应力分布
    im2 = ax2.imshow(prediction, cmap='jet', origin='lower', aspect='auto',
                     extent=[-20, 0, -2, 18], vmin=vmin, vmax=vmax)
    ax2.set_xlabel('μm', fontsize=12)
    ax2.set_ylabel('μm', fontsize=12)
    ax2.set_title('预测应力分布', fontsize=12, pad=20)
    ax2.set_xticks([-20, -15, -10, -5, 0])
    ax2.set_yticks([0, 2, 4, 6, 8, 10, 12, 14, 16, 18])
    cbar2 = plt.colorbar(im2, ax=ax2, shrink=0.8, aspect=20)
    cbar2.set_label('应力值', fontsize=10)

    # 子图3：误差分布
    im3 = ax3.imshow(error, cmap='Reds', origin='lower', aspect='auto',
                     extent=[-20, 0, -2, 18], vmin=0, vmax=error.max())
    ax3.set_xlabel('μm', fontsize=12)
    ax3.set_ylabel('μm', fontsize=12)
    ax3.set_title(f'绝对误差 (RMSE={np.sqrt(np.mean(error**2)):.4f})', fontsize=12, pad=20)
    ax3.set_xticks([-20, -15, -10, -5, 0])
    ax3.set_yticks([0, 2, 4, 6, 8, 10, 12, 14, 16, 18])
    cbar3 = plt.colorbar(im3, ax=ax3, shrink=0.8, aspect=20)
    cbar3.set_label('误差值', fontsize=10)

    # 在图片底部添加图号和统计信息
    mse = np.mean((truth - prediction)**2)
    max_error = error.max()
    fig.text(0.5, 0.02, f'文件{file_id} - 轮次{epoch} | MSE: {mse:.6f} | 最大误差: {max_error:.4f}',
             ha='center', fontsize=12, fontweight='bold')

    # 调整布局
    plt.tight_layout()
    plt.subplots_adjust(bottom=0.1)

    # 保存图片
    plt.savefig(f'{save_dir}/2d_stress_distribution_{file_id}_epoch_{epoch:04d}.png',
                dpi=300, bbox_inches='tight')
    plt.close(fig)

class Net(nn.Module):
    # 单段导线神经网络
    def __init__(self, mlp_layers):
        super(Net, self).__init__()
        self.layers = nn.ModuleList()
        for i in range(len(mlp_layers)-1):
            self.layers.append(nn.Linear(mlp_layers[i], mlp_layers[i+1]))

        self.weight_init(mean=0.0, std=0.02)

    # weight_init
    def weight_init(self, mean, std):
        for m in self.layers:
            normal_init(m, mean, std)

    # forward method - 输入参数：x,t,L,W,G
    def forward(self, x,t,L,W,G):
        he = torch.cat((x,t,L,W,G), 1)
        for l, m in enumerate(self.layers):
            he = m(he)
            if l!=len(self.layers)-1:
                he = F.relu(he)

        return he

def MSE_loss(input_batch, truth_batch):
    x=input_batch[:,0:1]
    t=input_batch[:,1:2]
    L=input_batch[:,2:3]
    W=input_batch[:,3:4]
    G=input_batch[:,4:5]
    u = mynet(x,t,L,W,G)
    loss = u - truth_batch
    loss = (loss**2).mean()
    return loss

mynet = Net(mynet_mlp)
print(mynet)
mynet = mynet.to(device)

optimizer = optim.Adam(mynet.parameters(), lr=args.lr, weight_decay=args.weight_decay)
MSE_batches = list(range(idx_MSE.shape[0]))
MSE_bz = int(idx_MSE.shape[0]/args.n_batches)
random.shuffle(MSE_batches)

# Validation grid - 单段导线版本 (只选择2个测试样本以减少冗余输出)
if(len(testing_list)>2):
    testing_list = testing_list[-2:]

valid_truth_list = []
valid_X_list = []
valid_T_list = []
valid_L_list = []
valid_W_list = []
valid_G_list = []

for file in testing_list:
    file_path = args.data_path + str(file)

    # Read .geo file for wire geometries - 只取第一个矩形
    with open(file_path+".geo") as f:
        lines = f.readlines()

    current_segment_length = None
    for line in lines:
        if line[0:9] == 'Rectangle':
            rect_vertices = re.findall('-*[0-9]+\.*[0-9]*', line)
            current_segment_length = max(abs(float(rect_vertices[4]+"e-6")),abs(float(rect_vertices[5]+"e-6")))
            break  # 只处理第一个段

    L_normalized = current_segment_length / max_length

    # Read .mat file for stress results and current
    data_mat = loadmat(file_path+".mat")
    current = data_mat['J']
    stress = data_mat['sVC']

    # Current density - 只取第一个段
    J = current[0][0]
    G = e * Z * rou * J / Omega
    G_normalized = G / max_G

    # Stress - 只处理第一个段
    truth = stress[0,0]  # shape = [L_n_points, T_n_points]

    # 生成配点索引 [x,t,L,W,G]
    truth = 2*((truth-stress_min) / stress_range)-1  # convert to [-1, 1]
    x = np.linspace(0,1, num=truth.shape[0], endpoint=True)
    t = np.linspace(0,1, num=truth.shape[1], endpoint=True)
    X, T = np.meshgrid(x, t)  # shape=[T_n_points, L_n_points]
    L = np.tile(L_normalized, [X.shape[0],X.shape[1]])
    W = np.tile(1.0, [X.shape[0],X.shape[1]])
    G = np.tile(G_normalized, [X.shape[0],X.shape[1]])

    X = np.expand_dims(X.flatten(), axis=-1)
    T = np.expand_dims(T.flatten(), axis=-1)
    L = np.expand_dims(L.flatten(), axis=-1)
    W = np.expand_dims(W.flatten(), axis=-1)
    G = np.expand_dims(G.flatten(), axis=-1)

    X = torch.FloatTensor(X).to(device)
    T = torch.FloatTensor(T).to(device)
    L = torch.FloatTensor(L).to(device)
    W = torch.FloatTensor(W).to(device)
    G = torch.FloatTensor(G).to(device)

    valid_truth_list.append(truth)
    valid_X_list.append(X)
    valid_T_list.append(T)
    valid_L_list.append(L)
    valid_W_list.append(W)
    valid_G_list.append(G)

# Training begins
last_saved_epoch = 0
loss_list = []
for epoch in range(args.n_epochs):
    mynet.train()
    random.shuffle(MSE_batches)
    loss_average = 0
    for batch in range(args.n_batches):
        # print(f"Epoch : {epoch}, Batch: {batch}")
        MSE_btch = idx_MSE[MSE_batches[batch*MSE_bz:(batch+1)*MSE_bz]]
        truth_MSE_btch = truth_MSE[MSE_batches[batch*MSE_bz:(batch+1)*MSE_bz]]

        MSE_btch = torch.FloatTensor(MSE_btch).to(device)
        truth_MSE_btch = torch.FloatTensor(truth_MSE_btch).to(device)

        mynet.zero_grad()

        loss_mse = MSE_loss(MSE_btch, truth_MSE_btch)

        loss = args.w_mse*loss_mse

        loss_average += loss.item()

        loss.backward()
        # print(mynet.il.weight.grad)
        optimizer.step()

    loss_average /= args.n_batches
    loss_list.append([loss_average])
    with open(args.run_dir+'/image_rmse_list.csv', "w", newline="") as f:
        writer = csv.writer(f)
        writer.writerows(loss_list)

    print(f"Epoch {epoch}:"\
            f" mean training loss: {loss_average:.6f},"\
            f" training loss: {loss:.6f}, MSE: {loss_mse.item():.6f}")

    if epoch !=0 and (epoch % args.save_freq == 0 or epoch == args.n_epochs-1):
        if os.path.exists(args.run_dir + '/trial_function_mlp_{}.pkl'.format(last_saved_epoch)):
            os.remove(args.run_dir + '/trial_function_mlp_{}.pkl'.format(last_saved_epoch))

        torch.save(mynet.state_dict(), args.run_dir + '/trial_function_mlp_{}.pkl'.format(epoch))
        print('Model saved')
        
        last_saved_epoch = epoch

        for k in range(len(testing_list)):
            valid_X = valid_X_list[k]
            valid_T = valid_T_list[k]
            valid_L = valid_L_list[k]
            valid_W = valid_W_list[k]
            valid_G = valid_G_list[k]
            valid_truth = valid_truth_list[k]

            # 预测单段导线的应力
            u = mynet(valid_X, valid_T, valid_L, valid_W, valid_G)
            u = to_numpy(u).reshape([T_n_points, -1]).T
            truth = valid_truth

            font_size = 24
            fig = plt.figure(figsize=(60, 20))
            gs = gridspec.GridSpec(3, 1)

            # Subplot 1: ground truth
            ax = plt.subplot(gs[0])
            ax.imshow(truth, cmap='rainbow', origin='upper', aspect='auto',  vmin = -1, vmax = 1)
            ax.set_title('Ground Truth',  fontsize = font_size)


            # Subplot 2: Prediction
            ax = plt.subplot(gs[1])
            ax.imshow(u, cmap='rainbow',  origin='upper', aspect='auto', vmin = -1, vmax = 1)
            ax.set_title('Prediction',  fontsize = font_size)

            # Subplot 3: Final stress
            ax = plt.subplot(gs[2])
            stress_end_truth = truth[:,-1]
            stress_end_pred = u[:,-1]
            ax.plot(stress_end_truth,'r-', label = 'Ground Truth')
            ax.plot(stress_end_pred,'b.', label='Prediction')
            ax.legend(loc='upper right')
            ax.set_title('Final stress',  fontsize = font_size)


            fig.suptitle(args.hparams)

            # plt.show()
            plt.savefig('{}/truth_vs_pred_{}_epoch_{}.png'.format(args.run_dir, str(testing_list[k]), str(epoch).zfill(4)), bbox_inches='tight')
            plt.close(fig)

            # 生成二维应力分布图（类似您要求的图）
            create_2d_stress_visualization(truth, u, args.run_dir, testing_list[k], epoch)

