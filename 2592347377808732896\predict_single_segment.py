"""
单段直导线电迁移应力预测脚本
用于加载训练好的模型权重进行预测
@author: Prediction script for single segment wire stress
"""

import torch
import numpy as np
import os
import argparse
from torch import nn
import torch.nn.functional as F
from scipy.io import loadmat
import re
import csv

import matplotlib as mpl
mpl.use('Agg')  # 使用非交互式后端
from matplotlib import pyplot as plt

import math

# 物理参数（与训练时保持一致）
time_interval = 1.e4
T_n_points = 101
time_length = time_interval * (T_n_points-1)
e = 1.69e-19
Ea = 0.84*e
kB = 1.3806e-23
T = 353.0
D0 = 7.5e-8
Da = D0*math.exp(-Ea/(kB*T))
B = 1e11
Omega = 1.182e-29
Z = 10
rou = 3.e-08
kappa = Da*B*Omega/(kB*T)

# 网络结构（与训练时保持一致）
mynet_mlp = [5, 128, 256, 512, 256, 128, 1]

def create_2d_stress_visualization(truth, prediction, save_dir, file_id, epoch):
    """
    创建二维直导线应力分布可视化图 - 参考训练代码的处理方式
    显示窄带状的导线区域，其他区域为白色背景
    """
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS']
    plt.rcParams['axes.unicode_minus'] = False

    # 创建2x1布局的图片
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))

    # 参考训练代码的坐标范围设置
    extent = [-20, 0, 0, 18]  # [x_min, x_max, y_min, y_max]
    
    print(f"数据形状: truth={truth.shape}, prediction={prediction.shape}")
    print(f"数据范围: truth=({truth.min():.4f}, {truth.max():.4f}), prediction=({prediction.min():.4f}, {prediction.max():.4f})")

    # 创建导线区域的掩码 - 基于数据的有效应力分布
    def create_wire_mask(data, narrow_band_height=3):
        """
        创建导线区域的掩码，识别真正的导线区域并强制窄带效果
        确保窄带位置在正确的y坐标（约7-9μm）
        """
        rows, cols = data.shape
        mask = np.zeros_like(data, dtype=bool)
        
        print(f"数据形状: {data.shape}")
        print(f"数据统计: min={data.min():.6f}, max={data.max():.6f}, mean={np.mean(data):.6f}")
        
        # 计算每行的应力统计 - 使用绝对值
        data_abs = np.abs(data)
        row_max = np.max(data_abs, axis=1)
        
        print(f"行统计: row_max范围=[{row_max.min():.6f}, {row_max.max():.6f}]")
        print(f"每行最大值分布:")
        for i in range(0, len(row_max), max(1, len(row_max)//10)):
            y_coord = i * 18 / rows  # 计算对应的y坐标(μm)
            print(f"  行{i:2d} (y≈{y_coord:.1f}μm): {row_max[i]:.6f}")
        
        # 使用非常低的阈值来确保检测到有效区域
        global_max = np.max(data_abs)
        threshold = global_max * 0.01  # 降低到1%的阈值
        
        # 找到有显著应力的行
        significant_rows = row_max > threshold
        significant_indices = np.where(significant_rows)[0]
        
        print(f"全局最大值: {global_max:.6f}")
        print(f"使用阈值: {threshold:.6f}")
        print(f"有效行数: {len(significant_indices)}")
        
        if len(significant_indices) > 0:
            print(f"有效行索引: {significant_indices}")
            
            # 目标：窄带应该位于y=7-9μm的位置
            # 计算目标行索引范围
            target_y_min = 7.0  # μm
            target_y_max = 9.0  # μm
            target_row_min = int(target_y_min * rows / 18)  # 18是y轴总范围
            target_row_max = int(target_y_max * rows / 18)
            
            print(f"目标y范围: {target_y_min}-{target_y_max}μm")
            print(f"目标行索引范围: {target_row_min}-{target_row_max}")
            
            # 在有效行中寻找与目标位置最接近的区域
            # 计算每个有效行到目标中心的距离
            target_center = (target_row_min + target_row_max) / 2
            distances = np.abs(significant_indices - target_center)
            
            # 选择距离目标中心最近的行作为起始点
            closest_idx = np.argmin(distances)
            center_row = significant_indices[closest_idx]
            
            print(f"目标中心行: {target_center:.1f}")
            print(f"选择的中心行: {center_row} (y≈{center_row * 18 / rows:.1f}μm)")
            
            # 以选定的中心行为基础，创建窄带
            half_height = narrow_band_height // 2
            start_row = max(0, center_row - half_height)
            end_row = min(rows, center_row + half_height + 1)
            
            # 确保选定的行在有效范围内
            selected_indices = []
            for row_idx in range(start_row, end_row):
                if row_idx in significant_indices or row_max[row_idx] > threshold * 0.1:
                    selected_indices.append(row_idx)
            
            # 如果选定的行太少，扩展选择范围
            if len(selected_indices) < narrow_band_height:
                # 直接选择目标范围内的行
                selected_indices = list(range(
                    max(0, target_row_min), 
                    min(rows, target_row_max + 1)
                ))[:narrow_band_height]
            
            # 设置掩码
            for row_idx in selected_indices:
                mask[row_idx, :] = True
                
            print(f"最终选择的行索引: {selected_indices}")
            print(f"对应的y坐标范围: {min(selected_indices) * 18 / rows:.1f}-{max(selected_indices) * 18 / rows:.1f}μm")
            print(f"实际显示行数: {len(selected_indices)}")
            
            # 验证掩码
            masked_data = data.copy()
            masked_data[~mask] = np.nan
            valid_values = masked_data[~np.isnan(masked_data)]
            print(f"掩码后有效值数量: {len(valid_values)}")
            if len(valid_values) > 0:
                print(f"掩码后数值范围: [{valid_values.min():.6f}, {valid_values.max():.6f}]")
        else:
            print("警告：未找到任何有效的应力区域！使用目标位置创建窄带。")
            # 如果没有找到有效区域，直接在目标位置创建窄带
            target_row_min = int(7.0 * rows / 18)
            target_row_max = int(9.0 * rows / 18)
            selected_indices = list(range(target_row_min, min(rows, target_row_max + 1)))
            for row_idx in selected_indices:
                mask[row_idx, :] = True
            print(f"在目标位置创建窄带: 行{target_row_min}-{target_row_max}")
        
        return mask

    # 创建掩码
    wire_mask = create_wire_mask(truth, narrow_band_height=3)  # 使用3行高度实现更窄的带状效果
    
    # 应用掩码 - 将非导线区域设为NaN
    truth_masked = truth.copy().astype(float)
    prediction_masked = prediction.copy().astype(float)
    
    truth_masked[~wire_mask] = np.nan
    prediction_masked[~wire_mask] = np.nan
    
    # 验证掩码后的数据
    print(f"掩码应用后:")
    print(f"真实数据有效像素数: {np.sum(~np.isnan(truth_masked))}")
    print(f"预测数据有效像素数: {np.sum(~np.isnan(prediction_masked))}")
    
    if np.sum(~np.isnan(truth_masked)) == 0:
        print("错误：掩码后没有有效数据！将显示原始数据的一个窄带区域。")
        # 如果掩码失败，手动创建一个窄带
        rows, cols = truth.shape
        center_row = rows // 2
        truth_masked = np.full_like(truth, np.nan, dtype=float)
        prediction_masked = np.full_like(prediction, np.nan, dtype=float)
        # 在中心附近创建3行的窄带
        start_row = max(0, center_row - 1)
        end_row = min(rows, center_row + 2)
        truth_masked[start_row:end_row, :] = truth[start_row:end_row, :]
        prediction_masked[start_row:end_row, :] = prediction[start_row:end_row, :]
        print(f"手动设置窄带: 行 {start_row} 到 {end_row-1}")

    # 左侧子图：真实应力分布
    im1 = ax1.imshow(truth_masked, cmap='jet', origin='lower', aspect='auto',
                     extent=extent, vmin=-1.5, vmax=1.5)
    ax1.set_title('真实应力分布 - 时间=1E7 s', fontsize=14, fontweight='bold')
    ax1.set_xlabel('μm', fontsize=12)
    ax1.set_ylabel('μm', fontsize=12)
    ax1.set_xticks([-20, -15, -10, -5, 0])
    ax1.set_yticks([0, 2, 4, 6, 8, 10, 12, 14, 16, 18])
    ax1.set_facecolor('white')  # 设置背景为白色
    ax1.grid(True, alpha=0.3)  # 添加网格线

    # 添加颜色条
    cbar1 = plt.colorbar(im1, ax=ax1, shrink=0.8)
    cbar1.set_label('x10^9', fontsize=10, rotation=0, labelpad=20)
    cbar1.set_ticks([-1.5, -1, -0.5, 0, 0.5, 1, 1.5])

    # 右侧子图：预测应力分布
    im2 = ax2.imshow(prediction_masked, cmap='jet', origin='lower', aspect='auto',
                     extent=extent, vmin=-1.5, vmax=1.5)
    ax2.set_title('预测应力分布 - 时间=1E7 s', fontsize=14, fontweight='bold')
    ax2.set_xlabel('μm', fontsize=12)
    ax2.set_ylabel('μm', fontsize=12)
    ax2.set_xticks([-20, -15, -10, -5, 0])
    ax2.set_yticks([0, 2, 4, 6, 8, 10, 12, 14, 16, 18])
    ax2.set_facecolor('white')  # 设置背景为白色
    ax2.grid(True, alpha=0.3)  # 添加网格线

    # 添加颜色条
    cbar2 = plt.colorbar(im2, ax=ax2, shrink=0.8)
    cbar2.set_label('x10^9', fontsize=10, rotation=0, labelpad=20)
    cbar2.set_ticks([-1.5, -1, -0.5, 0, 0.5, 1, 1.5])

    # 添加总标题
    fig.suptitle(f'图1 二维直导线电迁移应力分布示意图 - 文件test - 轮次{epoch}',
                 fontsize=16, fontweight='bold', y=0.02)

    # 调整布局
    plt.tight_layout()
    plt.subplots_adjust(bottom=0.15)

    # 保存图片
    plt.savefig(f'{save_dir}/2d_stress_distribution_test_epoch_{epoch:04d}.png',
                dpi=300, bbox_inches='tight', facecolor='white')
    plt.close(fig)

class Net(nn.Module):
    # 单段导线神经网络（与训练时保持一致）
    def __init__(self, mlp_layers):
        super(Net, self).__init__()
        self.layers = nn.ModuleList()
        for i in range(len(mlp_layers)-1):
            self.layers.append(nn.Linear(mlp_layers[i], mlp_layers[i+1]))

    # forward method - 输入参数：x,t,L,W,G
    def forward(self, x,t,L,W,G):
        he = torch.cat((x,t,L,W,G), 1)
        for l, m in enumerate(self.layers):
            he = m(he)
            if l!=len(self.layers)-1:
                he = F.relu(he)
        return he

def load_statistics(model_dir):
    """加载训练时的统计信息"""
    stats_file = os.path.join(model_dir, 'statistics.csv')
    if not os.path.exists(stats_file):
        raise FileNotFoundError(f"统计文件不存在: {stats_file}")
    
    with open(stats_file, 'r') as f:
        reader = csv.reader(f)
        next(reader)  # 跳过header
        values = next(reader)
        stats = [float(v) for v in values]

    max_time, max_length, max_G, stress_max, stress_min = stats
    return max_time, max_length, max_G, stress_max, stress_min

def predict_single_file(model, device, data_path, file_id, max_length, max_G, stress_max, stress_min):
    """预测单个文件的应力分布"""
    file_path = os.path.join(data_path, str(file_id))
    
    # 读取几何文件
    with open(file_path + ".geo") as f:
        lines = f.readlines()
    
    current_segment_length = None
    for line in lines:
        if line[0:9] == 'Rectangle':
            rect_vertices = re.findall('-*[0-9]+\.*[0-9]*', line)
            current_segment_length = max(abs(float(rect_vertices[4]+"e-6")),abs(float(rect_vertices[5]+"e-6")))
            break  # 只处理第一个段
    
    L_normalized = current_segment_length / max_length
    
    # 读取.mat文件
    data_mat = loadmat(file_path + ".mat")
    current = data_mat['J']
    stress = data_mat['sVC']
    
    # 电流密度 - 只取第一个段
    J = current[0][0]
    G = e * Z * rou * J / Omega
    G_normalized = G / max_G
    
    # 真实应力 - 只处理第一个段
    truth = stress[0,0]  # shape = [L_n_points, T_n_points]
    
    # 生成预测网格
    x = np.linspace(0,1, num=truth.shape[0], endpoint=True)
    t = np.linspace(0,1, num=truth.shape[1], endpoint=True)
    X, T = np.meshgrid(x, t)  # shape=[T_n_points, L_n_points]
    L = np.tile(L_normalized, [X.shape[0],X.shape[1]])
    W = np.tile(1.0, [X.shape[0],X.shape[1]])  # 宽度归一化为1
    G_grid = np.tile(G_normalized, [X.shape[0],X.shape[1]])
    
    # 转换为张量
    X_tensor = torch.FloatTensor(np.expand_dims(X.flatten(), axis=-1)).to(device)
    T_tensor = torch.FloatTensor(np.expand_dims(T.flatten(), axis=-1)).to(device)
    L_tensor = torch.FloatTensor(np.expand_dims(L.flatten(), axis=-1)).to(device)
    W_tensor = torch.FloatTensor(np.expand_dims(W.flatten(), axis=-1)).to(device)
    G_tensor = torch.FloatTensor(np.expand_dims(G_grid.flatten(), axis=-1)).to(device)
    
    # 预测
    model.eval()
    with torch.no_grad():
        prediction = model(X_tensor, T_tensor, L_tensor, W_tensor, G_tensor)
        prediction = prediction.cpu().numpy().reshape(truth.shape[1], truth.shape[0]).T
    
    # 反归一化
    stress_range = stress_max - stress_min
    truth_denorm = 2*((truth-stress_min) / stress_range)-1  # 归一化真实值用于比较
    prediction_denorm = (prediction + 1) / 2 * stress_range + stress_min  # 反归一化预测值
    truth_original = truth  # 保持原始真实值
    
    return truth_original, prediction_denorm, truth_denorm, prediction

def main():
    parser = argparse.ArgumentParser(description='单段直导线电迁移应力预测')
    parser.add_argument('--model-dir', type=str, required=True, help='训练模型目录路径')
    parser.add_argument('--data-path', type=str, default="./data/", help='数据路径')
    parser.add_argument('--file-id', type=int, required=True, help='要预测的文件ID')
    parser.add_argument('--output-dir', type=str, default="./predictions/", help='预测结果输出目录')
    parser.add_argument('--cuda', type=int, default=0, choices=[0, 1, 2, 3], help='CUDA设备索引')
    
    args = parser.parse_args()
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 设置设备
    device = torch.device(f"cuda:{args.cuda}" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")
    
    # 加载统计信息
    print("加载训练时的统计信息...")
    _, max_length, max_G, stress_max, stress_min = load_statistics(args.model_dir)
    print(f"最大长度: {max_length:.2e}, 最大G: {max_G:.2e}")
    print(f"应力范围: [{stress_min:.2e}, {stress_max:.2e}]")
    
    # 查找模型文件
    model_files = [f for f in os.listdir(args.model_dir) if f.startswith('trial_function_mlp_') and f.endswith('.pkl')]
    if not model_files:
        raise FileNotFoundError(f"在 {args.model_dir} 中未找到模型文件")
    
    # 选择最新的模型文件
    model_file = sorted(model_files)[-1]
    model_path = os.path.join(args.model_dir, model_file)
    print(f"加载模型: {model_path}")
    
    # 创建并加载模型
    model = Net(mynet_mlp)
    model.load_state_dict(torch.load(model_path, map_location=device))
    model = model.to(device)
    print("模型加载成功!")
    
    # 进行预测
    print(f"开始预测文件 {args.file_id}...")
    truth_original, prediction_denorm, truth_norm, prediction_norm = predict_single_file(
        model, device, args.data_path, args.file_id, max_length, max_G, stress_max, stress_min
    )
    
    # 计算误差
    mse = np.mean((truth_norm - prediction_norm)**2)
    rmse = np.sqrt(mse)
    print(f"预测完成! RMSE: {rmse:.6f}")
    
    # 生成可视化（使用归一化的数据，与训练时格式一致）
    print("生成可视化图片...")
    create_2d_stress_visualization(truth_norm, prediction_norm, args.output_dir, args.file_id, 1)
    
    # 保存预测结果
    np.save(os.path.join(args.output_dir, f'truth_{args.file_id}.npy'), truth_original)
    np.save(os.path.join(args.output_dir, f'prediction_{args.file_id}.npy'), prediction_denorm)
    
    print(f"预测结果已保存到: {args.output_dir}")
    print(f"可视化图片: {args.output_dir}/2d_stress_distribution_test_epoch_0001.png")

if __name__ == "__main__":
    main()
