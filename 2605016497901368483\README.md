# 清代西域诗集注文档分析工具

这个工具用于分析《清代西域诗集注》文档，统计"乌鲁木齐"、"伊犁"、"哈密"三个词语的出现次数，并为每个词语生成词云图。

## 功能特点

1. **词语统计**：精确统计三个目标词语在文档中的出现次数
2. **词云生成**：为每个词语生成相关的词云图
3. **多格式支持**：支持 .doc 和 .docx 格式的Word文档
4. **结果保存**：自动保存统计结果和词云图片

## 文件说明

- `document_analysis.py` - 主要分析程序（完整版）
- `simple_wordcloud.py` - 简化版分析程序（备用）
- `install_requirements.py` - 依赖包安装脚本
- `README.md` - 使用说明文档

## 安装依赖

### 方法1：使用安装脚本（推荐）

```bash
python install_requirements.py
```

### 方法2：手动安装

```bash
pip install python-docx docx2txt wordcloud matplotlib jieba numpy Pillow
```

## 使用方法

### 1. 准备文档文件

将以下任一文档文件放在程序目录中：
- `清代西域诗集注.doc`
- `清代西域诗辑注.docx`
- `清代西域诗研究（星汉老师论著）.doc`

### 2. 运行分析程序

#### 完整版（推荐）：
```bash
python document_analysis.py
```

#### 简化版（如果依赖包安装失败）：
```bash
python simple_wordcloud.py
```

### 3. 查看结果

程序运行完成后，会在 `results/` 目录中生成以下文件：

#### 统计结果：
- `analysis_results.json` - JSON格式的统计结果
- `analysis_results.txt` - 文本格式的统计结果

#### 词云文件：
- `乌鲁木齐_wordcloud.png` - 乌鲁木齐相关词云图
- `伊犁_wordcloud.png` - 伊犁相关词云图  
- `哈密_wordcloud.png` - 哈密相关词云图

#### 词云数据（简化版）：
- `乌鲁木齐_wordcloud_data.json` - 词频数据
- `乌鲁木齐_wordcloud.txt` - 文本格式词频列表

## 词云生成原理

### 完整版词云生成：
1. **上下文提取**：提取目标词语前后100个字符的上下文
2. **中文分词**：使用jieba分词库进行智能分词
3. **停用词过滤**：过滤常见的停用词和标点符号
4. **词云渲染**：使用WordCloud库生成可视化词云图
5. **字体支持**：使用系统黑体字体支持中文显示

### 简化版词云生成：
1. **上下文提取**：同完整版
2. **简单分词**：按字符进行1-3字词组合
3. **词频统计**：统计词语出现频率
4. **文本输出**：生成文本格式的高频词列表

## 技术特点

### 文档处理：
- 支持 .docx 格式（使用 python-docx）
- 支持 .doc 格式（使用 docx2txt）
- 备用简单解析方案（XML解析）

### 词语统计：
- 使用正则表达式精确匹配
- 避免部分匹配造成的误计数
- 支持中文字符处理

### 词云优化：
- 智能上下文提取
- 中文分词优化
- 停用词过滤
- 高质量图片输出（300 DPI）

## 故障排除

### 1. 依赖包安装失败
- 使用简化版程序：`python simple_wordcloud.py`
- 手动安装单个包：`pip install 包名`

### 2. 文档读取失败
- 检查文档文件是否存在
- 确认文档文件名是否正确
- 尝试将 .doc 文件另存为 .docx 格式

### 3. 中文显示问题
- 确保系统安装了中文字体
- Windows系统会自动使用黑体字体
- 其他系统可能需要修改字体路径

### 4. 词云生成失败
- 检查目标词语是否在文档中出现
- 确保有足够的上下文内容
- 使用简化版查看词频数据

## 输出示例

### 统计结果示例：
```
词语统计结果：
'乌鲁木齐' 出现次数: 15
'伊犁' 出现次数: 23  
'哈密' 出现次数: 8

三个词语总出现次数: 46
```

### 词云数据示例：
```
"乌鲁木齐" 相关词云数据
==============================

 1. 西域      (出现 8 次)
 2. 诗人      (出现 6 次)
 3. 边塞      (出现 5 次)
 4. 风光      (出现 4 次)
 5. 山川      (出现 3 次)
```

## 注意事项

1. 确保文档文件编码正确，避免乱码问题
2. 大文档处理可能需要较长时间，请耐心等待
3. 词云质量取决于文档中相关内容的丰富程度
4. 建议使用完整版程序获得最佳效果

## 版本信息

- 版本：1.0
- 开发语言：Python 3.x
- 支持系统：Windows, macOS, Linux
- 最后更新：2024年
