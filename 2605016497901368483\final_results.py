#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终结果展示脚本 - 包含词云图和统计结果的完整展示
"""

import os
import json
from datetime import datetime

def show_project_summary():
    """显示项目总结"""
    print("🎉 清代西域诗集注分析项目完成报告")
    print("=" * 60)
    print()
    
    print("📋 项目概述")
    print("-" * 40)
    print("✅ 成功分析《清代西域诗辑注.docx》文档")
    print("✅ 统计了'乌鲁木齐'、'伊犁'、'哈密'三个词语的出现次数")
    print("✅ 为每个词语生成了相关的词云数据")
    print("✅ 生成了可视化的词云图片")
    print("✅ 解决了中文字体显示问题")
    print()

def show_statistics():
    """显示统计结果"""
    print("📊 词语统计结果")
    print("-" * 40)
    
    # 读取统计数据
    results_file = "results/analysis_results.json"
    if os.path.exists(results_file):
        with open(results_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        word_counts = data.get("词语统计", {})
        total_count = data.get("总计", 0)
        
        print("📈 最终统计数据（完整版分析）:")
        for word, count in word_counts.items():
            percentage = (count / total_count * 100) if total_count > 0 else 0
            print(f"  🏛️  {word:<8}: {count:>3} 次 ({percentage:5.1f}%)")
        
        print(f"  📊 总计: {total_count} 次")
        
    else:
        print("❌ 未找到完整版统计结果")
    
    print()

def show_wordcloud_files():
    """显示词云文件信息"""
    print("☁️  词云图文件")
    print("-" * 40)
    
    words = ['乌鲁木齐', '伊犁', '哈密']
    
    for word in words:
        print(f"📍 {word}:")
        
        # 原版词云图
        original_png = f"results/{word}_wordcloud.png"
        if os.path.exists(original_png):
            size = os.path.getsize(original_png)
            print(f"  🖼️  原版词云图: {word}_wordcloud.png ({size:,} 字节)")
        
        # 改进版词云图
        improved_png = f"results/{word}_improved_wordcloud.png"
        if os.path.exists(improved_png):
            size = os.path.getsize(improved_png)
            print(f"  ✨ 改进版词云图: {word}_improved_wordcloud.png ({size:,} 字节)")
        
        # 词云数据
        data_json = f"results/{word}_wordcloud_data.json"
        if os.path.exists(data_json):
            size = os.path.getsize(data_json)
            print(f"  📊 词频数据: {word}_wordcloud_data.json ({size:,} 字节)")
        
        # 文本报告
        txt_file = f"results/{word}_wordcloud.txt"
        if os.path.exists(txt_file):
            size = os.path.getsize(txt_file)
            print(f"  📝 文本报告: {word}_wordcloud.txt ({size:,} 字节)")
        
        print()

def show_technical_achievements():
    """显示技术成就"""
    print("🔧 技术实现成就")
    print("-" * 40)
    
    achievements = [
        "✅ Word文档解析: 成功提取.docx格式文档内容",
        "✅ 中文文本处理: 正确处理中文字符和编码",
        "✅ 正则表达式匹配: 精确统计目标词语出现次数",
        "✅ 中文分词: 使用jieba库进行智能分词",
        "✅ 词频统计: 生成高质量的词频分析数据",
        "✅ 词云可视化: 生成美观的词云图片",
        "✅ 中文字体支持: 解决matplotlib中文显示问题",
        "✅ 多格式输出: 支持JSON、TXT、PNG等多种格式",
        "✅ 错误处理: 完善的异常处理和降级方案",
        "✅ 用户友好: 提供详细的使用说明和结果展示"
    ]
    
    for achievement in achievements:
        print(f"  {achievement}")
    
    print()

def show_file_structure():
    """显示文件结构"""
    print("📁 项目文件结构")
    print("-" * 40)
    
    print("📂 主要程序文件:")
    main_files = [
        "document_analysis.py - 完整版分析程序",
        "simple_wordcloud.py - 简化版分析程序", 
        "improved_wordcloud.py - 改进版词云生成器",
        "fix_chinese_font.py - 中文字体修复工具",
        "show_results.py - 结果展示脚本",
        "final_results.py - 最终报告脚本",
        "test_program.py - 程序测试脚本"
    ]
    
    for file_desc in main_files:
        print(f"  📄 {file_desc}")
    
    print("\n📂 结果文件:")
    if os.path.exists("results"):
        result_files = os.listdir("results")
        
        # 按类型分组
        png_files = [f for f in result_files if f.endswith('.png')]
        json_files = [f for f in result_files if f.endswith('.json')]
        txt_files = [f for f in result_files if f.endswith('.txt')]
        
        if png_files:
            print(f"  🖼️  图片文件 ({len(png_files)}个):")
            for f in sorted(png_files):
                print(f"    • {f}")
        
        if json_files:
            print(f"  📊 数据文件 ({len(json_files)}个):")
            for f in sorted(json_files):
                print(f"    • {f}")
        
        if txt_files:
            print(f"  📝 文本文件 ({len(txt_files)}个):")
            for f in sorted(txt_files):
                print(f"    • {f}")
    
    print()

def show_usage_guide():
    """显示使用指南"""
    print("📖 使用指南")
    print("-" * 40)
    
    print("🚀 快速开始:")
    print("  1. python simple_wordcloud.py     # 运行简化版分析")
    print("  2. python document_analysis.py    # 运行完整版分析")
    print("  3. python improved_wordcloud.py   # 生成改进版词云图")
    print("  4. python show_results.py         # 查看分析结果")
    print("  5. python final_results.py        # 查看完整报告")
    
    print("\n🔧 故障排除:")
    print("  • 中文显示问题: python fix_chinese_font.py")
    print("  • 依赖包安装: python install_requirements.py")
    print("  • 功能测试: python test_program.py")
    
    print("\n📂 查看结果:")
    print("  • 词云图片: results/*_wordcloud.png")
    print("  • 改进版词云: results/*_improved_wordcloud.png")
    print("  • 统计数据: results/analysis_results.json")
    print("  • 词频数据: results/*_wordcloud_data.json")
    
    print()

def show_data_insights():
    """显示数据洞察"""
    print("💡 数据洞察")
    print("-" * 40)
    
    print("📈 统计结果分析:")
    print("  • 伊犁出现频率最高，体现其在清代西域的重要地位")
    print("  • 乌鲁木齐作为政治中心，在诗集中占据重要位置")
    print("  • 哈密作为进疆门户，虽然出现次数相对较少但意义重大")
    
    print("\n☁️  词云特征:")
    print("  • 高频词汇多为文献注释用语('见前'、'此诗'、'作者')")
    print("  • 时代特征明显('乾隆'、'嘉庆'等年号频繁出现)")
    print("  • 地理位置词汇丰富('于乌'、'于伊'、'作于'等)")
    print("  • 军政色彩浓厚('将军'等词汇在伊犁相关内容中突出)")
    
    print("\n🎯 研究价值:")
    print("  • 为清代西域历史研究提供量化数据支撑")
    print("  • 揭示了不同地区在清代诗歌中的地位差异")
    print("  • 体现了清代西域诗歌的文献特征和时代背景")
    
    print()

def main():
    """主函数"""
    print()
    show_project_summary()
    show_statistics()
    show_wordcloud_files()
    show_technical_achievements()
    show_file_structure()
    show_usage_guide()
    show_data_insights()
    
    print("🎊 项目完成!")
    print("=" * 60)
    print("感谢使用清代西域诗集注分析工具！")
    print(f"报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

if __name__ == "__main__":
    main()
