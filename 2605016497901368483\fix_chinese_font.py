#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复中文字体显示问题的脚本
"""

import os
import matplotlib
import matplotlib.pyplot as plt
from matplotlib.font_manager import FontProperties
import warnings

def find_chinese_fonts():
    """查找系统中可用的中文字体"""
    print("正在查找系统中的中文字体...")
    
    # Windows系统常见中文字体路径
    font_paths = [
        ('黑体', 'C:/Windows/Fonts/simhei.ttf'),
        ('微软雅黑', 'C:/Windows/Fonts/msyh.ttc'),
        ('宋体', 'C:/Windows/Fonts/simsun.ttc'),
        ('楷体', 'C:/Windows/Fonts/simkai.ttf'),
        ('仿宋', 'C:/Windows/Fonts/simfang.ttf'),
        ('隶书', 'C:/Windows/Fonts/simli.ttf'),
    ]
    
    available_fonts = []
    for name, path in font_paths:
        if os.path.exists(path):
            available_fonts.append((name, path))
            print(f"✓ 找到字体: {name} - {path}")
        else:
            print(f"✗ 未找到字体: {name} - {path}")
    
    return available_fonts

def configure_matplotlib_chinese():
    """配置matplotlib支持中文显示"""
    print("\n正在配置matplotlib中文支持...")

    try:
        # 设置中文字体
        matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
        matplotlib.rcParams['axes.unicode_minus'] = False

        # 尝试清除字体缓存（不同版本的matplotlib方法不同）
        try:
            matplotlib.font_manager._rebuild()
        except AttributeError:
            try:
                matplotlib.font_manager.fontManager.__init__()
            except:
                pass  # 如果都失败就跳过缓存清理

        print("✓ matplotlib中文配置完成")
        return True
    except Exception as e:
        print(f"✗ matplotlib中文配置失败: {e}")
        return False

def test_chinese_display():
    """测试中文显示效果"""
    print("\n正在测试中文显示效果...")
    
    try:
        # 创建测试图形
        fig, ax = plt.subplots(figsize=(8, 6))
        
        # 测试数据
        words = ['乌鲁木齐', '伊犁', '哈密', '新疆', '西域']
        counts = [432, 565, 213, 100, 80]
        
        # 创建柱状图
        bars = ax.bar(words, counts, color=['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd'])
        
        # 设置标题和标签
        ax.set_title('清代西域诗集注词语统计测试', fontsize=16, fontweight='bold')
        ax.set_xlabel('地名', fontsize=12)
        ax.set_ylabel('出现次数', fontsize=12)
        
        # 在柱子上显示数值
        for bar, count in zip(bars, counts):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 5,
                   f'{count}', ha='center', va='bottom', fontsize=10)
        
        # 旋转x轴标签以避免重叠
        plt.xticks(rotation=45)
        plt.tight_layout()
        
        # 保存测试图片
        test_path = 'results/chinese_font_test.png'
        plt.savefig(test_path, dpi=300, bbox_inches='tight', 
                   facecolor='white', edgecolor='none')
        plt.close()
        
        print(f"✓ 中文显示测试完成，测试图片已保存: {test_path}")
        return True
        
    except Exception as e:
        print(f"✗ 中文显示测试失败: {e}")
        return False

def create_improved_wordcloud():
    """创建改进版的词云生成函数"""
    print("\n正在创建改进版词云生成脚本...")
    
    improved_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进版词云生成器 - 修复中文字体问题
"""

import os
import matplotlib
import matplotlib.pyplot as plt
from wordcloud import WordCloud
import json

# 配置matplotlib中文支持
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False

def find_best_chinese_font():
    """查找最佳中文字体"""
    font_paths = [
        'C:/Windows/Fonts/simhei.ttf',      # 黑体
        'C:/Windows/Fonts/msyh.ttc',        # 微软雅黑
        'C:/Windows/Fonts/simsun.ttc',      # 宋体
        'C:/Windows/Fonts/simkai.ttf',      # 楷体
    ]
    
    for path in font_paths:
        if os.path.exists(path):
            return path
    return None

def generate_improved_wordcloud(word, word_freq_data):
    """生成改进版词云图"""
    if not word_freq_data:
        print(f"没有词频数据生成 '{word}' 的词云")
        return
    
    print(f"正在生成改进版 '{word}' 词云图...")
    
    try:
        # 获取字体路径
        font_path = find_best_chinese_font()
        
        # 准备词频文本
        text_data = ' '.join([f'{w} ' * min(freq, 100) for w, freq in word_freq_data.items()])
        
        # 配置词云参数
        wordcloud_params = {
            'width': 1200,
            'height': 800,
            'background_color': 'white',
            'max_words': 80,
            'colormap': 'Set3',
            'relative_scaling': 0.6,
            'random_state': 42,
            'collocations': False,
            'prefer_horizontal': 0.7
        }
        
        if font_path:
            wordcloud_params['font_path'] = font_path
            print(f"使用字体: {font_path}")
        
        # 生成词云
        wordcloud = WordCloud(**wordcloud_params).generate(text_data)
        
        # 创建图形
        plt.figure(figsize=(12, 8))
        plt.imshow(wordcloud, interpolation='bilinear')
        plt.axis('off')
        
        # 设置标题
        plt.suptitle(f'"{word}" 相关词云图', fontsize=20, fontweight='bold', y=0.95)
        
        # 保存图片
        output_path = f'results/{word}_improved_wordcloud.png'
        plt.savefig(output_path, dpi=300, bbox_inches='tight', 
                   facecolor='white', edgecolor='none', pad_inches=0.2)
        plt.close()
        
        print(f"改进版词云图已保存: {output_path}")
        return True
        
    except Exception as e:
        print(f"生成改进版词云图失败: {e}")
        return False

def main():
    """主函数"""
    print("改进版词云生成器")
    print("=" * 40)
    
    # 处理三个词语
    words = ['乌鲁木齐', '伊犁', '哈密']
    
    for word in words:
        # 读取词频数据
        json_file = f'results/{word}_wordcloud_data.json'
        if os.path.exists(json_file):
            with open(json_file, 'r', encoding='utf-8') as f:
                word_freq_data = json.load(f)
            
            # 过滤掉目标词本身的分解
            filtered_data = {}
            for w, freq in word_freq_data.items():
                if w != word and len(w) > 1:
                    # 避免包含目标词的子串
                    if not (word in w and len(w) < len(word) + 2):
                        filtered_data[w] = freq
            
            # 取前30个高频词
            top_words = dict(sorted(filtered_data.items(), key=lambda x: x[1], reverse=True)[:30])
            
            if top_words:
                generate_improved_wordcloud(word, top_words)
            else:
                print(f"'{word}' 没有足够的有效词频数据")
        else:
            print(f"未找到 '{word}' 的词频数据文件")

if __name__ == "__main__":
    main()
'''
    
    # 保存改进版脚本
    with open('improved_wordcloud.py', 'w', encoding='utf-8') as f:
        f.write(improved_code)
    
    print("✓ 改进版词云生成脚本已创建: improved_wordcloud.py")

def main():
    """主函数"""
    print("中文字体修复工具")
    print("=" * 50)
    
    # 创建results目录
    if not os.path.exists('results'):
        os.makedirs('results')
    
    # 1. 查找中文字体
    available_fonts = find_chinese_fonts()
    
    if not available_fonts:
        print("\n❌ 未找到任何中文字体！")
        print("请确保系统已安装中文字体。")
        return False
    
    # 2. 配置matplotlib
    if not configure_matplotlib_chinese():
        return False
    
    # 3. 测试中文显示
    if not test_chinese_display():
        return False
    
    # 4. 创建改进版词云生成器
    create_improved_wordcloud()
    
    print("\n" + "=" * 50)
    print("✅ 中文字体修复完成！")
    print("\n使用建议:")
    print("1. 运行 python improved_wordcloud.py 生成改进版词云图")
    print("2. 查看 results/chinese_font_test.png 确认中文显示正常")
    print("3. 如果仍有问题，请重启Python环境")
    
    return True

if __name__ == "__main__":
    main()
