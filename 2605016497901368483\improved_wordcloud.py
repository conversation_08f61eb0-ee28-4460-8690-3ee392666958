#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进版词云生成器 - 修复中文字体问题
"""

import os
import matplotlib
import matplotlib.pyplot as plt
from wordcloud import WordCloud
import json

# 配置matplotlib中文支持
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False

def find_best_chinese_font():
    """查找最佳中文字体"""
    font_paths = [
        'C:/Windows/Fonts/simhei.ttf',      # 黑体
        'C:/Windows/Fonts/msyh.ttc',        # 微软雅黑
        'C:/Windows/Fonts/simsun.ttc',      # 宋体
        'C:/Windows/Fonts/simkai.ttf',      # 楷体
    ]
    
    for path in font_paths:
        if os.path.exists(path):
            return path
    return None

def generate_improved_wordcloud(word, word_freq_data):
    """生成改进版词云图"""
    if not word_freq_data:
        print(f"没有词频数据生成 '{word}' 的词云")
        return
    
    print(f"正在生成改进版 '{word}' 词云图...")
    
    try:
        # 获取字体路径
        font_path = find_best_chinese_font()
        
        # 准备词频文本
        text_data = ' '.join([f'{w} ' * min(freq, 100) for w, freq in word_freq_data.items()])
        
        # 配置词云参数
        wordcloud_params = {
            'width': 1200,
            'height': 800,
            'background_color': 'white',
            'max_words': 80,
            'colormap': 'Set3',
            'relative_scaling': 0.6,
            'random_state': 42,
            'collocations': False,
            'prefer_horizontal': 0.7
        }
        
        if font_path:
            wordcloud_params['font_path'] = font_path
            print(f"使用字体: {font_path}")
        
        # 生成词云
        wordcloud = WordCloud(**wordcloud_params).generate(text_data)
        
        # 创建图形
        plt.figure(figsize=(12, 8))
        plt.imshow(wordcloud, interpolation='bilinear')
        plt.axis('off')
        
        # 设置标题
        plt.suptitle(f'"{word}" 相关词云图', fontsize=20, fontweight='bold', y=0.95)
        
        # 保存图片
        output_path = f'results/{word}_improved_wordcloud.png'
        plt.savefig(output_path, dpi=300, bbox_inches='tight', 
                   facecolor='white', edgecolor='none', pad_inches=0.2)
        plt.close()
        
        print(f"改进版词云图已保存: {output_path}")
        return True
        
    except Exception as e:
        print(f"生成改进版词云图失败: {e}")
        return False

def main():
    """主函数"""
    print("改进版词云生成器")
    print("=" * 40)
    
    # 处理三个词语
    words = ['乌鲁木齐', '伊犁', '哈密']
    
    for word in words:
        # 读取词频数据
        json_file = f'results/{word}_wordcloud_data.json'
        if os.path.exists(json_file):
            with open(json_file, 'r', encoding='utf-8') as f:
                word_freq_data = json.load(f)
            
            # 过滤掉目标词本身的分解
            filtered_data = {}
            for w, freq in word_freq_data.items():
                if w != word and len(w) > 1:
                    # 避免包含目标词的子串
                    if not (word in w and len(w) < len(word) + 2):
                        filtered_data[w] = freq
            
            # 取前30个高频词
            top_words = dict(sorted(filtered_data.items(), key=lambda x: x[1], reverse=True)[:30])
            
            if top_words:
                generate_improved_wordcloud(word, top_words)
            else:
                print(f"'{word}' 没有足够的有效词频数据")
        else:
            print(f"未找到 '{word}' 的词频数据文件")

if __name__ == "__main__":
    main()
