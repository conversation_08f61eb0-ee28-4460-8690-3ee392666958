#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安装所需依赖包的脚本
"""

import subprocess
import sys
import os

def install_package(package):
    """安装单个包"""
    try:
        print(f"正在安装 {package}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✓ {package} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {package} 安装失败: {e}")
        return False

def main():
    """主函数"""
    print("开始安装文档分析所需的依赖包...")
    print("=" * 50)
    
    # 需要安装的包列表
    packages = [
        "python-docx",      # 处理.docx文件
        "docx2txt",         # 处理.doc文件
        "wordcloud",        # 生成词云
        "matplotlib",       # 绘图
        "jieba",           # 中文分词
        "numpy",           # 数值计算
        "Pillow",          # 图像处理
    ]
    
    success_count = 0
    failed_packages = []
    
    for package in packages:
        if install_package(package):
            success_count += 1
        else:
            failed_packages.append(package)
    
    print("\n" + "=" * 50)
    print(f"安装完成！成功: {success_count}/{len(packages)}")
    
    if failed_packages:
        print(f"失败的包: {', '.join(failed_packages)}")
        print("\n请手动安装失败的包:")
        for package in failed_packages:
            print(f"  pip install {package}")
    else:
        print("所有依赖包安装成功！")
        print("\n现在可以运行文档分析程序:")
        print("  python document_analysis.py")

if __name__ == "__main__":
    main()
