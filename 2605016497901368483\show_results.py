#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
结果展示脚本 - 美化显示分析结果
"""

import os
import json
from datetime import datetime

def load_json_file(file_path):
    """加载JSON文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"加载文件失败 {file_path}: {e}")
        return None

def show_word_statistics():
    """显示词语统计结果"""
    print("📊 词语统计结果")
    print("=" * 50)

    # 从分析结果文件中获取统计数据
    results_file = "results/simple_analysis_results.json"

    if os.path.exists(results_file):
        data = load_json_file(results_file)
        if data and "词语统计" in data:
            word_counts = data["词语统计"]
            total_count = data.get("总计", 0)

            # 显示统计结果
            for word, count in word_counts.items():
                print(f"🏛️  '{word}' 出现次数: {count:,} 次")

            print("-" * 50)
            print(f"📈 三个词语总出现次数: {total_count:,} 次")
        else:
            print("❌ 无法读取统计数据")
    else:
        print("❌ 统计结果文件不存在")

    print()

def show_wordcloud_data(word):
    """显示单个词语的词云数据"""
    print(f"☁️  '{word}' 相关词云数据")
    print("-" * 40)
    
    json_file = f"results/{word}_wordcloud_data.json"
    txt_file = f"results/{word}_wordcloud.txt"
    
    if os.path.exists(json_file):
        data = load_json_file(json_file)
        if data:
            # 显示前10个高频词
            sorted_words = sorted(data.items(), key=lambda x: x[1], reverse=True)[:10]
            
            for i, (w, freq) in enumerate(sorted_words, 1):
                # 过滤掉目标词本身的分解词
                if w != word and not (word in w and len(w) < len(word)):
                    print(f"  {i:2d}. {w:<12} ({freq:,} 次)")
            
            print(f"  💾 详细数据: {txt_file}")
        else:
            print("  ❌ 无法加载词云数据")
    else:
        print("  ❌ 词云数据文件不存在")
    
    print()

def show_file_info():
    """显示生成的文件信息"""
    print("📁 生成的文件")
    print("=" * 50)
    
    results_dir = "results"
    if not os.path.exists(results_dir):
        print("❌ 结果目录不存在")
        return
    
    files = os.listdir(results_dir)
    if not files:
        print("❌ 结果目录为空")
        return
    
    # 按文件类型分组
    json_files = [f for f in files if f.endswith('.json')]
    txt_files = [f for f in files if f.endswith('.txt')]
    png_files = [f for f in files if f.endswith('.png')]
    
    if json_files:
        print("📄 JSON数据文件:")
        for file in sorted(json_files):
            file_path = os.path.join(results_dir, file)
            size = os.path.getsize(file_path)
            print(f"  • {file} ({size:,} 字节)")
    
    if txt_files:
        print("\n📝 文本报告文件:")
        for file in sorted(txt_files):
            file_path = os.path.join(results_dir, file)
            size = os.path.getsize(file_path)
            print(f"  • {file} ({size:,} 字节)")
    
    if png_files:
        print("\n🖼️  词云图片文件:")
        for file in sorted(png_files):
            file_path = os.path.join(results_dir, file)
            size = os.path.getsize(file_path)
            print(f"  • {file} ({size:,} 字节)")
    
    print()

def show_analysis_summary():
    """显示分析摘要"""
    print("📋 分析摘要")
    print("=" * 50)
    
    # 检查文档文件
    doc_files = [
        "清代西域诗集注.doc",
        "清代西域诗辑注.docx", 
        "清代西域诗研究（星汉老师论著）.doc"
    ]
    
    analyzed_file = None
    for doc_file in doc_files:
        if os.path.exists(doc_file):
            analyzed_file = doc_file
            break
    
    if analyzed_file:
        file_size = os.path.getsize(analyzed_file)
        print(f"📖 分析文档: {analyzed_file}")
        print(f"📏 文档大小: {file_size:,} 字节")
    else:
        print("📖 分析文档: test_document.txt (测试文档)")
    
    print(f"🎯 目标词语: 乌鲁木齐、伊犁、哈密")
    print(f"⏰ 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🔧 分析方法: 简化版文档分析 + 词频统计")
    print()

def main():
    """主函数"""
    print("🎉 清代西域诗集注分析结果")
    print("=" * 60)
    print()
    
    # 显示分析摘要
    show_analysis_summary()
    
    # 显示词语统计
    show_word_statistics()
    
    # 显示各词语的词云数据
    target_words = ["乌鲁木齐", "伊犁", "哈密"]
    for word in target_words:
        show_wordcloud_data(word)
    
    # 显示文件信息
    show_file_info()
    
    print("✅ 分析完成！")
    print("💡 提示: 查看 results/ 目录中的详细文件")
    print("📊 如需生成可视化词云图，请安装 wordcloud 库后运行完整版程序")

if __name__ == "__main__":
    main()
