#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版词云生成器
如果完整版依赖包安装失败，可以使用这个简化版本
"""

import os
import re
import json
from collections import Counter
from datetime import datetime

class SimpleWordCloudGenerator:
    def __init__(self):
        self.target_words = ["乌鲁木齐", "伊犁", "哈密"]
        self.results_dir = "results"
        if not os.path.exists(self.results_dir):
            os.makedirs(self.results_dir)
    
    def read_text_file(self, file_path):
        """读取文本文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except UnicodeDecodeError:
            try:
                with open(file_path, 'r', encoding='gbk') as f:
                    return f.read()
            except:
                print(f"无法读取文件: {file_path}")
                return ""
    
    def extract_text_from_docx_simple(self, file_path):
        """简单方式提取docx文本（如果python-docx不可用）"""
        try:
            import zipfile
            import xml.etree.ElementTree as ET
            
            with zipfile.ZipFile(file_path, 'r') as zip_file:
                xml_content = zip_file.read('word/document.xml')
                root = ET.fromstring(xml_content)
                
                # 提取所有文本节点
                text_elements = []
                for elem in root.iter():
                    if elem.text:
                        text_elements.append(elem.text)
                
                return ' '.join(text_elements)
        except Exception as e:
            print(f"简单方式提取docx失败: {e}")
            return ""
    
    def count_words_in_text(self, text):
        """统计文本中目标词语的出现次数"""
        word_counts = {}
        
        for word in self.target_words:
            # 使用正则表达式统计
            pattern = re.compile(word)
            matches = pattern.findall(text)
            word_counts[word] = len(matches)
        
        return word_counts
    
    def extract_context_simple(self, text, word, context_length=50):
        """提取词语上下文（简化版）"""
        contexts = []
        
        for match in re.finditer(word, text):
            start = max(0, match.start() - context_length)
            end = min(len(text), match.end() + context_length)
            context = text[start:end]
            contexts.append(context)
        
        return contexts
    
    def simple_word_frequency(self, text):
        """简单的词频统计"""
        # 移除标点符号和数字
        cleaned_text = re.sub(r'[^\u4e00-\u9fff]', ' ', text)
        
        # 简单分词（按字符）
        words = []
        for i in range(len(cleaned_text) - 1):
            if cleaned_text[i].strip():
                # 单字
                words.append(cleaned_text[i])
                # 双字词
                if i < len(cleaned_text) - 1 and cleaned_text[i+1].strip():
                    words.append(cleaned_text[i:i+2])
                # 三字词
                if i < len(cleaned_text) - 2 and cleaned_text[i+2].strip():
                    words.append(cleaned_text[i:i+3])
        
        # 统计词频
        word_freq = Counter(words)
        
        # 过滤常见字符
        stop_chars = set(['的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没', '看', '好', '自', '己', '这', '那', '个', '中', '为', '与', '之', '以', '而', '或', '等', '及', '其', '所', '可', '能', '将', '从', '对', '于', '但', '又', '还', '只', '都', '已', '被', '把', '让', '使', '给', '向', '由', '因', '如', '若', '则', '即', '且', '并', '或'])
        
        filtered_freq = {word: count for word, count in word_freq.items() 
                        if len(word) > 1 and word not in stop_chars and count > 1}
        
        return filtered_freq
    
    def generate_text_wordcloud(self, word, contexts):
        """生成文本格式的词云（词频列表）"""
        if not contexts:
            return {}
        
        # 合并所有上下文
        combined_text = ' '.join(contexts)
        
        # 获取词频
        word_freq = self.simple_word_frequency(combined_text)
        
        # 取前20个高频词
        top_words = dict(sorted(word_freq.items(), key=lambda x: x[1], reverse=True)[:20])
        
        return top_words
    
    def save_wordcloud_data(self, word, word_freq):
        """保存词云数据到文件"""
        if not word_freq:
            print(f"'{word}' 没有足够的词频数据")
            return
        
        # 保存为JSON
        json_path = os.path.join(self.results_dir, f'{word}_wordcloud_data.json')
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(word_freq, f, ensure_ascii=False, indent=2)
        
        # 保存为文本格式
        txt_path = os.path.join(self.results_dir, f'{word}_wordcloud.txt')
        with open(txt_path, 'w', encoding='utf-8') as f:
            f.write(f'"{word}" 相关词云数据\n')
            f.write('=' * 30 + '\n\n')
            
            for i, (w, freq) in enumerate(word_freq.items(), 1):
                f.write(f'{i:2d}. {w:<10} (出现 {freq} 次)\n')
        
        print(f"'{word}' 词云数据已保存: {txt_path}")
    
    def analyze_document(self, file_path):
        """分析文档"""
        print(f"正在分析文档: {file_path}")
        
        # 读取文档内容
        if file_path.endswith('.txt'):
            text = self.read_text_file(file_path)
        elif file_path.endswith('.docx'):
            text = self.extract_text_from_docx_simple(file_path)
        else:
            print(f"不支持的文件格式: {file_path}")
            return
        
        if not text:
            print("无法提取文档内容")
            return
        
        print(f"文档内容长度: {len(text)} 字符")
        
        # 统计词语出现次数
        word_counts = self.count_words_in_text(text)
        
        print("\n词语统计结果:")
        print("-" * 30)
        total_count = 0
        for word, count in word_counts.items():
            print(f"'{word}': {count} 次")
            total_count += count
        print(f"总计: {total_count} 次")
        
        # 生成词云数据
        print("\n生成词云数据:")
        print("-" * 30)
        for word in self.target_words:
            if word_counts[word] > 0:
                contexts = self.extract_context_simple(text, word, 100)
                word_freq = self.generate_text_wordcloud(word, contexts)
                self.save_wordcloud_data(word, word_freq)
            else:
                print(f"'{word}' 未出现，跳过词云生成")
        
        # 保存统计结果
        results = {
            "词语统计": word_counts,
            "总计": total_count,
            "分析时间": str(datetime.now()),
            "文档文件": file_path,
            "文档长度": len(text)
        }

        results_path = os.path.join(self.results_dir, 'simple_analysis_results.json')
        with open(results_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)

        # 同时保存文本格式的统计结果
        txt_results_path = os.path.join(self.results_dir, 'word_statistics.txt')
        with open(txt_results_path, 'w', encoding='utf-8') as f:
            f.write("清代西域诗集注词语统计结果\n")
            f.write("=" * 40 + "\n\n")
            f.write(f"分析文档: {file_path}\n")
            f.write(f"文档长度: {len(text):,} 字符\n")
            f.write(f"分析时间: {datetime.now()}\n\n")

            f.write("词语统计:\n")
            f.write("-" * 30 + "\n")
            for word, count in word_counts.items():
                f.write(f"'{word}': {count:,} 次\n")
            f.write(f"\n总计: {total_count:,} 次\n")

        print(f"\n分析结果已保存: {results_path}")
        print(f"文本结果已保存: {txt_results_path}")

def main():
    """主函数"""
    print("简化版文档分析工具")
    print("=" * 40)
    
    generator = SimpleWordCloudGenerator()
    
    # 查找文档文件
    doc_files = [
        "清代西域诗集注.doc",
        "清代西域诗辑注.docx", 
        "清代西域诗研究（星汉老师论著）.doc"
    ]
    
    found_file = None
    for doc_file in doc_files:
        if os.path.exists(doc_file):
            found_file = doc_file
            break
    
    if found_file:
        generator.analyze_document(found_file)
    else:
        print("未找到文档文件，请确保以下文件之一存在:")
        for doc_file in doc_files:
            print(f"  - {doc_file}")
        
        # 如果没有找到Word文档，提示用户可以创建测试文本
        print("\n或者创建一个测试文本文件 'test_document.txt'")

if __name__ == "__main__":
    main()
