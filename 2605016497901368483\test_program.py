#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试程序 - 创建测试文档并验证分析功能
"""

import os
import sys
from datetime import datetime

def create_test_document():
    """创建测试文档"""
    test_content = """
清代西域诗集注研究

第一章 西域地理概述

乌鲁木齐作为新疆的首府，自古以来就是丝绸之路上的重要节点。在清代诗人的笔下，乌鲁木齐不仅是政治中心，更是文化交流的重要场所。许多诗人在乌鲁木齐留下了珍贵的诗篇，描绘了这座城市的繁华与美丽。

伊犁河谷被誉为"塞外江南"，其优美的自然风光吸引了众多文人墨客。清代诗人在伊犁创作了大量描写边塞风光的诗歌。伊犁的草原、河流、雪山都成为了诗人笔下的经典意象。从伊犁出发的商队和使节，将中原文化带到了更远的西域。

哈密作为进入新疆的门户，历来是兵家必争之地。清代在哈密设置了重要的军事据点，许多诗人在哈密驻留期间，创作了反映边塞生活的诗作。哈密的瓜果闻名天下，诗人们也常常在诗中提及哈密的物产丰富。

第二章 清代西域诗歌的特点

清代西域诗歌具有鲜明的地域特色。诗人们通过对乌鲁木齐、伊犁、哈密等地的描写，展现了西域独特的自然风貌和人文景观。

在乌鲁木齐的诗歌中，我们可以看到诗人对这座城市的深情描绘。从繁华的市集到宁静的夜晚，从雄伟的天山到清澈的河流，乌鲁木齐在诗人笔下呈现出多姿多彩的面貌。

伊犁河谷的诗歌则更多地体现了诗人对自然美景的赞美。伊犁的四季变化、草原牧歌、河流潺潺，都成为了诗人创作的灵感源泉。许多描写伊犁的诗歌至今仍被人们传诵。

哈密地区的诗歌往往带有浓厚的边塞色彩。诗人们在哈密感受到的不仅是自然风光，更是边疆将士的豪情壮志。哈密的诗歌中常常出现戍边、思乡等主题。

第三章 代表性诗人及作品

清代有许多诗人曾在西域地区生活和创作，他们的作品为我们了解当时的乌鲁木齐、伊犁、哈密提供了珍贵的史料。

这些诗人在乌鲁木齐期间创作的诗歌，不仅记录了城市的发展变化，也反映了当时的社会风貌。通过这些诗歌，我们可以了解清代乌鲁木齐的政治、经济、文化状况。

在伊犁地区，诗人们被当地的自然美景深深震撼。他们用诗歌记录下了伊犁河谷的壮美景色，这些作品成为了研究清代伊犁历史文化的重要资料。

哈密地区的诗歌作品虽然数量相对较少，但每一首都具有很高的史料价值。这些诗歌让我们能够了解清代哈密的军事地位和文化特色。

结语

通过对清代西域诗集的研究，我们可以更好地了解乌鲁木齐、伊犁、哈密等地在清代的历史地位和文化价值。这些诗歌不仅是文学作品，更是珍贵的历史文献，为我们研究清代西域历史提供了重要的参考资料。

乌鲁木齐、伊犁、哈密三地在清代西域诗歌中占据着重要地位，它们不仅是地理概念，更是文化符号，承载着丰富的历史内涵和文化意义。
"""
    
    # 保存为文本文件
    with open('test_document.txt', 'w', encoding='utf-8') as f:
        f.write(test_content)
    
    print("测试文档已创建: test_document.txt")
    return 'test_document.txt'

def test_simple_analysis():
    """测试简化版分析程序"""
    print("正在测试简化版分析程序...")
    
    try:
        # 导入简化版分析器
        from simple_wordcloud import SimpleWordCloudGenerator
        
        # 创建分析器实例
        generator = SimpleWordCloudGenerator()
        
        # 创建测试文档
        test_file = create_test_document()
        
        # 运行分析
        generator.analyze_document(test_file)
        
        print("✓ 简化版分析程序测试成功")
        return True
        
    except Exception as e:
        print(f"✗ 简化版分析程序测试失败: {e}")
        return False

def test_full_analysis():
    """测试完整版分析程序"""
    print("正在测试完整版分析程序...")
    
    try:
        # 检查依赖包
        required_packages = ['docx', 'docx2txt', 'wordcloud', 'matplotlib', 'jieba', 'numpy']
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package)
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            print(f"缺少依赖包: {', '.join(missing_packages)}")
            print("请先运行: python install_requirements.py")
            return False
        
        # 导入完整版分析器
        from document_analysis import DocumentAnalyzer
        
        # 创建分析器实例
        analyzer = DocumentAnalyzer()
        
        # 创建测试文档
        test_file = create_test_document()
        
        # 运行分析
        analyzer.run_analysis(test_file)
        
        print("✓ 完整版分析程序测试成功")
        return True
        
    except Exception as e:
        print(f"✗ 完整版分析程序测试失败: {e}")
        return False

def check_results():
    """检查分析结果"""
    print("\n检查分析结果...")
    
    results_dir = "results"
    if not os.path.exists(results_dir):
        print("✗ 结果目录不存在")
        return False
    
    # 检查文件
    expected_files = [
        'analysis_results.json',
        'analysis_results.txt',
        'simple_analysis_results.json'
    ]
    
    found_files = []
    for file_name in expected_files:
        file_path = os.path.join(results_dir, file_name)
        if os.path.exists(file_path):
            found_files.append(file_name)
            print(f"✓ 找到结果文件: {file_name}")
    
    if found_files:
        print(f"✓ 共找到 {len(found_files)} 个结果文件")
        return True
    else:
        print("✗ 未找到任何结果文件")
        return False

def main():
    """主测试函数"""
    print("清代西域诗集注分析工具测试程序")
    print("=" * 50)
    print(f"测试时间: {datetime.now()}")
    print()
    
    # 测试计数器
    total_tests = 0
    passed_tests = 0
    
    # 测试1: 简化版分析
    total_tests += 1
    if test_simple_analysis():
        passed_tests += 1
    
    print()
    
    # 测试2: 完整版分析
    total_tests += 1
    if test_full_analysis():
        passed_tests += 1
    
    # 测试3: 检查结果
    total_tests += 1
    if check_results():
        passed_tests += 1
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print(f"测试完成: {passed_tests}/{total_tests} 通过")
    
    if passed_tests == total_tests:
        print("✓ 所有测试通过！程序可以正常使用。")
    elif passed_tests > 0:
        print("⚠ 部分测试通过，程序可以部分使用。")
    else:
        print("✗ 所有测试失败，请检查程序配置。")
    
    # 使用建议
    print("\n使用建议:")
    if passed_tests >= 1:
        print("- 可以使用简化版程序: python simple_wordcloud.py")
    if passed_tests >= 2:
        print("- 可以使用完整版程序: python document_analysis.py")
    
    print("- 将Word文档放在当前目录中")
    print("- 查看 results/ 目录中的分析结果")

if __name__ == "__main__":
    main()
