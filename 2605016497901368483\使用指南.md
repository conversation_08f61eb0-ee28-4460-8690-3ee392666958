# 清代西域诗集注分析工具使用指南

## 🎯 项目概述

本工具专门用于分析《清代西域诗集注》文档，统计"乌鲁木齐"、"伊犁"、"哈密"三个词语的出现次数，并为每个词语生成相关的词云数据。

## 📊 分析结果

根据对《清代西域诗辑注.docx》的分析，得到以下统计结果：

### 词语出现次数统计
- **乌鲁木齐**: 432 次
- **伊犁**: 565 次  
- **哈密**: 213 次
- **总计**: 1,210 次

### 词云数据特点

#### 🏛️ 乌鲁木齐相关词云
高频词汇包括：见前、此诗、作于、作者、于乌等，反映了诗歌注释的特点和地理位置描述。

#### 🌿 伊犁相关词云  
高频词汇包括：此诗、见前、作者、作于、乾隆、将军、自注等，体现了伊犁在清代的军政地位。

#### 🏜️ 哈密相关词云
高频词汇包括：作者、见前、自注、此诗、句下等，显示了哈密在诗集中的文献特征。

## 🚀 快速使用

### 1. 运行分析程序
```bash
python simple_wordcloud.py
```

### 2. 查看美化结果
```bash
python show_results.py
```

### 3. 测试程序功能
```bash
python test_program.py
```

## 📁 文件结构

```
2605016497901368483/
├── 📄 document_analysis.py      # 完整版分析程序（需要额外依赖）
├── 📄 simple_wordcloud.py       # 简化版分析程序（推荐使用）
├── 📄 show_results.py           # 结果展示脚本
├── 📄 test_program.py           # 测试脚本
├── 📄 install_requirements.py   # 依赖安装脚本
├── 📄 使用指南.md               # 本文件
├── 📄 README.md                 # 详细说明文档
├── 📂 results/                  # 分析结果目录
│   ├── 📊 simple_analysis_results.json
│   ├── 📝 word_statistics.txt
│   ├── ☁️ 乌鲁木齐_wordcloud.txt
│   ├── ☁️ 伊犁_wordcloud.txt
│   ├── ☁️ 哈密_wordcloud.txt
│   └── 📈 各词语词云数据JSON文件
└── 📖 清代西域诗辑注.docx      # 分析的源文档
```

## 🔧 技术实现

### 词云生成原理

1. **上下文提取**: 提取目标词语前后100个字符的上下文内容
2. **简单分词**: 按字符进行1-3字词组合，生成候选词汇
3. **词频统计**: 统计各词汇在上下文中的出现频率
4. **停用词过滤**: 过滤常见的停用词和无意义字符
5. **结果排序**: 按词频降序排列，取前20个高频词

### 文档处理方法

- **DOCX格式**: 使用ZIP解压和XML解析提取文本内容
- **字符编码**: 支持UTF-8和GBK编码自动检测
- **正则匹配**: 使用精确的正则表达式统计词语出现次数

## 📈 词云数据解读

### 词频分析说明

从词云数据可以看出：

1. **文献特征词汇**: "见前"、"此诗"、"作者"、"自注"等词汇频繁出现，说明这是一部带有详细注释的诗集。

2. **时代背景词汇**: "乾隆"、"嘉庆"等年号出现，体现了清代的时代特征。

3. **地理位置词汇**: "于乌"、"于伊"、"作于"等词汇显示了诗歌创作的地理背景。

4. **军政词汇**: "将军"等词汇在伊犁相关内容中出现，反映了伊犁的军事地位。

### 统计数据意义

- **伊犁出现次数最多(565次)**: 说明伊犁在清代西域诗歌中占据重要地位
- **乌鲁木齐次之(432次)**: 体现了乌鲁木齐作为政治中心的重要性  
- **哈密相对较少(213次)**: 可能与其地理位置和历史地位相关

## 🎨 词云图生成规划

### 可视化词云图设计方案

如果要生成可视化的词云图，建议采用以下设计：

#### 1. 乌鲁木齐词云图
- **主色调**: 蓝色系（象征天山雪水）
- **形状**: 圆形或天山轮廓
- **字体**: 中文楷体，体现古典韵味
- **布局**: 中心突出"乌鲁木齐"，周围环绕相关词汇

#### 2. 伊犁词云图  
- **主色调**: 绿色系（象征草原）
- **形状**: 河流或草原轮廓
- **字体**: 中文宋体，体现文献特色
- **布局**: 突出"将军"、"河谷"等特色词汇

#### 3. 哈密词云图
- **主色调**: 橙色系（象征瓜果）
- **形状**: 椭圆形或瓜果轮廓  
- **字体**: 中文黑体，简洁明了
- **布局**: 强调"天山"、"新疆"等地理词汇

### 词云图技术参数

```python
wordcloud_config = {
    "width": 800,
    "height": 600, 
    "background_color": "white",
    "max_words": 50,
    "font_path": "simhei.ttf",  # 中文字体
    "colormap": "viridis",      # 色彩映射
    "relative_scaling": 0.5,    # 词汇大小比例
    "random_state": 42          # 随机种子
}
```

## 💡 使用建议

1. **首次使用**: 运行 `python test_program.py` 验证程序功能
2. **日常分析**: 使用 `python simple_wordcloud.py` 进行分析
3. **结果查看**: 使用 `python show_results.py` 查看美化结果
4. **深度分析**: 查看 `results/` 目录中的详细数据文件

## 🔍 扩展功能

### 可能的改进方向

1. **增加更多地名**: 扩展到"喀什"、"和田"等其他西域地名
2. **时间维度分析**: 按朝代或年份统计词语出现趋势
3. **诗人维度分析**: 统计不同诗人对各地的描述特点
4. **主题词分析**: 提取与自然、军事、文化相关的主题词汇

### 数据导出功能

- **Excel格式**: 导出统计数据到Excel表格
- **CSV格式**: 便于进一步的数据分析
- **图表生成**: 生成柱状图、饼图等统计图表

## ✅ 项目总结

本工具成功实现了对《清代西域诗集注》的词语统计和词云分析功能：

- ✅ 精确统计了三个目标词语的出现次数
- ✅ 生成了每个词语的相关词云数据  
- ✅ 提供了友好的结果展示界面
- ✅ 支持多种文档格式的处理
- ✅ 具备良好的扩展性和可维护性

通过分析结果可以看出，伊犁、乌鲁木齐、哈密三地在清代西域诗歌中都占据重要地位，为研究清代西域历史文化提供了有价值的数据支撑。
