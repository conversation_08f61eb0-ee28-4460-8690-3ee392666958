# 物流配送中心选址优化系统

## 项目概述

本项目实现了一个基于蚁群算法的物流配送中心选址优化系统。该系统能够在考虑多种约束条件的情况下，为物流网络设计提供最优的配送中心选址方案，包括：
- 配送中心建设成本
- 运输成本
- 碳排放成本
- 时间窗口约束（考虑最早和最晚送达时间）
- 配送中心容量约束

## 功能特点

- **多目标优化**：同时考虑经济成本、环境成本和服务质量
- **时间窗口约束**：考虑客户对配送时间的要求（最早和最晚送达时间）
- **时间惩罚机制**：对超出时间窗口的配送进行惩罚计算
- **容量约束**：考虑配送中心的处理能力限制
- **碳排放限制**：考虑环保要求，限制总碳排放量
- **可视化分析**：直观展示配送网络结构和各项指标，包括时间窗口信息

## 技术实现

### 核心算法

项目采用蚁群优化算法(ACO)求解物流配送中心选址问题：
- 信息素机制引导搜索方向
- 启发式信息提高搜索效率
- 局部搜索优化解的质量
- 多蚂蚁并行搜索提高求解效率

### 数据结构

- **候选配送中心**：包含坐标、建设成本、容量和绿色评分
- **需求点**：包含坐标、需求量、最早送达时间和最晚送达时间
- **距离矩阵**：存储需求点与配送中心之间的距离
- **时间计算**：基于距离和平均速度计算运输时间

### 约束条件

1. 配送中心容量约束
2. 碳排放总量约束
3. 时间窗口约束（最早和最晚送达时间）
4. 最大配送中心数量约束
5. 服务时间约束（考虑配送中心处理订单的固定服务时间）

## 使用方法

### 环境要求

- Python 3.6+
- 依赖库：
  - numpy
  - pandas
  - matplotlib

### 运行方式

```bash
python jianmo.py
```

### 参数配置

可在代码中修改以下参数以适应不同场景：

```python
# 物流网络参数
transport_cost_per_unit = 0.5      # 单位运输成本
carbon_cost_per_unit = 0.02        # 单位碳排放成本
max_carbon_emission = 2000         # 最大碳排放量
carbon_emission_factor = 0.1       # 碳排放因子
max_centers = 2                    # 最大配送中心数量

# 时间窗口相关参数
average_speed_km_h = 40            # 车辆平均速度(公里/小时)
time_penalty_per_hour_late = 50    # 每小时延迟的惩罚成本
fixed_service_time_h_at_center = 0.5 # 配送中心处理订单的固定服务时间(小时)

# 蚁群算法参数
n_ants = 30                        # 蚂蚁数量
n_iterations = 150                 # 最大迭代次数
alpha = 1.0                        # 信息素重要程度
beta = 3.0                         # 启发式信息重要程度
rho = 0.3                          # 信息素蒸发率
q0 = 0.9                           # 状态转移规则参数
```

## 输出结果

程序运行后将输出以下结果：

1. **文本输出**：
   - 需求点和候选中心之间的距离矩阵
   - 蚁群算法迭代过程
   - 最优解的详细信息（选中的配送中心、分配方案、成本明细等）

2. **可视化输出**：
   - 物流网络结构图，显示选中的配送中心、需求点及其分配关系
   - 成本和碳排放等关键指标

## 示例结果

运行程序后，将生成一个名为`logistics_network_design_aco_with_time_windows_single_plot.png`的图像文件，展示最优配送网络结构。

## 算法流程

1. **初始化**：设置参数，计算启发式信息
2. **构建解**：
   - 选择配送中心
   - 分配需求点
   - 检查解的可行性
3. **局部搜索**：优化当前解
4. **更新信息素**：根据解的质量更新信息素
5. **迭代优化**：重复步骤2-4直到达到最大迭代次数
6. **输出结果**：展示最优解及其可视化

## 未来改进方向

- 支持多种运输方式
- 考虑库存成本
- 引入随机需求模型
- 支持多周期规划
- 优化算法性能，提高求解速度
