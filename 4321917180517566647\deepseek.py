import pulp
import math

# 候选配送中心数据
candidates = [
    {'id': 'C1', 'x': 20, 'y': 30, 'cost': 800, 'capacity': 500},
    {'id': 'C2', 'x': 45, 'y': 25, 'cost': 700, 'capacity': 600},
    {'id': 'C3', 'x': 60, 'y': 50, 'cost': 900, 'capacity': 550},
    {'id': 'C4', 'x': 35, 'y': 60, 'cost': 750, 'capacity': 480},
    {'id': 'C5', 'x': 50, 'y': 40, 'cost': 850, 'capacity': 620},
]

# 需求点数据
demand_points = [
    {'id': 'D1', 'x': 10, 'y': 20, 'demand': 80},
    {'id': 'D2', 'x': 25, 'y': 35, 'demand': 120},
    {'id': 'D3', 'x': 26, 'y': 35, 'demand': 70},
    {'id': 'D4', 'x': 55, 'y': 45, 'demand': 95},
    {'id': 'D5', 'x': 35, 'y': 50, 'demand': 85},
    {'id': 'D6', 'x': 74, 'y': 15, 'demand': 88},
    {'id': 'D7', 'x': 38, 'y': 48, 'demand': 70},
    {'id': 'D8', 'x': 50, 'y': 80, 'demand': 47},
    {'id': 'D9', 'x': 55, 'y': 35, 'demand': 28},
    {'id': 'D10', 'x': 23, 'y': 45, 'demand': 39},
]

# 计算需求点到候选中心的直线距离
distances = {}
for dp in demand_points:
    for cand in candidates:
        dx = dp['x'] - cand['x']
        dy = dp['y'] - cand['y']
        distance = math.sqrt(dx**2 + dy**2)
        distances[(dp['id'], cand['id'])] = distance

# 输出部分距离示例
print("示例距离计算（D1到各候选中心）：")
for cand in candidates:
    print(f"D1到{cand['id']}: {distances[('D1', cand['id'])]:.2f} km")


# 创建模型
model = pulp.LpProblem("Green_Logistics_Network", pulp.LpMinimize)

# 定义变量
y = pulp.LpVariable.dicts("Build", [cand['id'] for cand in candidates], cat='Binary')
x = pulp.LpVariable.dicts("Assign", [(dp['id'], cand['id']) for dp in demand_points for cand in candidates], cat='Binary')

# 目标函数（总成本=建设成本+运输成本+碳排放成本）
construction_cost = pulp.lpSum([cand['cost'] * 10000 * y[cand['id']] for cand in candidates])  # 转换为元
transport_cost = pulp.lpSum([0.5 * dp['demand'] * distances[(dp['id'], cand['id'])] * x[(dp['id'], cand['id'])] 
                   for dp in demand_points for cand in candidates])
carbon_cost = pulp.lpSum([0.002 * dp['demand'] * distances[(dp['id'], cand['id'])] * x[(dp['id'], cand['id'])] 
                 for dp in demand_points for cand in candidates])

model += construction_cost + transport_cost + carbon_cost

# 约束条件
# 每个需求点必须被分配到一个设施
for dp in demand_points:
    model += pulp.lpSum([x[(dp['id'], cand['id'])] for cand in candidates]) == 1

# 设施容量约束
for cand in candidates:
    model += pulp.lpSum([dp['demand'] * x[(dp['id'], cand['id'])] for dp in demand_points]) <= cand['capacity'] * y[cand['id']]

# 最多建设2个设施
model += pulp.lpSum([y[cand['id']] for cand in candidates]) <= 2

# 碳排放总量约束
carbon_total = pulp.lpSum([0.1 * dp['demand'] * distances[(dp['id'], cand['id'])] * x[(dp['id'], cand['id'])] 
                  for dp in demand_points for cand in candidates])
model += carbon_total <= 2000

# 求解模型
solver = pulp.PULP_CBC_CMD(msg=True)
model.solve(solver)



# 输出结果
print("\n求解状态:", pulp.LpStatus[model.status])
print("总成本:", pulp.value(model.objective), "元")

# 被选中的配送中心
selected_facilities = [cand['id'] for cand in candidates if y[cand['id']].value() == 1]
print("\n选中的配送中心:", selected_facilities)

# 需求点分配情况
assignment = {}
for dp in demand_points:
    for cand in candidates:
        if x[(dp['id'], cand['id'])].value() == 1:
            assignment[dp['id']] = cand['id']
print("\n需求点分配:")
for dp_id, cand_id in assignment.items():
    print(f"{dp_id} -> {cand_id}")

# 各设施处理量
print("\n各设施处理量（吨/月）:")
for cand in candidates:
    if y[cand['id']].value() == 1:
        total = sum(dp['demand'] for dp in demand_points if assignment[dp['id']] == cand['id'])
        print(f"{cand['id']}: {total} (容量: {cand['capacity']})")

# 碳排放总量
total_carbon = pulp.value(carbon_total)
print(f"\n总碳排放量: {total_carbon:.2f} 吨/月 (约束: ≤2000 吨/月)")
