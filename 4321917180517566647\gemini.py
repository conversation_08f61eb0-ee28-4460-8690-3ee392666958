import math
import pandas as pd
from pulp import *

# --- 数据定义 ---

# 候选中心数据 (Candidate Centers Data)
centers_data = {
    'C1': {'coords': (20, 30), 'cost': 800, 'capacity': 500, 'greenness': 4.2},
    'C2': {'coords': (45, 25), 'cost': 700, 'capacity': 600, 'greenness': 3.8},
    'C3': {'coords': (60, 50), 'cost': 900, 'capacity': 550, 'greenness': 4.5},
    'C4': {'coords': (35, 60), 'cost': 750, 'capacity': 480, 'greenness': 4.0},
    'C5': {'coords': (50, 40), 'cost': 850, 'capacity': 620, 'greenness': 3.5}
}
candidate_centers = list(centers_data.keys()) # ['C1', 'C2', 'C3', 'C4', 'C5']

# 需求点数据 (Demand Points Data)
demand_data = {
    'D1': {'coords': (10, 20), 'demand': 80},
    'D2': {'coords': (25, 35), 'demand': 120},
    'D3': {'coords': (26, 35), 'demand': 70}, # 注意: D2 和 D3 非常接近
    'D4': {'coords': (55, 45), 'demand': 95},
    'D5': {'coords': (35, 50), 'demand': 85},
    'D6': {'coords': (74, 15), 'demand': 88},
    'D7': {'coords': (38, 48), 'demand': 70},
    'D8': {'coords': (50, 80), 'demand': 47},
    'D9': {'coords': (55, 35), 'demand': 28},
    'D10': {'coords': (23, 45), 'demand': 39}
}
demand_points = list(demand_data.keys()) # ['D1', ..., 'D10']

# 参数 (Parameters)
num_centers_to_build = 2 # 需要建设的中心数量
unit_transport_cost = 0.5  # 元/吨·公里
unit_carbon_cost = 0.02   # 元/吨CO₂
carbon_emission_coeff = 0.1 # 吨CO₂/吨·公里
max_carbon_emission = 2000 # 吨CO₂/月 (最大允许碳排放量)

# 将建设成本从 万元 转换为 元
for c in candidate_centers:
    centers_data[c]['cost'] *= 10000

# 计算有效的每吨公里变动成本 (包含运输成本和碳排放成本)
# 总变动成本 = 运输成本 + 碳排放成本
# 运输成本 = unit_transport_cost * demand * distance
# 碳排放成本 = unit_carbon_cost * 碳排放量 = unit_carbon_cost * (carbon_emission_coeff * demand * distance)
# 每吨公里总变动成本因子 = unit_transport_cost + unit_carbon_cost * carbon_emission_coeff
effective_variable_cost_per_ton_km = unit_transport_cost + (unit_carbon_cost * carbon_emission_coeff)

# --- 距离计算 ---
def euclidean_distance(coord1, coord2):
    """计算两点间的欧几里得距离"""
    return math.sqrt((coord1[0] - coord2[0])**2 + (coord1[1] - coord2[1])**2)

distances = {}
print("--- 数据预处理：计算距离 (km) ---")
distance_rows = []
for i in demand_points:
    distances[i] = {}
    row_data = {'Demand Point': i}
    for j in candidate_centers:
        dist = euclidean_distance(demand_data[i]['coords'], centers_data[j]['coords'])
        distances[i][j] = dist
        row_data[j] = f"{dist:.2f}" # 格式化显示
    distance_rows.append(row_data)

# 使用Pandas以表格形式显示距离
distance_df = pd.DataFrame(distance_rows)
distance_df.set_index('Demand Point', inplace=True)
print(distance_df)
print("-" * 50)

# 计算总月需求量，用于后续检查
total_demand = sum(demand_data[i]['demand'] for i in demand_points)
print(f"总月需求量: {total_demand} 吨")
print("-" * 50)



# --- 模型构建 ---

# 创建最小化问题实例
prob = LpProblem("GreenLogisticsLocation", LpMinimize)

# 决策变量 (Decision Variables)
# y_j = 1 如果中心 j 被建设, 0 否则 (二元变量)
use_center = LpVariable.dicts("Use Center", candidate_centers, 0, 1, LpBinary)

# x_ij = 1 如果需求点 i 由中心 j 服务, 0 否则 (二元变量)
assign_customer = LpVariable.dicts("Assign Customer",
                                   [(i, j) for i in demand_points for j in candidate_centers],
                                   0, 1, LpBinary)

# 目标函数: 最小化总成本 (Objective Function: Minimize Total Cost)
# 总成本 = Σ(建设成本) + Σ(变动成本)
# 变动成本 = 运输成本 + 碳排放成本

# 建设成本部分
construction_cost = lpSum(centers_data[j]['cost'] * use_center[j] for j in candidate_centers)

# 变动成本部分 (运输 + 碳排放)
# 使用前面计算的 effective_variable_cost_per_ton_km
variable_cost = lpSum(effective_variable_cost_per_ton_km * demand_data[i]['demand'] * distances[i][j] * assign_customer[(i, j)]
                      for i in demand_points for j in candidate_centers)

# 总目标函数
prob += construction_cost + variable_cost, "Total_Cost"


# --- 约束条件 (Constraints) ---

# 1. 需求覆盖约束: 每个客户必须被一个且仅一个中心服务
for i in demand_points:
    prob += lpSum(assign_customer[(i, j)] for j in candidate_centers) == 1, f"Demand_Coverage_{i}"

# 2. 容量约束: 服务一个中心 j 的总需求量不能超过其容量
#    同时, 只有当中心 j 被建设 (use_center[j]=1) 时, 才能有客户分配给它 (assign_customer[(i, j)]才能为1)
for j in candidate_centers:
    prob += lpSum(demand_data[i]['demand'] * assign_customer[(i, j)] for i in demand_points) <= centers_data[j]['capacity'] * use_center[j], f"Capacity_{j}"

# 3. 建设数量约束: 必须恰好建设 Q (这里是2) 个中心
prob += lpSum(use_center[j] for j in candidate_centers) == num_centers_to_build, "Num_Centers_Constraint"

# 4. 碳排放总量约束: 总的碳排放量不能超过最大允许值
total_carbon_emissions = lpSum(carbon_emission_coeff * demand_data[i]['demand'] * distances[i][j] * assign_customer[(i, j)]
                               for i in demand_points for j in candidate_centers)
prob += total_carbon_emissions <= max_carbon_emission, "Carbon_Emission_Constraint"


# --- 模型代码展示 (概念性) ---
# 上述代码定义了模型。现在我们将求解它。
print("--- 模型构建完成 ---")
# 打印完整的模型结构会很长。我们将在求解后打印目标和约束的摘要。
# print(prob) # 如果需要，这会打印出完整的LP公式


# --- 求解模型 ---
print("\n--- 开始求解优化问题 ---")
# prob.solve() # 使用默认的 CBC 求解器
# 如果需要，可以指定求解器，例如 CBC
solver = PULP_CBC_CMD(msg=0) # msg=0 禁止显示求解器过程信息
prob.solve(solver)

# --- 结果分析 ---
print(f"\n求解器状态: {LpStatus[prob.status]}")

if LpStatus[prob.status] == 'Optimal':
    print("\n--- 找到最优解 ---")

    # 总成本
    total_cost_value = value(prob.objective)
    print(f"最小总成本: {total_cost_value:,.2f} 元/月")

    # 选择了哪些中心?
    selected_centers = [j for j in candidate_centers if use_center[j].varValue > 0.9] # y_j = 1 的中心
    print(f"\n选定的中心 ({len(selected_centers)}个): {', '.join(selected_centers)}")

    # 建设成本贡献
    selected_construction_cost = sum(centers_data[j]['cost'] for j in selected_centers)
    print(f"  - 总建设成本: {selected_construction_cost:,.2f} 元/月")

    # 分配情况和变动成本
    assignments = []
    total_variable_cost_calc = 0
    total_transport_cost_calc = 0
    total_carbon_emitted_calc = 0
    total_carbon_cost_calc = 0
    print("\n客户分配情况:")
    for j in selected_centers:
        print(f"  中心 {j} 服务:")
        center_demand = 0
        center_assignments_list = []
        for i in demand_points:
            if assign_customer[(i, j)].varValue > 0.9: # x_ij = 1 的客户
                demand = demand_data[i]['demand']
                distance = distances[i][j]
                var_cost_contrib = effective_variable_cost_per_ton_km * demand * distance
                transport_cost_contrib = unit_transport_cost * demand * distance
                carbon_emitted_contrib = carbon_emission_coeff * demand * distance
                carbon_cost_contrib = unit_carbon_cost * carbon_emitted_contrib

                center_assignments_list.append(f"    - {i} (需求: {demand} t, 距离: {distance:.2f} km)")
                assignments.append({'Demand Point': i, 'Assigned Center': j})
                center_demand += demand
                total_variable_cost_calc += var_cost_contrib
                total_transport_cost_calc += transport_cost_contrib
                total_carbon_emitted_calc += carbon_emitted_contrib
                total_carbon_cost_calc += carbon_cost_contrib
        # 对分配列表排序（可选，为了输出一致）
        center_assignments_list.sort()
        for item in center_assignments_list:
            print(item)
        print(f"  中心 {j} 服务的总需求量: {center_demand} 吨 (容量: {centers_data[j]['capacity']} 吨)")
        if center_demand > centers_data[j]['capacity']:
             print(f"  警告: 中心 {j} 容量超出 (最优解中不应发生)")


    print(f"\n计算得到的变动成本 (运输 + 碳排放成本): {total_variable_cost_calc:,.2f} 元/月")
    print(f"  - 其中运输成本部分: {total_transport_cost_calc:,.2f} 元/月")
    print(f"  - 其中碳排放成本部分: {total_carbon_cost_calc:,.2f} 元/月")

    # 验证总成本
    print(f"验证的总成本 (建设 + 变动): {selected_construction_cost + total_variable_cost_calc:,.2f} 元/月")


    # 碳排放检查
    print(f"\n总碳排放量 (手动计算): {total_carbon_emitted_calc:.2f} 吨 CO₂/月") # 这是我们基于分配结果手动累加的
    print(f"最大允许碳排放量: {max_carbon_emission} 吨 CO₂/月")
    if total_carbon_emitted_calc <= max_carbon_emission:
        print("碳排放约束得到满足 (基于手动计算)。")
    else:
        print("警告: 碳排放约束未满足 (基于手动计算)！")

    # 使用 Pandas 显示分配结果表
    assignment_df = pd.DataFrame(assignments)
    print("\n分配结果汇总表:")
    print(assignment_df.to_string(index=False))

    # --- 从求解器检查碳排放约束 (修正后) ---
    print("\n--- 从求解器检查碳排放约束 ---")
    # 直接获取定义约束时左侧表达式的值
    carbon_constraint_LHS_value = value(total_carbon_emissions)
    print(f"碳排放约束左侧值 (求解器计算结果): {carbon_constraint_LHS_value:.2f}")

    # 获取约束的右侧限制值 (可以直接用我们定义的变量)
    carbon_constraint_limit = max_carbon_emission
    print(f"碳排放约束右侧限制值: {carbon_constraint_limit}")

    # 获取约束的松弛量 (Slack) - 这是最直接的检查方式
    # 对于 <= 约束, slack = RHS - LHS
    # 如果 slack >= 0, 则约束满足
    try:
        carbon_slack = prob.constraints["Carbon_Emission_Constraint"].slack
        print(f"碳排放约束松弛量: {carbon_slack:.2f} (松弛量 >= 0 表示满足)")
        if carbon_slack >= -1e-6: # 加一个小的容差防止浮点数精度问题
             print("约束满足状态确认: 满足")
        else:
             print("约束满足状态确认: 不满足")
    except KeyError:
        print("错误：无法找到名为 'Carbon_Emission_Constraint' 的约束。")


else:
    print("\n--- 未找到最优解 ---")
    print(f"求解器状态: {LpStatus[prob.status]}")

print("\n--- 分析结束 ---")