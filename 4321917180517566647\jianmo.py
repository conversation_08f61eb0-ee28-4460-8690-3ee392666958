import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from math import sqrt
import random
from matplotlib import font_manager

# 把本机已有的中文字体加入 font_manager
font_manager.fontManager.addfont(r"C:\Windows\Fonts\SimHei.ttf")

# 告诉 matplotlib 用它作默认字体 避免字体显示乱码
plt.rcParams['font.family'] = 'SimHei'
plt.rcParams['axes.unicode_minus'] = False


# 1. 数据准备
# 定义候选配送中心数据
centers = {
    'C1': {'coords': (20, 30), 'cost': 800, 'capacity': 500, 'green_score': 4.2},
    'C2': {'coords': (45, 25), 'cost': 700, 'capacity': 600, 'green_score': 3.8},
    'C3': {'coords': (60, 50), 'cost': 900, 'capacity': 550, 'green_score': 4.5},
    'C4': {'coords': (35, 60), 'cost': 750, 'capacity': 480, 'green_score': 4.0},
    'C5': {'coords': (50, 40), 'cost': 850, 'capacity': 620, 'green_score': 3.5}
}

# 定义需求点数据 (增加时间窗信息)
demands = {
    'D1': {'coords': (10, 20), 'demand': 80,  'earliest_time': 8, 'latest_time': 12},
    'D2': {'coords': (25, 35), 'demand': 120, 'earliest_time': 9, 'latest_time': 14},
    'D3': {'coords': (26, 35), 'demand': 70,  'earliest_time': 8, 'latest_time': 11},
    'D4': {'coords': (55, 45), 'demand': 95,  'earliest_time': 10, 'latest_time': 15},
    'D5': {'coords': (35, 50), 'demand': 85,  'earliest_time': 9, 'latest_time': 13},
    'D6': {'coords': (74, 15), 'demand': 88,  'earliest_time': 13, 'latest_time': 17},
    'D7': {'coords': (38, 48), 'demand': 70,  'earliest_time': 8, 'latest_time': 12},
    'D8': {'coords': (50, 80), 'demand': 47,  'earliest_time': 14, 'latest_time': 18},
    'D9': {'coords': (55, 35), 'demand': 28,  'earliest_time': 10, 'latest_time': 16},
    'D10': {'coords': (23, 45), 'demand': 39, 'earliest_time': 9, 'latest_time': 14}
}

# 定义参数
transport_cost_per_unit = 0.5
carbon_cost_per_unit = 0.02
max_carbon_emission = 2000
carbon_emission_factor = 0.1
max_centers = 2

average_speed_km_h = 40
time_penalty_per_hour_late = 50
fixed_service_time_h_at_center = 0.5

# 2. 数据预处理
distances = {}
for i in demands:
    for j in centers:
        x1, y1 = demands[i]['coords']
        x2, y2 = centers[j]['coords']
        distances[(i, j)] = sqrt((x1 - x2)**2 + (y1 - y2)**2)

print("需求点和候选中心之间的距离（公里）:")
distance_df = pd.DataFrame(columns=centers.keys(), index=demands.keys())
for i in demands:
    for j in centers:
        distance_df.loc[i, j] = round(distances[(i, j)], 2)
print(distance_df)

total_demand = sum(demands[i]['demand'] for i in demands)
print(f"\n总需求量: {total_demand} 吨/月")
total_capacity = sum(centers[j]['capacity'] for j in centers)
print(f"所有候选中心的总容量: {total_capacity} 吨/月")
print(f"选择 {max_centers} 个中心的容量范围: {min([centers[j]['capacity'] for j in centers])*max_centers} 到 {max([centers[j]['capacity'] for j in centers])*max_centers} 吨/月")

# 3. 蚁群算法实现
class AntColonyOptimization:
    def __init__(self, centers, demands, distances, transport_cost_per_unit,
                 carbon_cost_per_unit, max_carbon_emission, carbon_emission_factor, max_centers,
                 average_speed_km_h, time_penalty_per_hour_late, fixed_service_time_h_at_center,
                 n_ants=20, n_iterations=100, alpha=1.0, beta=2.0, rho=0.5, q0=0.9):
        self.centers = centers
        self.demands = demands
        self.distances = distances
        self.transport_cost_per_unit = transport_cost_per_unit
        self.carbon_cost_per_unit = carbon_cost_per_unit
        self.max_carbon_emission = max_carbon_emission
        self.carbon_emission_factor = carbon_emission_factor
        self.max_centers = max_centers
        self.n_ants = n_ants
        self.n_iterations = n_iterations
        self.alpha = alpha
        self.beta = beta
        self.rho = rho
        self.q0 = q0
        self.average_speed_km_h = average_speed_km_h
        self.time_penalty_per_hour_late = time_penalty_per_hour_late
        self.fixed_service_time_h_at_center = fixed_service_time_h_at_center
        self.center_names = list(centers.keys())
        self.demand_names = list(demands.keys())
        self.n_centers = len(centers)
        self.n_demands = len(demands)
        self.center_pheromone = np.ones(self.n_centers)
        self.assignment_pheromone = np.ones((self.n_demands, self.n_centers))
        self.best_solution = None
        self.best_cost = float('inf')

    def calculate_heuristic_information(self):
        self.center_heuristic = np.zeros(self.n_centers)
        for j, center_name in enumerate(self.center_names):
            cost = self.centers[center_name]['cost']
            capacity = self.centers[center_name]['capacity']
            self.center_heuristic[j] = capacity / (cost + 1)
        self.assignment_heuristic = np.zeros((self.n_demands, self.n_centers))
        for i, demand_name in enumerate(self.demand_names):
            for j, center_name in enumerate(self.center_names):
                distance = self.distances[(demand_name, center_name)]
                if distance > 0:
                    self.assignment_heuristic[i, j] = 1.0 / distance
                else:
                    self.assignment_heuristic[i, j] = 1.0

    def construct_solution(self):
        selected_centers = self.select_centers()
        assignments = self.assign_demands(selected_centers)
        is_feasible, total_carbon = self.check_solution_feasibility(selected_centers, assignments)
        if is_feasible:
            total_cost, cost_details = self.calculate_solution_cost(selected_centers, assignments)
            return {
                'selected_centers': selected_centers, 'assignments': assignments,
                'total_cost': total_cost, 'cost_details': cost_details,
                'total_carbon': total_carbon, 'is_feasible': True
            }
        else:
            return {
                'selected_centers': selected_centers, 'assignments': assignments,
                'total_cost': float('inf'), 'cost_details': None,
                'total_carbon': total_carbon, 'is_feasible': False
            }

    def select_centers(self):
        center_indices = list(range(self.n_centers))
        probabilities = np.zeros(self.n_centers)
        for j in range(self.n_centers):
            probabilities[j] = (self.center_pheromone[j] ** self.alpha) * (self.center_heuristic[j] ** self.beta)
        if np.sum(probabilities) > 0:
            probabilities = probabilities / np.sum(probabilities)
        else:
            probabilities = np.ones(self.n_centers) / self.n_centers
        num_to_select = min(self.max_centers, self.n_centers)
        if np.sum(probabilities) == 0:
             selected_indices = np.random.choice(center_indices, size=num_to_select, replace=False)
        else:
            selected_indices = np.random.choice(center_indices, size=num_to_select, replace=False, p=probabilities)
        return [self.center_names[j] for j in selected_indices]

    def assign_demands(self, selected_centers):
        assignments = {}
        if not selected_centers: return assignments
        selected_indices = [self.center_names.index(j) for j in selected_centers]
        remaining_capacity = {j: self.centers[j]['capacity'] for j in selected_centers}
        sorted_demands = sorted(self.demand_names, key=lambda x: self.demands[x]['demand'], reverse=True)
        for demand_name in sorted_demands:
            i = self.demand_names.index(demand_name)
            demand_value = self.demands[demand_name]['demand']
            probabilities = np.zeros(len(selected_centers))
            valid_assignment_possible = False
            for idx, center_idx_in_all_centers in enumerate(selected_indices):
                center_name = self.center_names[center_idx_in_all_centers]
                if remaining_capacity[center_name] >= demand_value:
                    probabilities[idx] = (self.assignment_pheromone[i, center_idx_in_all_centers] ** self.alpha) * \
                                       (self.assignment_heuristic[i, center_idx_in_all_centers] ** self.beta)
                    valid_assignment_possible = True
            if not valid_assignment_possible:
                for idx, center_idx_in_all_centers in enumerate(selected_indices):
                     probabilities[idx] = self.assignment_heuristic[i, center_idx_in_all_centers] ** self.beta
            if np.sum(probabilities) > 0:
                probabilities = probabilities / np.sum(probabilities)
                if random.random() < self.q0:
                    selected_idx_in_selected_list = np.argmax(probabilities)
                else:
                    selected_idx_in_selected_list = np.random.choice(range(len(selected_centers)), p=probabilities)
                selected_center_name = selected_centers[selected_idx_in_selected_list]
                assignments[demand_name] = selected_center_name
                remaining_capacity[selected_center_name] -= demand_value
            elif selected_centers:
                selected_center_name = random.choice(selected_centers)
                assignments[demand_name] = selected_center_name
                remaining_capacity[selected_center_name] -= demand_value
        return assignments

    def check_solution_feasibility(self, selected_centers, assignments):
        if not selected_centers or not assignments: return False, float('inf')
        center_loads = {j: 0 for j in selected_centers}
        for demand_node, center_node in assignments.items():
            if center_node not in center_loads: return False, float('inf')
            center_loads[center_node] += self.demands[demand_node]['demand']
        for center_node in selected_centers:
            if center_loads.get(center_node, 0) > self.centers[center_node]['capacity']:
                return False, float('inf')
        total_carbon = 0
        for demand_node, center_node in assignments.items():
            demand_value = self.demands[demand_node]['demand']
            distance = self.distances[(demand_node, center_node)]
            total_carbon += demand_value * distance * self.carbon_emission_factor
        if total_carbon > self.max_carbon_emission:
            return False, total_carbon
        return True, total_carbon

    def calculate_solution_cost(self, selected_centers, assignments):
        construction_cost = sum(self.centers[j]['cost'] for j in selected_centers) * 10000
        transportation_cost = 0
        for i, j in assignments.items():
            demand_value = self.demands[i]['demand']
            distance = self.distances[(i, j)]
            transportation_cost += demand_value * distance * self.transport_cost_per_unit
        carbon_emission = 0
        for i, j in assignments.items():
            demand_value = self.demands[i]['demand']
            distance = self.distances[(i, j)]
            carbon_emission += demand_value * distance * self.carbon_emission_factor
        carbon_cost = carbon_emission * self.carbon_cost_per_unit
        time_window_penalty_cost = 0
        total_lateness_hours = 0
        if self.average_speed_km_h <= 0:
            time_window_penalty_cost = float('inf')
        else:
            operational_start_hour = 8.0
            for demand_node, center_node in assignments.items():
                distance = self.distances[(demand_node, center_node)]
                travel_time_h = distance / self.average_speed_km_h
                ready_to_dispatch_time_point = operational_start_hour + self.fixed_service_time_h_at_center
                arrival_at_demand_point = ready_to_dispatch_time_point + travel_time_h
                demand_latest_time = self.demands[demand_node]['latest_time']
                lateness = 0
                if arrival_at_demand_point > demand_latest_time:
                    lateness = arrival_at_demand_point - demand_latest_time
                    time_window_penalty_cost += lateness * self.time_penalty_per_hour_late
                    total_lateness_hours += lateness
        total_cost = construction_cost + transportation_cost + carbon_cost + time_window_penalty_cost
        cost_details = {
            'construction_cost': construction_cost, 'transportation_cost': transportation_cost,
            'carbon_cost': carbon_cost, 'carbon_emission': carbon_emission,
            'time_window_penalty_cost': time_window_penalty_cost,
            'total_lateness_hours': total_lateness_hours
        }
        return total_cost, cost_details

    def update_pheromones(self, solutions):
        self.center_pheromone = (1 - self.rho) * self.center_pheromone
        self.assignment_pheromone = (1 - self.rho) * self.assignment_pheromone
        feasible_solutions = [s for s in solutions if s['is_feasible'] and s['total_cost'] != float('inf')]
        if not feasible_solutions: return
        sorted_solutions = sorted(feasible_solutions, key=lambda x: x['total_cost'])
        n_best = min(3, len(sorted_solutions))
        for k in range(n_best):
            solution = sorted_solutions[k]
            delta = 1.0 / solution['total_cost'] if solution['total_cost'] > 0 else 1.0
            for j_name in solution['selected_centers']:
                if j_name in self.center_names:
                    j_idx = self.center_names.index(j_name)
                    self.center_pheromone[j_idx] += delta
            if solution['assignments']:
                for i_name, j_name in solution['assignments'].items():
                    if i_name in self.demand_names and j_name in self.center_names:
                        i_idx = self.demand_names.index(i_name)
                        j_idx = self.center_names.index(j_name)
                        self.assignment_pheromone[i_idx, j_idx] += delta

    def local_search(self, solution):
        if not solution['is_feasible'] or solution['total_cost'] == float('inf'):
            return solution
        current_best_solution = solution.copy()
        current_best_cost = solution['total_cost']
        selected_centers = list(current_best_solution['selected_centers'])
        assignments = current_best_solution['assignments'].copy()
        for _ in range(self.n_demands // 2):
            if not assignments: break
            demand_to_move = random.choice(list(assignments.keys()))
            original_center = assignments[demand_to_move]
            potential_new_centers = [c for c in selected_centers if c != original_center]
            if not potential_new_centers: continue
            new_center_candidate = random.choice(potential_new_centers)
            assignments[demand_to_move] = new_center_candidate
            is_feasible, total_carbon = self.check_solution_feasibility(selected_centers, assignments)
            if is_feasible:
                new_total_cost, new_cost_details = self.calculate_solution_cost(selected_centers, assignments)
                if new_total_cost < current_best_cost:
                    current_best_cost = new_total_cost
                    current_best_solution = {
                        'selected_centers': selected_centers, 'assignments': assignments.copy(),
                        'total_cost': new_total_cost, 'cost_details': new_cost_details,
                        'total_carbon': total_carbon, 'is_feasible': True
                    }
                else:
                    assignments[demand_to_move] = original_center
            else:
                assignments[demand_to_move] = original_center
        return current_best_solution

    def run(self):
        print("\n正在运行蚁群算法求解...")
        self.calculate_heuristic_information()
        # REMOVED: all_iteration_best_costs = []

        for iteration in range(self.n_iterations):
            solutions_this_iteration = []
            for _ in range(self.n_ants):
                ant_solution = self.construct_solution()
                if ant_solution['is_feasible']:
                    ant_solution = self.local_search(ant_solution)
                solutions_this_iteration.append(ant_solution)
                if ant_solution['is_feasible'] and ant_solution['total_cost'] < self.best_cost:
                    self.best_solution = ant_solution
                    self.best_cost = ant_solution['total_cost']
            self.update_pheromones(solutions_this_iteration)
            # REMOVED: all_iteration_best_costs.append(self.best_cost)

            if (iteration + 1) % 10 == 0 or iteration == 0 or iteration == self.n_iterations - 1:
                current_iter_feasible_costs = [s['total_cost'] for s in solutions_this_iteration if s['is_feasible']]
                best_iter_cost_display = min(current_iter_feasible_costs) if current_iter_feasible_costs else float('inf')
                print(f"迭代 {iteration + 1}/{self.n_iterations}, "
                      f"当前迭代最佳成本: {best_iter_cost_display:.2f}, "
                      f"全局最佳成本: {self.best_cost:.2f}")
        if self.best_solution:
            print(f"\n蚁群算法找到最优解，总成本: {self.best_cost:.2f} 元")
        else:
            print("\n在当前参数设置下未找到可行解。")
        return self.best_solution # MODIFIED: Return only best_solution


# 4. 运行蚁群算法
aco = AntColonyOptimization(
    centers=centers, demands=demands, distances=distances,
    transport_cost_per_unit=transport_cost_per_unit, carbon_cost_per_unit=carbon_cost_per_unit,
    max_carbon_emission=max_carbon_emission, carbon_emission_factor=carbon_emission_factor,
    max_centers=max_centers, average_speed_km_h=average_speed_km_h,
    time_penalty_per_hour_late=time_penalty_per_hour_late,
    fixed_service_time_h_at_center=fixed_service_time_h_at_center,
    n_ants=30, n_iterations=150, alpha=1.0, beta=3.0, rho=0.3, q0=0.9
)

solution = aco.run() # MODIFIED: Receive only one value

# 5. 结果分析
if solution and solution['is_feasible']:
    selected_centers_sol = solution['selected_centers']
    assignments_sol = solution['assignments']
    cost_details_sol = solution['cost_details']
    total_cost_sol = solution['total_cost']
    total_carbon_sol = solution['total_carbon']

    print("\n选中的配送中心:")
    for j in selected_centers_sol:
        print(f"{j}: 坐标{centers[j]['coords']}, 容量{centers[j]['capacity']}吨/月, 建设成本{centers[j]['cost']}万元")

    print("\n需求点分配方案:")
    for i, j in assignments_sol.items():
        print(f"需求点 {i} (需求量: {demands[i]['demand']}吨, 最晚: {demands[i]['latest_time']}h) 分配到中心 {j} (距离: {distances[(i, j)]:.2f}公里)")

    center_loads = {j: 0 for j in selected_centers_sol}
    for i, j in assignments_sol.items():
        center_loads[j] += demands[i]['demand']

    print("\n中心容量利用情况:")
    for j in selected_centers_sol:
        utilization = center_loads[j] / centers[j]['capacity'] * 100 if centers[j]['capacity'] > 0 else 0
        print(f"{j}: {center_loads[j]} / {centers[j]['capacity']} 吨 ({utilization:.2f}%)")

    construction_cost_val = cost_details_sol['construction_cost']
    transportation_cost_val = cost_details_sol['transportation_cost']
    carbon_cost_val = cost_details_sol['carbon_cost']
    time_penalty_cost_val = cost_details_sol['time_window_penalty_cost']
    carbon_emission_val = cost_details_sol['carbon_emission']
    total_lateness = cost_details_sol.get('total_lateness_hours', 0)

    print("\n成本明细:")
    if total_cost_sol > 0 :
        print(f"建设成本: {construction_cost_val/10000:.2f} 万元 ({construction_cost_val:.2f} 元) ({construction_cost_val/total_cost_sol*100:.2f}%)")
        print(f"运输成本: {transportation_cost_val:.2f} 元 ({transportation_cost_val/total_cost_sol*100:.2f}%)")
        print(f"碳排放成本: {carbon_cost_val:.2f} 元 ({carbon_cost_val/total_cost_sol*100:.2f}%)")
        print(f"时间窗惩罚成本: {time_penalty_cost_val:.2f} 元 ({time_penalty_cost_val/total_cost_sol*100:.2f}%)")
    else:
        print(f"建设成本: {construction_cost_val/10000:.2f} 万元 ({construction_cost_val:.2f} 元)")
        print(f"运输成本: {transportation_cost_val:.2f} 元")
        print(f"碳排放成本: {carbon_cost_val:.2f} 元")
        print(f"时间窗惩罚成本: {time_penalty_cost_val:.2f} 元")
    print(f"总成本: {total_cost_sol:.2f} 元")

    print(f"\n总碳排放量: {carbon_emission_val:.2f} 吨CO₂/月 (限额: {max_carbon_emission} 吨CO₂/月)")
    if max_carbon_emission > 0:
        print(f"碳排放利用率: {carbon_emission_val/max_carbon_emission*100:.2f}%")
    print(f"总晚点时间: {total_lateness:.2f} 小时")

    # 6. 可视化结果
    plt.figure(figsize=(12, 10)) # MODIFIED: Restored original figure size

    # REMOVED: Subplot for iteration curve

    label_offsets = {
        'D1': (5, 5), 'D2': (-20, 5), 'D3': (15, 5), 'D4': (5, 5), 'D5': (5, 5),
        'D6': (5, 5), 'D7': (5, 5), 'D8': (5, 5), 'D9': (5, 5), 'D10': (5, 5)
    }
    demand_info_offsets = {
        'D1': (5, -10), 'D2': (-20, -15), 'D3': (15, -15), 'D4': (5, -10), 'D5': (5, -10),
        'D6': (5, -10), 'D7': (5, -10), 'D8': (5, -10), 'D9': (5, -10), 'D10': (5, -10)
    }

    for i in demands:
        x, y = demands[i]['coords']
        size = min(max(demands[i]['demand'] * 1.5, 50), 250)
        plt.scatter(x, y, s=size, c='blue', alpha=0.7, edgecolors='black')
        offset_x, offset_y = label_offsets.get(i, (5,5))
        plt.annotate(i, (x, y), xytext=(offset_x, offset_y), textcoords='offset points', fontsize=10, fontweight='bold')
        info_offset_x, info_offset_y = demand_info_offsets.get(i, (5,-10))
        # MODIFIED: Kept time window info in annotation, smaller font
        plt.annotate(f"{demands[i]['demand']}t\n[{demands[i]['earliest_time']}-{demands[i]['latest_time']}h]", (x, y),
                     xytext=(info_offset_x, info_offset_y), textcoords='offset points', fontsize=7)

    for j in centers:
        x, y = centers[j]['coords']
        if j in selected_centers_sol:
            plt.scatter(x, y, s=200, c='red', marker='s', alpha=0.9, edgecolors='black')
            plt.annotate(j, (x, y), xytext=(-5, -15), textcoords='offset points', fontweight='bold')
            plt.annotate(f"容量: {centers[j]['capacity']}吨", (x, y), xytext=(-25, -25), textcoords='offset points', fontsize=8)
        else:
            plt.scatter(x, y, s=100, c='gray', marker='s', alpha=0.5, edgecolors='black')
            plt.annotate(j, (x, y), xytext=(-5, -15), textcoords='offset points')

    for i, j_assigned in assignments_sol.items():
        x1, y1 = demands[i]['coords']
        x2, y2 = centers[j_assigned]['coords']
        plt.plot([x1, x2], [y1, y2], 'k--', linewidth=0.5, alpha=0.3)

    plt.xlim(0, 80)
    plt.ylim(0, 85)
    plt.grid(True, linestyle='--', alpha=0.3)
    plt.title('物流配送中心选址结果可视化 (蚁群算法)', fontsize=16) # MODIFIED: Restored original title
    plt.xlabel('X坐标 (km)', fontsize=12)
    plt.ylabel('Y坐标 (km)', fontsize=12)

    from matplotlib.lines import Line2D
    legend_elements = [ # MODIFIED: Slightly updated demand point label in legend
        Line2D([0], [0], marker='o', color='w', markerfacecolor='blue', markersize=10, label='需求点 (需求量, 时间窗)'),
        Line2D([0], [0], marker='s', color='w', markerfacecolor='red', markersize=10, label='已选中心'),
        Line2D([0], [0], marker='s', color='w', markerfacecolor='gray', markersize=10, label='未选中心'),
        Line2D([0], [0], linestyle='--', color='black', alpha=0.3, label='分配关系')
    ]
    plt.legend(handles=legend_elements, loc='lower right', fontsize=9) # MODIFIED: Kept slightly smaller legend font

    # MODIFIED: Updated textstr to include new costs and restored original position/fontsize
    textstr = '\n'.join((
        f'总成本: {total_cost_sol:.2f} 元',
        f'  建设成本: {construction_cost_val/10000:.2f} 万元',
        f'  运输成本: {transportation_cost_val:.2f} 元',
        f'  碳排放成本: {carbon_cost_val:.2f} 元',
        f'  时间窗罚金: {time_penalty_cost_val:.2f} 元',
        f'总碳排放量: {carbon_emission_val:.2f} 吨$CO_2$/月',
        f'总晚点时间: {total_lateness:.2f} 小时'
    ))
    props = dict(boxstyle='round', facecolor='wheat', alpha=0.5)
    plt.text(0.05, 0.05, textstr, transform=plt.gcf().transFigure, fontsize=10, # MODIFIED: Restored original text parameters
             verticalalignment='bottom', bbox=props)

    plt.tight_layout() # MODIFIED: Removed rect argument
    plt.savefig('logistics_network_design_aco_with_time_windows_single_plot.png', dpi=300, bbox_inches='tight')
    plt.show()
else:
    print("未找到可行解或最优解，请调整参数后重试。")
    # REMOVED: Plotting iteration curve if no solution found