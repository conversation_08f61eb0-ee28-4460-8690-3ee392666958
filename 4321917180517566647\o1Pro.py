# ========================================
# 文件名: step1_data_preprocessing.py
# ========================================

import math
import pandas as pd

# 1. 候选中心数据（以字典或DataFrame形式存储均可）
candidates_data = {
    'Center': ['C1','C2','C3','C4','C5'],
    'x': [20, 45, 60, 35, 50],       # X坐标
    'y': [30, 25, 50, 60, 40],       # Y坐标
    'fixed_cost': [800, 700, 900, 750, 850],  # 建设成本(万元)
    'capacity': [500, 600, 550, 480, 620],    # 最大容量(吨/月)
    'green_score': [4.2, 3.8, 4.5, 4.0, 3.5]   # 绿色度评分(暂不在模型计算中使用)
}

df_centers = pd.DataFrame(candidates_data)

# 2. 需求点数据
demands_data = {
    'DemandPoint': ['D1','D2','D3','D4','D5','D6','D7','D8','D9','D10'],
    'x': [10, 25, 26, 55, 35, 74, 38, 50, 55, 23],
    'y': [20, 35, 35, 45, 50, 15, 48, 80, 35, 45],
    'demand': [80,120,70,95,85,88,70,47,28,39]  # 月需求量(吨)
}

df_demands = pd.DataFrame(demands_data)

# 3. 其他参数
unit_transport_cost = 0.5   # 元/(吨*公里)
carbon_emission_factor = 0.1  # 吨CO2/(吨*公里)
unit_carbon_cost = 0.02       # 元/吨CO2
max_carbon = 2000             # 最大允许碳排放(吨CO2/月)
Q = 2                         # 允许建设的最大设施数

# 4. 预处理：计算各需求点到各中心的欧式距离
def euclidean_distance(x1, y1, x2, y2):
    return math.sqrt((x1 - x2)**2 + (y1 - y2)**2)

# 建立一个距离的字典，key=(需求点, 中心)，value=距离
distances = {}
for i in range(len(df_demands)):
    for j in range(len(df_centers)):
        di = df_demands.loc[i, 'DemandPoint']   # D1, D2, ...
        cj = df_centers.loc[j, 'Center']        # C1, C2, ...
        x_i, y_i = df_demands.loc[i, 'x'], df_demands.loc[i, 'y']
        x_j, y_j = df_centers.loc[j, 'x'], df_centers.loc[j, 'y']
        
        dist = euclidean_distance(x_i, y_i, x_j, y_j)
        distances[(di, cj)] = dist

# 可以将结果打印或写入文件，这里仅展示一部分
print("==== 部分距离预览 ====")
for k, v in list(distances.items())[:10]:  # 仅展示前10条
    print(f"{k}: {v:.2f}")





# ========================================
# 文件名: step2_model_building.py
# ========================================

import pulp

# ============ 数据读取（可直接在同一脚本中使用，也可从csv/DataFrame中读） ============
# 这里假设与 step1_data_preprocessing.py 写在同一个文件或者已经import。
# 如果是独立文件，需要相应导入并共享距离、需求、容量等信息。

# ------ 整理成便于使用的索引 ------
demand_points = df_demands['DemandPoint'].tolist()  # ['D1','D2',...,'D10']
centers = df_centers['Center'].tolist()            # ['C1','C2','C3','C4','C5']

demand_dict = df_demands.set_index('DemandPoint')['demand'].to_dict()  # {D1:80, D2:120, ...}
capacity_dict = df_centers.set_index('Center')['capacity'].to_dict()   # {C1:500, C2:600, ...}
fixed_cost_dict = df_centers.set_index('Center')['fixed_cost'].to_dict() # {C1:800, C2:700, ...}

# =========== 建立 PuLP 模型 ===========
model = pulp.LpProblem("Green_Distribution_Center_Selection", pulp.LpMinimize)

# =========== 定义决策变量 ===========
# y_j: 是否建设中心 j
y = pulp.LpVariable.dicts('build_center',
                          centers,
                          cat=pulp.LpBinary)

# x_{ij}: 需求点 i 是否由中心 j 服务
x = pulp.LpVariable.dicts('assign',
                          [(i, j) for i in demand_points for j in centers],
                          cat=pulp.LpBinary)

# =========== 构建目标函数 ===========

# 运输+碳排放 成本
transport_and_carbon_cost = []
for i in demand_points:
    for j in centers:
        dist_ij = distances[(i, j)]
        # 运输成本 = 0.5 * dist_ij * demand_i
        transport_cost_ij = unit_transport_cost * dist_ij * demand_dict[i]
        # 碳排放成本 = 0.02 * 0.1 * dist_ij * demand_i = 0.002 * dist_ij * demand_i
        carbon_cost_ij = unit_carbon_cost * carbon_emission_factor * dist_ij * demand_dict[i]
        # 合并得到 (运输 + 碳排放) 成本
        total_move_cost_ij = transport_cost_ij + carbon_cost_ij
        
        transport_and_carbon_cost.append(total_move_cost_ij * x[(i,j)])

# 建设成本
facility_fixed_cost = []
for j in centers:
    facility_fixed_cost.append(fixed_cost_dict[j] * y[j])

# 目标函数：Minimize (建设成本 + 运输/碳排放成本)
model += (pulp.lpSum(facility_fixed_cost) + pulp.lpSum(transport_and_carbon_cost)), "Total_Cost"

# =========== 约束 1：每个需求点只能由一个中心满足 ===========
for i in demand_points:
    model += pulp.lpSum([x[(i, j)] for j in centers]) == 1, f"OneCenterPerDemand_{i}"

# =========== 约束 2：设施容量不超过上限 ===========
for j in centers:
    model += pulp.lpSum([demand_dict[i] * x[(i, j)] for i in demand_points]) <= capacity_dict[j] * y[j], f"Capacity_{j}"

# =========== 约束 3：最多建设 Q=2 个中心 ===========
model += pulp.lpSum([y[j] for j in centers]) <= Q, "MaxFacilityConstraint"

# =========== 约束 4：碳排放总量不超过 2000 吨 ===========
model += pulp.lpSum([carbon_emission_factor * distances[(i, j)] * demand_dict[i] * x[(i, j)]
                     for i in demand_points for j in centers]) <= max_carbon, "MaxCarbonConstraint"

# =========== 模型构建完成，接下来可以求解 ===========

print("模型已成功构建，准备求解...")



# ========================================
# 文件名: step3_model_solve.py
# ========================================

# 在同一个脚本中可以直接写在后面，否则需要重新导入 model 对象
solution_status = model.solve(pulp.PULP_CBC_CMD(msg=0))  # msg=0表示不输出详细日志

print(f"求解结束，求解状态: {pulp.LpStatus[solution_status]}")

# 获取最优目标值
optimal_cost = pulp.value(model.objective)
print(f"最优总成本 = {optimal_cost:.2f} (万元+运输碳排放折算成本)")

# 查看哪些中心被选中
selected_centers = [j for j in centers if pulp.value(y[j]) == 1]
print(f"选中的中心: {selected_centers}")

# 查看每个需求点的分配结果
assignments = {}
for i in demand_points:
    for j in centers:
        if pulp.value(x[(i, j)]) == 1:
            assignments[i] = j
            break

print("需求点 -> 分配中心:")
for i, j in assignments.items():
    print(f"  {i} -> {j}")

# 计算实际总碳排放量，检验约束
actual_carbon = 0
for i in demand_points:
    for j in centers:
        if pulp.value(x[(i, j)]) == 1:
            actual_carbon += carbon_emission_factor * distances[(i, j)] * demand_dict[i]

print(f"实际碳排放量 = {actual_carbon:.2f} 吨CO2/月 (上限= {max_carbon})")

