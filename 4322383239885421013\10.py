#10.2
# 读入目标因子个数 k (0 <= k <= 100)
k = int(input().strip())

# 计算 n 的全部因子并返回排序后的列表
def get_divisors(n: int):
    divs = []
    i = 1
    while i * i <= n:           # 只枚举到 √n
        if n % i == 0:
            divs.append(i)
            if i * i != n:      # 避免重复加入平方根
                divs.append(n // i)
        i += 1
    return sorted(divs)         # 从小到大排序

# 枚举 1 ~ 20000
for n in range(1, 20001):
    d = get_divisors(n)
    if len(d) == k:             # 找到第一个满足条件的 n
        print(n)                # 第 1 行：输出 n
        print(*d)               # 第 2 行：输出所有因子（空格分隔）
        break
else:
    # 没找到任何符合要求的 n
    print("NO SOLUTION")
#10.4
def is_prime(n):
    if n < 2:
        return False
    for i in range(2, int(n**0.5)+1):
        if n % i == 0:
            return False
    return True

for i in range(100, 1000):
    if str(i) == str(i)[::-1] and is_prime(i):
        print(i)


#10.5
import math

n = int(input())
k = int(input()) - 1
nums = list(range(1, n + 1))
res = []

for i in range(n - 1, -1, -1):
    f = math.factorial(i)
    idx = k // f
    res.append(str(nums.pop(idx)))
    k %= f

print(''.join(res))
