#11.4
# 读入上底 n 和高 m（保证 3 <= n <= m <= 10）
n, m = map(int, input().split())

for i in range(m):                         # i = 0..m-1
    left = ' ' * (m - i - 1)               # 左侧缩进：每下一行缩进减 1
    width = n + 2 * i                      # 当前行应有的总宽度

    if i == 0:                             # 顶行：连续 n 颗星
        print(left + '*' * n)
    elif i == m - 1:                       # 底行：连续 width 颗星
        print('*' * width)                 # 缩进已为 0，可直接输出
    else:                                  # 中间空心行
        print(left + '*' + ' ' * (width - 2) + '*')



#11.5
# 读入整数 n（3 <= n <= 10）
n = int(input().strip())

width = 4 * n - 3          # 顶端（第 0 行）星号总数，n=3 时为 9

for i in range(n):
    left = ' ' * (2 * i)   # 每往下一行左侧缩进 2 个空格
    if i == 0:             # 顶端——全部是星号
        print('*' * width)
    elif i == n - 1:       # 最底——只剩中间一颗星
        print(left + '*')
    else:                  # 中间行：两端各 1 星 + 中间空隙
        gap = ' ' * (width - 4 * i - 2)
        print(left + '*' + gap + '*')