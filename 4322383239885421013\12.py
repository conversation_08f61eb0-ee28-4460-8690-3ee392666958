#12.1
# 读入整数 n（3 <= n <= 10）
n = int(input().strip())

# 依次打印第 1 ~ n-1 行（空心部分）
for i in range(1, n):
    left_spaces = ' ' * (2 * (n - i))      # 每往下一行缩进减少 2 个空格
    if i == 1:
        print(left_spaces + '*')           # 顶点：只有 1 颗星
    else:
        inner_spaces = ' ' * (2 * i - 3)   # 两颗星之间的空隙
        print(left_spaces + '*' + inner_spaces + '*')

# 打印最后一行（实心底边），恰好 2*n - 1 个星号
print('*' * (2 * n - 1))


#12.2
# 读入奇数 n (3 ≤ n ≤ 20)
n = int(input().strip())
mid = n // 2            # 中心行下标

for i in range(n):      # i = 0 … n-1
    offset = abs(mid - i)          # 距离中轴的“层数”
    indent = ' ' * (2 * offset)    # 左侧缩进：每离中心一层多 2 个空格
    level = mid - offset           # 当前行星号对之间的“层级”

    if level == 0:                 # 顶行或底行——只打印 1 颗星
        print(indent + '*')
    else:                          # 其余行——两端各 1 颗星，中间空心
        inner = ' ' * (4 * level - 1)   # 两星之间空格数：3,7,11,… 递增 4
        print(indent + '*' + inner + '*')


#12.3
# 读入整数 n（3?≤?n?≤?10）
n = int(input().strip())

# 自上而下打印倒等腰三角形
for i in range(n, 0, -1):                   # i 表示当前行应有的星号“层数”
    indent = ' ' * (2 * (n - i))            # 左侧缩进：每往下一行 +2 空格
    stars  = '*' * (2 * i - 1)              # 当前行星号个数：2*i-1
    print(indent + stars)


#12.4
# 读入两个整数 n（上底长）、m（高度），保证 3 <= n <= m <= 10
n, m = map(int, input().split())

# 自上而下打印 m 行
for i in range(m):                       # i = 0 … m-1
    indent = ' ' * (m - i - 1)           # 左侧缩进：最顶行缩进 m-1 个空格，每行递减 1
    stars  = '*' * (n + 2 * i)           # 星号数：上底 n，每往下一行 +2
    print(indent + stars)


#12.5

# 读入行数 n（1 ≤ n ≤ 15）
n = int(input().strip())

for i in range(1, n + 1):
    # 左缩进：每往下一行减少 1 个空格
    indent = ' ' * (n - i)

    # 左半部分：从第 i 个字母递减到 A
    left = [chr(ord('A') + k) for k in range(i - 1, -1, -1)]
    # 右半部分：从 B 再递增回第 i 个字母（跳过重复的 A）
    right = [chr(ord('A') + k) for k in range(1, i)]

    print(indent + ''.join(left + right))