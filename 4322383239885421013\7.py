#7.1

# 计算最大公约数
def gcd(a, b):
        while b:
            a, b = b, a % b
        return a

# 计算最小公倍数
def lcm(a, b):
    # LCM(a, b) = (a * b) / GCD(a, b)
    return (a * b) // gcd(a, b)

valid_lcm_count = 0 # 有效最小公倍数计数器

# 循环直到输出10个有效最小公倍数结果
while valid_lcm_count < 10:
    try:
        line = input()
        n_str, m_str = line.split()
        n = int(n_str)
        m = int(m_str)

        # 根据题目要求判断输出
        if m == 0:
            print("ERR")
        elif n <= 0 or m < 0:
            print("No")
        else: # n > 0 and m > 0
            result = lcm(n, m)
            print(result)
            valid_lcm_count += 1 # 统计有效LCM结果

    except EOFError:
        # 输入结束时退出
        break
    except ValueError:
        # 忽略无效输入行
        continue

#7.2
# 简单的加法计算器

while True:
    try:
        # 读取两个整数
        line = input()
        a, b = map(int, line.split())

        # 判断输入是否为结束标志 0 0
        if a == 0 and b == 0:
            break

        # 计算并输出它们的和
        print(a + b)

    except EOFError:
        # 输入结束时退出循环
        break
    # 省略了对非数字输入的错误处理，假设输入格式正确直到结束。

#7.3
n = int(input())
a, b = 1, 1
res = []
while a <= n:
    res.append(a)
    a, b = b, a + b
print(' '.join(map(str, res)))

#7.4
k = int(input())
s = 0
n = 0
while s <= k:
    n += 1
    s += 1 / n
print(n)



#7.5
m = int(input())
s = 0
n = 0
i = 1
while s < m:
    s += i
    i += 2
    n += 1
print(n)
