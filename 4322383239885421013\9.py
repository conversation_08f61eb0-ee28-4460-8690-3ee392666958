#9.2
def is_leap(y):
    return y % 4 == 0 and (y % 100 != 0 or y % 400 == 0)

# 每月天数
month_days = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31]

y, m, d = map(int, input().split())

# 计算从1980年1月1日到目标日期之间的天数
days = 0
for year in range(1980, y):
    days += 366 if is_leap(year) else 365

for month in range(1, m):
    days += month_days[month - 1]
    if month == 2 and is_leap(y):
        days += 1

days += d - 1

# 1980年1月1日是星期2（周二），所以加上2再模7
weekday = (days + 2) % 7
print(7 if weekday == 0 else weekday)


#9.3
x1, x2, x3, xn = map(int, input().split())
d = x2 - x1
seq = []
x = x1
while True:
    seq.append(x)
    if x == xn:
        break
    x += d

def format_num(n):
    return f"({n})" if n < 0 else str(n)

if len(seq) <= 4:
    expr = '+'.join(format_num(i) for i in seq)
else:
    expr = '+'.join(format_num(seq[i]) for i in range(3)) + '+...+' + format_num(seq[-1])

print(f"{expr}={sum(seq)}")

#9.4
L = int(input())
D = int(input())
X = int(input())

res = []
for i in range(L, D + 1):
    if sum(int(d) for d in str(i)) == X:
        res.append(i)

print(res[0])
print(res[-1])


#9.5
def is_prime(n: int) -> bool:
    """Miller-Rabin 质数判定，n < 2^50 时取下列 5 个基数即可保证确定性"""
    if n < 2:
        return False
    # 先过滤掉小质数及其倍数，可大幅减枝
    small = [2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31, 37]
    for p in small:
        if n == p:
            return True
        if n % p == 0:
            return False

    # 将 n-1 写成 d * 2^s 形式（d 为奇数）
    d, s = n - 1, 0
    while d & 1 == 0:
        d >>=1
        s += 1

        # 下面 5 个基数对 n < 2^64 (更别说 2^50) 足够
    for a in (2, 3, 5, 7, 11, 13, 17):
        x = pow(a, d, n)
        if x in (1, n-1):
                continue
        for _ in range(s - 1):
            x = pow(x, 2, n)
            if x == n - 1:
                break
        else:                        # 没有通过一次“逃逸”，必为合数
            return False
    return True                      # 全部基数测试通过，则为质数

n = int(input().strip())
print('T' if is_prime(n) else 'F')
