# 二维斜碰建模项目

## 项目简介

本项目在原有一维正碰模型的基础上，扩展实现了二维斜碰的物理建模。通过引入角度参数，模拟质点在二维平面内的斜向碰撞过程，包括完全弹性、非弹性和完全非弹性三种碰撞类型。

## 文件结构

```
4367045016112965736/
├── oblique_collision_simulator.py    # 主要的斜碰模拟器代码
├── 斜碰建模演示.ipynb               # Jupyter notebook演示文件
├── 斜碰建模说明.md                  # 详细的建模说明文档
├── 课程作业.docx                    # 原始的正碰作业文档
├── read_doc.py                      # 文档读取工具（已重构为模拟器）
└── README.md                        # 本文件
```

## 核心特性

### 物理建模
- **二维斜碰**：支持任意角度的碰撞模拟
- **三种碰撞类型**：完全弹性、非弹性、完全非弹性
- **速度分解**：将速度分解为法向和切向分量
- **物理准确性**：遵循动量守恒和能量守恒定律

### 可视化
- **实时动画**：matplotlib动画显示碰撞过程
- **轨迹追踪**：显示质点运动轨迹
- **对比展示**：三种碰撞类型并排对比
- **Jupyter支持**：完美适配Jupyter Notebook环境

## 快速开始

### 1. 基本使用

```python
from oblique_collision_simulator import ObliqueCollisionSimulator
from IPython.display import HTML

# 创建模拟器
sim = ObliqueCollisionSimulator(m1=1.0, m2=1.5, dt=0.01, total_time=3.0)

# 设置初始条件（斜碰场景）
sim.set_initial_conditions(
    x1=0.0, y1=0.0, vx1=1.2, vy1=0.8,    # 质点1
    x2=1.8, y2=0.5, vx2=-0.9, vy2=-0.3   # 质点2
)

# 运行模拟
sim.simulate('elastic')  # 完全弹性碰撞

# 创建并显示动画
ani = sim.create_animation("完全弹性斜碰")
HTML(ani.to_jshtml())
```

### 2. 三种碰撞类型对比

```python
from oblique_collision_simulator import create_comparison_animation
from IPython.display import HTML

# 创建对比动画
comparison_ani = create_comparison_animation()
HTML(comparison_ani.to_jshtml())
```

### 3. 在Jupyter Notebook中使用

直接打开 `斜碰建模演示.ipynb` 文件，按顺序运行各个单元格即可看到完整的演示。

## 建模思路

### 核心原理

1. **速度分解**：将二维速度分解为沿连心线的法向分量和垂直连心线的切向分量
2. **碰撞计算**：只对法向分量应用一维碰撞公式，切向分量保持不变
3. **速度合成**：将计算后的法向分量和原切向分量合成新的二维速度

### 数学模型

#### 连心线方向
```
n⃗ = (x₁ - x₂, y₁ - y₂) / |r⃗₁ - r⃗₂|
```

#### 速度分解
```
v_n = v⃗ · n⃗  (法向分量)
v_t = v⃗ · t⃗  (切向分量，t⃗ ⊥ n⃗)
```

#### 碰撞公式
- **完全弹性**：v₁ₙ' = [(m₁-m₂)v₁ₙ + 2m₂v₂ₙ] / (m₁+m₂)
- **非弹性**：在弹性公式基础上乘以恢复系数e
- **完全非弹性**：v₁ₙ' = v₂ₙ' = (m₁v₁ₙ + m₂v₂ₙ) / (m₁+m₂)

## 与原有正碰模型的对比

| 特性 | 正碰模型 | 斜碰模型 |
|------|----------|----------|
| 维度 | 一维 | 二维 |
| 角度 | 固定(0°/180°) | 任意角度 |
| 速度处理 | 直接计算 | 分解+合成 |
| 物理真实性 | 特殊情况 | 更接近现实 |
| 复杂度 | 简单 | 中等 |

## 技术要求

### Python环境
- Python 3.6+
- numpy
- matplotlib
- IPython (用于Jupyter)
- math (标准库)

### 安装依赖
```bash
pip install numpy matplotlib ipython jupyter
```

## 运行方式

### 1. 直接运行Python脚本
```bash
cd 4367045016112965736
python oblique_collision_simulator.py
```

### 2. 在Jupyter Notebook中运行
```bash
cd 4367045016112965736
jupyter notebook 斜碰建模演示.ipynb
```

## 扩展可能性

1. **多质点系统**：扩展到多个质点的同时碰撞
2. **边界约束**：添加墙壁等边界条件
3. **摩擦力**：考虑表面摩擦的影响
4. **三维扩展**：扩展到三维空间的碰撞
5. **实时交互**：添加用户交互界面

## 应用场景

- **物理教学**：碰撞理论的可视化教学
- **游戏开发**：物理引擎的碰撞检测
- **工程分析**：机械碰撞的仿真分析
- **科研模拟**：粒子物理的研究工具

## 注意事项

1. **中文字体**：如果遇到中文显示问题，请安装合适的中文字体
2. **动画性能**：大量质点或长时间模拟可能影响动画流畅度
3. **数值精度**：极小的时间步长可能导致数值误差累积

## 作者信息

本项目基于原有的一维正碰模型扩展而来，实现了更加真实的二维斜碰物理建模。

---

**使用建议**：建议先阅读 `斜碰建模说明.md` 了解详细的物理原理，然后在Jupyter Notebook中运行演示代码体验效果。
