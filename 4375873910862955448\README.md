# Excel表格区域导出图片工具

## 简介
这是一个Windows版本的Excel表格区域导出工具，可以直接从Excel文件中读取指定区域并导出为图片。

## 依赖安装
```bash
pip install pywin32 pillow
```

## 核心功能

### export_sheet_as_image 函数
```python
def export_sheet_as_image(excel_path, sheet_name, area_str, output_image_path):
    """
    从Excel文件中导出指定区域为图片
    :param excel_path: Excel文件路径
    :param sheet_name: Sheet名称
    :param area_str: 所选区域，如 "A2:C8" 或 "(A2,C8)"
    :param output_image_path: 输出图片路径
    :return: 图片保存路径
    """
```

## 使用方法

### 1. 直接调用函数
```python
from excel_screenshot_hotkey import export_sheet_as_image

# 导出Excel指定区域为图片
result_path = export_sheet_as_image(
    excel_path="data.xlsx",      # Excel文件路径
    sheet_name="Sheet1",         # 工作表名称
    area_str="A2:C8",           # 区域范围
    output_image_path="output.png"  # 输出图片路径
)

print(f"图片已保存到: {result_path}")
```

### 2. 运行测试示例
```bash
python test_example.py
```

## 支持的区域格式
- Excel标准格式: `"A1:C5"`
- 括号格式: `"(A1,C5)"`
- 逗号分隔格式: `"A1,C5"`

## 功能特点
- 使用COM接口获取Excel实际显示效果（所见即所得）
- 保持原始格式、颜色、字体、边框等所有样式
- 支持复杂表格格式（合并单元格、条件格式等）
- 自动启动和关闭Excel应用程序
- 支持PNG格式输出

## 文件说明
- `excel_screenshot_hotkey.py` - 主程序文件，包含核心导出函数
- `excel_to_image.py` - GUI界面版本（功能完整）
- `test_example.py` - 使用示例和测试代码
- `README.md` - 本说明文件

## 使用示例
```python
# 示例1: 导出A1到C5区域
export_sheet_as_image("data.xlsx", "Sheet1", "A1:C5", "table1.png")

# 示例2: 使用括号格式
export_sheet_as_image("data.xlsx", "Sheet1", "(A2,E10)", "table2.png")

# 示例3: 指定完整路径
export_sheet_as_image(
    r"C:\Users\<USER>\Documents\数据.xlsx",
    "销售数据",
    "B2:F20",
    r"C:\Users\<USER>\Desktop\销售表格.png"
)
```

---
工具专为Windows系统优化，支持中文Excel文件和中文内容显示。
