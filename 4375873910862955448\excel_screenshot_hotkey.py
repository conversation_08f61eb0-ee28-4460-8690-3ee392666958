#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel表格区域导出为图片工具 - Windows版本
使用COM接口获取Excel的实际显示效果（所见即所得）

依赖安装：
pip install pywin32 pillow

使用方法：
export_sheet_as_image(excel_path, sheet_name, area_str, output_image_path)
"""

import os
import time
from datetime import datetime
from PIL import ImageGrab
import win32com.client


def get_clipboard_image():
    """从剪贴板获取图片"""
    try:
        image = ImageGrab.grabclipboard()
        return image
    except Exception as e:
        print(f"❌ 从剪贴板获取图片失败: {e}")
        return None


def normalize_area_string(area_str):
    """
    标准化区域字符串为Excel格式
    :param area_str: 区域字符串，如 "A1:C5" 或 "(A1,C5)" 或 "A1,C5"
    :return: Excel标准格式字符串，如 "A1:C5"
    """
    # 清理字符串，移除括号和空格
    area_str = area_str.strip().replace('(', '').replace(')', '')

    if ':' in area_str:
        # 已经是Excel标准格式 "A1:C5"
        return area_str
    elif ',' in area_str:
        # 逗号分隔格式 "A1,C5" 转换为 "A1:C5"
        start_cell, end_cell = area_str.split(',')
        return f"{start_cell.strip()}:{end_cell.strip()}"
    else:
        raise ValueError(f"无法解析区域字符串: {area_str}")


def export_sheet_as_image(excel_path, sheet_name, area_str, output_image_path):
    """
    从Excel文件中导出指定区域为图片（所见即所得）
    :param excel_path: Excel文件路径
    :param sheet_name: Sheet名称
    :param area_str: 所选区域，如 "A2:C8" 或 "(A2,C8)"
    :param output_image_path: 输出图片路径
    :return: 图片保存路径
    """
    excel_app = None
    workbook = None

    try:
        print(f"📖 正在打开Excel文件: {excel_path}")
        print(f"📄 工作表: {sheet_name}")
        print(f"� 区域: {area_str}")

        # 检查文件是否存在
        if not os.path.exists(excel_path):
            raise FileNotFoundError(f"Excel文件不存在: {excel_path}")

        # 标准化区域字符串
        excel_range = normalize_area_string(area_str)
        print(f"📊 标准化区域: {excel_range}")

        # 启动Excel应用程序
        excel_app = win32com.client.Dispatch("Excel.Application")
        excel_app.Visible = False  # 不显示Excel窗口
        excel_app.DisplayAlerts = False  # 不显示警告

        # 打开工作簿
        workbook = excel_app.Workbooks.Open(os.path.abspath(excel_path))

        # 选择工作表
        try:
            worksheet = workbook.Worksheets(sheet_name)
        except:
            # 如果按名称找不到，尝试按索引
            try:
                worksheet = workbook.Worksheets(1)
                print(f"⚠️ 未找到工作表'{sheet_name}'，使用第一个工作表: {worksheet.Name}")
            except:
                raise ValueError(f"无法找到工作表: {sheet_name}")

        # 选择指定区域
        range_obj = worksheet.Range(excel_range)

        # 使用CopyPicture方法复制为图片
        # xlScreen = 1, xlBitmap = 2
        range_obj.CopyPicture(1, 2)  # 复制屏幕显示效果为位图

        print("� 正在从剪贴板获取图片...")

        # 等待一下确保复制完成
        time.sleep(0.5)

        # 从剪贴板获取图片
        image = get_clipboard_image()

        if not image:
            raise Exception("无法从剪贴板获取图片，可能是区域选择有误或Excel版本不支持")

        # 确保输出目录存在
        output_dir = os.path.dirname(output_image_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)

        # 保存图片
        image.save(output_image_path, "PNG")

        print(f"✅ 图片已保存: {output_image_path}")
        print(f"📏 图片尺寸: {image.size[0]}x{image.size[1]}")

        return output_image_path

    except Exception as e:
        print(f"❌ 导出失败: {str(e)}")
        raise

    finally:
        # 清理资源
        try:
            if workbook:
                workbook.Close(False)  # 不保存关闭
            if excel_app:
                excel_app.Quit()
        except:
            pass





def main():
    """示例用法"""
    # 示例调用
    excel_path = "bind_card_final_20250606(1).xlsx"  # 替换为实际的Excel文件路径
    sheet_name = "Sheet3"     # 替换为实际的工作表名称
    area_str = "A1:D180"        # 替换为实际的区域
    output_path = f"导出图片_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"

    try:
        result_path = export_sheet_as_image(excel_path, sheet_name, area_str, output_path)
        print(f"\n🎉 导出成功！图片路径: {result_path}")
    except Exception as e:
        print(f"\n❌ 导出失败: {e}")


if __name__ == "__main__":
    main()
