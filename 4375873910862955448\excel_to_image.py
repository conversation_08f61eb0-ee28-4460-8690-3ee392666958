import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import pandas as pd
import numpy as np
from PIL import Image, ImageDraw, ImageFont, ImageGrab
import os
import sys
import time
import threading
from datetime import datetime
import io

# 检测操作系统
import platform
SYSTEM_OS = platform.system()
print(f"检测到操作系统: {SYSTEM_OS}")

# 尝试导入COM相关库（仅Windows）
WIN32_AVAILABLE = False
if SYSTEM_OS == "Windows":
    try:
        import win32com.client
        import win32clipboard
        import win32con
        WIN32_AVAILABLE = True
        print("✓ win32com 库已加载（Windows）")
    except ImportError:
        print("⚠ win32com 库未安装，COM接口功能将不可用")
else:
    print(f"⚠ COM接口不支持 {SYSTEM_OS} 系统")

# 尝试导入热键库
KEYBOARD_AVAILABLE = False
try:
    import keyboard
    KEYBOARD_AVAILABLE = True
    print("✓ keyboard 库已加载")
    if SYSTEM_OS == "Linux":
        print("⚠ Linux系统可能需要root权限使用热键功能")
except ImportError:
    print("⚠ keyboard 库未安装，热键功能将不可用")

# 跨平台剪贴板支持
CLIPBOARD_AVAILABLE = False
try:
    if SYSTEM_OS == "Linux":
        # Linux剪贴板支持
        import subprocess
        CLIPBOARD_AVAILABLE = True
        print("✓ Linux剪贴板支持已启用")
    elif SYSTEM_OS == "Darwin":  # macOS
        import subprocess
        CLIPBOARD_AVAILABLE = True
        print("✓ macOS剪贴板支持已启用")
    else:  # Windows
        CLIPBOARD_AVAILABLE = WIN32_AVAILABLE
        print("✓ Windows剪贴板支持已启用" if CLIPBOARD_AVAILABLE else "⚠ Windows剪贴板支持不可用")
except ImportError:
    print("⚠ 剪贴板功能不可用")

class WPSExcelImageCapture:
    def __init__(self, master):
        self.master = master
        self.master.title("WPS/Excel表格区域截图工具")
        self.master.geometry("600x500")
        
        # 数据存储
        self.clipboard_data = None
        self.current_app = None
        self.hotkey_enabled = False
        
        # 创建界面
        self.create_widgets()
        
        # 设置热键
        if KEYBOARD_AVAILABLE:
            self.setup_hotkeys()
        
    def create_widgets(self):
        """创建主界面"""
        # 主框架
        main_frame = ttk.Frame(self.master)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 标题和说明
        title_frame = ttk.Frame(main_frame)
        title_frame.pack(fill=tk.X, pady=(0, 10))
        
        title_label = ttk.Label(title_frame, text="WPS/Excel表格区域截图工具", 
                               font=("Arial", 14, "bold"))
        title_label.pack()
        
        desc_label = ttk.Label(title_frame,
                              text="在WPS或Excel中选择表格区域，截取屏幕图片（所见即所得）",
                              font=("Arial", 9))
        desc_label.pack(pady=(5, 0))
        
        # 操作方式选择
        method_frame = ttk.LabelFrame(main_frame, text="选择操作方式", padding=10)
        method_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.method_var = tk.StringVar(value="clipboard")
        
        # 剪贴板方式（跨平台）
        ttk.Radiobutton(method_frame, text="剪贴板图片方式 (推荐)",
                       variable=self.method_var, value="clipboard").pack(anchor=tk.W)

        if SYSTEM_OS == "Windows":
            ttk.Label(method_frame, text="  → 在WPS/Excel中右键选择'复制为图片'，保持原始样式",
                     font=("Arial", 8), foreground="gray").pack(anchor=tk.W, padx=(20, 0))
        elif SYSTEM_OS == "Linux":
            ttk.Label(method_frame, text="  → 在LibreOffice中复制为图片（需要xclip）",
                     font=("Arial", 8), foreground="gray").pack(anchor=tk.W, padx=(20, 0))
        elif SYSTEM_OS == "Darwin":
            ttk.Label(method_frame, text="  → 在Numbers/Excel中复制为图片（需要pngpaste）",
                     font=("Arial", 8), foreground="gray").pack(anchor=tk.W, padx=(20, 0))

        # COM接口方式（仅Windows）
        if WIN32_AVAILABLE and SYSTEM_OS == "Windows":
            ttk.Radiobutton(method_frame, text="COM接口截图方式 (仅Windows)",
                           variable=self.method_var, value="com").pack(anchor=tk.W, pady=(5, 0))
            ttk.Label(method_frame, text="  → 自动截取WPS/Excel中选中区域的屏幕图片",
                     font=("Arial", 8), foreground="gray").pack(anchor=tk.W, padx=(20, 0))

        # 手动截图方式（跨平台）
        ttk.Radiobutton(method_frame, text="手动截图方式",
                       variable=self.method_var, value="screenshot").pack(anchor=tk.W, pady=(5, 0))
        ttk.Label(method_frame, text="  → 手动选择屏幕区域进行截图（跨平台）",
                 font=("Arial", 8), foreground="gray").pack(anchor=tk.W, padx=(20, 0))
        
        # 操作按钮
        button_frame = ttk.LabelFrame(main_frame, text="操作", padding=10)
        button_frame.pack(fill=tk.X, pady=(0, 10))
        
        btn_row1 = ttk.Frame(button_frame)
        btn_row1.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Button(btn_row1, text="获取表格数据", command=self.capture_table_data).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(btn_row1, text="预览图片", command=self.preview_image).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(btn_row1, text="保存图片", command=self.save_image).pack(side=tk.LEFT)
        
        # 设置选项
        self.create_settings_panel(main_frame)
        
        # 热键设置
        if KEYBOARD_AVAILABLE:
            self.create_hotkey_panel(main_frame)
        
        # 状态显示
        self.status_var = tk.StringVar(value="就绪 - 请选择操作方式")
        status_label = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_label.pack(fill=tk.X, pady=(10, 0))
        
    def create_settings_panel(self, parent):
        """创建设置面板"""
        settings_frame = ttk.LabelFrame(parent, text="图片设置", padding=10)
        settings_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 第一行：格式和质量
        row1 = ttk.Frame(settings_frame)
        row1.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(row1, text="输出格式:").pack(side=tk.LEFT)
        self.format_var = tk.StringVar(value="PNG")
        format_combo = ttk.Combobox(row1, textvariable=self.format_var, 
                                   values=["PNG", "JPG", "PDF"], width=8, state="readonly")
        format_combo.pack(side=tk.LEFT, padx=(5, 20))
        
        ttk.Label(row1, text="图片质量:").pack(side=tk.LEFT)
        self.quality_var = tk.IntVar(value=300)
        quality_spin = ttk.Spinbox(row1, from_=72, to=600, textvariable=self.quality_var, width=8)
        quality_spin.pack(side=tk.LEFT, padx=(5, 0))
        
        # 第二行：字体和样式
        row2 = ttk.Frame(settings_frame)
        row2.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(row2, text="字体大小:").pack(side=tk.LEFT)
        self.font_size_var = tk.IntVar(value=12)
        ttk.Spinbox(row2, from_=8, to=24, textvariable=self.font_size_var, width=8).pack(side=tk.LEFT, padx=(5, 20))
        
        self.show_grid_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(row2, text="显示网格线", variable=self.show_grid_var).pack(side=tk.LEFT, padx=(0, 10))
        
        self.auto_save_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(row2, text="自动保存", variable=self.auto_save_var).pack(side=tk.LEFT)
        
    def create_hotkey_panel(self, parent):
        """创建热键设置面板"""
        hotkey_frame = ttk.LabelFrame(parent, text="热键设置", padding=10)
        hotkey_frame.pack(fill=tk.X, pady=(0, 10))
        
        row = ttk.Frame(hotkey_frame)
        row.pack(fill=tk.X)
        
        self.hotkey_enabled_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(row, text="启用热键", variable=self.hotkey_enabled_var,
                       command=self.toggle_hotkey).pack(side=tk.LEFT)
        
        ttk.Label(row, text="热键: Ctrl+Shift+S").pack(side=tk.LEFT, padx=(20, 0))
        ttk.Label(row, text="(在WPS/Excel中选中区域后按热键即可保存)", 
                 font=("Arial", 8), foreground="gray").pack(side=tk.LEFT, padx=(10, 0))
        
    def setup_hotkeys(self):
        """设置全局热键"""
        try:
            keyboard.add_hotkey('ctrl+shift+s', self.hotkey_capture)
            print("热键 Ctrl+Shift+S 已设置")
        except Exception as e:
            print(f"设置热键失败: {e}")
            
    def toggle_hotkey(self):
        """切换热键启用状态"""
        self.hotkey_enabled = self.hotkey_enabled_var.get()
        if self.hotkey_enabled:
            self.status_var.set("热键已启用 - 在WPS/Excel中选中区域后按 Ctrl+Shift+S")
        else:
            self.status_var.set("热键已禁用")
            
    def hotkey_capture(self):
        """热键触发的截图功能"""
        if not self.hotkey_enabled:
            return
            
        try:
            # 给用户一点时间切换回WPS/Excel
            time.sleep(0.1)
            
            # 根据当前设置的方法进行截图
            method = self.method_var.get()
            if method == "clipboard":
                self.capture_from_clipboard()
            elif method == "com" and WIN32_AVAILABLE:
                self.capture_from_com()
            else:
                self.capture_screenshot()
                
            # 如果启用了自动保存
            if self.auto_save_var.get() and self.clipboard_data:
                self.auto_save_image()
                
        except Exception as e:
            print(f"热键截图失败: {e}")
            
    def capture_table_data(self):
        """根据选择的方式获取表格数据"""
        method = self.method_var.get()
        
        try:
            if method == "clipboard":
                self.capture_from_clipboard()
            elif method == "com" and WIN32_AVAILABLE:
                self.capture_from_com()
            elif method == "screenshot":
                self.capture_screenshot()
            else:
                messagebox.showwarning("警告", "请选择有效的操作方式")
                
        except Exception as e:
            messagebox.showerror("错误", f"获取数据失败：{str(e)}")
            self.status_var.set("获取数据失败")

    def capture_from_clipboard(self):
        """从剪贴板获取图片数据（所见即所得）"""
        try:
            # 首先尝试从剪贴板获取图片
            clipboard_image = self.get_image_from_clipboard()

            if clipboard_image:
                self.screenshot_image = clipboard_image
                self.clipboard_data = [["剪贴板图片"]]  # 占位数据
                self.status_var.set("已从剪贴板获取图片数据")
                return

            # 如果剪贴板中没有图片，提示用户操作
            result = messagebox.askyesno("提示",
                "剪贴板中没有找到图片数据。\n\n" +
                "请按以下步骤操作：\n" +
                "1. 在WPS/Excel中选择表格区域\n" +
                "2. 右键选择'复制为图片'或使用Ctrl+Alt+C\n" +
                "3. 然后点击'是'重新获取\n\n" +
                "或者点击'否'使用手动截图方式")

            if result:
                # 重新尝试获取剪贴板图片
                clipboard_image = self.get_image_from_clipboard()
                if clipboard_image:
                    self.screenshot_image = clipboard_image
                    self.clipboard_data = [["剪贴板图片"]]
                    self.status_var.set("已从剪贴板获取图片数据")
                else:
                    messagebox.showinfo("提示", "仍然没有找到图片数据，将使用手动截图方式")
                    self.capture_screenshot()
            else:
                # 使用手动截图方式
                self.capture_screenshot()

        except Exception as e:
            messagebox.showerror("错误", f"从剪贴板获取数据失败：{str(e)}")
            # 出错时也使用手动截图方式
            self.capture_screenshot()

    def parse_clipboard_text(self, text_data):
        """解析剪贴板中的文本数据"""
        if not text_data:
            return

        # 按行分割
        lines = text_data.strip().split('\n')

        # 解析表格数据
        table_data = []
        for line in lines:
            # Excel复制的数据通常用制表符分隔
            if '\t' in line:
                row_data = line.split('\t')
            else:
                # 如果没有制表符，尝试其他分隔符
                row_data = [line]
            table_data.append(row_data)

        self.clipboard_data = table_data
        print(f"解析到 {len(table_data)} 行数据")

    def capture_from_com(self):
        """通过COM接口获取当前选中的区域并截图"""
        if not WIN32_AVAILABLE:
            messagebox.showerror("错误", "COM接口不可用，请安装pywin32库")
            return

        try:
            # 尝试连接到Excel
            excel_app = None
            wps_app = None

            try:
                excel_app = win32com.client.GetActiveObject("Excel.Application")
                self.current_app = "Excel"
            except:
                try:
                    wps_app = win32com.client.GetActiveObject("KET.Application")  # WPS的COM接口
                    self.current_app = "WPS"
                except:
                    try:
                        wps_app = win32com.client.GetActiveObject("ET.Application")  # WPS表格的另一个接口
                        self.current_app = "WPS"
                    except:
                        messagebox.showerror("错误", "未找到运行中的Excel或WPS程序")
                        return

            app = excel_app if excel_app else wps_app

            # 获取当前选中的区域
            selection = app.Selection

            if selection is None:
                messagebox.showwarning("警告", "没有选中任何区域")
                return

            # 尝试获取选中区域的屏幕位置并截图
            try:
                # 方法1：尝试使用COM接口复制选中区域为图片
                if hasattr(selection, 'CopyPicture'):
                    # 复制为图片格式
                    selection.CopyPicture(1, 2)  # xlScreen, xlBitmap

                    # 从剪贴板获取图片
                    screenshot_image = self.get_image_from_clipboard()

                    if screenshot_image:
                        self.screenshot_image = screenshot_image
                        self.clipboard_data = [["COM截图"]]  # 占位数据
                        self.status_var.set(f"已从{self.current_app}截取选中区域图片")
                        return

                # 方法2：如果CopyPicture失败，尝试获取选中区域的屏幕坐标
                messagebox.showinfo("提示", "请确保选中区域在屏幕上可见，3秒后将自动截取选中区域")

                # 给用户时间查看提示
                self.master.after(3000, lambda: self.capture_selection_area(app, selection))

            except Exception as e:
                print(f"COM截图失败，尝试备用方法: {e}")
                # 备用方法：提示用户手动截图
                messagebox.showinfo("提示", "将在3秒后截取屏幕，请确保选中区域可见")
                self.master.after(3000, self.capture_visible_selection)

        except Exception as e:
            messagebox.showerror("错误", f"COM接口获取数据失败：{str(e)}")

    def get_image_from_clipboard(self):
        """跨平台从剪贴板获取图片"""
        try:
            if SYSTEM_OS == "Windows":
                # Windows系统使用PIL的ImageGrab
                from PIL import ImageGrab
                image = ImageGrab.grabclipboard()
                return image

            elif SYSTEM_OS == "Linux":
                # Linux系统使用xclip
                try:
                    import subprocess
                    import tempfile

                    # 尝试从剪贴板获取图片
                    with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp_file:
                        result = subprocess.run([
                            'xclip', '-selection', 'clipboard', '-t', 'image/png', '-o'
                        ], stdout=tmp_file, stderr=subprocess.PIPE)

                        if result.returncode == 0:
                            image = Image.open(tmp_file.name)
                            os.unlink(tmp_file.name)  # 删除临时文件
                            return image
                        else:
                            print("Linux剪贴板中没有图片数据")
                            return None

                except FileNotFoundError:
                    print("Linux系统需要安装xclip: sudo apt-get install xclip")
                    return None

            elif SYSTEM_OS == "Darwin":  # macOS
                # macOS系统使用pngpaste
                try:
                    import subprocess
                    import tempfile

                    with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp_file:
                        result = subprocess.run([
                            'pngpaste', tmp_file.name
                        ], stderr=subprocess.PIPE)

                        if result.returncode == 0:
                            image = Image.open(tmp_file.name)
                            os.unlink(tmp_file.name)
                            return image
                        else:
                            print("macOS剪贴板中没有图片数据")
                            return None

                except FileNotFoundError:
                    print("macOS系统需要安装pngpaste: brew install pngpaste")
                    return None

            return None

        except Exception as e:
            print(f"从剪贴板获取图片失败: {e}")
            return None

    def capture_selection_area(self, app, selection):
        """捕获选中区域的屏幕截图"""
        try:
            # 尝试获取选中区域的屏幕坐标
            # 这个方法可能因不同版本的Office/WPS而有所不同

            # 先截取整个屏幕
            full_screenshot = ImageGrab.grab()

            # 由于无法直接获取精确坐标，提示用户手动选择
            messagebox.showinfo("提示", "请在接下来的截图界面中，用鼠标框选刚才在WPS/Excel中选中的区域")

            # 启动手动截图选择
            self.manual_screenshot_with_reference(full_screenshot)

        except Exception as e:
            print(f"获取选中区域坐标失败: {e}")
            self.capture_visible_selection()

    def capture_visible_selection(self):
        """截取当前可见的选中区域"""
        try:
            # 隐藏主窗口
            self.master.withdraw()
            time.sleep(0.5)

            # 截取整个屏幕
            screenshot = ImageGrab.grab()

            # 创建截图选择界面
            self.create_screenshot_selector_with_preview(screenshot)

        except Exception as e:
            self.master.deiconify()
            messagebox.showerror("错误", f"截图失败：{str(e)}")

    def manual_screenshot_with_reference(self, reference_image):
        """带参考图像的手动截图"""
        self.master.withdraw()
        time.sleep(0.5)

        # 创建截图选择界面
        self.create_screenshot_selector_with_preview(reference_image)

    def create_screenshot_selector_with_preview(self, screenshot):
        """创建带预览的截图选择器"""
        # 创建全屏窗口用于选择区域
        selector = tk.Toplevel()
        selector.attributes('-fullscreen', True)
        selector.attributes('-alpha', 0.3)
        selector.configure(bg='black')

        # 添加说明文字
        info_label = tk.Label(selector,
                             text="请用鼠标拖拽选择要截图的区域（选择WPS/Excel中刚才选中的表格区域）\n按ESC取消",
                             fg='white', bg='black', font=('Arial', 16))
        info_label.pack(pady=50)

        # 绑定鼠标事件
        self.selection_start = None
        self.selection_end = None

        def on_mouse_down(event):
            self.selection_start = (event.x_root, event.y_root)

        def on_mouse_drag(event):
            if self.selection_start:
                self.selection_end = (event.x_root, event.y_root)

        def on_mouse_up(event):
            if self.selection_start and self.selection_end:
                # 计算选择区域
                x1, y1 = self.selection_start
                x2, y2 = self.selection_end

                # 确保坐标正确
                left = min(x1, x2)
                top = min(y1, y2)
                right = max(x1, x2)
                bottom = max(y1, y2)

                # 检查选择区域大小
                if (right - left) < 10 or (bottom - top) < 10:
                    messagebox.showwarning("警告", "选择区域太小，请重新选择")
                    return

                # 截取选中区域（保持原始样式）
                region_screenshot = screenshot.crop((left, top, right, bottom))

                # 保存截图
                self.screenshot_image = region_screenshot
                self.clipboard_data = [["屏幕截图"]]  # 占位数据

                selector.destroy()
                self.master.deiconify()  # 恢复主窗口
                self.status_var.set(f"已截取屏幕区域 ({right-left}x{bottom-top}) - 保持原始样式")

        def on_escape(event):
            selector.destroy()
            self.master.deiconify()  # 恢复主窗口

        selector.bind('<Button-1>', on_mouse_down)
        selector.bind('<B1-Motion>', on_mouse_drag)
        selector.bind('<ButtonRelease-1>', on_mouse_up)
        selector.bind('<Escape>', on_escape)
        selector.focus_set()

    def safe_unicode_convert(self, value):
        """安全地转换值为Unicode字符串，处理中文编码问题"""
        if value is None:
            return ""

        try:
            # 如果已经是字符串，直接返回
            if isinstance(value, str):
                return value

            # 如果是数字，转换为字符串
            if isinstance(value, (int, float)):
                return str(value)

            # 尝试不同的编码方式
            if isinstance(value, bytes):
                # 尝试UTF-8编码
                try:
                    return value.decode('utf-8')
                except UnicodeDecodeError:
                    # 尝试GBK编码（中文Windows常用）
                    try:
                        return value.decode('gbk')
                    except UnicodeDecodeError:
                        # 尝试latin-1编码
                        try:
                            return value.decode('latin-1')
                        except UnicodeDecodeError:
                            # 最后使用错误处理
                            return value.decode('utf-8', errors='replace')

            # 其他类型，强制转换为字符串
            return str(value)

        except Exception as e:
            print(f"字符编码转换失败: {e}, 原始值: {repr(value)}")
            # 如果所有方法都失败，返回安全的字符串表示
            return str(value) if value is not None else ""

    def get_chinese_font(self, font_size):
        """获取支持中文的字体"""
        # 常见的中文字体路径（Windows系统）
        chinese_fonts = [
            "C:/Windows/Fonts/simhei.ttf",      # 黑体
            "C:/Windows/Fonts/simsun.ttc",      # 宋体
            "C:/Windows/Fonts/simkai.ttf",      # 楷体
            "C:/Windows/Fonts/simfang.ttf",     # 仿宋
            "C:/Windows/Fonts/msyh.ttc",        # 微软雅黑
            "C:/Windows/Fonts/msyhbd.ttc",      # 微软雅黑粗体
            "simhei.ttf",                       # 系统路径中的黑体
            "simsun.ttf",                       # 系统路径中的宋体
            "msyh.ttf",                         # 系统路径中的微软雅黑
            "arial.ttf"                         # 备用英文字体
        ]

        for font_path in chinese_fonts:
            try:
                font = ImageFont.truetype(font_path, font_size)
                print(f"✓ 成功加载字体: {font_path}")
                return font
            except (OSError, IOError):
                continue

        # 如果所有字体都加载失败，使用默认字体
        print("⚠ 无法加载中文字体，使用默认字体")
        try:
            return ImageFont.load_default()
        except:
            # 创建一个基本的字体对象
            return None

    def ensure_unicode_text(self, text):
        """确保文本是正确的Unicode字符串"""
        if not text:
            return ""

        try:
            # 如果已经是字符串，检查是否包含正确的中文字符
            if isinstance(text, str):
                # 检查是否有乱码字符（通常是问号或方块）
                if '?' in text or '□' in text or '\ufffd' in text:
                    print(f"检测到可能的乱码文本: {repr(text)}")
                return text

            # 如果是字节串，尝试解码
            if isinstance(text, bytes):
                return self.safe_unicode_convert(text)

            # 其他类型转换为字符串
            return str(text)

        except Exception as e:
            print(f"文本Unicode处理失败: {e}, 原始文本: {repr(text)}")
            return str(text) if text is not None else ""

    def capture_screenshot(self):
        """屏幕截图方式"""
        try:
            # 隐藏主窗口
            self.master.withdraw()

            # 等待一下让窗口完全隐藏
            time.sleep(0.5)

            # 创建截图选择窗口
            self.create_screenshot_selector()

        except Exception as e:
            self.master.deiconify()  # 恢复主窗口
            messagebox.showerror("错误", f"屏幕截图失败：{str(e)}")

    def create_screenshot_selector(self):
        """创建截图选择器"""
        # 获取屏幕截图
        screenshot = ImageGrab.grab()

        # 创建全屏窗口用于选择区域
        selector = tk.Toplevel()
        selector.attributes('-fullscreen', True)
        selector.attributes('-alpha', 0.3)
        selector.configure(bg='black')

        # 添加说明文字
        info_label = tk.Label(selector, text="请拖拽鼠标选择要截图的区域，按ESC取消",
                             fg='white', bg='black', font=('Arial', 16))
        info_label.pack(pady=50)

        # 绑定鼠标事件
        self.selection_start = None
        self.selection_end = None
        self.selection_rect = None

        def on_mouse_down(event):
            self.selection_start = (event.x_root, event.y_root)

        def on_mouse_drag(event):
            if self.selection_start:
                self.selection_end = (event.x_root, event.y_root)
                # 这里可以添加实时显示选择框的代码

        def on_mouse_up(event):
            if self.selection_start and self.selection_end:
                # 计算选择区域
                x1, y1 = self.selection_start
                x2, y2 = self.selection_end

                # 确保坐标正确
                left = min(x1, x2)
                top = min(y1, y2)
                right = max(x1, x2)
                bottom = max(y1, y2)

                # 截取选中区域
                region_screenshot = screenshot.crop((left, top, right, bottom))

                # 将截图转换为表格数据格式（这里简化处理）
                self.clipboard_data = [["截图区域"]]  # 占位数据
                self.screenshot_image = region_screenshot

                selector.destroy()
                self.master.deiconify()  # 恢复主窗口
                self.status_var.set(f"已截取屏幕区域 ({right-left}x{bottom-top})")

        def on_escape(event):
            selector.destroy()
            self.master.deiconify()  # 恢复主窗口

        selector.bind('<Button-1>', on_mouse_down)
        selector.bind('<B1-Motion>', on_mouse_drag)
        selector.bind('<ButtonRelease-1>', on_mouse_up)
        selector.bind('<Escape>', on_escape)
        selector.focus_set()

    def preview_image(self):
        """预览生成的图片"""
        if not hasattr(self, 'screenshot_image'):
            messagebox.showwarning("警告", "请先获取表格数据")
            return

        try:
            # 直接显示截图（保持原始样式）
            self.show_image_preview(self.screenshot_image)

        except Exception as e:
            messagebox.showerror("错误", f"预览失败：{str(e)}")

    def show_image_preview(self, image):
        """显示图片预览窗口"""
        preview_window = tk.Toplevel(self.master)
        preview_window.title("图片预览")
        preview_window.geometry("800x600")

        # 调整图片大小以适应预览窗口
        display_image = image.copy()
        window_width, window_height = 750, 550
        img_width, img_height = display_image.size

        # 计算缩放比例
        scale = min(window_width/img_width, window_height/img_height)
        if scale < 1:
            new_width = int(img_width * scale)
            new_height = int(img_height * scale)
            display_image = display_image.resize((new_width, new_height), Image.Resampling.LANCZOS)

        # 转换为tkinter可显示的格式
        from tkinter import PhotoImage
        import io

        # 将PIL图像转换为PhotoImage
        bio = io.BytesIO()
        display_image.save(bio, format='PNG')
        bio.seek(0)

        # 创建PhotoImage
        photo = PhotoImage(data=bio.getvalue())

        # 显示图片
        label = tk.Label(preview_window, image=photo)
        label.image = photo  # 保持引用
        label.pack(expand=True)

    def generate_table_image(self):
        """根据表格数据生成图片"""
        if not self.clipboard_data:
            return None

        try:
            rows = len(self.clipboard_data)
            cols = max(len(row) for row in self.clipboard_data) if self.clipboard_data else 0

            if rows == 0 or cols == 0:
                return None

            # 计算图片尺寸
            font_size = self.font_size_var.get()
            cell_width = max(120, font_size * 10)
            cell_height = max(40, font_size * 3)

            img_width = cols * cell_width
            img_height = rows * cell_height

            # 创建图片
            image = Image.new('RGB', (img_width, img_height), 'white')
            draw = ImageDraw.Draw(image)

            # 尝试加载字体（优先使用中文字体）
            font = self.get_chinese_font(font_size)

            # 绘制表格
            for i, row_data in enumerate(self.clipboard_data):
                for j, cell_value in enumerate(row_data):
                    if j >= cols:  # 防止超出列数
                        break

                    x1 = j * cell_width
                    y1 = i * cell_height
                    x2 = x1 + cell_width
                    y2 = y1 + cell_height

                    # 绘制边框
                    if self.show_grid_var.get():
                        draw.rectangle([x1, y1, x2, y2], outline='black', width=1)

                    # 绘制文本
                    if cell_value and str(cell_value).strip():
                        text = str(cell_value).strip()

                        # 确保文本是正确的Unicode字符串
                        text = self.ensure_unicode_text(text)

                        # 计算文本位置（居中）
                        if font:
                            try:
                                bbox = draw.textbbox((0, 0), text, font=font)
                                text_width = bbox[2] - bbox[0]
                                text_height = bbox[3] - bbox[1]
                            except:
                                # 如果textbbox不可用，使用textsize
                                try:
                                    text_width, text_height = draw.textsize(text, font=font)
                                except:
                                    # 估算文本尺寸
                                    text_width = len(text) * font_size // 2
                                    text_height = font_size
                        else:
                            # 没有字体时的估算
                            text_width = len(text) * 8
                            text_height = 12

                        text_x = x1 + (cell_width - text_width) // 2
                        text_y = y1 + (cell_height - text_height) // 2

                        # 确保文本不超出单元格边界
                        text_x = max(x1 + 2, text_x)
                        text_y = max(y1 + 2, text_y)

                        # 绘制文本
                        try:
                            draw.text((text_x, text_y), text, fill='black', font=font)
                        except Exception as e:
                            print(f"绘制文本失败: {e}, 文本: {repr(text)}")
                            # 尝试不使用字体绘制
                            try:
                                draw.text((text_x, text_y), text, fill='black')
                            except:
                                # 如果还是失败，跳过这个文本
                                pass

            return image

        except Exception as e:
            print(f"生成表格图片失败: {e}")
            return None

    def save_image(self):
        """保存图片"""
        if not hasattr(self, 'screenshot_image'):
            messagebox.showwarning("警告", "请先获取表格数据")
            return

        try:
            # 直接使用截图（保持原始样式）
            image = self.screenshot_image

            if not image:
                messagebox.showerror("错误", "没有可保存的图片")
                return

            # 选择保存路径
            file_types = [("PNG图片", "*.png"), ("JPG图片", "*.jpg"), ("PDF文件", "*.pdf")]
            default_ext = self.format_var.get().lower()

            # 生成默认文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            default_filename = f"表格截图_{timestamp}.{default_ext}"

            save_path = filedialog.asksaveasfilename(
                title="保存图片",
                initialfile=default_filename,
                defaultextension=f".{default_ext}",
                filetypes=file_types
            )

            if not save_path:
                return

            # 保存图片
            if self.format_var.get() == "JPG":
                # JPG不支持透明度，需要转换
                if image.mode in ('RGBA', 'LA'):
                    background = Image.new('RGB', image.size, 'white')
                    background.paste(image, mask=image.split()[-1] if image.mode == 'RGBA' else None)
                    image = background
                image.save(save_path, "JPEG", quality=95)
            elif self.format_var.get() == "PDF":
                image.save(save_path, "PDF", quality=95)
            else:
                image.save(save_path, "PNG")

            self.status_var.set(f"图片已保存: {os.path.basename(save_path)}")
            messagebox.showinfo("成功", f"图片已保存到：\n{save_path}")

        except Exception as e:
            messagebox.showerror("错误", f"保存图片失败：{str(e)}")

    def auto_save_image(self):
        """自动保存图片"""
        try:
            if not hasattr(self, 'screenshot_image'):
                return

            image = self.screenshot_image

            if not image:
                return

            # 生成自动保存的文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"auto_capture_{timestamp}.{self.format_var.get().lower()}"

            # 保存到当前目录
            save_path = os.path.join(os.getcwd(), filename)

            if self.format_var.get() == "JPG":
                if image.mode in ('RGBA', 'LA'):
                    background = Image.new('RGB', image.size, 'white')
                    background.paste(image, mask=image.split()[-1] if image.mode == 'RGBA' else None)
                    image = background
                image.save(save_path, "JPEG", quality=95)
            else:
                image.save(save_path, self.format_var.get())

            self.status_var.set(f"已自动保存: {filename}")

        except Exception as e:
            print(f"自动保存失败: {e}")

if __name__ == "__main__":
    root = tk.Tk()
    app = WPSExcelImageCapture(root)
    root.mainloop()
