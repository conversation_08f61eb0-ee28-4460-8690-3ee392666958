{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["机器学习实验1 线性回归"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 单变量线性回归"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "plt.rcParams['font.sans-serif']=['SimHei'] #用来正常显示中文标签\n", "plt.rcParams['axes.unicode_minus']=False #用来正常显示负号"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>人口</th>\n", "      <th>收益</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>6.1101</td>\n", "      <td>17.5920</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>5.5277</td>\n", "      <td>9.1302</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>8.5186</td>\n", "      <td>13.6620</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>7.0032</td>\n", "      <td>11.8540</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5.8598</td>\n", "      <td>6.8233</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       人口       收益\n", "0  6.1101  17.5920\n", "1  5.5277   9.1302\n", "2  8.5186  13.6620\n", "3  7.0032  11.8540\n", "4  5.8598   6.8233"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["path = 'data/regress_data1.csv'\n", "data = pd.read_csv(path)\n", "data.head()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>人口</th>\n", "      <th>收益</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>97.000000</td>\n", "      <td>97.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>8.159800</td>\n", "      <td>5.839135</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>3.869884</td>\n", "      <td>5.510262</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>5.026900</td>\n", "      <td>-2.680700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>5.707700</td>\n", "      <td>1.986900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>6.589400</td>\n", "      <td>4.562300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>8.578100</td>\n", "      <td>7.046700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>22.203000</td>\n", "      <td>24.147000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["              人口         收益\n", "count  97.000000  97.000000\n", "mean    8.159800   5.839135\n", "std     3.869884   5.510262\n", "min     5.026900  -2.680700\n", "25%     5.707700   1.986900\n", "50%     6.589400   4.562300\n", "75%     8.578100   7.046700\n", "max    22.203000  24.147000"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["data.describe()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["看下数据长什么样子"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 864x576 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["data.plot(kind='scatter', x='人口', y='收益', figsize=(12,8))\n", "plt.xlabel('人口', fontsize=18)\n", "plt.ylabel('收益', rotation=0, fontsize=18)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["现在让我们使用梯度下降来实现线性回归，以最小化代价函数。 "]}, {"cell_type": "markdown", "metadata": {}, "source": ["首先，我们将创建一个以参数$w$为特征函数的代价函数\n", "$$J\\left( w  \\right)=\\frac{1}{2m}\\sum\\limits_{i=1}^{m}{{{\\left( {{h}}\\left( {{x}^{(i)}} \\right)-{{y}^{(i)}} \\right)}^{2}}}$$\n", "其中：\\\\[{{h}}\\left( x \\right)={{w}^{T}}X={{w }_{0}}{{x}_{0}}+{{w }_{1}}{{x}_{1}}+{{w }_{2}}{{x}_{2}}+...+{{w }_{n}}{{x}_{n}}\\\\] "]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["def computeCost(X, y, w):\n", "    inner = np.power(((X * w.T) - y), 2)# (m,n) @ (n, 1) -> (n, 1)\n", "#     return np.sum(inner) / (2 * len(X))\n", "    return np.sum(inner) / (2 * X.shape[0])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["让我们在训练集中添加一列，以便我们可以使用向量化的解决方案来计算代价和梯度。"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Ones</th>\n", "      <th>人口</th>\n", "      <th>收益</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>6.1101</td>\n", "      <td>17.59200</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>5.5277</td>\n", "      <td>9.13020</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>8.5186</td>\n", "      <td>13.66200</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1</td>\n", "      <td>7.0032</td>\n", "      <td>11.85400</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1</td>\n", "      <td>5.8598</td>\n", "      <td>6.82330</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>92</th>\n", "      <td>1</td>\n", "      <td>5.8707</td>\n", "      <td>7.20290</td>\n", "    </tr>\n", "    <tr>\n", "      <th>93</th>\n", "      <td>1</td>\n", "      <td>5.3054</td>\n", "      <td>1.98690</td>\n", "    </tr>\n", "    <tr>\n", "      <th>94</th>\n", "      <td>1</td>\n", "      <td>8.2934</td>\n", "      <td>0.14454</td>\n", "    </tr>\n", "    <tr>\n", "      <th>95</th>\n", "      <td>1</td>\n", "      <td>13.3940</td>\n", "      <td>9.05510</td>\n", "    </tr>\n", "    <tr>\n", "      <th>96</th>\n", "      <td>1</td>\n", "      <td>5.4369</td>\n", "      <td>0.61705</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>97 rows × 3 columns</p>\n", "</div>"], "text/plain": ["    Ones       人口        收益\n", "0      1   6.1101  17.59200\n", "1      1   5.5277   9.13020\n", "2      1   8.5186  13.66200\n", "3      1   7.0032  11.85400\n", "4      1   5.8598   6.82330\n", "..   ...      ...       ...\n", "92     1   5.8707   7.20290\n", "93     1   5.3054   1.98690\n", "94     1   8.2934   0.14454\n", "95     1  13.3940   9.05510\n", "96     1   5.4369   0.61705\n", "\n", "[97 rows x 3 columns]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["data.insert(0, 'Ones', 1)\n", "data"]}, {"cell_type": "markdown", "metadata": {}, "source": ["现在我们来做一些变量初始化。"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["# set X (training data) and y (target variable)\n", "cols = data.shape[1]\n", "X = data.iloc[:,:cols-1]#X是所有行，去掉最后一列\n", "y = data.iloc[:,cols-1:]#X是所有行，最后一列"]}, {"cell_type": "markdown", "metadata": {}, "source": ["观察下 X (训练集) and y (目标变量)是否正确."]}, {"cell_type": "code", "execution_count": 10, "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Ones</th>\n", "      <th>人口</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>6.1101</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>5.5277</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>8.5186</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1</td>\n", "      <td>7.0032</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1</td>\n", "      <td>5.8598</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Ones      人口\n", "0     1  6.1101\n", "1     1  5.5277\n", "2     1  8.5186\n", "3     1  7.0032\n", "4     1  5.8598"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["X.head()#head()是观察前5行"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>收益</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>17.5920</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>9.1302</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>13.6620</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>11.8540</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>6.8233</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        收益\n", "0  17.5920\n", "1   9.1302\n", "2  13.6620\n", "3  11.8540\n", "4   6.8233"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["y.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["代价函数是应该是numpy矩阵，所以我们需要转换X和Y，然后才能使用它们。 我们还需要初始化w。"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["X = np.matrix(X.values)\n", "y = np.matrix(y.values)\n", "w = np.matrix(np.array([0,0]))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["w 是一个(1,2)矩阵"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["matrix([[0, 0]])"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["w"]}, {"cell_type": "markdown", "metadata": {}, "source": ["看下维度"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["((97, 2), (1, 2), (97, 1))"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["X.shape, w.shape, y.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["计算代价函数 (theta初始值为0)."]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["32.072733877455676"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["computeCost(X, y, w)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Batch Gradient Decent（批量梯度下降）\n", "\n", "$${{w }_{j}}:={{w }_{j}}- \\alpha \\frac{1}{m}\\sum\\limits_{i=1}^m \\frac{\\partial }{\\partial {{w}_{j}}}J\\left( w \\right)$$"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["def batch_gradientDescent(X, y, w, alpha, iters):\n", "    temp = np.matrix(np.zeros(w.shape))\n", "    parameters = int(w.ravel().shape[1])\n", "    cost = np.zeros(iters)\n", "\n", "    for i in range(iters):\n", "        error = (X * w.T) - y\n", "\n", "        for j in range(parameters):\n", "            term = np.multiply(error, X[:, j])\n", "            temp[0, j] = w[0, j] - ((alpha / len(X)) * np.sum(term))\n", "\n", "        w = temp\n", "        cost[i] = computeCost(X, y, w)\n", "\n", "    return w, cost"]}, {"cell_type": "markdown", "metadata": {}, "source": ["初始化一些附加变量 - 学习速率α和要执行的迭代次数。"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["alpha = 0.01\n", "iters = 1000"]}, {"cell_type": "markdown", "metadata": {}, "source": ["现在让我们运行梯度下降算法来将我们的参数θ适合于训练集。"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["matrix([[-3.24140214,  1.1272942 ]])"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["g, cost = batch_gradientDescent(X, y, w, alpha, iters)\n", "g"]}, {"cell_type": "markdown", "metadata": {}, "source": ["最后，我们可以使用我们拟合的参数计算训练模型的代价函数（误差）。"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/plain": ["4.515955503078912"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["computeCost(X, y, g)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["现在我们来绘制线性模型以及数据，直观地看出它的拟合。"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 864x576 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["x = np.linspace(data['人口'].min(), data['人口'].max(), 100)\n", "f = g[0, 0] + (g[0, 1] * x)\n", "\n", "fig, ax = plt.subplots(figsize=(12, 8))\n", "ax.plot(x, f, 'r', label='预测值')\n", "ax.scatter(data['人口'], data['收益'], label='训练数据')\n", "ax.legend(loc=2)\n", "ax.set_xlabel('人口', fontsize=18)\n", "ax.set_ylabel('收益', rotation=0, fontsize=18)\n", "ax.set_title('预测收益和人口规模', fontsize=18)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["由于梯度方程式函数也在每个训练迭代中输出一个代价的向量，所以我们也可以绘制。 请注意，代价总是降低 - 这是凸优化问题的一个例子。"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAtgAAAH6CAYAAAA9XzulAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAALEgAACxIB0t1+/AAAADh0RVh0U29mdHdhcmUAbWF0cGxvdGxpYiB2ZXJzaW9uMy4xLjMsIGh0dHA6Ly9tYXRwbG90bGliLm9yZy+AADFEAAAgAElEQVR4nOzdd5hdVb3/8fc3k5AOBAgllBCQ3n+E0DEUEZDeFKSoCCpS75UqXJWqgIUiVxH00oUAIiBVqkhN6AJKCz2Q0BJCAiFZvz/WPsxkMpNkkp3ZM2fer+fZzzmzzzrnfA+H6/2cxXevFSklJEmSJJWjW9UFSJIkSfXEgC1JkiSVyIAtSZIklciALUmSJJXIgC1JkiSVyIAtSZIklciALamuRcS+ETGoxNdbISL2auH8jhHx9YhoaOPrRUQs0cbnrBYRC7TlOTN5rV4zeWzBiDg0IvqW8V4dSUQMLG77RMR5EbFm8feyTcYcFBErVFOhpM7MgC2pbkVED+A44L6IWKo4t3hEpFkc683kZfcDLo6IdZqd/y/gdKB/K7V0j4hlImK9iNg9Io6LiKuAt4Db2/jRzgf+0fyHQ0Qs3NoTImLViBjcwkPHRcR9rYTo4cA5wGptrK9Di4hFgX9HxK5AD+CHwJIRsSrwYkQsFxE9yf+cZ/gxJUmzYsCWVLdSSlOArwDdgbuKWctJxcNbAUOaHSsXj31Se42IaChmOWsz0ycDjwODmoxZD/gy8N2U0ofFrHTPiOjepJz+wIvAI8CFwCnAQuQAe3pE9GnDR9sNmEoO2Us2OX9LRJxX1LRoRGzU5LFfAt9v+iIREcA3gVdSShOLz9o7Imr/v2EX4K6U0iPF+G4R0avprPdMfqTc04bPU5rivb81szEppXfJP4j+F+hZnP4UOBa4LqX0MvlHRQMwYt5VK6ledZ/1EEnqvFJKbxYzlb8CPgLmKx56M6U0uunYJsFxapPT6wEPFo83HX5Ts78B7mx2bi/gz0UdH0TEKsC4lNJHETEOuCil9Ocm7382cFgLH+PUlNIJTT7TuIj4CnA/cBaNs6yTgInF/S8DlwK1z/RZcTS1DbAocEwRqjcF7m7+WSOi+Za/v2f6sH4zcGWzMe+08Dk6hIjYtLh7JvkHGOTZ+teAzyNiOPD/gNEppeeaPK8b0COl9Gn7VSupMzJgS6p7KaXHImLLlNLUJiG6T0T0aza0NpvZtI/6CWB5YDLTB9QtgKvIQez1Zq/TQA7yHzar46VZlPoZ8Ci5DaXmTmYMxrWQvTX5R0PNpzT+OJjc/CnF0dSRwBkppTFFX/lhwDrA28B2wHnAqjSG9iC3VExp9jrPpZQum8Vn60i+DOxK/hy1dqBdgAnkz9cdWBtYtoUfF1cB32inOiV1UgZsSXWpaH+YrzbbmFKqBc9aiB41k6f3rt1JKU2OiHeAFcgBtha41gM+Js8aL9L0rckh7f2U0oQm9fQBPkspfd5Cnb2K530GfJJSer7J41OK8xQ/CLYlt7BMBd5KKb3W5OU+L14HZgzT0ylmaVcCdi5OHV58lpeK198euDKl9OrMXqczSimdApwSEScAy5G/v9OAbimly4sLSN8BvkMxow/8Hbia3FYiSTNlD7akerU4uWXj3qIv92vF+UlAX6AhpRRND/L/JvYAni8ukKxZHXgYeIDclnE/efa3e5O/a8cD5JaSg5vVcx0wpdajDCxMbquYRg60FzCLUExu5/gj8CfgBuCoZo/3YDb+d724yO9PwD+BvSPiDGAo+SLN8eSgvitwQJOe6pYukJyliFi2eP73I+KhiPg0Ip6PiF1aGPvt4rHJEfF4RGzXwpiDIuI/EfFJRDwZEXu28r77RsRLEfFxRPyt6L+vPRYR8VPgBGDf4vRqwB+Lf092If8QezOlNLpoJVoIeCGl1Py/VkjSDAzYkupSSuntlNIm5AsCofHixuvJLQ9Tm1+YRw67U8itAls2ea2HU0o9U0oDUkqLAMsW436UUlqk2TEgpdQ3pXR8s5K+DSwFLEFj+P4++WLJIcCPZuMzvZxS6p9SWpQc5Ju3avRi+v7x1kwGBgIbA3sX730BuR1lCXKryKvA/EDtQslJM77MF3pHxCLNjvmajfkNMBr4b+AD4JqI2KL2YEQcTv7x8HwxZgJwY0Ts2GTM0eT+76fIP3BeAa5qOqawG/mizt8BvyX3mp9SvEYv4Fbyj5NdyD+KAB4CTgWOAH5QnFuteE5PYAAztgJJUotsEZHUVUwrbr9Bnin+nBxG7wZeAA4iTzp0I7eIfAB5zWlyIG0aXLcmh9l/RZN1k5vpDvRMKf0LcuCvPRARtR7r+YATgWNTSuNbuGhyVpoH7L7MPAhT1DI+IpYuLrzch9xv/JOijWZMROwP/DGlNCEiaq83rdUXzD8Yms/Y7wFc0+Tvu1JK3wCIiIvIFxQeTV7dpT85AF+fUtqlGPM7YCTwm4i4EVgA+AkwIqW0ZzHmD8DLwHfJM/o12wLrppSeLMYtT/4xUWv5+QVwVErpqYg4kbySy8PkizV3IP8I+wewfvF6tRVjZtVDL0mAAVtSF1LMqn4KTC6W8CMiFgTeSSl9XPzdjRyoe0bEZ8AzM3nJu2fyGMCb5FnrpjXsQ+7fnkIO6bsBy7XUDjEbmreUfAl4d7aemMP1/MDPgaNTSmOL+n5AvnDzqIhYmTxbD/CliFiM/KNgdErpgyYvN4I8A97UU83+/mKVkZTSpIi4nTyzDHmWvB95Brs2ZmpE/Ak4u/hcywN9gIubjJnWpL6mRtTCdeFZcgtMzTtAj4hYnRzyLya37CxM7mE/HfgXcGbRI78W+QfX6BbeS5JmYMCW1JXsSLGucbPZ4iMi4ogWxg8hz5x+AkxNKaVi3JnA2rXZ6ZZEXgO7V7NzS5ED47nk3t/XyTPqd5JbHuZYRCxNXmv72TY87Wzg38BFxWs0kFcNmQZcW9zWVlT5W3G/N7An8Ncmr/NaSunvs3iv5sH/PXILCjReJPp2szFvFbeLksMvwJhZvA/klViaaj77fjGwIvn/B85H/g6+Qf4vAB+RZ6wXKsZtQJ79fiilNKseeUkC7MGW1HV0I4e8ZYElyb3GJ5JbRRK5NWCJ4rEh5AD2ZkppfErp8yJcr0VebeJ6oHtErBIRKzc7Vo+IdYFptVlx+KKP9wpysPxx7XxK6W7g18xh+0FEbFisUDKMPDs/s9VRmj7vp8C3yIH1hoh4kbxKxgnkHxULF/3mmxdPWQkYUPSi/3XGV5yl5tvBL0rjMobjWhlTa80YC7xf3F+82ef4djHT3dT7zERKaWhKaX7yhkG/rvXPk2etr0opTUspjQNuIfej70Hbd9uU1IU5gy2pqziVPDs7JKX0fjGbfAR5Fnd5cg/whimlFnuYI2IF4DbyRXHDyCuFTCav3NGPHJwhz4j2Jl8UV2s76U7ecGZY8R6fNJ1BTyn9dzFuKPDlFtZebs0i5NVJTiGv7XxXSqm2C2VDq8/KPiPPEPcsPtP/Ak8Xm+AsD1weeW3smv7ArRFxQkrp1tmsr6l9gP8DiLwt+1fIq5hAvmDzY/KyeDcWYxrIF4a+Qu6Rf5fcX74veTa95vvkmec2iYgdyK0pj0bEl8nhfjUaly2E/O/GHcX7XtLW95DUdRmwJdW72qzoWsDeRbgeSA5p48lbn/chz/zeHhG7FLOXX4iIjYG/kMPe7iml8U0e24e8Y+KyTWesm2ko3uvAlNLjM6m1G/liu6YbmdxPy/+1sQ+5f/t+4F5yGNytyePNV/EIGtfIBjg9pXRa8RkCWBfYOiIuI7fR9CWH3gWK8RPIQffSiFiz6UWbwCrFP4fmrmtyf/2IGFHUum/xumcAFBdTnkC+oPF6cqj9OrAmsEvRmvFhRJwMnFb0yd9FDunDaFxqry3uJ7e6bEEOz8uQe+Y3johrigs+3yFf3DqOWf9gkaQvGLAl1bstyC0g+wC3RMQ3yRexAQxPKX0EfBQR25BXkXg5In5LbgN5lLxk3CnkZdx2ahquZ1ex2c3+szG0N/kCzNG1E0XrxsSmgyJvhLIKeVZ3b+Ay4DmK2d/Cv4u6a3rRuMkOwJDIW4YPB75K7jm+qri/KLBxSmlsRCzT5Dk/IF/0eRF5p8ea7Zr9XXNPk/uHkWeozyL/UNklpVRbIo+U0tkRMR44lrwKyHPADimlm5uMOT0iPiAvK7h9MWaXlNL1Lbz3TBUXaY4oXm8n8n9hGE3uj3+2mMX/I3A5uU3m3oj4Skrpzba+l6SuJ7xmQ1K9i4hNgEfIgXl18ozzESmlD5uNG0Ce0T6Q3AJyGHmm82bgAGBlZlyybwfyTOy65IshIc8Uz0fezbHFtZMj4mPgBymlS+fg8ywK3FfU93LxeY5OKf1jJs+5B3g8pXRk0bM9lhy4byJfzHcbcD45uG5G7s3+KjlcHgD0SilNKX6gXApskFJ6ZDZqXZYcqDdPKd3T1s86L0TEkuQQvz/534cTgd8WffbrFH/vCPw0pXRKsVTjXeRJqaNTShdVVLqkTsKALanLKHqcP0opvTCLcYsBPVJKb0TEcuSAuBC5haDpdumtvgQ5YJ+bUjq6lff4HPh+SunCNn6M2vN7t9Yv3sr4xclbtb9f/L0H8EDTGdmiFeazlNKjRQ/0q8XnOKfYXrzWTrJWSumJ2XzfZel4AXsf4Kfkmfjzi/+KUXvsOOBrwOEppVFNzi9ZjP9ZSunB9q1YUmdjwJYktSgi5p+TlhhJ6uoM2JIkSVKJXAdbkiRJKpEBW5IkSSpRXS3Tt8gii6Rll1226jIkSZJU50aNGjUupTSwpcfqKmAvu+yyjBw5suoyJEmSVOci4tXWHrNFRJIkSSqRAVuSJEkqkQFbkiRJKpEBW5IkSSqRAVuSJEkqkQFbkiRJKpEBW5IkSSqRAVuSJEkqkQFbkiRJKpEBW5IkSSqRAVuSJEkqkQFbkiRJKpEBW5IkSSqRAVuSJEkqkQFbkiRJKpEBW5IkSSqRAbsMjz8OL7xQdRWSJEnqAAzYZdhtNzjppKqrkCRJUgdgwC5DBKRUdRWSJEnqAAzYZTBgS5IkqWDALoMBW5IkSQUDdhkM2JIkSSoYsMtgwJYkSVLBgF0GA7YkSZIKBuwyGLAlSZJUMGCXoVs3A7YkSZIAA3Y5ImDatKqrkCRJUgdgwC6DLSKSJEkqGLDLYMCWJElSwYBdBgO2JEmSCgbsMhiwJUmSVDBgl8GALUmSpIIBuwwGbEmSJBUM2GUwYEuSJKlgwC6DAVuSJEkFA3YZDNiSJEkqGLDL4FbpkiRJKhiwy+BW6ZIkSSoYsMtgi4gkSZIKBuwyGLAlSZJUMGCXwYAtSZKkggG7DAZsSZIkFQzYZTBgS5IkqWDALoMBW5IkSQUDdhkM2JIkSSoYsMtgwJYkSVLBgF0GA7YkSZIKBuwyuFW6JEmSCgbsMrhVuiRJkgoG7DLYIiJJkqSCAbsMBmxJkiQVDNhlMGBLkiSpYMAugwFbkiRJBQN2GQzYkiRJKhiwy2DAliRJUsGAXQYDtiRJkgqVB+yIOD8idmjlse4R8VpE3FMca7R3fbPFgC1JkqRC9yrfPCI2BRZPKd3YypA1gStTSse0Y1ltZ8CWJElSobIZ7IjoAfwBGB0RO7UybANg+4h4JCIuiohKfxC0yoAtSZKkQpUtIvsBzwJnAMMi4tAWxjwKbJVSGgb0ALZrx/pmX7dubpUuSZIkoNqAvQ5wQUppDHAZsHkLY55KKb1d3B8JrNB8QEQcFBEjI2Lk2LFj5121M+MMtiRJkgpVBuwXgeWK+0OBV1sYc2lErBURDcDOwJPNB6SULkgpDU0pDR04cOC8q3ZmDNiSJEkqVBmwLwI2j4j7gIOBayLilGZjTgIuBZ4AHkwp/b2da5w9BmxJkiQVKrtoMKU0Adij2el/NhvzDHklkY7NgC1JkqRC5etg1wUDtiRJkgoG7DIYsCVJklQwYJfBgC1JkqSCAbsMBmxJkiQVDNhlMGBLkiSpYMAugwFbkiRJBQN2Gbp1M2BLkiQJMGCXIwKmTau6CkmSJHUABuwy2CIiSZKkggG7DAZsSZIkFQzYZTBgS5IkqWDALoMBW5IkSQUDdhkM2JIkSSoYsMtgwJYkSVLBgF0GA7YkSZIKBuwyGLAlSZJUMGCXwYAtSZKkggG7DG6VLkmSpIIBuwxulS5JkqSCAbsMtohIkiSpYMAugwFbkiRJBQN2GQzYkiRJKhiwy2DAliRJUsGAXQYDtiRJkgoG7DIYsCVJklQwYJfBgC1JkqSCAbsMBmxJkiQVDNhlMGBLkiSpYMAug1ulS5IkqWDALoNbpUuSJKlgwC6DLSKSJEkqGLDLYMCWJElSwYBdBgO2JEmSCgbsMhiwJUmSVDBgl8GALUmSpIIBuwwGbEmSJBUM2GUwYEuSJKlgwC6DAVuSJEkFA3YZDNiSJEkqGLDL0K34x2jIliRJ6vIM2GWIyLcGbEmSpC7PgF0GA7YkSZIKBuwyGLAlSZJUMGCXwYAtSZKkggG7DAZsSZIkFQzYZTBgS5IkqWDALoMBW5IkSQUDdhkM2JIkSSoYsMtgwJYkSVLBgF0GA7YkSZIKBuwyGLAlSZJUMGCXoVvxj3HatGrrkCRJUuUM2GVwBluSJEkFA3YZDNiSJEkqGLDLYMCWJElSwYBdBgO2JEmSCgbsMhiwJUmSVDBgl8GALUmSpIIBuwwGbEmSJBUM2GUwYEuSJKlgwC6DAVuSJEkFA3YZDNiSJEkqGLDL4FbpkiRJKhiwy+AMtiRJkgoG7DIYsCVJklQwYJfBgC1JkqSCAbsMBmxJkiQVDNhlMGBLkiSpYMAugwFbkiRJBQN2GQzYkiRJKhiwy2DAliRJUsGAXQYDtiRJkgoG7DIYsCVJklQwYJehtlW6AVuSJKnLM2CXoTaDPW1atXVIkiSpcgbsMtgiIkmSpIIBuwwGbEmSJBUM2GUwYEuSJKlgwC6DAVuSJEkFA3YZDNiSJEkqGLDLYMCWJElSwYBdBgO2JEmSCpUH7Ig4PyJ2mMnjF0XEgxFxQnvW1SYGbEmSJBUqDdgRsSmweErpxlYe3xVoSCltCCwXESu0a4Gzy4AtSZKkQmUBOyJ6AH8ARkfETq0MGw5cXdy/HdikHUprO7dKlyRJUqHKGez9gGeBM4BhEXFoC2P6Am8W998HFms+ICIOioiRETFy7Nix86zYmXKrdEmSJBWqDNjrABeklMYAlwGbtzDmY6B3cb8fLdSbUrogpTQ0pTR04MCB86zYmbJFRJIkSYUqA/aLwHLF/aHAqy2MGUVjW8hawOh5X9YcMGBLkiSp0L3C974I+GNEfAPoAfwoIk5JKTVdLeR64B8RMQjYFtiggjpnzYAtSZKkQmUBO6U0Adij2el/NhszPiKGA18BzkgpfdRO5bWNAVuSJEmFKmewZ0tK6QMaVxLpmAzYkiRJKlS+0UxdMGBLkiSpYMAugwFbkiRJBQN2GQzYkiRJKhiwy2DAliRJUsGAXQa3SpckSVLBgF0Gt0qXJElSwYBdhgUXzLfvv19tHZIkSaqcAbsMSy2Vb19/vdo6JEmSVDkDdhkGDoSePQ3YkiRJMmCXIiLPYr/xRtWVSJIkqWIG7LIsvbQz2JIkSTJgl2bVVWHUKHj55aorkSRJUoUM2GU57jjo3h0OOcT1sCVJkrowA3ZZlloKTjkFbrkFLrmk6mokSZJUEQN2mQ45BDbdFA47DF57repqJEmSVAEDdpkaGuD//g+mToXvfMedHSVJkrogA3bZllsOfvUruPNOOP/8qquRJElSOzNgzwsHHgjbbgtHHw3/+U/V1UiSJKkdGbDnhQi48ELo1Qv22w8+/7zqiiRJktRODNjzyqBBuUXk4YfhjDOqrkaSJEntxIA9L33jG7DnnvDTn8ITT1RdjSRJktqBAXteO/98WHhh2Hdf+PTTqquRJEnSPGbAntcWXjj3Yz/zDPzP/1RdjSRJkuYxA3Z7+NrX8soiZ54J99xTdTWSJEmahwzY7eVXv4IvfSm3inzwQdXVSJIkaR4xYLeXfv3giitgzBj43vcgpaorkiRJ0jxgwG5PQ4fCySfDiBFw8cVVVyNJkqR5wIDd3o46Cr78ZTjkEHjxxaqrkSRJUskM2O2toQEuvRR69IBvfhOmTKm6IkmSJJXIgF2FpZeGCy6ARx6Bn/2s6mokSZJUIgN2VfbYA779bTjtNLjvvqqrkSRJUkkM2FU6+2xYbrm8dN+HH1ZdjSRJkkpgwK5S//556b4338wb0bh0nyRJUqdnwK7asGFw6qlwzTXw+99XXY0kSZLmkgG7IzjqKPjqV+GII+DJJ6uuRpIkSXPBgN0RdOsGl1wCCy0EX/86fPxx1RVJkiRpDhmwO4pFF8392C+8AD/8YdXVSJIkaQ4ZsDuS4cPhxBPzbLZbqUuSJHVKBuyO5sQTc9A++GB47rmqq5EkSVIbGbA7moYGuPxy6NMH9twTJk2quiJJkiS1gQG7Ixo0CC69FJ55Bo48supqJEmS1AYG7I5qm23gmGPy2thXXFF1NZIkSZpNBuyO7OSTYZNN8i6P//pX1dVIkiRpNrQ5YEfEwIjoMzdvGhErRUT/Fs4vFRExN69dV3r0gKuuyluq77YbTJhQdUWSJEmahTmZwT4UeCsiBsMXoXh8RHy3+Lt3RCw3i9f4JfCfiOjV7PzVwAMR0TAHddWnQYNyyH7xRTjgAEip6ookSZI0E3MSsLcBnk0pvVr8/SHQH1iw+Pv/gDsiYkBLT46I+YEtgAtTSpObnF8f2BBYDVh6DuqqX1/+Mpx2GowYAeecU3U1kiRJmok2BeyIWAZYF/hd7VxK6WPgM6B3ceonwBLAha28zHeABuC3zc6fDjwK/BW4NCJ6tqW2unfUUbDzzvCjH8E//1l1NZIkSWpFW2ewfwhMBK5pdv59oB9ASul54EfA75s/OSJ6kFtMbkkpjWly/gBgE+D7wMHAosDVETFfG+urXxHwpz/B4MF5fex33626IkmSJLVgtgN2RPQFvktuD/mk2cPvA1+0hKSUzgeejIjdm407GFiO3FZSe90tybPZP0opPZZSmgBsC6wP3B8Ry872p6l3Cy4I114L778Pe+0FU6dWXZEkSZKaacsM9n8DC7Xy2HvAyhFxSET8X0T8GxgDXN7kYsjFye0jX6TCiPgm8DdyP/YXzcUppZeBLwOLAP+KiNPmduWSurHWWvC738Fdd+Vt1SVJktShdJ+dQRGxAnAs8FHx92LkXurVgbWAlYEEzEfuoz4NGAn8O6X0eUR0B64CJgP3Fa+xH3AxcA5wc0Ssn1J6uHhsEPAVYCdyKP9/wKclfN76sP/+8MADcPrpMHQo7Lpr1RVJkiSpMMuAXaxXfTXwInA3uXVjEvAt4EHgXGAo8HVg45RSS30LSwKDgMPJoRngcuDDlNINEXErMA54uHhsqeJ1100p7R4RPVp53a7rnHPgqadgv/1gxRVh9dWrrkiSJEnMXotIT+BzYB9gCkBKaXxKaaWU0rdSSr8nB/D+wBotvUCxpN+6KaURTc5NTSndUPzZALzZ5Cm15fs+LcZOmf2P1EX07Jn7sfv3z6uLfPBB1RVJkiSJ2QjYKaVxwPoppadmMuyf5Fnt7WbyOuNn8vx+5J7tmmnFrbPWMzNoUA7Zr70Ge+/tRY+SJEkdwGxd5JhSmjaLxz8BbgO+MYd1DADGzuFzu7aNNoLzzoNbb4UTTqi6GkmSpC5vTnZybM0fgTUiYtu2PKnYLn15YHSJtXQtBx0E3/se/PzncPXVVVcjSZLUpZUZsG8CngTOLrZDJyJ2j4htImJm77M3ucf7yRJr6XrOOSfPZn/72/niR0mSJFWirQG7O/mCxBmklBJwALA0cGNEDAD2BS5t4TUCICKWIi/pd1mxwYzm1HzzwTXX5M1odt45b0YjSZKkdtfWgD1fcbQopTSKPCO9AfASeUfGvzbr4e4JdC/W1v47+YLGY5u9VM8m76fZtcQScN118OabeTv1KS6+IkmS1N7aGrB70hh+W5RS+guwMXmN7P8AZzYb0h3oTZ7F/hjYJqX0XrMxvZvdanatvz5ccAHceSccdhikVHVFkiRJXcps7eRYk1I6YDbHjQS+1spjO9TuR8R6RWtJ8zH3U7SRaA7svz889xz84hew2mpwyCFVVyRJktRltClgl62lcK2SnHYaPP88HH543ulx662rrkiSJKlLKHMVEXUk3brBZZfBGmvAHnvkGW1JkiTNcwbsetavH9xwA/TqBdtvD+81b3WXJElS2QzY9W6ZZeD66/PKIrvtBp99VnVFkiRJdc2A3RVsuCFcdBHcey8cfLAri0iSJM1DlV7kqHb0zW/mPuxTT4VVV4X/+q+qK5IkSapLBuyu5KST8soiP/oRDBkCu+xSdUWSJEl1xxaRrqRbN7jkEhg2DPbeGx56qOqKJEmS6o4Bu6vp0wduvBGWXBJ22AFeeqnqiiRJkuqKAbsrGjgQbr45X+y47bYwblzVFUmSJNUNA3ZXteKKeY3s116DnXaCSZOqrkiSJKkuGLC7so02yrs9Pvgg7L8/TJtWdUWSJEmdngG7q9t9dzjrLBgxAo45pupqJEmSOj2X6RMceSSMHp2D9uDBcMghVVckSZLUaRmwBRHw61/nfuzDD88rjLhGtiRJ0hyxRURZQwNccQWsvz7stRfcc0/VFUmSJHVKBmw16tMHbroJll8edtwRHn+86ookSZI6HQO2prfQQnDbbbDggnmNbDeikSRJapNKAnZEdI+I1yLinuJYo5VxTzQZ85X2rrPLWmopuP12+Pxz2HprGDOm6ookSZI6japmsNcErkwpDS+Op5sPiIiFgeebjLmj/cvswlZeOe/2+M47sM028NFHVVckSZLUKVQVsDcAto+IRyLioohoaTWT9YFhEfFARFwfEf3buUYNGwbXXQfPPpt3e5w8ueqKJEmSOryqAvajwFYppWFAD2C7Fsa8DHw1pbQR8BTw7XasTzVbbw0XXwz33ZdXF/n886orkiRJ6tCqCthPpZTeLu6PBFZoYczLwIuzGENEHBQRIyNi5NixY8uvVDlYn302XH89HHSQW6pLkiTNRFUB+9KIWCsiGoCdgSdbGHMqsENxf/dWxpBSun0QVDwAACAASURBVCClNDSlNHTgwIHzplrBoYfCT34Cf/oTHHEEpFR1RZIkSR1SVTs5ngRcAQRwA/BYRFyYUvpukzG/Aq6PiNOAB4GL279MTecnP4GPP4Zf/hL69YPTTqu6IkmSpA6nkoCdUnqGvJJIU99tNuZt8oWO6igi4Mwzc8g+/fQcso8/vuqqJEmSOpSqZrDVWUXA+efDxInw4x/nkH3YYVVXJUmS1GEYsNV23brlXuyJE+Hww6FvXzjggKqrkiRJ6hDcKl1zpnt3uPLKvAnNgQfm+5IkSTJgay707AnXXgubbgr77gt//WvVFUmSJFXOgK2506cP3HQTrLsu7Lkn/O1vVVckSZJUKQO25l7//nDrrbDGGrDrrnDzzVVXJEmSVBkDtsoxYADccUcO2bvsArfcUnVFkiRJlTBgqzy1kL366jlk33pr1RVJkiS1OwO2ylUL2auuCjvvDLfdVnVFkiRJ7cqArfIttBD8/e+wyiqw005w++1VVyRJktRuDNiaN2ohe+WVc8i+446qK5IkSWoXBmzNOwsvnEP2iivCjjvaky1JkroEA7bmrUUWgTvvzO0iO+4I119fdUWSJEnzlAFb814tZP+//we77w5XXVV1RZIkSfOMAVvto7a6yMYbw957w8UXV12RJEnSPGHAVvvp3z9vQLPllvCtb8Hvfld1RZIkSaUzYKt99ekDN9wA228PP/gB/OY3VVckSZJUKgO22l+vXnDttbkf+8gj4bTTqq5IkiSpNN2rLkBd1HzzwZVX5rD94x/DhAk5aEdUXZkkSdJcMWCrOt2754sd+/WDn/8cxo3LfdkNDVVXJkmSNMcM2KpWt25w/vl5Kb9TToEPPoDLL4eePauuTJIkaY4YsFW9CDj55Lzz45FHwocfwl/+klcdkSRJ6mS8yFEdxxFHwCWXwD33wBZb5JYRSZKkTsaArY5l333z7PUzz8Amm8Brr1VdkSRJUpsYsNXx7LAD3H47vP123vnxueeqrkiSJGm2GbDVMW26Kdx7L0yZkmeyH3ig6ookSZJmiwFbHdfaa8M//5kvftxyS7juuqorkiRJmiUDtjq25ZfPIXvttfPOj+eeW3VFkiRJM2XAVsc3cCDceSfsuCMcdhgcdRRMm1Z1VZIkSS0yYKtz6NMHrr0WDj4YzjoL9t4bPv206qokSZJm4EYz6jwaGuC882DwYDjmGBgzJi/pN2BA1ZVJkiR9wRlsdS4RcPTRcMUVeWWRTTaBV16puipJkqQvGLDVOe21F9x2G7z1Fqy/vsv4SZKkDsOArc5r883hoYdggQXy/csvr7oiSZIkA7Y6uZVWyiF7ww1hn33gf/7HFUYkSVKlDNjq/BZeOG+t/p3vwMkn5/aRSZOqrkqSJHVRBmzVh/nmgwsvhDPOgBEjYPjwvMqIJElSOzNgq35E5E1orrsOnnkGhg2DJ5+suipJktTFGLBVf3beGe6/P/dib7xx3qBGkiSpnRiwVZ/WWQceeQTWWAN23x1OOMGLHyVJUrswYKt+DRoE99yTL3489VTYcUf46KOqq5IkSXXOgK361rNnvvjxt7/NG9MMGwbPP191VZIkqY4ZsFX/IuDgg+HOO+GDD3LIvvHGqquSJEl1yoCtrmOzzWDUKFhxxdwucvLJ9mVLkqTSGbDVtSy9NPzjH427Pu62m33ZkiSpVAZsdT29e8Mll8Cvf51bRYYOhaeeqroqSZJUJwzY6poi4Igj8iojEyfCBhvAxRdXXZUkSaoDBmx1bZtsAo8/ngP2t74FBx4IkydXXZUkSerEDNjSYovB7bfDccflJf022ghefrnqqiRJUidlwJYAuneH007LPdmvvALrrutSfpIkaY4YsKWmtt8eHnsMllsuL+V3zDEwZUrVVUmSpE7EgC01N2QI/POf8L3vwRln5PWzR4+uuipJktRJGLCllvTqBb/7HVx9NTz7LKy9NowYUXVVkiSpEzBgSzOzxx7wxBOw8sqw557w/e/DpElVVyVJkjowA7Y0K0OG5N0fjzkGfv97WG89+Ne/qq5KkiR1UAZsaXb06AE//zncdhuMHZt3f7zgAkip6sokSVIHY8CW2mLrreHJJ2HTTfNFkHvsAe+9V3VVkiSpAzFgS221+OJw663wi1/ADTfAGmvkjWokSZIwYEtzpls3OPpoePhhWHBB+OpX4bDDvABSkiQZsKW5ss46MGpUDtfnnpt3gHzssaqrkiRJFTJgS3Ord284++zcJvLRR7D++nD66TB1atWVSZKkChiwpbJ85Svw9NOw885w/PEwfDi88krVVUmSpHZmwJbKtNBCeffHiy/Oq42ssQb87//CtGlVVyZJktqJAVsqWwTst1+ezd5wQzj4YNhqK2ezJUnqIgzY0rwyeHDuy77gAhg50tlsSZK6CAO2NC9FwIEHwjPPwEYbOZstSVIXYMCW2sMyy+Rt1p3NliSp7hmwpfbS2mz2Sy9VXZkkSSqRAVtqb7XZ7D/8IW9Ss/rqedv1KVOqrkySJJXAgC1VIQK++1149lnYbjs49lgYOhQeeaTqyiRJ0lwyYEtVWnJJuPZa+Mtf4L33YIMN4PDDYcKEqiuTJElzyIAtdQQ775xns3/4Qzj3XFh1VbjxxqqrkiRJc8CALXUU88+fw/UDD8CCC8KOO8Kee8Lbb1ddmSRJagMDttTRbLABPPYYnHYa3HADrLwynH02fP551ZVJkqTZYMCWOqIePeC44xqX9DviCFh3Xbj//qorkyRJs2DAljqyL30Jbr4ZrrsOPvwQNt0U9t8f3nmn6sokSVIrDNhSRxcBu+ySL4I8/ni48kpYaSU47zzbRiRJ6oAM2FJn0bcvnHoqPP00DBsGhx4K662XL4qUJEkdhgFb6mxWWinvBDliBIwbBxtvDPvuC2++WXVlkiQJA7bUOUXA7rvDc8/liyFHjIAVV4STToJPPqm6OkmSujQDttSZ9euXl/N77rm85fpPfpKX9fvznyGlqquTJKlLMmBL9WDIkDyLfe+9sMgisNdesMkm8OijVVcmSVKXU1nAjojuEfFaRNxTHGu0Mu5nEfFoRPy2vWuUOp3NNsuh+qKL4KWX8sWQ++8Pb71VdWWSJHUZVc5grwlcmVIaXhxPNx8QEesCmwDDgHcjYqv2LlLqdBoa4Dvfgf/8B445JreLfOlLcMIJMH581dVJklT3qgzYGwDbR8QjEXFRRHRvYcyXgWtTSgm4Ddi0XSuUOrP554ef/zz3Z++8c17ib/nl4Zxz4LPPqq5OkqS6VWXAfhTYKqU0DOgBbNfCmL5Abe2x94HFmg+IiIMiYmREjBw7duw8K1bqtJZbDq64IreOrLkmHH44rLIKXHUVTJtWdXWSJNWdKgP2Uymlt4v7I4EVWhjzMdC7uN+PFupNKV2QUhqaUho6cODAeVOpVA+GDoW//x1uuSWvPvKNb8D668Pdd1ddmSRJdaXKgH1pRKwVEQ3AzsCTLYwZRe7BBlgLGN1OtUn1KQK22QYeewwuvhjeeQe22AK23Raeeqrq6iRJqgtVBuyTgEuBJ4AHgcci4sJmY+4H1omIs4FjgSvbt0SpTjU0wH775QshzzwTHnoI1l47L+/3739XXZ0kSZ1apA6+GUVE9Aa+BjyWUnp5ZmOHDh2aRo4c2T6FSfXkgw/grLPg7LNh0qS89fr//E/u35YkSTOIiFEppaEtPdbhN5pJKU1KKV0zq3AtaS4MGJBXGXn5ZTjiiHwB5Eorwfe/D2+8UXV1kiR1Kh0+YEtqR4suCr/8Zd6k5nvfgz/+Ma+hffjhMGZM1dVJktQpGLAlzWjQIDjvPHjhBdhnH/jtb3O7yNFHw7vvVl2dJEkdmgFbUusGD4YLL8yb1ey6a+7TXnZZ+K//grffnuXTJUnqigzYkmZthRXgssty0N5jj7wb5JAh8MMfwmuvVV2dJEkdigFb0uxbaaW8fva//51XGvnDH3KP9oEH5gskJUmSAVvSHFh++RyuX3wxh+tLL4UVV4T993cdbUlSl2fAljTnllkmXwD58stw2GEwYgSssgrsvjs88kjV1UmSVAkDtqS5N2gQ/OpXMHo0HHcc3HknrL8+DB8ON98MHXxDK0mSymTAllSeRRfNG9a89loO3C+9BF/7Gqy5JlxyCUyZUnWFkiTNcwZsSeXr3x+OPDK3jlxyST63//65d/vXv4YJE6qtT5KkeciALWne6dEjrzby1FO5VWT55fMa2sssA8cfD2++WXWFkiSVzoAtad6LgG23hbvvhocfhi23hJ//PG9as/fe+ZwkSXXCgC2pfQ0bBtdck/uzDzsM/vY32GAD2HBD+POf7dOWJHV6BmxJ1RgyBH75S3jjDTj3XBg3DvbaK58//XR4772qK5QkaY4YsCVVq39/OOSQvEHNTTfldbSPPx6WWipvYvP001VXKElSmxiwJXUM3brlJf3uuAOeeQb22w8uuywv8bfJJnDFFfDpp1VXKUnSLBmwJXU8q60Gv/99bh856yx45x345jdh6aXh2GPhlVeqrlCSpFYZsCV1XAsvDP/937l95LbbYOON4cwz83J/220HN94IU6dWXaUkSdMxYEvq+Lp1g623hr/8BV59FU48EZ54AnbcMYft006DMWOqrlKSJMCALamzWWop+NnPctAeMSIH7B//OJ/faSe44Qb4/POqq5QkdWEGbEmdU48esPvucOed8PzzuZXk4YdzyK71ar/wQtVVSpK6IAO2pM5vpZXgF7+A11+H66+H9dbLF0euuCJsthlcfDFMnFh1lZKkLsKALal+9OjR2Cby+ut5w5oxY+Bb34IlloDvfS/PcqdUdaWSpDpmwJZUn5ZYIreJ/PvfcO+9sMsucOmleVv2lVeGU06B0aOrrlKSVIcM2JLqW0Rjm8jbb8OFF+bwfeKJeVv2zTbL5z78sOpKJUl1woAtqetYYAE44AC45548e33qqfDuu3lL9sUXhz33zGtrT5lSdaWSpE7MgC2paxo8GI4/Hp57Dh55BA46CO6+O6+tPWgQHHooPPAATJtWdaWSpE7GgC2pa4vIq46ccw689Va+QHLzzeEPf8g7Rw4ZAkcdBaNGeXGkJGm2GLAlqaZHD9hhB7j66tw6csklsMYa8JvfwNChsMIKcMIJ8PTThm1JUqsM2JLUkvnnh333hZtugnfeyRdCDhmSl/5bc01YfXU46aS8SokkSU1EqqNZmKFDh6aRI0dWXYakevbOO3DttXDVVfCPf+SZ7LXXht12g113hVVWyW0nkqS6FhGjUkpDW3zMgC1Jc+jNN2HEiBy2H3oon1tppRy0d90V1l3XsC1JdcqALUnz2ltv5W3ar7suLwM4dSosvXRj2N54Y2hoqLpKSVJJDNiS1J7eey+vp/2Xv8Btt8Gnn8LAgbDzznlHyc03h169qq5SkjQXDNiSVJUJE+DWW/PM9k03wccfQ9++sPXWsP328LWvwWKLVV2lJKmNDNiS1BFMngx33ZWD9o03whtv5B7tYcNy2N5hh7xCiX3bktThGbAlqaNJCZ58Mgftm27Ku0lC7tuuhW1bSSSpwzJgS1JHN2YM/O1vOXDfcQd88gn06QNbbQXbbANf/Sost1zVVUqSCgZsSepMJk+Gu+/OYfuWW2D06Hx+hRVy0N5mGxg+PPdyS5IqYcCWpM4qJXjhhXyh5G235eA9aRLMNx9sumnj7Pbqq9u7LUntyIAtSfVi8mS4//7GwP3MM/n8oEE5aG+1FWyxBSy+eLV1SlKdM2BLUr16440ctG+7Lfduf/hhPr/aarDlljlsDx8OCyxQaZmSVG8M2JLUFUydCo8/DnfemY/778/tJN26wdChOXBvuSVstBH07l11tZLUqRmwJakr+vRTeOihHLbvugsefhg+/xx69swhe8st8+z20KH5nCRpthmwJUl5V8l//KMxcD/xRD7fqxdssAFstlk+NtjAFUokaRYM2JKkGY0bl9tI7rsvH48/DtOmQffueVa7Frg33hgWXLDqaiWpQzFgS5Jmbfx4eOCBxsD9yCMwZUpe/m+ttXLY3nTTHLiXWKLqaiWpUgZsSVLbTZqU+7ZrgfuBB/I5gMGDcx/3hhvm2zXXhB49qq1XktrRzAJ29/YuRpLUSfTunS+CHD48//3ZZ/DYY/Dgg/m47z648srGscOGNQbuDTeERRapqnJJqpQz2JKkOff663lm+8EH8+3jj+eVSiBv7b7hhvkYNgzWWMNZbkl1wxYRSVL7mDQJRo5snOV+4AF49938WM+esPbasN56jcdKK+V1uiWpkzFgS5KqkRKMHg2PPpqPRx6BUaNg4sT8+Pzzw7rrTh+6l1kmX1gpSR2YAVuS1HFMnQrPP98YuB99FJ58Mq9YAjBwYA7a664L66yTj8GDDd2SOhQDtiSpY/v0U3jqqelD9/PP53W5AQYMyO0ltcC9zjq5vaS71+pLqoYBW5LU+XzyCTz9dL5w8vHH8womTz+dwzjklUvWXHP60L3GGnlnSkmaxwzYkqT6MGVKntmuhe7HH89bvn/0UX68oQFWXDEH7abHsst6MaWkUhmwJUn1KyV45ZXGwP3007ndZPToxjH9+sFqqzUG7jXXzLcLL1xZ2ZI6NwO2JKnrmTABnnkmB+6mx/vvN45ZYonpZ7pXWQVWXjmvbiJJM+FOjpKkrqd//8aNbmpSgrffnjF0n3deY283wJJLwqqr5sBdO1ZdNa9wIkmzYMCWJHUdETBoUD6++tXG859/Di+9BM8913g8+yxcdFHjmt2QW0qaBu7a/aWXdhlBSV+wRUSSpNZMmwZvvDF96K7df++9xnF9++aLK1dcMW8R3/R2oYWqq1/SPGOLiCRJc6Jbt7yz5DLLTD/jDTB27PSh+4UX8jbx11yTN9OpWXjhGUP3Civko1+/9v08ktqFAVuSpDkxcGA+Ntts+vOffZZXNfnPf3Lo/s9/8nHXXXDJJdOPHTSoMWwvvzwst1zj7YAB7fdZJJXKgC1JUpnmmy/vMrnSSjM+NnEivPji9MH7hRfg+uth3Ljpxy644PSBu3Ysv3zu+XYXS6nD8v86JUlqL337wlpr5aO5CRPg5ZdnPJ58MgfwKVMaxzY0wODB0wfwZZfN5wYPhkUXdWMdqUIGbEmSOoL+/VsP31OnwptvthzAr7su94M31bNnnuWuBe7mx1JLQY8e7fO5pC7IgC1JUkfX0NB4seXw4TM+Pn48vPpqy8ctt+S1v5uqLVfYPHgvs0wO5kstlVtUXHpQmiMGbEmSOrv552/cjbIln34Kr7/ecgB/6CEYMSKvBd5Unz45aC+5ZL5tetTODRxoK4rUAgO2JEn1rmdP+NKX8tGSqVPzLPerr+ZWlDfeyEft/r33wltvzRjCe/RoDNvNg/igQbD44nk7+t695/1nlDoQA7YkSV1dQ0NjMG7N1Knw7rvTB/CmIXzUKPjrX2Hy5Bmfu8ACOWjXjlrwbn4ssIBtKaoLBmxJkjRrDQ2NQXhoi5vXQUrwwQe5HeXtt6c/xozJtw89lG8nTZrx+b16zRi+a38vtlheHaV29Okzbz+vNBcM2JIkqRwReWv4hRZqeTWUmpTyhZm10N1SGH/+ebj77hzYW9K37/SBu7VjscXybpquG6525L9tkiSpfUXkdpAFFmh5Q56mJk+Gd97J7SmtHa+/nltU3n13xj7x2vstvPD0wXvgwHxukUXybdP7iyySA7ztKppDBmxJktRx9erVuIzgrEybBh9+OPMw/u678MQTee3w1mbHIe/I2Vr4bu2cPeQqGLAlSVJ96NatsUVl5ZVnPX7q1Byyx42D995rvG16v3b7zDP59v338/Na0r07DBjQ8rHggq0/NmBA3mjIcF43DNiSJKlramjIs8+LLDL7z5k2DT76qOVAPm5cDuy147334MUXG/+eNm3mtcwqhA8Y0Nhas8ACef3z2v0+fQzoHYgBW5IkaXZ169YYdltbV7wlKcGECdMH8Fkdr7zSeL+1WfOahobpA3dr92f2WP/+XgxaEv8pSpIkzWsROczOP//s9ZM3lRJ8/HEO2h991HiMHz/z+2+8Ac8+23i+pQtAm+vbN9fYv38++vVr+XZmjzW97aKBvWt+akmSpM4iojHUzqmU8oossxPMx4/Ps+0ff5xv33pr+r8//nj237dXr1mH8H79crBvy9HQMOf/LNpBpQE7IhYDbk0prdPCY92Bl4sD4NCU0tPtWZ8kSVJdiMhb1vfunTfvmRvTpsEnn8wYumd22/T+hx/mpRVrf0+cCJ991rYaevZsDNu//jXsttvcfaaSVT2DfRbQu5XH1gSuTCkd0471SJIkaWa6dWuceS7LlCk5tE+cmIP3xImzfyyxRHl1lKSygB0RWwATgTGtDNkA2D4iNgeeBr6XUpqN5iFJkiR1Kj16NF5sWQe6VfGmETEfcCJw7EyGPQpslVIaBvQAtmuP2iRJkqS5UUnAJgfr81NKH85kzFMppbeL+yOBFVoaFBEHRcTIiBg5duzYsuuUJEmS2qSqgL0V8MOIuAdYOyIubGHMpRGxVkQ0ADsDT7b0QimlC1JKQ1NKQwcOHDjvKpYkSZJmQyU92CmlzWr3i5D9q4g4JaV0QpNhJwFXAAHckFL6e/tWKUmSJLVd1auIkFIaXtw9odn5Z8griUiSJEmdRlUtIpIkSVJdMmBLkiRJJTJgS5IkSSUyYEuSJEklMmBLkiRJJTJgS5IkSSUyYEuSJEklMmBLkiRJJTJgS5IkSSUyYEuSJEklMmBLkiRJJTJgS5IkSSUyYEuSJEklipRS1TWUJiLGAq9W9PaLAOMqem+1H7/nrsHvuf75HXcNfs9dQ1Xf8+CU0sCWHqirgF2liBiZUhpadR2at/yeuwa/5/rnd9w1+D13DR3xe7ZFRJIkSSqRAVuSJEkqkQG7PBdUXYDahd9z1+D3XP/8jrsGv+euocN9z/ZgS5IkSSVyBluSJEkqkQG7BBFxUUQ8GBEnVF2L5l5ELBARt0TE7RHxl4iYr6Xv2O+984uIxSLi8eK+33GdiojzI2KH4r7fc52JiAERcXNEjIyI3xfn/J7rSPG/1f8o7veIiBsj4p8R8Z22nGtPBuy5FBG7Ag0ppQ2B5SJihapr0lz7JvCrlNLWwBjgGzT7jv3e68ZZQO+Wvk+/4/oQEZsCi6eUbvR7rlv7ApcXy7T1j4ij8XuuGxExALgY6FucOhQYlVLaGNg9Ivq34Vy7MWDPveHA1cX924FNqitFZUgpnZ9SuqP4cyCwDzN+x8NbOKdOJCK2ACaSf0QNx++47kRED+APwOiI2Am/53r1HrB6RCwILA0Mwe+5nkwFvg6ML/4eTuN3eR8wtA3n2o0Be+71Bd4s7r8PLFZhLSpRRGwIDABeZ8bv2O+9E4uI+YATgWOLUy19n37Hnd9+wLPAGcAw4If4Pdej+4HBwGHAc8B8+D3XjZTS+JTSR01Oze7/Xlf6nRuw597HQO/ifj/8Z1oXImIh4FzgO7T8Hfu9d27HAuenlD4s/vY7rk/r8P/bu/doKcsqjuPfHzcDlcQA87KUyGUs0SiF0BDRUgMLldJCKlMkpKQ7mUUur2UuXKGrvC5B8VKZpZJmaaYlmBap5YUwybCEpQWCRuIF2f2xn5FpmoGDnpxzxt9nrbPOed95532fOQ+Oe56z373h4oh4AriSXMXyPLeek4EpEXEasAiYgOe5lbX1/bqpc+5/YK/ePaz/U9MQYEnzhmLtoaxuXgN8NSIeo/4ce947twOA4yX9CngHMBbPcStaDAwsPw8FBuB5bkV9gN0ldQWGA9/C89zK2vr/5KbOebfX8mIt6npgnqTtgDHAXk0ej716xwJ7ANMlTQcuBT5eM8eB573Tioh9Kz+XIPsQ/nc+Pced3yxgtqTxQHcyJ/MnnueWcyb5Pr0TcBcwE//33MrmADeVG5h3BX5LpoK0Zd9rxo1m2kG5w/VA4I7yp0hrMfXm2PPeWjzHrw+e59cHz3NrKx+U9gFuruRnt3XfazZGB9hmZmZmZu3HOdhmZmZmZu3IAbaZmZmZWTtygG1m1kSSeknqW7NvmKQ9lE6T9Likjb5fS+onqdf/YYx9JG2xCcf3K997SfqupLeX7QFVx0x2Nz0za1UOsM3MmkTSZsCDZCOUap8DZpef15Atnte14ZSfAZZJ2qmcfwdJz0iaVLZ7Shq4wTPUNxFYKmnQxg6U1B94uLSm7k42d9le0q7AYkkDy+s+HzjyFYzFzKzDc4BtZtYkEfE8cBNwlKSdASSJrNP9vci70F9gfYtgJHXdwCr1aGBhqd8OsArYEtiqbF8G/KJUU/gvkoZLWlAC5FqHAPdHxKI2vKZ/AF8ELgA2K7ufJ5v7XBsRjwKDga5kvXkzs5bjANvMrLlmAAImle19yJa+Z0kK4GxgF0lRttcCP6w9iaQdgT2BCyv7ImI1GaBXupmdDGwLXFJnHCuAtwLXSHq5R0IJ/EcCP5Y0qMHXG6qOH1n1ug4sP+8H/A1YKGm/sr0kIv5U9bwuZWXbzKzTc6MZM7MmiojHJI0Gbi27pgC/IRseUb4fDHyobPekvuOBfwM/qtn/FNkmmIhYJGka2eGwdhyLJU0AfkoGx1+oGo/I5h0z61z3BaA/8FzZHgV8EHgRGFb2jQP+RaaMdCO7Zw4oHxiqXQ2Mb/D6zMw6DdfBNjPrICR1B34GXBURl5Z9pwMjI2K/DTxvc3KF+JGI2KvmsYeAOyNictW+bco5a4NxJJ0FnEAGyouAR4FTIuLsOscuAP4ZEQfXeezrZC55X2AC0CUirpL0RuBJ4FPA7eXwW8lV+Qsi4u+NXqeZWWfhFWwzsyaRNI5cdV4HPAQ8Ta5Ur5XUk2zv/Gbgqeo0DPK9u0tEVHKzvwRs3eAyK4BBkqYCQ4G9gV2AFyQtqMrXrjgZeByYT+ZRrwTOK+OZDMyLiHslbUumpHy65jWpnONE4DDyA8Ng4MuSVgH9yNzspRGxtFUbrQAABMJJREFUpDxna/LDgYNrM2sJDrDNzJrnYrJKyA5kpY4BZHBaz5qa7bnAYaXU3YlkcF5ZnZ4I7AYMAQaRgXoPYAHwTeD3wMMRsbb2IhHxHPAdSYOBo4GjImJNKdN3DnAqcC9waDnv3Mpzy4eAuWQe+TgySAe4G/gG8Hmgd9k3GLil5F33ARxcm1nLcIqImVmTSVoNTAWuI3OsXyRXtbci0zQuAaZXDidXgNeS1TnuICty3A4MBw4iA+m7ytdQ4CNAn4h4qY3j2ToinpI0sFT9oKR2rAI+GRGXlLrcAyqPVz33PcDyiLhf0klkisgZ5Er6WOB6YB6wLCLGS3oLmYYyMCL+ugm/NjOzDstVRMzMOoiIeDoinoiIFRGxkry5sTtwHHBoRKyKiJXlmOWsD7Q/RgblRMQzEfG2iDg6Ii4ic5u3BHZvyxhKPvcDko6pCZ53LN/7l+usqw2uiyeBLpJ2I3O5uwJvItNSBJwJXATsW9JJhpBpKEvaMj4zs87AKSJmZh1Q6YZ4CjAH+DMwS9KzEfFy7eiIWC5peESsy1i1rjvJ9JKDgT+04dJTyTKB99Xs36d8fx+ZZtLIHDKY7kampYwvX5uTaSzbkfnic4C9gBHA3eE/p5pZC/EKtplZByOpB3AFufo7PSLOBK4CrpR0UPWxG+vwGBHPAjfThvJ3krYEppHVPGqD8U8AtwF7S3rXBq43NCJ6kwH6zIjoGxF9yZs4ry4r38vJmx8/CxwB3LKxsZmZdSYOsM3MOpDSZfF6Mpd6UkQsKw8dBzwAXCtpWKPnNzAb2F3SmI0cN4O8cfGkmjF9lMzlnkIG/nPqdYOsOn4s8G6gu6RRksaTNzXOqDrsXDLo7w9cvmkvx8ysY3OAbWbWMfSQtBWZxjEamBoR11YeLNU9jiBzrq+Q1HUTzn0j8EfgXEm9ASQdLml0uVkRSe8ny/B9LSJWVZ4oaV+yO+SMiHgE+ApZCeTWUqqvnvnAh8kc8cuB75M3OY6oGveTwEvAcnKl3sysZbiKiJlZE5WGLKeTFTqOIYNXRcScBscfASyOiPtq9p8DjIiIuqvbkvYkA9/fkfWpLyNXmbcha1M/DPwFGFZJO5E0hezeOBc4spInLWkPsjnMc8DEiPh5g2seQAbYvyZvYjwWeC/Zkn02cAOwP7AaODAiljb+TZmZdR4OsM3MmkTSqWQ6xkyy0sdEMlf5BuAecnV3NbnC2wPoRbY970OmVlwYEZX61+eTAfaQDVxvHPADsrnNFsDlETGpPDYZWBgR8yWNIutWjyhjO6G2ZnapEnIdsDPwS+DwiFglaXtgDJmzvVt5fedFREh6Z9k+hOwOeUapt30beVPkCREx6xX9Ms3MOhBXETEza4KSwzwWmBYR3y77ZpK51mPIVuK9afw+fVdEnFW1vVn5aigirpM0gmwWsxNVOdERcXHVof2AbYGxEXFjg3M9WFbFZwArqtJK9icb38wCPlD5AFCMJj8YDI+Ie8p5Hior4rOAhRsav5lZZ+EVbDOzJpHUMyJqOzR2CJK6bKxCiZmZ1ecA28zMzMysHbmKiJmZmZlZO3KAbWZmZmbWjhxgm5mZmZm1IwfYZmZmZmbtyAG2mZmZmVk7coBtZmZmZtaO/gPL4HycR34gJwAAAABJRU5ErkJggg==\n", "text/plain": ["<Figure size 864x576 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots(figsize=(12, 8))\n", "ax.plot(np.arange(iters), cost, 'r')\n", "ax.set_xlabel('迭代次数', fontsize=18)\n", "ax.set_ylabel('代价', rotation=0, fontsize=18)\n", "ax.set_title('误差和训练Epoch数', fontsize=18)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 多变量线性回归"]}, {"cell_type": "markdown", "metadata": {}, "source": ["练习还包括一个房屋价格数据集，其中有2个变量（房子的大小，卧室的数量）和目标（房子的价格）。 我们使用我们已经应用的技术来分析数据集。"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>面积</th>\n", "      <th>房间数</th>\n", "      <th>价格</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2104</td>\n", "      <td>3</td>\n", "      <td>399900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1600</td>\n", "      <td>3</td>\n", "      <td>329900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2400</td>\n", "      <td>3</td>\n", "      <td>369000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1416</td>\n", "      <td>2</td>\n", "      <td>232000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>3000</td>\n", "      <td>4</td>\n", "      <td>539900</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     面积  房间数      价格\n", "0  2104    3  399900\n", "1  1600    3  329900\n", "2  2400    3  369000\n", "3  1416    2  232000\n", "4  3000    4  539900"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["path = 'data/regress_data2.csv'\n", "data2 = pd.read_csv(path)\n", "data2.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["对于此任务，我们添加了另一个预处理步骤 - 特征归一化。 这个对于pandas来说很简单"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>面积</th>\n", "      <th>房间数</th>\n", "      <th>价格</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0.130010</td>\n", "      <td>-0.223675</td>\n", "      <td>0.475747</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>-0.504190</td>\n", "      <td>-0.223675</td>\n", "      <td>-0.084074</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0.502476</td>\n", "      <td>-0.223675</td>\n", "      <td>0.228626</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>-0.735723</td>\n", "      <td>-1.537767</td>\n", "      <td>-0.867025</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1.257476</td>\n", "      <td>1.090417</td>\n", "      <td>1.595389</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         面积       房间数        价格\n", "0  0.130010 -0.223675  0.475747\n", "1 -0.504190 -0.223675 -0.084074\n", "2  0.502476 -0.223675  0.228626\n", "3 -0.735723 -1.537767 -0.867025\n", "4  1.257476  1.090417  1.595389"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["data2 = (data2 - data2.mean()) / data2.std()\n", "data2.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["现在我们重复第1部分的预处理步骤，并对新数据集运行线性回归程序。"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.13070336960771892"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["# add ones column\n", "data2.insert(0, 'Ones', 1)\n", "\n", "# set X (training data) and y (target variable)\n", "cols = data2.shape[1]\n", "X2 = data2.iloc[:,0:cols-1]\n", "y2 = data2.iloc[:,cols-1:cols]\n", "\n", "# convert to matrices and initialize theta\n", "X2 = np.matrix(X2.values)\n", "y2 = np.matrix(y2.values)\n", "w2 = np.matrix(np.array([0,0,0]))\n", "\n", "# perform linear regression on the data set\n", "g2, cost2 = batch_gradientDescent(X2, y2, w2, alpha, iters)\n", "\n", "# get the cost (error) of the model\n", "computeCost(X2, y2, g2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["我们也可以快速查看这一个的训练进程。"]}, {"cell_type": "code", "execution_count": 25, "metadata": {"scrolled": false}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 864x576 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots(figsize=(12,8))\n", "ax.plot(np.arange(iters), cost2, 'r')\n", "ax.set_xlabel('迭代次数', fontsize=18)\n", "ax.set_ylabel('代价', rotation=0, fontsize=18)\n", "ax.set_title('误差和训练Epoch数', fontsize=18)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["我们也可以使用scikit-learn的线性回归函数，而不是从头开始实现这些算法。 我们将scikit-learn的线性回归算法应用于第1部分的数据，并看看它的表现。"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"text/plain": ["LinearRegression()"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["from sklearn.linear_model import LinearRegression\n", "model = LinearRegression()\n", "model.fit(X, y)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["scikit-learn model的预测表现"]}, {"cell_type": "code", "execution_count": 27, "metadata": {"scrolled": false}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 864x576 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["x = np.array(X[:, 1].A1)\n", "f = model.predict(X).flatten()\n", "\n", "fig, ax = plt.subplots(figsize=(12, 8))\n", "ax.plot(x, f, 'r', label='预测值')\n", "ax.scatter(data['人口'], data['收益'], label='训练数据')\n", "ax.legend(loc=2, fontsize=18)\n", "ax.set_xlabel('人口', fontsize=18)\n", "ax.set_ylabel('收益', rotation=0, fontsize=18)\n", "ax.set_title('预测收益和人口规模', fontsize=18)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## $L_2$正则化\n", "$J (  { w } ) = \\frac { 1 } { 2 } \\sum _ { i = 1 } ^ { m } ( h _ { w} ( x ^ { ( i ) } ) - y ^ { ( i ) } ) ^ { 2 } + \\lambda \\sum _ { j = 1 } ^ { n } w_ { j } ^ { 2 }$，此时称作`Ridge Regression`："]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/plain": ["Ridge()"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["from sklearn.linear_model import Ridge\n", "model = Ridge()\n", "model.fit(X, y)"]}, {"cell_type": "code", "execution_count": 29, "metadata": {"scrolled": false}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 864x576 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["x2 = np.array(X[:, 1].A1)\n", "f2 = model.predict(X).flatten()\n", "\n", "fig, ax = plt.subplots(figsize=(12, 8))\n", "ax.plot(x2, f2, 'r', label='预测值Ridge')\n", "ax.scatter(data['人口'], data['收益'], label='训练数据')\n", "ax.legend(loc=2, fontsize=18)\n", "ax.set_xlabel('人口', fontsize=18)\n", "ax.set_ylabel('收益', rotation=0, fontsize=18)\n", "ax.set_title('预测收益和人口规模', fontsize=18)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## $L_1$正则化：\n", "$J (  {w } ) = \\frac { 1 } { 2 } \\sum _ { i = 1 } ^ { m } ( h _ { w} ( x ^ { ( i ) } ) - y ^ { ( i ) } ) ^ { 2 } + \\lambda \\sum _ { j = 1 } ^ { n } | w _ { j } |$，此时称作`Lasso Regression` "]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"data": {"text/plain": ["<PERSON><PERSON>()"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["from sklearn.linear_model import Lasso\n", "model = Lasso()\n", "model.fit(X, y)"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 864x576 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["x3= np.array(X[:, 1].A1)\n", "f3 = model.predict(X).flatten()\n", "\n", "fig, ax = plt.subplots(figsize=(12, 8))\n", "ax.plot(x3, f3, 'r', label='预测值Lasso')\n", "ax.scatter(data['人口'], data['收益'], label='训练数据')\n", "ax.legend(loc=2, fontsize=18)\n", "ax.set_xlabel('人口', fontsize=18)\n", "ax.set_ylabel('收益', rotation=0, fontsize=18)\n", "ax.set_title('预测收益和人口规模', fontsize=18)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 调参"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["from sklearn.model_selection import cross_val_score\n", "alphas = np.logspace(-3, 2, 50)\n", "test_scores = []\n", "for alpha in alphas:\n", "    clf = Ridge(alpha)\n", "    test_score = np.sqrt(-cross_val_score(clf, X, y, cv=5, scoring='neg_mean_squared_error'))\n", "    test_scores.append(np.mean(test_score))"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "plt.plot(alphas, test_scores)\n", "plt.title(\"Alpha vs CV Error\");\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 最小二乘法(LSM)：\n", "\n", "最小二乘法的需要求解最优参数$w^{*}$：\n", "\n", "已知：目标函数\n", "\n", "$J\\left( w  \\right)=\\frac{1}{2m}\\sum\\limits_{i=1}^{m}{{{\\left( {h}\\left( {x^{(i)}} \\right)-{y^{(i)}} \\right)}^{2}}}$\n", "\n", "其中：${h}\\left( x \\right)={w^{T}}X={w_{0}}{x_{0}}+{w_{1}}{x_{1}}+{w_{2}}{x_{2}}+...+{w_{n}}{x_{n}}$\n", "\n", "将向量表达形式转为矩阵表达形式，则有$J(w )=\\frac{1}{2}{{\\left( Xw -y\\right)}^{2}}$ ，其中$X$为$m$行$n+1$列的矩阵（$m$为样本个数，$n$为特征个数），$w$为$n+1$行1列的矩阵(包含了$w_0$)，$y$为$m$行1列的矩阵，则可以求得最优参数$w^{*} ={{\\left( {X^{T}}X \\right)}^{-1}}{X^{T}}y$ \n", "\n", "梯度下降与最小二乘法的比较：\n", "\n", "梯度下降：需要选择学习率$\\alpha$，需要多次迭代，当特征数量$n$大时也能较好适用，适用于各种类型的模型\t\n", "\n", "最小二乘法：不需要选择学习率$\\alpha$，一次计算得出，需要计算${{\\left( {{X}^{T}}X \\right)}^{-1}}$，如果特征数量$n$较大则运算代价大，因为矩阵逆的计算时间复杂度为$O(n^3)$，通常来说当$n$小于10000 时还是可以接受的，只适用于线性模型，不适合逻辑回归模型等其他模型"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [], "source": ["def LSM(X, y):\n", "    w = np.linalg.inv(X.T@X)@X.T@y#X.T@X等价于X.T.dot(X)\n", "    return w"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"data": {"text/plain": ["matrix([[-3.89578088],\n", "        [ 1.19303364]])"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["final_w2=LSM(X, y)#感觉和批量梯度下降的theta的值有点差距\n", "final_w2"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [], "source": ["#梯度下降得到的结果是matrix([[-3.24140214,  1.1272942 ]])"]}, {"cell_type": "markdown", "metadata": {"collapsed": true}, "source": ["## 参考\n", "- 机器学习，吴恩达\n", "- 《统计学习方法》，李航"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 1}