{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["机器学习实验3 朴素贝叶斯\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["1．朴素贝叶斯法是典型的生成学习方法。生成方法由训练数据学习联合概率分布\n", "$P(X,Y)$，然后求得后验概率分布$P(Y|X)$。具体来说，利用训练数据学习$P(X|Y)$和$P(Y)$的估计，得到联合概率分布：\n", "\n", "$$P(X,Y)＝P(Y)P(X|Y)$$\n", "\n", "概率估计方法可以是极大似然估计或贝叶斯估计。\n", "\n", "2．朴素贝叶斯法的基本假设是条件独立性，\n", "\n", "$$\\begin{aligned} P(X&=x | Y=c_{k} )=P\\left(X^{(1)}=x^{(1)}, \\cdots, X^{(n)}=x^{(n)} | Y=c_{k}\\right) \\\\ &=\\prod_{j=1}^{n} P\\left(X^{(j)}=x^{(j)} | Y=c_{k}\\right) \\end{aligned}$$\n", "\n", "\n", "这是一个较强的假设。由于这一假设，模型包含的条件概率的数量大为减少，朴素贝叶斯法的学习与预测大为简化。因而朴素贝叶斯法高效，且易于实现。其缺点是分类的性能不一定很高。\n", "\n", "3．朴素贝叶斯法利用贝叶斯定理与学到的联合概率模型进行分类预测。\n", "\n", "$$P(Y | X)=\\frac{P(X, Y)}{P(X)}=\\frac{P(Y) P(X | Y)}{\\sum_{Y} P(Y) P(X | Y)}$$\n", " \n", "将输入$x$分到后验概率最大的类$y$。\n", "\n", "$$y=\\arg \\max _{c_{k}} P\\left(Y=c_{k}\\right) \\prod_{j=1}^{n} P\\left(X_{j}=x^{(j)} | Y=c_{k}\\right)$$\n", "\n", "后验概率最大等价于0-1损失函数时的期望风险最小化。\n", "\n", "\n", "模型：\n", "\n", "- 高斯模型\n", "- 多项式模型\n", "- 伯努利模型"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "from sklearn.datasets import load_iris\n", "from sklearn.model_selection import train_test_split\n", "from collections import Counter\n", "import math"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# data\n", "def create_data():\n", "    iris = load_iris()\n", "    df = pd.DataFrame(iris.data, columns=iris.feature_names)\n", "    df['label'] = iris.target\n", "    df.columns = [\n", "        'sepal length', 'sepal width', 'petal length', 'petal width', 'label'\n", "    ]\n", "    data = np.array(df.iloc[:100, :])\n", "    # print(data)\n", "    return data[:, :-1], data[:, -1]"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["X, y = create_data()\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[5.1, 3.5, 1.4, 0.2],\n", "       [4.9, 3. , 1.4, 0.2],\n", "       [4.7, 3.2, 1.3, 0.2],\n", "       [4.6, 3.1, 1.5, 0.2],\n", "       [5. , 3.6, 1.4, 0.2],\n", "       [5.4, 3.9, 1.7, 0.4],\n", "       [4.6, 3.4, 1.4, 0.3],\n", "       [5. , 3.4, 1.5, 0.2],\n", "       [4.4, 2.9, 1.4, 0.2],\n", "       [4.9, 3.1, 1.5, 0.1],\n", "       [5.4, 3.7, 1.5, 0.2],\n", "       [4.8, 3.4, 1.6, 0.2],\n", "       [4.8, 3. , 1.4, 0.1],\n", "       [4.3, 3. , 1.1, 0.1],\n", "       [5.8, 4. , 1.2, 0.2],\n", "       [5.7, 4.4, 1.5, 0.4],\n", "       [5.4, 3.9, 1.3, 0.4],\n", "       [5.1, 3.5, 1.4, 0.3],\n", "       [5.7, 3.8, 1.7, 0.3],\n", "       [5.1, 3.8, 1.5, 0.3],\n", "       [5.4, 3.4, 1.7, 0.2],\n", "       [5.1, 3.7, 1.5, 0.4],\n", "       [4.6, 3.6, 1. , 0.2],\n", "       [5.1, 3.3, 1.7, 0.5],\n", "       [4.8, 3.4, 1.9, 0.2],\n", "       [5. , 3. , 1.6, 0.2],\n", "       [5. , 3.4, 1.6, 0.4],\n", "       [5.2, 3.5, 1.5, 0.2],\n", "       [5.2, 3.4, 1.4, 0.2],\n", "       [4.7, 3.2, 1.6, 0.2],\n", "       [4.8, 3.1, 1.6, 0.2],\n", "       [5.4, 3.4, 1.5, 0.4],\n", "       [5.2, 4.1, 1.5, 0.1],\n", "       [5.5, 4.2, 1.4, 0.2],\n", "       [4.9, 3.1, 1.5, 0.2],\n", "       [5. , 3.2, 1.2, 0.2],\n", "       [5.5, 3.5, 1.3, 0.2],\n", "       [4.9, 3.6, 1.4, 0.1],\n", "       [4.4, 3. , 1.3, 0.2],\n", "       [5.1, 3.4, 1.5, 0.2],\n", "       [5. , 3.5, 1.3, 0.3],\n", "       [4.5, 2.3, 1.3, 0.3],\n", "       [4.4, 3.2, 1.3, 0.2],\n", "       [5. , 3.5, 1.6, 0.6],\n", "       [5.1, 3.8, 1.9, 0.4],\n", "       [4.8, 3. , 1.4, 0.3],\n", "       [5.1, 3.8, 1.6, 0.2],\n", "       [4.6, 3.2, 1.4, 0.2],\n", "       [5.3, 3.7, 1.5, 0.2],\n", "       [5. , 3.3, 1.4, 0.2],\n", "       [7. , 3.2, 4.7, 1.4],\n", "       [6.4, 3.2, 4.5, 1.5],\n", "       [6.9, 3.1, 4.9, 1.5],\n", "       [5.5, 2.3, 4. , 1.3],\n", "       [6.5, 2.8, 4.6, 1.5],\n", "       [5.7, 2.8, 4.5, 1.3],\n", "       [6.3, 3.3, 4.7, 1.6],\n", "       [4.9, 2.4, 3.3, 1. ],\n", "       [6.6, 2.9, 4.6, 1.3],\n", "       [5.2, 2.7, 3.9, 1.4],\n", "       [5. , 2. , 3.5, 1. ],\n", "       [5.9, 3. , 4.2, 1.5],\n", "       [6. , 2.2, 4. , 1. ],\n", "       [6.1, 2.9, 4.7, 1.4],\n", "       [5.6, 2.9, 3.6, 1.3],\n", "       [6.7, 3.1, 4.4, 1.4],\n", "       [5.6, 3. , 4.5, 1.5],\n", "       [5.8, 2.7, 4.1, 1. ],\n", "       [6.2, 2.2, 4.5, 1.5],\n", "       [5.6, 2.5, 3.9, 1.1],\n", "       [5.9, 3.2, 4.8, 1.8],\n", "       [6.1, 2.8, 4. , 1.3],\n", "       [6.3, 2.5, 4.9, 1.5],\n", "       [6.1, 2.8, 4.7, 1.2],\n", "       [6.4, 2.9, 4.3, 1.3],\n", "       [6.6, 3. , 4.4, 1.4],\n", "       [6.8, 2.8, 4.8, 1.4],\n", "       [6.7, 3. , 5. , 1.7],\n", "       [6. , 2.9, 4.5, 1.5],\n", "       [5.7, 2.6, 3.5, 1. ],\n", "       [5.5, 2.4, 3.8, 1.1],\n", "       [5.5, 2.4, 3.7, 1. ],\n", "       [5.8, 2.7, 3.9, 1.2],\n", "       [6. , 2.7, 5.1, 1.6],\n", "       [5.4, 3. , 4.5, 1.5],\n", "       [6. , 3.4, 4.5, 1.6],\n", "       [6.7, 3.1, 4.7, 1.5],\n", "       [6.3, 2.3, 4.4, 1.3],\n", "       [5.6, 3. , 4.1, 1.3],\n", "       [5.5, 2.5, 4. , 1.3],\n", "       [5.5, 2.6, 4.4, 1.2],\n", "       [6.1, 3. , 4.6, 1.4],\n", "       [5.8, 2.6, 4. , 1.2],\n", "       [5. , 2.3, 3.3, 1. ],\n", "       [5.6, 2.7, 4.2, 1.3],\n", "       [5.7, 3. , 4.2, 1.2],\n", "       [5.7, 2.9, 4.2, 1.3],\n", "       [6.2, 2.9, 4.3, 1.3],\n", "       [5.1, 2.5, 3. , 1.1],\n", "       [5.7, 2.8, 4.1, 1.3]])"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["X"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "       0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "       0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 1.,\n", "       1., 1., 1., 1., 1., 1., 1., 1., 1., 1., 1., 1., 1., 1., 1., 1., 1.,\n", "       1., 1., 1., 1., 1., 1., 1., 1., 1., 1., 1., 1., 1., 1., 1., 1., 1.,\n", "       1., 1., 1., 1., 1., 1., 1., 1., 1., 1., 1., 1., 1., 1., 1.])"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["y"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["(array([5.6, 2.9, 3.6, 1.3]), 1.0)"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["X_test[0], y_test[0]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["参考：https://machinelearningmastery.com/naive-bayes-classifier-scratch-python/\n", "\n", "## GaussianNB 高斯朴素贝叶斯\n", "\n", "特征的可能性被假设为高斯\n", "\n", "概率密度函数：\n", "$$P(x_i | y_k)=\\frac{1}{\\sqrt{2\\pi\\sigma^2_{yk}}}exp(-\\frac{(x_i-\\mu_{yk})^2}{2\\sigma^2_{yk}})$$\n", "\n", "数学期望(mean)：$\\mu$\n", "\n", "方差：$\\sigma^2=\\frac{\\sum(X-\\mu)^2}{N}$"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["class NaiveBayes:\n", "    def __init__(self):\n", "        self.model = None\n", "\n", "    # 数学期望\n", "    @staticmethod\n", "    def mean(X):\n", "        return sum(X) / float(len(X))\n", "\n", "    # 标准差（方差）\n", "    def stdev(self, X):\n", "        avg = self.mean(X)\n", "        return math.sqrt(sum([pow(x - avg, 2) for x in X]) / float(len(X)))\n", "\n", "    # 概率密度函数\n", "    def gaussian_probability(self, x, mean, stdev):\n", "        exponent = math.exp(-(math.pow(x - mean, 2) /\n", "                              (2 * math.pow(stdev, 2))))\n", "        return (1 / (math.sqrt(2 * math.pi) * stdev)) * exponent\n", "\n", "    # 处理X_train\n", "    def summarize(self, train_data):\n", "        summaries = [(self.mean(i), self.stdev(i)) for i in zip(*train_data)]\n", "        return summaries\n", "\n", "    # 分类别求出数学期望和标准差\n", "    def fit(self, X, y):\n", "        labels = list(set(y))\n", "        data = {label: [] for label in labels}\n", "        for f, label in zip(X, y):\n", "            data[label].append(f)\n", "        self.model = {\n", "            label: self.summarize(value)\n", "            for label, value in data.items()\n", "        }\n", "        return 'gaussianNB train done!'\n", "\n", "    # 计算概率\n", "    def calculate_probabilities(self, input_data):\n", "        # summaries:{0.0: [(5.0, 0.37),(3.42, 0.40)], 1.0: [(5.8, 0.449),(2.7, 0.27)]}\n", "        # input_data:[1.1, 2.2]\n", "        probabilities = {}\n", "        for label, value in self.model.items():\n", "            probabilities[label] = 1\n", "            for i in range(len(value)):\n", "                mean, stdev = value[i]\n", "                probabilities[label] *= self.gaussian_probability(\n", "                    input_data[i], mean, stdev)\n", "        return probabilities\n", "\n", "    # 类别\n", "    def predict(self, X_test):\n", "        # {0.0: 2.9680340789325763e-27, 1.0: 3.5749783019849535e-26}\n", "        label = sorted(self.calculate_probabilities(X_test).items(),\n", "                       key=lambda x: x[-1])[-1][0]\n", "        return label\n", "\n", "    def score(self, X_test, y_test):\n", "        right = 0\n", "        for X, y in zip(X_test, y_test):\n", "            label = self.predict(X)\n", "            if label == y:\n", "                right += 1\n", "\n", "        return right / float(len(X_test))"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["model = NaiveBayes()"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["'gaussianNB train done!'"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["model.fit(X_train, y_train)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.0\n"]}], "source": ["print(model.predict([4.4,  3.2,  1.3,  0.2]))"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["1.0"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["model.score(X_test, y_test)"]}, {"cell_type": "markdown", "metadata": {"collapsed": true}, "source": ["### scikit-learn实例"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["from sklearn.naive_bayes import GaussianNB, BernoulliNB, MultinomialNB\n", "# 高斯模型、伯努利模型和多项式模型"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["GaussianNB()"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["clf = GaussianNB()\n", "clf.fit(X_train, y_train)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["1.0"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["clf.score(X_test, y_test)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([0.])"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["clf.predict([[4.4,  3.2,  1.3,  0.2]])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 参考\n", "- Prof. <PERSON> Machine Learning. Stanford University\n", "- 李航，《统计学习方法》，清华大学出版社"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 2}