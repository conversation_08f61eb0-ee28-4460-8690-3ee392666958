# 机器学习实验报告说明

## 📁 文件清单

### 主要文件
- **机器学习实验报告.md** - 完整的Markdown格式实验报告
- **机器学习实验报告_简版.docx** - Word格式实验报告（可直接使用）
- **实验结果生成器.py** - 生成实验图表的Python脚本
- **转换为Word.py** - Markdown转Word的转换工具

### 实验结果图表
- **实验一_线性回归结果.png** - 线性回归参数调整结果图
- **实验二_逻辑回归结果.png** - 逻辑回归参数调整结果图  
- **实验三_朴素贝叶斯结果.png** - 朴素贝叶斯分类结果图
- **实验四_聚类结果.png** - K-means聚类结果图
- **实验总结对比.png** - 四个实验的总结对比图

## 📊 实验内容概述

### 实验一：线性回归模型
- **参数调整**：学习率(0.001-1.0)、迭代次数(100-2000)、特征缩放
- **主要发现**：α=0.01为最优学习率，特征缩放提升收敛速度87.5%
- **性能指标**：单变量R²=0.70，多变量R²=0.73

### 实验二：逻辑回归模型  
- **参数调整**：学习率(0.001-0.1)、正则化参数λ(0-10)
- **主要发现**：λ=1时达到最佳偏差-方差平衡
- **性能指标**：训练准确率83%，测试准确率80%

### 实验三：朴素贝叶斯分类器
- **参数调整**：数据集大小(30-100)、测试集比例(20%-50%)
- **主要发现**：条件独立假设在鸢尾花数据集上成立
- **性能指标**：准确率100%，花瓣长度为最重要特征

### 实验四：K-means聚类
- **参数调整**：K值(2-5)、迭代次数(5-50)、初始化方法
- **主要发现**：通过肘部法则确定K=3为最优
- **性能指标**：轮廓系数0.73，8次迭代收敛

## 🔧 使用说明

### 查看实验报告
1. **Word版本**：直接打开 `机器学习实验报告_简版.docx`
2. **Markdown版本**：使用支持Markdown的编辑器打开 `机器学习实验报告.md`

### 重新生成图表
```bash
python 实验结果生成器.py
```

### 转换为Word文档
```bash
python 转换为Word.py
```

## 📈 实验结果汇总

| 实验 | 算法类型 | 最优参数 | 性能指标 | 主要发现 |
|------|----------|----------|----------|----------|
| 实验一 | 线性回归 | α=0.01, iter=1000 | R²=0.73 | 特征缩放提升收敛速度87.5% |
| 实验二 | 逻辑回归 | α=0.01, λ=1 | 准确率=80% | 正则化防止过拟合，提升泛化能力 |
| 实验三 | 朴素贝叶斯 | 样本=100, 测试=30% | 准确率=100% | 条件独立假设在该数据集上成立 |
| 实验四 | K-means | K=3, iter=10 | 轮廓系数=0.73 | 肘部法则有效确定最优聚类数 |

## 🎯 核心发现

### 参数调整的重要性
1. **学习率**：影响收敛速度和稳定性，需要在收敛速度和稳定性间平衡
2. **正则化参数**：平衡偏差和方差，防止过拟合
3. **迭代次数**：确保充分训练但避免过拟合
4. **聚类数量**：影响聚类粒度和效果

### 算法特点总结
1. **线性回归**：适用于连续值预测，对特征缩放敏感
2. **逻辑回归**：适用于分类问题，正则化效果显著
3. **朴素贝叶斯**：基于概率的分类，在特征独立时效果优异
4. **K-means聚类**：无监督学习，在数据探索中应用广泛

## 💡 实验体会

1. **理论与实践结合**：通过实际编程加深了对算法原理的理解
2. **参数敏感性**：不同算法对参数的敏感性不同，需要仔细调试
3. **数据预处理重要性**：特征缩放等预处理步骤对算法效果影响显著
4. **评估指标多样性**：需要使用多种指标全面评估模型性能

## 📝 报告特色

- ✅ 完整的四个实验内容
- ✅ 详细的参数调整过程
- ✅ 丰富的实验结果图表
- ✅ 深入的结果分析
- ✅ 实用的总结与体会
- ✅ 标准的学术报告格式

---

**实验完成日期：** 2024年12月  
**报告生成日期：** 2024年12月  
**技术支持：** Python + Matplotlib + Scikit-learn
