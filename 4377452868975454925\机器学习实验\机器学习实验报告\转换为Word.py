#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
将Markdown实验报告转换为Word文档
"""

import os
import subprocess
import sys

def install_pandoc():
    """安装pandoc（如果需要）"""
    try:
        subprocess.run(['pandoc', '--version'], check=True, capture_output=True)
        print("Pandoc已安装")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("Pandoc未安装，请手动安装pandoc")
        print("下载地址：https://pandoc.org/installing.html")
        return False

def convert_md_to_docx():
    """转换Markdown到Word文档"""
    input_file = "机器学习实验报告.md"
    output_file = "机器学习实验报告.docx"
    
    if not os.path.exists(input_file):
        print(f"错误：找不到文件 {input_file}")
        return False
    
    try:
        # 使用pandoc转换
        cmd = [
            'pandoc',
            input_file,
            '-o', output_file,
            '--from', 'markdown',
            '--to', 'docx',
            '--reference-doc', 'template.docx' if os.path.exists('template.docx') else None
        ]
        
        # 移除None值
        cmd = [x for x in cmd if x is not None]
        
        subprocess.run(cmd, check=True)
        print(f"成功转换为 {output_file}")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"转换失败：{e}")
        return False

def create_simple_word_report():
    """创建简单的Word报告（备用方案）"""
    try:
        from docx import Document
        from docx.shared import Inches
        
        # 创建新文档
        doc = Document()
        
        # 添加标题
        title = doc.add_heading('机器学习实验报告', 0)
        
        # 添加基本信息
        doc.add_paragraph('学生姓名：[学生姓名]')
        doc.add_paragraph('学号：[学号]')
        doc.add_paragraph('专业：光电信息与计算机工程')
        doc.add_paragraph('实验日期：2024年12月')
        
        # 读取Markdown内容并简单转换
        with open('机器学习实验报告.md', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 简单的Markdown到Word转换
        lines = content.split('\n')
        for line in lines:
            if line.startswith('# '):
                doc.add_heading(line[2:], level=1)
            elif line.startswith('## '):
                doc.add_heading(line[3:], level=2)
            elif line.startswith('### '):
                doc.add_heading(line[4:], level=3)
            elif line.startswith('#### '):
                doc.add_heading(line[5:], level=4)
            elif line.startswith('!['):
                # 处理图片
                if '](' in line and ')' in line:
                    img_path = line.split('](')[1].split(')')[0]
                    if os.path.exists(img_path):
                        try:
                            doc.add_picture(img_path, width=Inches(6))
                        except:
                            doc.add_paragraph(f"[图片: {img_path}]")
            elif line.startswith('|') and '|' in line[1:]:
                # 简单表格处理
                doc.add_paragraph(line)
            elif line.strip():
                doc.add_paragraph(line)
        
        # 保存文档
        doc.save('机器学习实验报告_简版.docx')
        print("成功创建简版Word报告：机器学习实验报告_简版.docx")
        return True
        
    except ImportError:
        print("python-docx库未安装，请运行：pip install python-docx")
        return False
    except Exception as e:
        print(f"创建Word文档失败：{e}")
        return False

def main():
    """主函数"""
    print("机器学习实验报告转换工具")
    print("=" * 40)
    
    # 方法1：使用pandoc转换（推荐）
    if install_pandoc():
        if convert_md_to_docx():
            print("转换完成！")
            return
    
    # 方法2：使用python-docx创建简版（备用）
    print("\n尝试备用方案...")
    if create_simple_word_report():
        print("备用方案完成！")
        return
    
    # 方法3：手动转换说明
    print("\n自动转换失败，请手动转换：")
    print("1. 安装pandoc：https://pandoc.org/installing.html")
    print("2. 运行命令：pandoc 机器学习实验报告.md -o 机器学习实验报告.docx")
    print("3. 或者将Markdown内容复制到Word中手动格式化")

if __name__ == "__main__":
    main()
