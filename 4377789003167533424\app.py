from flask import Flask, request, jsonify
from flask import render_template
import os
import base64
from spike import predict
from infer import predict_docu
from face import predict_face
from cat import predict_cat
from model import rmbg, get_color_from_name
app = Flask(__name__)

@app.route("/")
def hello():
    return render_template('index.html')

def return_img_stream(img_local_path):
    '''
    发送本地图片
    '''
    import base64
    img_stream = ''
    with open(img_local_path,'rb') as img_f:
        img_stream = img_f.read()
        img_stream = base64.b64encode(img_stream).decode()
    return img_stream

@app.route("/sh",methods=['GET','POST'])
def imgshow():
    img_path = './'+str(filename)
    img_stream = return_img_stream(img_path)
    return render_template('detect.html',img_stream=img_stream)

@app.route("/detect", methods=['GET', 'POST'])
def detect():
    if request.method == 'POST':
        f = request.files['file']  # 获取客户端传输的名为file的文件
        global filename, original_img_stream, result_img_stream
        filename = f.filename

        # 获取当前文件夹
        file_path = os.path.join(os.getcwd(), filename)
        f.save(file_path)

        # 保存原图的base64编码
        original_img_stream = return_img_stream(file_path)

        # 进行头盔检测
        result_path = predict(file_path)

        # 保存检测结果的base64编码
        if result_path and os.path.exists(result_path):
            result_img_stream = return_img_stream(result_path)
        else:
            result_img_stream = original_img_stream  # 如果检测失败，显示原图

        return render_template('detect.html',
                             original_img=original_img_stream,
                             result_img=result_img_stream,
                             show_results=True)

    return render_template('detect.html', show_results=False)

@app.route("/document",methods=['GET','POST'])
def document():
    if request.method=='POST':
        f = request.files['file'] #获取客户端传输的名为file的文件
        global filename
        filename = f.filename
        global filepath #获取当期文件夹
        file_path = os.path.join(os.getcwd(),filename)
        f.save(file_path)
        predict_docu(file_path)
    return render_template('document.html')


@app.route("/face_detect", methods=['GET', 'POST'])
def face():
    if request.method == 'POST':
        f = request.files['file']  # 获取客户端传输的名为file的文件
        global filename, original_img_stream, result_img_stream
        filename = f.filename

        # 获取当前文件夹
        file_path = os.path.join(os.getcwd(), filename)
        f.save(file_path)

        # 保存原图的base64编码
        original_img_stream = return_img_stream(file_path)

        # 进行人脸检测
        predict_face(file_path)
        result_img_stream = return_img_stream(file_path)  # 人脸检测会修改原图

        return render_template('face.html',
                             original_img=original_img_stream,
                             result_img=result_img_stream,
                             show_results=True)

    return render_template('face.html', show_results=False)

@app.route("/photo_process", methods=['GET', 'POST'])
def photo_process():
    if request.method == 'POST':
        f = request.files['file']  # 获取客户端传输的名为file的文件
        background_color = request.form.get('background_color', '白色')
        size_option = request.form.get('size_option', '保持原图大小')
        width = request.form.get('width', '295')
        height = request.form.get('height', '413')

        global filename, original_img_stream, nobg_img_stream, photo_img_stream
        filename = f.filename

        # 获取当前文件夹
        file_path = os.path.join(os.getcwd(), filename)
        f.save(file_path)

        # 保存原图的base64编码
        original_img_stream = return_img_stream(file_path)

        # 获取背景颜色RGB值
        bg_color = get_color_from_name(background_color)

        # 进行证件照处理
        try:
            nobg_path, photo_path = rmbg(file_path, bg_color, width, height, size_option)

            # 保存处理结果的base64编码
            if nobg_path and os.path.exists(nobg_path):
                nobg_img_stream = return_img_stream(nobg_path)
            else:
                nobg_img_stream = original_img_stream

            if photo_path and os.path.exists(photo_path):
                photo_img_stream = return_img_stream(photo_path)
            else:
                photo_img_stream = original_img_stream

            return render_template('photo.html',
                                 original_img=original_img_stream,
                                 nobg_img=nobg_img_stream,
                                 photo_img=photo_img_stream,
                                 show_results=True,
                                 selected_color=background_color)
        except Exception as e:
            print(f"证件照处理出错: {str(e)}")
            return render_template('photo.html',
                                 original_img=original_img_stream,
                                 show_results=False,
                                 error_message=str(e))

    return render_template('photo.html', show_results=False)

@app.route("/cat_detect",methods=['GET','POST'])
def cat():
    if request.method=='POST':
        f = request.files['file'] #获取客户端传输的名为file的文件
        global filename
        filename = f.filename
        global filepath #获取当期文件夹
        file_path = os.path.join(os.getcwd(),filename)
        f.save(file_path)
        predict_cat(file_path)
    return render_template('cat.html')

if __name__ == '__main__':
    app.run(host="0.0.0.0",port=5000)
