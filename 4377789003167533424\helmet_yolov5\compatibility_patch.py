"""
PyTorch兼容性补丁
解决 'Upsample' object has no attribute 'recompute_scale_factor' 错误
"""

import torch
import torch.nn as nn

# 保存原始的Upsample类
_original_upsample = nn.Upsample

class CompatibleUpsample(nn.Upsample):
    """兼容性Upsample类，添加recompute_scale_factor属性"""
    
    def __init__(self, *args, **kwargs):
        # 移除recompute_scale_factor参数（如果存在）
        kwargs.pop('recompute_scale_factor', None)
        super().__init__(*args, **kwargs)
        # 添加recompute_scale_factor属性以保持兼容性
        self.recompute_scale_factor = None

# 替换PyTorch的Upsample类
nn.Upsample = CompatibleUpsample
torch.nn.Upsample = CompatibleUpsample

print("已应用PyTorch兼容性补丁")
