"""
修复PyTorch版本兼容性问题的脚本
解决 'Upsample' object has no attribute 'recompute_scale_factor' 错误
"""

import torch
import sys
from pathlib import Path

def fix_model_compatibility(model_path, output_path=None):
    """
    修复模型权重文件的兼容性问题
    
    Args:
        model_path: 原始模型权重文件路径
        output_path: 修复后的模型权重文件路径，如果为None则覆盖原文件
    """
    if output_path is None:
        output_path = model_path
    
    print(f"正在加载模型: {model_path}")
    
    # 加载模型权重
    checkpoint = torch.load(model_path, map_location='cpu')
    
    # 获取模型状态字典
    if 'model' in checkpoint:
        model_state = checkpoint['model']
    else:
        model_state = checkpoint
    
    # 修复Upsample层的兼容性问题
    fixed_state = {}
    for key, value in model_state.state_dict().items():
        # 跳过recompute_scale_factor相关的键
        if 'recompute_scale_factor' not in key:
            fixed_state[key] = value
    
    # 重新构建模型状态
    model_state.load_state_dict(fixed_state, strict=False)
    
    # 手动移除Upsample模块中的recompute_scale_factor属性
    def remove_recompute_scale_factor(module):
        for child in module.children():
            if isinstance(child, torch.nn.Upsample):
                if hasattr(child, 'recompute_scale_factor'):
                    delattr(child, 'recompute_scale_factor')
            remove_recompute_scale_factor(child)
    
    remove_recompute_scale_factor(model_state)
    
    # 保存修复后的模型
    print(f"正在保存修复后的模型: {output_path}")
    torch.save(checkpoint, output_path)
    print("模型修复完成!")

if __name__ == "__main__":
    # 修复helmet.pt模型
    model_path = "helmet.pt"
    if Path(model_path).exists():
        fix_model_compatibility(model_path)
        print(f"已修复模型文件: {model_path}")
    else:
        print(f"模型文件不存在: {model_path}")
