from model import rmbg
import cv2

def predict_docu(input_img):

    # 证件照的背景颜色
    # color = "#FFFFFF" # 白色（用于护照、签证、身份证等）
    color = "#438EDB" # 蓝色（用于毕业证、工作证等）
    # color = "#FF0000" # 红色（用于一些特殊的证件照）

    # 证件照的大小
    width = 295
    height = 413  # 一寸（295像素 x 413像素）

    # 是否保持原图大小
    # size_opt = "不保持原图大小"
    size_opt = "保持原图大小" # 如果选了这个会保持输入图片的大小，忽略上面的 证件照的大小 参数



    # color, width, height 这三个参数不影响抠图，只会影响证件照的结果
    out_path, output_image_path = rmbg(input_img, color, width, height, size_opt)

    print('抠图后的图片： ', out_path)
    print('证件照： ', output_image_path)
