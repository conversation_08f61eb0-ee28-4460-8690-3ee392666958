import onnxruntime as ort
import cv2
import numpy as np
from PIL import Image, ImageDraw
import re
from skimage import io

class BriaRMBG_ONNX:
    def __init__(self, model_path):
        self.session = ort.InferenceSession(model_path)
    
    def __call__(self, input_tensor):
        input_name = self.session.get_inputs()[0].name
        outputs = self.session.run(None, {input_name: input_tensor})
        return outputs

def preprocess_image(im: np.ndarray, model_input_size: list) -> np.ndarray:
    if len(im.shape) < 3:
        im = cv2.cvtColor(im, cv2.COLOR_GRAY2BGR)  # 将灰度图像转换为BGR格式
    im = cv2.resize(im, model_input_size, interpolation=cv2.INTER_LINEAR)  # 调整图像大小
    im = im.astype(np.float32)  # 将图像数据类型转换为float32
    im /= 255.0  # 归一化到[0, 1]范围
    mean = [0.5, 0.5, 0.5]
    std = [1.0, 1.0, 1.0]
    im -= mean
    im /= std
    return im[np.newaxis, :, :, :]

def postprocess_image(result: np.ndarray, im_size: list) -> np.ndarray:
    result = result[0]  # 移除batch维度
    ma = np.max(result)
    mi = np.min(result)
    result = (result - mi) / (ma - mi)  # 归一化到[0, 1]范围
    result = (result * 255).astype(np.uint8)  # 将数据类型转换回uint8
    result = cv2.resize(result, [im_size[1], im_size[0]], interpolation=cv2.INTER_LINEAR)  # 调整图像大小
    return result


def add_background_to_image(input_image_path, output_image_path, background_color, out_size=None):
    """
    给透明背景的PNG人像图像添加任意颜色的背景。

    :param input_image_path: 输入图像的路径
    :param output_image_path: 输出图像的路径
    :param background_color: 背景颜色 (R, G, B)
    :param size: 输出图像的大小 (width, height) 默认与输入图像相同
    """
    # 打开输入图像
    image = Image.open(input_image_path)

    # 如果图像不是PNG格式，先转换为PNG
    if image.format != 'PNG':
        image = image.convert('RGBA')
    
    if out_size is None:
        out_size = image.size

    image
    out_image = Image.new('RGB', image.size, background_color)
    out_image.paste(image, (0,0), image)

    out_image.resize(out_size)

    # 保存新的图像
    out_image.save(output_image_path)

def rmbg(input_image_path, background_color, out_size_w, out_size_h, size_opt):
    import os

    if size_opt == "保持原图大小":
        shape = cv2.imread(input_image_path).shape
        out_size = (int(shape[1]), int(shape[0]))  # 注意：PIL使用(width, height)
    else:
        out_size = (int(out_size_w), int(out_size_h))

    # 确保results目录存在
    os.makedirs("results", exist_ok=True)

    # 生成输出文件路径，保存到results目录
    filename = os.path.basename(input_image_path)
    name, ext = os.path.splitext(filename)

    # 抠图文件路径（透明背景）
    out_path = os.path.join("results", f"{name}_nobg.png")
    # 证件照文件路径（有背景）
    output_image_path = os.path.join("results", f"{name}_photo{ext}")

    net = BriaRMBG_ONNX(f"./model.onnx")

    # prepare input
    model_input_size = [1024, 1024]
    orig_im = io.imread(input_image_path)
    orig_im_size = orig_im.shape[0:2]
    image = preprocess_image(orig_im, model_input_size)
    image = np.transpose(image, (0, 3, 1, 2))  # ONNX通常需要CHW格式

    # inference
    result = net(image)

    # post process
    result_image = postprocess_image(result[0][0], orig_im_size)

    # save result
    pil_im = Image.fromarray(result_image)
    no_bg_image = Image.new("RGBA", pil_im.size, (0, 0, 0, 0))
    orig_image = Image.open(input_image_path)
    no_bg_image.paste(orig_image, mask=pil_im)
    no_bg_image.save(out_path)

    print(f"背景颜色: {background_color}, 输出尺寸: {out_size}")
    add_background_to_image(out_path, output_image_path, background_color, out_size)

    return out_path, output_image_path

def get_color_from_name(color_name):
    """根据颜色名称返回RGB值"""
    color_map = {
        "红色": (255, 0, 0),
        "蓝色": (0, 0, 255),
        "白色": (255, 255, 255),
        "灰色": (128, 128, 128),
        "黑色": (0, 0, 0),
        "绿色": (0, 255, 0),
        "黄色": (255, 255, 0),
        "紫色": (128, 0, 128),
        "橙色": (255, 165, 0),
        "粉色": (255, 192, 203)
    }
    return color_map.get(color_name, (255, 255, 255))  # 默认白色
