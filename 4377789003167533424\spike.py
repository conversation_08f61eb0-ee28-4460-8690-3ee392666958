import cv2
import torch
import sys
import os
from pathlib import Path

# 添加YOLOv5路径到系统路径
current_dir = Path(__file__).parent
yolo_path = current_dir / "helmet_yolov5"
sys.path.append(str(yolo_path))

# 导入兼容性补丁
sys.path.insert(0, str(yolo_path))
import compatibility_patch

# 导入YOLOv5相关模块
from models.experimental import attempt_load
from utils.general import non_max_suppression, scale_coords
from utils.torch_utils import select_device
from utils.datasets import letterbox
import numpy as np

# 全局变量存储模型
model = None
device = None
names = None

def load_model():
    """加载YOLOv5头盔检测模型"""
    global model, device, names

    if model is None:
        print("正在加载YOLOv5头盔检测模型...")
        device = select_device('cpu')  # 使用CPU

        # 模型权重文件路径
        weights_path = yolo_path / "helmet.pt"

        # 加载模型
        model = attempt_load(str(weights_path), map_location=device)
        model.eval()

        # 获取类别名称
        names = model.module.names if hasattr(model, 'module') else model.names
        print(f"模型加载完成，检测类别: {names}")

    return model, device, names

def predict(img_path):
    """使用YOLOv5进行头盔检测"""
    try:
        # 加载模型
        model, device, names = load_model()

        # 读取图像
        img0 = cv2.imread(img_path)
        if img0 is None:
            print(f"无法读取图像: {img_path}")
            return None

        # 创建结果图像的副本，不修改原图
        result_img = img0.copy()

        # 图像预处理
        img_size = 640
        img = letterbox(img0, img_size, stride=32)[0]
        img = img[:, :, ::-1].transpose(2, 0, 1)  # BGR to RGB, to 3x416x416
        img = np.ascontiguousarray(img)
        img = torch.from_numpy(img).to(device)
        img = img.float() / 255.0  # 0 - 255 to 0.0 - 1.0
        if img.ndimension() == 3:
            img = img.unsqueeze(0)

        # 推理
        with torch.no_grad():
            pred = model(img)[0]

        # NMS
        pred = non_max_suppression(pred, conf_thres=0.25, iou_thres=0.45)

        # 处理检测结果
        detection_count = 0
        for i, det in enumerate(pred):
            if len(det):
                # 将坐标缩放回原图尺寸
                det[:, :4] = scale_coords(img.shape[2:], det[:, :4], img0.shape).round()

                # 绘制检测框
                for *xyxy, conf, cls in reversed(det):
                    label = f'{names[int(cls)]} {conf:.2f}'
                    x1, y1, x2, y2 = map(int, xyxy)

                    # 根据类别设置颜色
                    if names[int(cls)] == 'hat':
                        color = (255, 0, 0)  # 蓝色 (BGR格式)
                    else:
                        color = (0, 0, 255)  # 红色 (BGR格式)

                    # 绘制边界框
                    cv2.rectangle(result_img, (x1, y1), (x2, y2), color, 2)

                    # 绘制标签
                    font = cv2.FONT_HERSHEY_DUPLEX
                    cv2.putText(result_img, label, (x1, y1 - 10), font, 0.8, color, 1)

                    print(f"检测到: {label} 位置: ({x1}, {y1}, {x2}, {y2})")
                    detection_count += 1

        # 生成结果文件路径
        import os
        filename = os.path.basename(img_path)
        name, ext = os.path.splitext(filename)
        result_path = os.path.join("results", f"{name}_detected{ext}")

        # 确保results目录存在
        os.makedirs("results", exist_ok=True)

        # 保存结果图像
        cv2.imwrite(result_path, result_img)
        print(f"检测结果已保存到: {result_path}")
        print(f"共检测到 {detection_count} 个目标")

        return result_path

    except Exception as e:
        print(f"头盔检测出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

