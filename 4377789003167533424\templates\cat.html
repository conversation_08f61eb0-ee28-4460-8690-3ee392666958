<!DOCTYPE html>
<html lang="en">
  <head>
    <title>猫脸识别</title>
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <!-- <link rel="stylesheet" href="D:\WEB\static\style.css"> -->
  </head>
  <body>
    <h2 id="title">猫脸识别</h2>
    <br />
    <br />
    <img id="imageDisplay" width="500" height="500" />
    <img id="result_show" src="data:;base64,{{ img_stream }}">
    <br />
    <form method="POST" enctype="multipart/form-data">
      <br />
      <input type="file" id="imageFile" onchange="displayImage()" 
        <input type="file" name="file">
        <br />
        <input id=button_detect  type="submit" value="开始检测">
    </form>
    <br />
    <div id="imagePath"></div>
    <script>
        function displayImage() {
            var fileInput = document.getElementById("imageFile");
            var imagePath = document.getElementById("imagePath");
            var imageDisplay = document.getElementById("imageDisplay");
            var file = fileInput.files[0];
            var reader = new FileReader();
            reader.readAsDataURL(file);
            reader.onload = function (e) {
              //imagePath.innerHTML = "Image Path: " + fileInput.value;
              imageDisplay.src = e.target.result;
            };
      }
      function Show() {
              var img = document.getElementById('img');
              img.src = '/sh';
          }
    </script>
      <!-- <button id="my-button" onclick="Detect()">检测</button> -->
      <a href="http://127.0.0.1:5000/sh">
          <button id="button_show" onclick="Show()">显示图片</button>
      </a>
      
  </body>
  <style>
    #title{
    text-align: center; 
    color: black;
    font-size: xx-large;
    background-color: cadetblue;
}
    #imageDisplay{
        margin-left: 350px;
        position: relative;
    }
    #button_detect{
      width: 100px;
      height: 40px;
      background-color: skyblue;
      font-size: larger;
      margin-left: 900px;
      margin-top: auto;
    }
    #button_show{
      width: 100px;
      height: 40px;
      background-color: skyblue;
      font-size: larger;
      margin-left:900px;
      margin-top: auto;
    }
    #imageFile{
      width: 300px;
      height: 40px;
      background-color:white;
      font-size: larger;
      margin-left:900px;
      margin-top: auto;
    }
    #result_show{
      height: 500px;
      width: 500px;
      margin-left: 200px;
      
    }
  </style>
</html>
