<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <title>安全帽检测系统</title>
    <meta charset="UTF-8">
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }

        #title {
            text-align: center;
            color: white;
            font-size: 2em;
            background-color: #2196F3;
            padding: 20px;
            margin-bottom: 30px;
            border-radius: 10px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .upload-section {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            border: 2px dashed #ddd;
            border-radius: 10px;
        }

        .file-input {
            margin: 20px 0;
        }

        .file-input input[type="file"] {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }

        .submit-btn {
            background-color: #2196F3;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            margin-top: 20px;
        }

        .submit-btn:hover {
            background-color: #1976D2;
        }

        .preview-section {
            text-align: center;
            margin: 20px 0;
        }

        .preview-image {
            max-width: 400px;
            max-height: 400px;
            border: 2px solid #ddd;
            border-radius: 10px;
            margin-top: 10px;
        }

        .results-section {
            margin-top: 30px;
        }

        .image-comparison {
            display: flex;
            justify-content: space-around;
            gap: 20px;
            margin-top: 20px;
            flex-wrap: wrap;
        }

        .image-item {
            text-align: center;
            flex: 1;
            min-width: 300px;
        }

        .image-item h3 {
            margin-bottom: 10px;
            color: #333;
        }

        .image-item img {
            max-width: 100%;
            max-height: 500px;
            border: 2px solid #ddd;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .detection-info {
            background-color: #e3f2fd;
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
            text-align: center;
        }
    </style>
</head>
<body>
    <h1 id="title">安全帽检测系统</h1>

    <div class="container">
        <div class="upload-section">
            <h3>上传图片进行安全帽检测</h3>
            <form method="POST" enctype="multipart/form-data">
                <div class="file-input">
                    <input type="file" id="imageFile" name="file" accept="image/*" onchange="displayImage()" required>
                </div>
                <div class="preview-section">
                    <img id="imageDisplay" class="preview-image" style="display: none;">
                </div>
                <button type="submit" class="submit-btn">开始检测</button>
            </form>
        </div>

        {% if show_results %}
        <div class="results-section">
            <h2>检测结果</h2>
            <div class="image-comparison">
                <div class="image-item">
                    <h3>原始图片</h3>
                    <img src="data:image/jpeg;base64,{{ original_img }}" alt="原始图片">
                </div>
                <div class="image-item">
                    <h3>检测结果</h3>
                    <img src="data:image/jpeg;base64,{{ result_img }}" alt="检测结果">
                </div>
            </div>
            <div class="detection-info">
                <p><strong>检测说明：</strong></p>
                <p>🔵 蓝色框：检测到安全帽</p>
                <p>🔴 红色框：检测到人员</p>
                <p>检测结果已保存到 results 文件夹</p>
            </div>
        </div>
        {% endif %}
    </div>

    <script>
        function displayImage() {
            var fileInput = document.getElementById("imageFile");
            var imageDisplay = document.getElementById("imageDisplay");
            var file = fileInput.files[0];

            if (file) {
                var reader = new FileReader();
                reader.onload = function(e) {
                    imageDisplay.src = e.target.result;
                    imageDisplay.style.display = "block";
                };
                reader.readAsDataURL(file);
            }
        }
    </script>
</body>
  <style>
    #title{
    text-align: center; 
    color: black;
    font-size: xx-large;
    background-color: cadetblue;
}
    #imageDisplay{
        margin-left: 350px;
        position: relative;
    }
    #button_detect{
      width: 100px;
      height: 40px;
      background-color: skyblue;
      font-size: larger;
      margin-left: 900px;
      margin-top: auto;
    }
    #button_show{
      width: 100px;
      height: 40px;
      background-color: skyblue;
      font-size: larger;
      margin-left:900px;
      margin-top: auto;
    }
    #imageFile{
      width: 300px;
      height: 40px;
      background-color:white;
      font-size: larger;
      margin-left:900px;
      margin-top: auto;
    }
    #result_show{
      height: 500px;
      width: 500px;
      margin-left: 200px;
      
    }
  </style>
</html>
