<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <title>证件照处理系统</title>
    <meta charset="UTF-8">
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        #title {
            text-align: center;
            color: white;
            font-size: 2em;
            background-color: #4CAF50;
            padding: 20px;
            margin-bottom: 30px;
            border-radius: 10px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .form-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 10px;
        }
        
        .form-row {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            gap: 15px;
        }
        
        .form-row label {
            min-width: 120px;
            font-weight: bold;
        }
        
        .form-row input, .form-row select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .color-options {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .color-option {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .color-preview {
            width: 20px;
            height: 20px;
            border: 1px solid #ccc;
            border-radius: 3px;
        }
        
        .submit-btn {
            background-color: #4CAF50;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            margin-top: 20px;
        }
        
        .submit-btn:hover {
            background-color: #45a049;
        }
        
        .results-section {
            margin-top: 30px;
        }
        
        .image-container {
            display: flex;
            justify-content: space-around;
            gap: 20px;
            margin-top: 20px;
            flex-wrap: wrap;
        }
        
        .image-item {
            text-align: center;
            flex: 1;
            min-width: 250px;
        }
        
        .image-item h3 {
            margin-bottom: 10px;
            color: #333;
        }
        
        .image-item img {
            max-width: 100%;
            max-height: 400px;
            border: 2px solid #ddd;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .error-message {
            color: red;
            background-color: #ffe6e6;
            padding: 10px;
            border-radius: 5px;
            margin-top: 20px;
        }
        
        .preview-image {
            max-width: 300px;
            max-height: 300px;
            border: 2px solid #ddd;
            border-radius: 10px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <h1 id="title">证件照处理系统</h1>
    
    <div class="container">
        <form method="POST" enctype="multipart/form-data">
            <div class="form-section">
                <h3>选择图片</h3>
                <div class="form-row">
                    <label for="imageFile">选择图片:</label>
                    <input type="file" id="imageFile" name="file" accept="image/*" onchange="displayImage()" required>
                </div>
                <div id="imagePreview" style="text-align: center;">
                    <img id="previewImg" class="preview-image" style="display: none;">
                </div>
            </div>
            
            <div class="form-section">
                <h3>背景颜色设置</h3>
                <div class="form-row">
                    <label>选择背景颜色:</label>
                    <div class="color-options">
                        <div class="color-option">
                            <input type="radio" id="white" name="background_color" value="白色" checked>
                            <div class="color-preview" style="background-color: white;"></div>
                            <label for="white">白色</label>
                        </div>
                        <div class="color-option">
                            <input type="radio" id="red" name="background_color" value="红色">
                            <div class="color-preview" style="background-color: red;"></div>
                            <label for="red">红色</label>
                        </div>
                        <div class="color-option">
                            <input type="radio" id="blue" name="background_color" value="蓝色">
                            <div class="color-preview" style="background-color: blue;"></div>
                            <label for="blue">蓝色</label>
                        </div>
                        <div class="color-option">
                            <input type="radio" id="gray" name="background_color" value="灰色">
                            <div class="color-preview" style="background-color: gray;"></div>
                            <label for="gray">灰色</label>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="form-section">
                <h3>尺寸设置</h3>
                <div class="form-row">
                    <label for="size_option">尺寸选项:</label>
                    <select id="size_option" name="size_option" onchange="toggleSizeInputs()">
                        <option value="保持原图大小">保持原图大小</option>
                        <option value="自定义尺寸">自定义尺寸</option>
                    </select>
                </div>
                <div id="customSize" style="display: none;">
                    <div class="form-row">
                        <label for="width">宽度(像素):</label>
                        <input type="number" id="width" name="width" value="295" min="100" max="2000">
                    </div>
                    <div class="form-row">
                        <label for="height">高度(像素):</label>
                        <input type="number" id="height" name="height" value="413" min="100" max="2000">
                    </div>
                </div>
            </div>
            
            <button type="submit" class="submit-btn">开始处理</button>
        </form>
        
        {% if show_results %}
        <div class="results-section">
            <h2>处理结果</h2>
            <div class="image-container">
                <div class="image-item">
                    <h3>原始图片</h3>
                    <img src="data:image/jpeg;base64,{{ original_img }}" alt="原始图片">
                </div>
                {% if nobg_img %}
                <div class="image-item">
                    <h3>抠图结果</h3>
                    <img src="data:image/png;base64,{{ nobg_img }}" alt="抠图结果">
                </div>
                {% endif %}
                {% if photo_img %}
                <div class="image-item">
                    <h3>证件照 ({{ selected_color }}背景)</h3>
                    <img src="data:image/jpeg;base64,{{ photo_img }}" alt="证件照">
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}
        
        {% if error_message %}
        <div class="error-message">
            处理出错: {{ error_message }}
        </div>
        {% endif %}
    </div>
    
    <script>
        function displayImage() {
            var fileInput = document.getElementById("imageFile");
            var previewImg = document.getElementById("previewImg");
            var file = fileInput.files[0];
            
            if (file) {
                var reader = new FileReader();
                reader.onload = function(e) {
                    previewImg.src = e.target.result;
                    previewImg.style.display = "block";
                };
                reader.readAsDataURL(file);
            }
        }
        
        function toggleSizeInputs() {
            var sizeOption = document.getElementById("size_option").value;
            var customSize = document.getElementById("customSize");
            
            if (sizeOption === "自定义尺寸") {
                customSize.style.display = "block";
            } else {
                customSize.style.display = "none";
            }
        }
        
        // 如果有选中的颜色，保持选中状态
        {% if selected_color %}
        document.addEventListener('DOMContentLoaded', function() {
            var colorInputs = document.querySelectorAll('input[name="background_color"]');
            colorInputs.forEach(function(input) {
                if (input.value === "{{ selected_color }}") {
                    input.checked = true;
                }
            });
        });
        {% endif %}
    </script>
</body>
</html>
