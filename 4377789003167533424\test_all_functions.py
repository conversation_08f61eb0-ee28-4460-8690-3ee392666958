"""
测试所有功能的脚本
"""

import os
from spike import predict
from model import rmbg, get_color_from_name

def test_helmet_detection():
    """测试安全帽检测功能"""
    print("=== 测试安全帽检测功能 ===")
    test_image = "images.png"
    
    if os.path.exists(test_image):
        print(f"使用测试图像: {test_image}")
        result_path = predict(test_image)
        if result_path and os.path.exists(result_path):
            print(f"✅ 安全帽检测成功，结果保存到: {result_path}")
        else:
            print("❌ 安全帽检测失败")
    else:
        print(f"❌ 测试图像不存在: {test_image}")
    print()

def test_photo_processing():
    """测试证件照处理功能"""
    print("=== 测试证件照处理功能 ===")
    test_image = "images.png"
    
    if os.path.exists(test_image):
        print(f"使用测试图像: {test_image}")
        
        # 测试不同背景颜色
        colors = ["白色", "红色", "蓝色"]
        for color in colors:
            print(f"测试 {color} 背景...")
            bg_color = get_color_from_name(color)
            try:
                nobg_path, photo_path = rmbg(test_image, bg_color, 295, 413, "保持原图大小")
                if nobg_path and os.path.exists(nobg_path):
                    print(f"  ✅ 抠图成功: {nobg_path}")
                if photo_path and os.path.exists(photo_path):
                    print(f"  ✅ 证件照生成成功: {photo_path}")
            except Exception as e:
                print(f"  ❌ 处理失败: {str(e)}")
    else:
        print(f"❌ 测试图像不存在: {test_image}")
    print()

def check_results_folder():
    """检查results文件夹"""
    print("=== 检查results文件夹 ===")
    if os.path.exists("results"):
        files = os.listdir("results")
        print(f"results文件夹中有 {len(files)} 个文件:")
        for file in files:
            print(f"  - {file}")
    else:
        print("results文件夹不存在")
    print()

def main():
    print("开始测试所有功能...\n")
    
    # 确保results目录存在
    os.makedirs("results", exist_ok=True)
    
    # 测试各项功能
    test_helmet_detection()
    test_photo_processing()
    check_results_folder()
    
    print("所有测试完成！")
    print("现在可以访问 http://127.0.0.1:5000 查看Web界面")

if __name__ == "__main__":
    main()
