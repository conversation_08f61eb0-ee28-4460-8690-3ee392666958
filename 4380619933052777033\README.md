# 二分类决策树模型实现

本项目严格按照机器学习文档流程实现了一个完整的二分类决策树模型，包括数据生成、可视化、模型训练、预测和评估。

## 文件说明

- `simple_decision_tree.py` - **主要代码文件**，严格按照文档8个步骤实现
- `requirements.txt` - 项目依赖包
- `README.md` - 项目说明文档
- `课堂样例.docx` - 原始文档参考

## 生成的结果文件

- `data_distribution.png` - 数据分布散点图
- `confusion_matrix.png` - 混淆矩阵可视化
- `decision_boundary.png` - 决策边界图

## 功能特点

### 1. 数据生成
- 使用 `make_classification` 生成二分类数据集
- 可自定义样本数量和特征数量
- 确保数据的可重现性（random_state=42）

### 2. 数据可视化
- 二维散点图显示数据分布
- 不同颜色表示不同类别
- 支持中文标签显示

### 3. 数据划分
- 按8:2比例划分训练集和测试集
- 显示划分后的数据统计信息

### 4. 模型训练
- 使用决策树分类算法
- 可配置决策树参数
- 显示训练后的模型信息

### 5. 模型预测
- 对测试集进行预测
- 返回预测结果

### 6. 结果可视化
- 混淆矩阵热力图
- 决策边界可视化
- 清晰的图表标注

### 7. 性能评估
- 准确率计算
- 精确率、召回率、F1分数
- 详细的性能报告

## 安装依赖

```bash
pip install -r requirements.txt
```

## 运行代码

```bash
python simple_decision_tree.py
```

## 代码流程

1. **导入所需库** - 导入numpy、matplotlib、sklearn等必要库
2. **数据生成** - 使用make_classification生成二分类数据
3. **数据可视化** - 绘制散点图显示数据分布
4. **数据划分** - 划分训练集和测试集
5. **模型训练** - 训练决策树分类器
6. **模型预测** - 对测试集进行预测
7. **结果可视化** - 显示混淆矩阵
8. **性能评估** - 计算并显示各项性能指标

## 运行结果

程序运行后会显示：
- 数据生成信息（150个样本，2个特征）
- 数据分布散点图
- 训练集（120样本）和测试集（30样本）统计
- 混淆矩阵热力图
- 决策边界可视化
- 详细的性能评估报告

### 实际运行效果
- **准确率**: 90.00%
- **精确率**: 92.86%
- **召回率**: 86.67%
- **测试样本**: 30个（27个正确，3个错误）

## 性能指标

- **准确率 (Accuracy)**: 预测正确的样本占总样本的比例
- **精确率 (Precision)**: 预测为正例中实际为正例的比例
- **召回率 (Recall)**: 实际正例中被正确预测的比例
- **F1分数**: 精确率和召回率的调和平均数

## 使用说明

1. **安装依赖**：`pip install -r requirements.txt`
2. **运行程序**：`python simple_decision_tree.py`
3. **查看结果**：程序会生成三个PNG图片文件

## 注意事项

- 确保已安装所有依赖包（numpy, matplotlib, scikit-learn）
- 代码支持中文显示，需要系统安装SimHei字体
- 可以通过修改代码中的参数来调整数据集大小和模型参数
- 所有随机种子已固定（random_state=42），确保结果可重现
- 程序会自动保存可视化图片到当前目录

## 代码特点

- 严格按照机器学习文档的8个步骤实现
- 代码结构清晰，注释详细
- 包含完整的性能评估和可视化
- 适合学习和教学使用
