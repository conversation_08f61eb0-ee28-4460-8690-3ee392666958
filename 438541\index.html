<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>告白申请</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #f0f0f0;
            font-family: 'Arial', sans-serif;
            overflow: hidden;
        }

        .window {
            width: 600px; /* 从500px扩大到600px */
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            overflow: hidden;
            position: relative;
        }

        .window-header {
            height: 30px;
            background-color: #f0f0f0;
            display: flex;
            align-items: center;
            padding: 0 10px;
            border-bottom: 1px solid #e0e0e0;
        }

        .window-buttons {
            display: flex;
            margin-right: 10px;
        }

        .window-button {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .window-button.red {
            background-color: #ff5f56;
        }

        .window-button.yellow {
            background-color: #ffbd2e;
        }

        .window-button.green {
            background-color: #27c93f;
        }

        .window-title {
            flex-grow: 1;
            text-align: center;
            font-size: 14px;
            color: #666;
        }

        .window-content {
            padding: 30px;
            text-align: center;
            overflow: hidden;
            position: relative;
            min-height: 380px; /* 增加最小高度以提供更多空间 */
        }

        .message {
            margin-bottom: 30px;
            font-size: 18px;
            color: #333;
        }

        .emoji-container {
            width: 180px; /* 增加表情符号大小 */
            height: 180px; /* 增加表情符号大小 */
            margin: 10px auto 40px; /* 调整上下边距 */
            position: relative;
        }

        .emoji {
            width: 100%;
            height: 100%;
            background: linear-gradient(#ffdb58, #ffdb58);
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            transition: all 0.3s;
        }

        .eyes {
            position: absolute;
            top: 50px; /* 调整眼睛位置 */
            width: 100%;
            display: flex;
            justify-content: center;
            transition: all 0.3s;
        }

        .eye {
            width: 35px; /* 增大眼睛 */
            height: 35px; /* 增大眼睛 */
            background-color: white;
            border-radius: 50%;
            margin: 0 15px; /* 增加眼睛间距 */
            position: relative;
            transition: all 0.3s;
        }

        .pupil {
            width: 16px; /* 增大瞳孔 */
            height: 16px; /* 增大瞳孔 */
            background-color: #333;
            border-radius: 50%;
            position: absolute;
            top: 10px; /* 调整瞳孔位置 */
            left: 10px; /* 调整瞳孔位置 */
            transition: all 0.2s;
        }

        .mouth {
            position: absolute;
            top: 95px; /* 调整嘴巴位置 */
            width: 90px; /* 增大嘴巴宽度 */
            height: 50px; /* 增大嘴巴高度 */
            background-color: #d63031;
            border-radius: 50px 50px 70px 70px;
            transition: all 0.3s;
            overflow: hidden;
            transform-origin: center;
            left: 50%; /* 使嘴巴水平居中 */
            transform: translateX(-50%); /* 使嘴巴水平居中 */
        }

        .tooth {
            width: 20px;
            height: 12px;
            background-color: white;
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            border-radius: 0 0 5px 5px;
            transition: all 0.3s;
        }

        .cheeks {
            position: absolute;
            top: 85px; /* 调整脸颊位置 */
            width: 100%;
            display: flex;
            justify-content: space-between;
            padding: 0 30px; /* 增加左右边距 */
            box-sizing: border-box;
            transition: opacity 0.3s;
        }

        .cheek {
            width: 25px;
            height: 12px;
            background-color: #ffaaaa;
            border-radius: 50%;
            opacity: 0.8;
            transition: opacity 0.3s;
        }

        /* 新增的元素和状态 */
        .eyebrows {
            position: absolute;
            top: 30px; /* 调整眉毛位置 */
            width: 100%;
            display: flex;
            justify-content: center;
            opacity: 0;
            transition: all 0.3s;
        }

        .eyebrow {
            width: 25px;
            height: 5px;
            background-color: #333;
            border-radius: 3px;
            margin: 0 15px;
            transform: rotate(0deg);
            transition: all 0.3s;
        }

        .sweat {
            position: absolute;
            top: 35px;
            right: 20px;
            width: 10px;
            height: 15px;
            background-color: #74b9ff;
            border-radius: 50%;
            opacity: 0;
            transform: rotate(15deg);
            transition: all 0.3s;
        }

        .buttons {
            position: relative; /* 改为相对定位，以便定位接受按钮 */
            margin-top: 50px; /* 增加与上方元素的间距 */
            height: 60px; /* 添加高度以容纳按钮 */
            width: 100%; /* 确保宽度占满整个容器 */
        }

        .btn {
            padding: 10px 30px; /* 恢复正常的按钮大小 */
            border: none;
            border-radius: 8px; /* 保留圆角效果 */
            font-size: 16px; /* 恢复正常字体大小 */
            font-weight: bold; /* 保留加粗效果 */
            cursor: pointer;
            transition: all 0.3s;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); /* 保留阴影效果 */
        }

        .btn-accept {
            background-color: #2ecc71; /* 更鲜艳的绿色 */
            color: white;
            position: absolute; /* 绝对定位 */
            left: 50px; /* 固定在左侧 */
            bottom: 0; /* 固定在底部 */
        }

        .btn-accept:hover {
            background-color: #27ae60; /* 悬停时的颜色变化 */
            transform: translateY(-3px); /* 悬停时微微上浮 */
            box-shadow: 0 6px 10px rgba(0, 0, 0, 0.15); /* 增强阴影 */
        }

        .btn-reject {
            background-color: #e74c3c; /* 更鲜艳的红色 */
            color: white;
            position: absolute; /* 绝对定位 */
            right: 50px; /* 初始位置在右侧 */
            bottom: 0; /* 初始位置在底部 */
        }

        .btn-reject:hover {
            background-color: #c0392b; /* 悬停时的颜色变化 */
            transform: translateY(-3px); /* 悬停时微微上浮 */
            box-shadow: 0 6px 10px rgba(0, 0, 0, 0.15); /* 增强阴影 */
        }

        .success-card {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            text-align: center;
            z-index: 10;
            display: none;
        }

        .success-card .title {
            color: #e74c3c;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .success-card .subtitle {
            color: #27ae60;
            font-size: 16px;
            margin-bottom: 10px;
        }

        .success-card .heart {
            color: #e74c3c;
            font-size: 24px;
            margin-top: 10px;
        }

        .overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 5;
            display: none;
        }
    </style>
</head>
<body>
    <div class="window">
        <div class="window-header">
            <div class="window-buttons">
                <div class="window-button red"></div>
                <div class="window-button yellow"></div>
                <div class="window-button green"></div>
            </div>
            <div class="window-title">to 小王同学</div>
        </div>
        <div class="window-content">
            <div class="message">对方向您发起了一条告白申请~</div>
            <div class="emoji-container">
                <div class="emoji">
                    <div class="eyebrows">
                        <div class="eyebrow left"></div>
                        <div class="eyebrow right"></div>
                    </div>
                    <div class="eyes">
                        <div class="eye">
                            <div class="pupil"></div>
                        </div>
                        <div class="eye">
                            <div class="pupil"></div>
                        </div>
                    </div>
                    <div class="mouth">
                        <div class="tooth"></div>
                    </div>
                    <div class="cheeks">
                        <div class="cheek"></div>
                        <div class="cheek"></div>
                    </div>
                    <div class="sweat"></div>
                </div>
            </div>
            <div class="buttons">
                <button class="btn btn-accept">接受</button>
                <button class="btn btn-reject" style="right: 50px; bottom: 0;">拒绝</button>
            </div>
        </div>
        <div class="overlay"></div>
        <div class="success-card">
            <div class="title">恭喜!</div>
            <div class="subtitle">你有对象啦</div>
            <div class="heart">❤️</div>
        </div>
    </div>

    <script>
        const emoji = document.querySelector('.emoji');
        const pupils = document.querySelectorAll('.pupil');
        const mouth = document.querySelector('.mouth');
        const btnAccept = document.querySelector('.btn-accept');
        const btnReject = document.querySelector('.btn-reject');
        const overlay = document.querySelector('.overlay');
        const successCard = document.querySelector('.success-card');

        // 全局变量，用于追踪拒绝按钮的点击次数
        let rejectClickCount = 0;
        const MAX_REJECT_CLICKS = 7; // 最大点击次数，之后按钮将消失

        // 颜色混合函数
        function mixColors(color1, color2, ratio) {
            // 将颜色从十六进制转换为RGB
            const hex2rgb = (hex) => {
                const r = parseInt(hex.slice(1, 3), 16);
                const g = parseInt(hex.slice(3, 5), 16);
                const b = parseInt(hex.slice(5, 7), 16);
                return [r, g, b];
            };

            // 将RGB转换为十六进制
            const rgb2hex = (r, g, b) => {
                return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
            };

            const rgb1 = hex2rgb(color1);
            const rgb2 = hex2rgb(color2);

            // 混合颜色
            const mixed = rgb1.map((channel, i) => {
                return Math.round(channel * (1 - ratio) + rgb2[i] * ratio);
            });

            return rgb2hex(mixed[0], mixed[1], mixed[2]);
        }

        // 眼睛跟随鼠标移动
        document.addEventListener('mousemove', (e) => {
    const mouseX = e.clientX;
    const mouseY = e.clientY;

    // 让瞳孔跟随鼠标移动
    pupils.forEach(pupil => {
        const rect = pupil.getBoundingClientRect();
        const pupilX = rect.left + rect.width / 2;
        const pupilY = rect.top + rect.height / 2;

        const deltaX = mouseX - pupilX;
        const deltaY = mouseY - pupilY;

        const angle = Math.atan2(deltaY, deltaX);
        
        // 增加最大移动距离从3像素到8像素，并减小分母从30到20以增加响应灵敏度
        const distance = Math.min(8, Math.sqrt(deltaX * deltaX + deltaY * deltaY) / 20);

        const moveX = Math.cos(angle) * distance;
        const moveY = Math.sin(angle) * distance;

        pupil.style.transform = `translate(${moveX}px, ${moveY}px)`;
    });

    // 获取按钮位置
    const windowContent = document.querySelector('.window-content');
    const windowRect = windowContent.getBoundingClientRect();
    const acceptRect = btnAccept.getBoundingClientRect();
    const acceptCenterX = acceptRect.left + acceptRect.width / 2;
    const acceptCenterY = acceptRect.top + acceptRect.height / 2;

    // 计算鼠标与"接受"按钮的距离
    const distanceToAccept = Math.sqrt(
        Math.pow(mouseX - acceptCenterX, 2) +
        Math.pow(mouseY - acceptCenterY, 2)
    );

    // 最大影响距离 - 超过这个距离表情不会再改变
    const maxDistance = 300;

    // 计算距离的比例 (0-1)，0表示在按钮上，1表示在最大距离或更远
    const distanceRatio = Math.min(1, distanceToAccept / maxDistance);

    // 找到当前所有的拒绝按钮
    const rejectButtons = document.querySelectorAll('.btn-reject');

    // 计算鼠标与任意拒绝按钮的最短距离
    let minDistanceToReject = Infinity;
    let nearReject = false;

    // 检查与每个拒绝按钮的距离
    rejectButtons.forEach(btn => {
        const btnRect = btn.getBoundingClientRect();
        const rejectCenterX = btnRect.left + btnRect.width / 2;
        const rejectCenterY = btnRect.top + btnRect.height / 2;

        const distanceToReject = Math.sqrt(
            Math.pow(mouseX - rejectCenterX, 2) +
            Math.pow(mouseY - rejectCenterY, 2)
        );

        // 更新最短距离
        if (distanceToReject < minDistanceToReject) {
            minDistanceToReject = distanceToReject;
        }

        // 检查是否在按钮附近
        if (distanceToReject < 100) {
            nearReject = true;
        }
    });

    // 最大影响距离 - 超过这个距离表情不会再改变
    const maxRejectDistance = 300;

    // 计算距离的比例 (0-1)，0表示在按钮上，1表示在最大距离或更远
    const rejectDistanceRatio = Math.min(1, minDistanceToReject / maxRejectDistance);

    // 获取表情元素
    const eyebrows = document.querySelectorAll('.eyebrow');
    const cheeks = document.querySelectorAll('.cheek');
    const sweat = document.querySelector('.sweat');
    const tooth = document.querySelector('.tooth');
    const eyes = document.querySelectorAll('.eye');

    // 判断鼠标是否离按钮较远（既不靠近接受也不靠近拒绝）
    const nearAccept = distanceRatio < 0.7; // 距离接受按钮不超过210px
    const farFromBoth = !nearReject && !nearAccept;

    // 如果靠近拒绝按钮，显示恐惧表情
    if (nearReject) {
        // 计算恐惧表情程度 - 基于与拒绝按钮的距离
        // 嘴巴形状从微微下垂到上半圆形 (倒置的微笑)

        // 嘴巴宽度从60px到70px
        const scaredMouthWidth = 60 + (10 * (1 - rejectDistanceRatio));

        // 嘴巴高度从25px到35px (越近按钮嘴巴弧度越大)
        const scaredMouthHeight = 25 + (10 * (1 - rejectDistanceRatio));

        // 嘴巴位置只需轻微变化
        const scaredMouthTop = 90 + (2 * (1 - rejectDistanceRatio));

        // 关键变化：嘴型变为上半圆
        // 上边弧度增大，下边弧度减小，形成上半圆
        const topCurve = 30 + (40 * (1 - rejectDistanceRatio)); // 越近上边弧度越大
        const bottomCurve = 30 - (20 * (1 - rejectDistanceRatio)); // 越近下边弧度越小

        mouth.style.borderRadius = `${topCurve}px ${topCurve}px ${bottomCurve}px ${bottomCurve}px`;
        mouth.style.height = `${scaredMouthHeight}px`;
        mouth.style.width = `${scaredMouthWidth}px`;
        mouth.style.top = `${scaredMouthTop}px`;
        mouth.style.backgroundColor = '#e84118';

        // 眼睛维持正常大小
        eyes.forEach(eye => {
            eye.style.height = '30px';
            eye.style.width = '30px';
            eye.style.borderRadius = '50%';
        });

        // 瞳孔大小保持不变
        const pupilSize = 18; // 固定瞳孔大小
        const pupilPosition = 8; // 固定位置
        pupils.forEach(pupil => {
            pupil.style.width = `${pupilSize}px`;
            pupil.style.height = `${pupilSize}px`;
            pupil.style.top = `${pupilPosition}px`;
            pupil.style.left = `${pupilPosition}px`;
        });

        // 调整牙齿 (保持透明度不变)
        tooth.style.opacity = '1';
        tooth.style.height = `${10}px`;
        tooth.style.width = `${12}px`;

        // 隐藏脸颊
        cheeks.forEach(cheek => {
            cheek.style.opacity = '0';
        });

        // 显示眉毛和汗水 (越近越明显)
        const eyebrowOpacity = 0.7 + (0.3 * (1 - rejectDistanceRatio));
        const eyebrowAngle = 25 + (10 * (1 - rejectDistanceRatio));
        const eyebrowY = -3 - (2 * (1 - rejectDistanceRatio));

        eyebrows.forEach((eyebrow, index) => {
            eyebrow.style.opacity = `${eyebrowOpacity}`;
            if (index === 0) { // 左眉毛
                eyebrow.style.transform = `rotate(${eyebrowAngle}deg) translateY(${eyebrowY}px)`;
            } else { // 右眉毛
                eyebrow.style.transform = `rotate(-${eyebrowAngle}deg) translateY(${eyebrowY}px)`;
            }
        });

        // 汗水越近越明显
        sweat.style.opacity = `${0.7 + (0.3 * (1 - rejectDistanceRatio))}`;
        sweat.style.height = `${15 + (5 * (1 - rejectDistanceRatio))}px`;

        // 整体表情 - 颜色从黄色逐渐向蓝绿色过渡
        // 计算颜色混合比例，让蓝绿色从头部逐渐出现并向下扩散
        const gradientStart = (1 - rejectDistanceRatio); // 蓝绿色占比

        // 生成渐变背景
        if (gradientStart < 0.05) {
            // 几乎没有蓝绿色
            emoji.style.background = 'linear-gradient(#ffdb58, #ffdb58)';
        } else if (gradientStart < 0.3) {
            // 轻微的蓝绿色在顶部和中部
            const topColor = mixColors('#ffdb58', '#98E8C1', gradientStart * 3);
            const midColor = mixColors('#ffdb58', '#98E8C1', gradientStart * 1.5);
            emoji.style.background = `linear-gradient(${topColor} 30%, ${midColor} 50%, #ffdb58 80%)`;
        } else {
            // 明显的蓝绿色渐变，覆盖到嘴巴一半
            const topColor = mixColors('#ffdb58', '#98E8C1', gradientStart);
            const midColor = mixColors('#ffdb58', '#98E8C1', gradientStart * 0.7);
            const lowerMidColor = mixColors('#ffdb58', '#98E8C1', gradientStart * 0.4);
            emoji.style.background = `linear-gradient(
                ${topColor} 20%, 
                ${midColor} 40%, 
                ${lowerMidColor} 60%, 
                #ffdb58 80%
            )`;
        }
        // 表情不需要缩小效果
        emoji.style.transform = 'scale(1)';
    }
    // 根据与"接受"按钮的距离变化表情 - 越近越开心
    else if (nearAccept) {
        // 根据距离比例调整表情

        // 嘴巴随距离变化 - 越近越开心
        // 将嘴型从微笑逐渐变为下半圆形
        // 根据距离调整嘴型 - 越近越像下半圆

        // 宽度从70px到80px
        const mouthWidth = 70 + (10 * (1 - distanceRatio));

        // 高度从35px到40px
        const mouthHeight = 35 + (5 * (1 - distanceRatio));

        // 嘴巴位置从85px到80px (越开心嘴巴越向上)
        const mouthTop = 85 - (5 * (1 - distanceRatio));

        // 颜色从普通红到鲜艳红
        const redValue = 214 + (19 * (1 - distanceRatio));
        const greenValue = 48 - (18 * (1 - distanceRatio));
        const blueValue = 49 - (11 * (1 - distanceRatio));

        mouth.style.height = `${mouthHeight}px`;
        mouth.style.width = `${mouthWidth}px`;
        mouth.style.top = `${mouthTop}px`;
        mouth.style.backgroundColor = `rgb(${redValue}, ${greenValue}, ${blueValue})`;

        // 关键变化：嘴巴的弧度从微笑变为下半圆
        // 越近"接受"按钮，上边缘的弧度越大，让它变成一个半圆
        const borderRadiusTop = 50 - (40 * (1 - distanceRatio)); // 接近0表示上边几乎是直线
        const borderRadiusBottom = 70 + (30 * (1 - distanceRatio)); // 下边保持圆弧
        mouth.style.borderRadius = `${borderRadiusTop}px ${borderRadiusTop}px ${borderRadiusBottom}px ${borderRadiusBottom}px`;

        // 眼睛随距离变化 - 越近越眯起
        const eyeHeight = 30 - (5 * (1 - distanceRatio));
        eyes.forEach(eye => {
            eye.style.height = `${eyeHeight}px`;
            eye.style.width = '30px';
            eye.style.borderRadius = '50%';
        });

        // 调整牙齿 (保持透明度不变)
        tooth.style.opacity = '1';
        tooth.style.height = '16px';
        tooth.style.width = '14px';

        // 脸颊随距离变化 - 越近越红
        const cheekOpacity = 0.8 + (0.2 * (1 - distanceRatio));
        const cheekWidth = 25 + (5 * (1 - distanceRatio));
        const cheekHeight = 12 + (3 * (1 - distanceRatio));
        const cheekRed = 255;
        const cheekGreen = 170 - (40 * (1 - distanceRatio));
        const cheekBlue = 170 - (40 * (1 - distanceRatio));

        cheeks.forEach(cheek => {
            cheek.style.opacity = `${cheekOpacity}`;
            cheek.style.width = `${cheekWidth}px`;
            cheek.style.height = `${cheekHeight}px`;
            cheek.style.backgroundColor = `rgb(${cheekRed}, ${cheekGreen}, ${cheekBlue})`;
        });

        // 隐藏眉毛和汗水
        eyebrows.forEach(eyebrow => {
            eyebrow.style.opacity = '0';
        });
        sweat.style.opacity = '0';

        // 整体表情缩放 - 越近越大
        const scale = 1 + (0.05 * (1 - distanceRatio));
        emoji.style.background = 'linear-gradient(#ffdb58, #ffdb58)';
        emoji.style.transform = `scale(${scale})`;
    }
    // 新增: 远离所有按钮时的期待表情
    else if (farFromBoth) {
        // 创建一个期待的表情 - 嘴巴小而圆，眼睛略大，眉毛轻微抬起

        // 嘴型改为椭圆形小嘴，略微张开，表示惊喜或期待
        // 添加嘴巴动画效果
        const mouthCycle = (Date.now() % 3000) / 3000; // 3秒一个周期
        
        // 计算嘴巴的大小和形状变化
        // 实现轻微张合和形状变化的动画
        let mouthHeight, mouthWidth, mouthTop, mouthBorderRadius;
        
        if (mouthCycle < 0.3) { // 第一阶段：嘴巴微微闭合
            const phase = mouthCycle / 0.3; // 0-1的变化比例
            mouthHeight = 35 - 15 * phase;
            mouthWidth = 55 - 5 * phase;
            mouthTop = 95 + 2 * phase;
            mouthBorderRadius = `${50 - 10 * phase}% ${50 - 10 * phase}% ${50 - 5 * phase}% ${50 - 5 * phase}%`;
        } else if (mouthCycle < 0.5) { // 第二阶段：保持闭合状态一小段时间
            mouthHeight = 20;
            mouthWidth = 50;
            mouthTop = 97;
            mouthBorderRadius = '40% 40% 45% 45%';
        } else if (mouthCycle < 0.8) { // 第三阶段：嘴巴逐渐张开
            const phase = (mouthCycle - 0.5) / 0.3; // 0-1的变化比例
            mouthHeight = 20 + 20 * phase;
            mouthWidth = 50 + 10 * phase;
            mouthTop = 97 - 2 * phase;
            mouthBorderRadius = `${40 + 15 * phase}% ${40 + 15 * phase}% ${45 + 10 * phase}% ${45 + 10 * phase}%`;
        } else { // 第四阶段：保持张开状态
            mouthHeight = 40;
            mouthWidth = 60;
            mouthTop = 95;
            mouthBorderRadius = '55% 55% 55% 55%';
        }
        
        mouth.style.height = `${mouthHeight}px`;
        mouth.style.width = `${mouthWidth}px`;
        mouth.style.top = `${mouthTop}px`;
        mouth.style.borderRadius = mouthBorderRadius;
        mouth.style.backgroundColor = '#e84393'; // 更鲜艳的粉红色

        // 调整牙齿 - 随嘴巴动画变化
        const toothOpacity = mouthHeight > 25 ? 1 : 0.5; // 嘴巴张开时牙齿更明显
        tooth.style.opacity = `${toothOpacity}`; 
        tooth.style.height = `${Math.max(8, mouthHeight * 0.3)}px`;
        tooth.style.width = `${Math.max(12, mouthWidth * 0.25)}px`;
        
        // 眼睛更大，表示惊喜和期待
        eyes.forEach(eye => {
            eye.style.height = '35px'; // 更大的眼睛
            eye.style.width = '35px';
            eye.style.borderRadius = '50%';
            // 添加轻微的眼睛动画效果
            const blinkCycle = (Date.now() % 7000) / 7000; // 7秒一个眨眼周期
            if (blinkCycle > 0.97) {
                eye.style.height = '5px'; // 眨眼时眼睛高度变小
                eye.style.borderRadius = '45% 45% 50% 50%';
            }
        });

        // 瞳孔更大更亮，居中显示
        pupils.forEach(pupil => {
            pupil.style.width = '18px';
            pupil.style.height = '18px';
            pupil.style.top = '9px';
            pupil.style.left = '9px';
            pupil.style.backgroundColor = '#2c3e50'; // 更深的瞳孔颜色增加对比度
        });

        // 眉毛上扬更明显，表示惊喜和期待
        eyebrows.forEach((eyebrow, index) => {
            eyebrow.style.opacity = '0.7';
            eyebrow.style.width = '28px'; // 更长的眉毛
            eyebrow.style.height = '6px'; // 稍微粗一点
            const angle = index === 0 ? -15 : 15; // 更大的角度
            const yOffset = -4; // 上移更多
            eyebrow.style.transform = `rotate(${angle}deg) translateY(${yOffset}px)`;
        });

        // 脸颊更明显，表现期待和兴奋
        cheeks.forEach(cheek => {
            cheek.style.opacity = '0.7';
            cheek.style.width = '25px';
            cheek.style.height = '12px';
            cheek.style.backgroundColor = '#fd79a8'; // 更鲜艳的粉色
        });

        // 隐藏汗水
        sweat.style.opacity = '0';
        
        // 让表情有更明显的呼吸效果，像是期待中轻微的紧张
        const breathCycle = (Date.now() % 2500) / 2500; // 加快呼吸周期到2.5秒
        const breathScale = 1 + 0.03 * Math.sin(breathCycle * Math.PI * 2); // 增大缩放效果
        
        // 添加轻微的摇晃效果，表示期待的不安分
        const wiggleCycle = (Date.now() % 4000) / 4000; // 4秒一个周期
        const wiggleAmount = 0.7 * Math.sin(wiggleCycle * Math.PI * 2); // 很小的摇晃角度
        
        emoji.style.transform = `scale(${breathScale}) rotate(${wiggleAmount}deg)`;
        
        // 保持原本的黄色，不使用渐变
        emoji.style.background = 'linear-gradient(#ffdb58, #ffdb58)';
    }
});

        // 添加随机性函数，用于生成按钮的随机位置
        function getRandomButtonPosition(windowRect, buttonWidth, buttonHeight) {
            // 小的安全边距，允许按钮出现在更大范围内
            const safeMargin = 5;

            // 计算可用的窗口区域 - 整个窗口内容区域
            const availableWidth = windowRect.width - buttonWidth - safeMargin * 2;
            const availableHeight = windowRect.height - buttonHeight - safeMargin * 2;

            // 生成完全随机的位置，覆盖整个窗口区域
            let x = safeMargin + Math.random() * availableWidth;
            let y = safeMargin + Math.random() * availableHeight;

            // 根据点击次数增加按钮的移动速度和随机性
            const moveSpeed = 1 + rejectClickCount * 0.8; // 增大移动速度系数

            // 添加更大的随机性，使按钮移动更加不可预测
            // 随机选择一个方向进行偏移
            const direction = Math.floor(Math.random() * 8); // 8个方向
            const offset = 30 * moveSpeed; // 更大的偏移量

            switch(direction) {
                case 0: // 左上
                    x -= Math.random() * offset;
                    y -= Math.random() * offset;
                    break;
                case 1: // 上
                    y -= Math.random() * offset;
                    break;
                case 2: // 右上
                    x += Math.random() * offset;
                    y -= Math.random() * offset;
                    break;
                case 3: // 右
                    x += Math.random() * offset;
                    break;
                case 4: // 右下
                    x += Math.random() * offset;
                    y += Math.random() * offset;
                    break;
                case 5: // 下
                    y += Math.random() * offset;
                    break;
                case 6: // 左下
                    x -= Math.random() * offset;
                    y += Math.random() * offset;
                    break;
                case 7: // 左
                    x -= Math.random() * offset;
                    break;
            }

            // 确保按钮仍然在窗口内
            x = Math.max(safeMargin, Math.min(x, windowRect.width - buttonWidth - safeMargin));
            y = Math.max(safeMargin, Math.min(y, windowRect.height - buttonHeight - safeMargin));

            return { x, y };
        }

        // 创建一个处理拒绝按钮点击的函数
        function handleRejectClick() {
            // 移除当前按钮
            this.remove();

            // 增加点击计数
            rejectClickCount++;
            console.log("拒绝按钮点击次数:", rejectClickCount);

            // 如果达到最大点击次数，不再创建新按钮
            if (rejectClickCount >= MAX_REJECT_CLICKS) {
                console.log("已达到最大点击次数，不再创建新按钮");
                return; // 按钮完全消失
            }

            // 创建新按钮
            const windowContent = document.querySelector('.window-content');
            const windowRect = windowContent.getBoundingClientRect();
            
            // 获取接受按钮的尺寸作为基准
            const acceptRect = btnAccept.getBoundingClientRect();
            const acceptWidth = acceptRect.width;
            const acceptHeight = acceptRect.height;
            
            // 计算拒绝按钮的尺寸 - 在接受按钮尺寸的70%到130%之间随机
            // 确保按钮大小变化不会太大，始终控制在基准按钮的±30%范围内
            const sizeRatio = 0.7 + Math.random() * 0.6; // 生成0.7到1.3之间的随机数
            const buttonWidth = Math.round(acceptWidth * sizeRatio);
            const buttonHeight = Math.round(acceptHeight * sizeRatio);

            // 使用随机位置函数生成新按钮的位置
            const { x: newX, y: newY } = getRandomButtonPosition(windowRect, buttonWidth, buttonHeight);

            // 创建新按钮
            const newRejectBtn = document.createElement('button');
            newRejectBtn.className = 'btn btn-reject';
            newRejectBtn.textContent = '拒绝';
            newRejectBtn.style.position = 'absolute';
            newRejectBtn.style.left = `${newX}px`;
            newRejectBtn.style.top = `${newY}px`;
            
            // 设置按钮的宽度和高度
            newRejectBtn.style.width = `${buttonWidth}px`;
            newRejectBtn.style.height = `${buttonHeight}px`;
            
            // 根据尺寸调整内边距，确保按钮文字居中且美观
            const paddingV = Math.max(5, Math.min(10, Math.round(buttonHeight * 0.2)));
            const paddingH = Math.max(10, Math.min(30, Math.round(buttonWidth * 0.2)));
            newRejectBtn.style.padding = `${paddingV}px ${paddingH}px`;

            // 为新按钮添加相同的点击事件处理程序
            newRejectBtn.addEventListener('click', handleRejectClick);

            // 添加到 DOM
            windowContent.appendChild(newRejectBtn);

            // 表情变化
            mouth.style.borderRadius = '70px 70px 50px 50px';
            mouth.style.backgroundColor = '#636e72';
            emoji.style.backgroundColor = '#fdcb6e';
        }

        // 为初始拒绝按钮添加点击事件
        btnReject.addEventListener('click', handleRejectClick);

        // 接受按钮点击事件
        btnAccept.addEventListener('click', () => {
            overlay.style.display = 'block';
            successCard.style.display = 'block';

            // 获取表情元素
            const eyebrows = document.querySelectorAll('.eyebrow');
            const cheeks = document.querySelectorAll('.cheek');
            const sweat = document.querySelector('.sweat');
            const tooth = document.querySelector('.tooth');
            const eyes = document.querySelectorAll('.eye');

            // 修改表情为超级开心 - 下半圆形嘴型
            mouth.style.borderRadius = '10px 10px 100px 100px';
            mouth.style.height = '45px';
            mouth.style.width = '85px';
            mouth.style.top = '80px';
            mouth.style.backgroundColor = '#e74c3c';

            // 眼睛变成爱心形状
            eyes.forEach(eye => {
                eye.style.height = '22px';
                eye.style.borderRadius = '50%';
                eye.style.transform = 'rotate(0deg)';
            });

            // 调整牙齿
            tooth.style.opacity = '1';
            tooth.style.height = '16px';
            tooth.style.width = '14px';

            // 突出脸颊
            cheeks.forEach(cheek => {
                cheek.style.opacity = '1';
                cheek.style.width = '35px';
                cheek.style.height = '18px';
                cheek.style.backgroundColor = '#ff7979';
            });

            // 隐藏眉毛和汗水
            eyebrows.forEach(eyebrow => {
                eyebrow.style.opacity = '0';
            });
            sweat.style.opacity = '0';

            // 整体表情
            emoji.style.background = 'linear-gradient(#ffdb58, #ffdb58)';
            emoji.style.transform = 'scale(1.15) rotate(0deg)';
        });

        // 在JavaScript中添加overflow属性
        const windowContent = document.querySelector('.window-content');
        windowContent.style.overflow = 'hidden';

        console.log("点击次数:", rejectClickCount);
    </script>
</body>
</html>