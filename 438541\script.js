document.addEventListener("DOMContentLoaded", function () {
    const canvas = document.getElementById("smileyCanvas");
    const ctx = canvas.getContext("2d");
    const acceptBtn = document.getElementById("acceptBtn");
    const rejectBtn = document.getElementById("rejectBtn");
    const message = document.getElementById("message");
    let isMouseOverReject = false;
    let smileyAngle = 0;  // 小人目光的角度

    const drawSmiley = (expression = "neutral", mouseX = 100, mouseY = 100) => {
        // 清除之前的绘图
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // 绘制圆形背景（小人脸）
        ctx.beginPath();
        ctx.arc(100, 100, 80, 0, Math.PI * 2);
        ctx.fillStyle = "#FFD700";
        ctx.fill();
        ctx.closePath();

        // 绘制眼睛
        ctx.beginPath();
        ctx.arc(60, 60, 15, 0, Math.PI * 2);  // 左眼
        ctx.arc(140, 60, 15, 0, Math.PI * 2);  // 右眼
        ctx.fillStyle = "#fff";
        ctx.fill();
        ctx.closePath();

        // 绘制瞳孔
        ctx.beginPath();
        ctx.arc(60 + Math.cos(smileyAngle) * 5, 60 + Math.sin(smileyAngle) * 5, 5, 0, Math.PI * 2); // 左眼
        ctx.arc(140 + Math.cos(smileyAngle) * 5, 60 + Math.sin(smileyAngle) * 5, 5, 0, Math.PI * 2); // 右眼
        ctx.fillStyle = "#000";
        ctx.fill();
        ctx.closePath();

        // 绘制嘴巴
        ctx.beginPath();
        if (expression === "happy") {
            ctx.arc(100, 130, 50, 0, Math.PI);  // 笑脸
        } else if (expression === "sad") {
            ctx.arc(100, 160, 50, Math.PI, 0, true);  // 难看或紧张
        } else {
            ctx.arc(100, 140, 50, Math.PI * 0.2, Math.PI * 0.8);  // 中性
        }
        ctx.strokeStyle = "#000";
        ctx.lineWidth = 5;
        ctx.stroke();
        ctx.closePath();
    };

    // 监听鼠标移动事件
    document.addEventListener("mousemove", function (e) {
        const mouseX = e.clientX;
        const mouseY = e.clientY;

        // 计算小人眼睛的角度
        const deltaX = mouseX - 100;  // 鼠标的X偏移
        const deltaY = mouseY - 100;  // 鼠标的Y偏移
        smileyAngle = Math.atan2(deltaY, deltaX); // 计算角度

        // 更新表情
        const acceptBtnRect = acceptBtn.getBoundingClientRect();
        if (mouseX > acceptBtnRect.left && mouseX < acceptBtnRect.right && mouseY > acceptBtnRect.top && mouseY < acceptBtnRect.bottom) {
            drawSmiley("happy", mouseX, mouseY); // 笑脸
        } else if (isMouseOverReject) {
            drawSmiley("sad", mouseX, mouseY); // 难看表情
        } else {
            drawSmiley("neutral", mouseX, mouseY); // 中性表情
        }
    });

    // 监听鼠标靠近拒绝按钮时的效果
    rejectBtn.addEventListener("mouseenter", function () {
        isMouseOverReject = true;
        drawSmiley("sad"); // 难看表情
    });

    rejectBtn.addEventListener("mouseleave", function () {
        isMouseOverReject = false;
        drawSmiley("neutral"); // 恢复中性表情
    });

    // 点击拒绝按钮时，拒绝按钮跳动
    rejectBtn.addEventListener("click", function () {
        const x = Math.random() * 300 - 150;
        const y = Math.random() * 300 - 150;
        rejectBtn.style.left = `${50 + x}%`;
        rejectBtn.style.top = `${50 + y}%`;
        drawSmiley("sad"); // 难看表情
    });

    // 点击接受按钮后出现消息
    acceptBtn.addEventListener("click", function () {
        message.style.display = "block";
        rejectBtn.style.display = "none"; // 隐藏拒绝按钮
    });

    // 初始化：拒绝按钮一开始在中间
    rejectBtn.style.left = "50%";
    rejectBtn.style.top = "50%";

    // 默认表情
    drawSmiley("neutral");
});
