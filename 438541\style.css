body {
    background-color: #f0f8ff;
    font-family: Arial, sans-serif;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    margin: 0;
}

.container {
    text-align: center;
    position: relative;
}

#smileyCanvas {
    border-radius: 50%;
    background-color: #FFD700;
}

.buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 20px;
}

button {
    padding: 10px 20px;
    font-size: 16px;
    cursor: pointer;
}

#acceptBtn {
    background-color: green;
    color: white;
    border: none;
    border-radius: 5px;
}

#rejectBtn {
    background-color: red;
    color: white;
    border: none;
    border-radius: 5px;
    position: absolute;
    top: 50%;
    left: 50%;
    display: none;
    transform: translate(-50%, -50%);
}

#message {
    display: none;
    margin-top: 20px;
    font-size: 18px;
    color: green;
}

button:hover {
    opacity: 0.8;
}
