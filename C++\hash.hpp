#ifndef HASH_HPP_
#define HASH_HPP_

#include <list>
#include <vector>
#include <cstdlib>

// 实现一个简单的整数哈希集合
class HashSet {
 public:
  using Iterator = std::list<int>::iterator;
  
  // 构造和析构函数
  HashSet();
  HashSet(const HashSet&);
  HashSet& operator=(HashSet);
  ~HashSet();

  // 基本操作
  void insert(int key);            
  void erase(int key);             
  bool contains(int key) const;    
  Iterator find(int key);          
  Iterator erase(Iterator it);     
  
  // 容量相关
  std::size_t size() const;       
  bool empty() const;            
  
  // 桶和负载相关
  void rehash(std::size_t newSize);        
  std::size_t bucketCount() const;         
  std::size_t bucketSize(std::size_t b) const;  
  std::size_t bucket(int key) const;       
  float loadFactor() const;                
  float maxLoadFactor() const;            
  void maxLoadFactor(float maxLoad);       
  
  // 迭代器
  Iterator begin();
  Iterator end();

 private:
  // 预定义的桶大小集合（素数）
  const std::vector<std::size_t> sizes {
    1ul, 13ul, 59ul, 127ul, 257ul, 541ul,
    1'109ul, 2'357ul, 5'087ul, 10'273ul, 20'753ul, 42'043ul,
    85'229ul, 172'933ul, 351'061ul, 712'697ul, 1'447'153ul, 2'938'679ul
  };

  std::vector<std::vector<Iterator>> buckets;  
  std::list<int> elements;                     
  std::size_t numElements = 0;                 
  float maxLoad = 0.75;                        
  std::size_t index = 0;                       
  
  // 辅助函数
  void reserveBuckets(std::size_t newSize);    
  void redistributeElements();                 
  std::size_t find_next_prime_index(std::size_t target_size) const;  
};
#endif
