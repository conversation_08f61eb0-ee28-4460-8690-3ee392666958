#coding:utf-8
import sys
import math
import pandas as pd
import numpy as np
from random import*
import numpy as np
from math import*
#数据预处理
data=pd.read_csv(sys.path[0]+'\\distance.csv',sep=', ',header=0)
data_array=data.values[0::,0::]
relative_matrix = pd.read_csv(sys.path[0]+'\\relative_matrix.csv',sep=',',header=0)
matrix_array = relative_matrix.values[0::][0::]
with open(sys.path[0]+'\\matrix_array.txt', 'w') as f:
    f.write(str([[int(x) for x in row] for row in matrix_array]))
print(matrix_array)

for i in range(0,90):
    for j in range(0,90):
        if matrix_array[i][j]==1:
            matrix_array[i][j]=math.sqrt((data_array[i][0]-data_array[j][0])*(data_array[i][0]-data_array[j][0])+(data_array[i][1]-data_array[j][1])*(data_array[i][1]-data_array[j][1]))
        else:
            matrix_array[i][j]=10000000
for i in range(0,90):
    for j in range(0,90):
        if i==j:
            matrix_array[i][j]=0
insert=[0]*90
for i in range(0,90):
    insert[i]=i

#四个点
points=[0,80,81,82]

#核心代码

#结果路径点
final_array=[]