from flask import Flask, request, jsonify
from flask_cors import CORS
from transformers import Bert<PERSON>oken<PERSON>, BertModel
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity
import re
import threading
import os
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 设置代理
os.environ["HTTP_PROXY"] = "http://localhost:7897"
os.environ["HTTPS_PROXY"] = "http://localhost:7897"

# 设置huggingface_hub的代理
import huggingface_hub
huggingface_hub.constants.HF_ENDPOINT = "https://huggingface.co"

app = Flask(__name__)
CORS(app)

# 中文分句函数
def split_chinese_sentences(text):
    # 使用正则表达式按句号、问号、感叹号等分割句子
    pattern = r'([。！？!?])'
    sentences = []
    # 分割文本
    segments = re.split(pattern, text)
    # 组合分割的片段为完整的句子
    i = 0
    while i < len(segments):
        if i + 1 < len(segments) and re.match(pattern, segments[i + 1]):
            sentences.append(segments[i] + segments[i + 1])
            i += 2
        else:
            # 处理可能没有结束标点的段落
            if segments[i].strip():
                sentences.append(segments[i])
            i += 1
    # 过滤掉空句子
    return [s.strip() for s in sentences if s.strip()]

# 模型加载器（单例模式）
class ModelLoader:
    _instance = None
    _model_lock = threading.Lock()
    
    def __new__(cls):
        if not cls._instance:
            with cls._model_lock:
                if not cls._instance:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not hasattr(self, 'model'):
            try:
                # 本地模型路径，如果已下载
                local_model_path = "./models/bert-base-chinese"
                
                # 尝试先从本地加载模型
                try:
                    logger.info("尝试从本地加载bert-base-chinese模型...")
                    self.tokenizer = BertTokenizer.from_pretrained(
                        local_model_path if os.path.exists(local_model_path) else "bert-base-chinese", 
                        local_files_only=os.path.exists(local_model_path)
                    )
                    self.model = BertModel.from_pretrained(
                        local_model_path if os.path.exists(local_model_path) else "bert-base-chinese", 
                        local_files_only=os.path.exists(local_model_path)
                    )
                    logger.info("模型加载成功")
                    
                    # 如果是从Hugging Face下载的，保存到本地
                    if not os.path.exists(local_model_path):
                        logger.info("保存模型到本地以供离线使用...")
                        os.makedirs(local_model_path, exist_ok=True)
                        self.tokenizer.save_pretrained(local_model_path)
                        self.model.save_pretrained(local_model_path)
                        logger.info(f"模型已保存到 {local_model_path}")
                except Exception as e:
                    logger.error(f"离线加载失败: {str(e)}")
                    
                    # 如果离线加载失败，尝试在线模式
                    logger.info("尝试在线下载bert-base-chinese模型...")
                    self.tokenizer = BertTokenizer.from_pretrained("bert-base-chinese")
                    self.model = BertModel.from_pretrained("bert-base-chinese")
                    
                    # 保存到本地
                    logger.info("保存模型到本地以供离线使用...")
                    os.makedirs(local_model_path, exist_ok=True)
                    self.tokenizer.save_pretrained(local_model_path)
                    self.model.save_pretrained(local_model_path)
                    logger.info(f"模型已保存到 {local_model_path}")
                
                print("bert-base-chinese模型加载完成")
            except Exception as e:
                error_message = f"加载模型时出错: {str(e)}"
                print(error_message)
                logger.error(error_message)
                raise

# 使用BERT进行提取式摘要
def bert_extractive_summary(text, tokenizer, model, max_length=150):
    # 将文本分成句子
    sentences = split_chinese_sentences(text)
    if not sentences:
        return ""
    
    # 如果只有一个句子，直接返回截断后的内容
    if len(sentences) == 1:
        return sentences[0][:max_length]
    
    # 获取句子的BERT表示
    sentence_embeddings = []
    for sentence in sentences:
        inputs = tokenizer(sentence, return_tensors="pt", padding=True, truncation=True)
        outputs = model(**inputs)
        # 使用[CLS]标记的输出作为整个句子的表示
        embedding = outputs.last_hidden_state[:, 0, :].detach().numpy()
        sentence_embeddings.append(embedding.flatten())
    
    # 计算句子之间的余弦相似度
    similarity_matrix = cosine_similarity(sentence_embeddings)
    
    # 计算每个句子的分数 (句子与其他所有句子的相似度之和)
    scores = np.sum(similarity_matrix, axis=1)
    
    # 按重要性排序句子索引
    ranked_indices = np.argsort(scores)[::-1]
    
    # 严格控制摘要长度
    summary = ""
    for idx in ranked_indices:
        # 如果添加当前句子后长度超过max_length
        if len(summary) + len(sentences[idx]) <= max_length:
            summary += sentences[idx]
        else:
            # 如果摘要为空（表示第一个句子就超过了max_length），截断句子
            if not summary:
                remaining_length = max_length - len(summary)
                summary = sentences[idx][:remaining_length]
            # 如果已有内容但添加下一句会超长，停止添加
            break
    
    # 确保不超过最大长度
    if len(summary) > max_length:
        summary = summary[:max_length]
    
    return summary

@app.route('/summarize', methods=['POST'])
def generate_summary():
    data = request.json
    text = data.get('text', '')
    max_length = min(int(data.get('max_length', 150)), 500)
    
    if not text:
        return jsonify({"error": "Empty input text"}), 400
    
    try:
        # 确保文本是UTF-8编码
        if isinstance(text, str):
            text = text.encode('utf-8').decode('utf-8')
        
        # 获取模型实例
        model_loader = ModelLoader()
        # 生成摘要
        summary = bert_extractive_summary(text, model_loader.tokenizer, model_loader.model, max_length)
        
        return jsonify({
            "original_length": len(text),
            "summary_length": len(summary),
            "summary": summary
        })
    except Exception as e:
        import traceback
        error_log = traceback.format_exc()
        print(error_log)
        logger.error(error_log)
        return jsonify({"error": str(e)}), 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, threaded=True)
