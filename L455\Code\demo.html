<!DOCTYPE html>  
<html lang="zh-CN">  
<head>  
  <meta charset="UTF-8">  
  <meta name="viewport" content="width=device-width, initial-scale=1.0">  
  <title>AI文本摘要生成器</title>  
  <script src="https://cdn.tailwindcss.com"></script>  
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">  
  <style>  
    @keyframes gradientBg {  
        0% { background-position: 0% 50%; }  
        50% { background-position: 100% 50%; }  
        100% { background-position: 0% 50%; }  
    }  
    .gradient-bg {  
        background: linear-gradient(135deg, #f0fdf4, #f8fafc, #ecfdf5);  
        background-size: 200% 200%;  
        animation: gradientBg 12s ease infinite;  
    }  
    .glass-panel {  
        background: rgba(255, 255, 255, 0.85);  
        backdrop-filter: blur(12px);  
        border: 1px solid rgba(255, 255, 255, 0.3);  
    }  
    .smooth-transition {  
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);  
    }  
    .high-contrast {  
        filter: contrast(120%);  
    }  
    .high-contrast .glass-panel {  
        background: rgba(255, 255, 255, 0.95) !important;  
    }  
    .tooltip {  
        position: relative;  
    }  
    .tooltip:hover::after {  
        content: attr(data-tooltip);  
        position: absolute;  
        bottom: 100%;  
        left: 50%;  
        transform: translateX(-50%);  
        background: rgba(0,0,0,0.7);  
        color: white;  
        padding: 4px 8px;  
        border-radius: 4px;  
        font-size: 12px;  
        white-space: nowrap;  
    }  
  </style>  
</head>  
<body class="gradient-bg min-h-screen p-8">  
  <div class="max-w-6xl mx-auto space-y-8">  
    <!-- 标题 -->  
    <div class="text-center space-y-2">  
      <h1 class="text-4xl font-bold bg-gradient-to-r from-emerald-600 to-teal-500 bg-clip-text text-transparent">  
        AI 智能摘要生成器  
      </h1>  
      <p class="text-gray-500">基于自然语言处理的智能文本压缩技术</p>  
    </div>  

    <!-- 主界面 -->  
    <div class="grid lg:grid-cols-2 gap-8">  
      <!-- 输入区 -->  
      <div class="glass-panel rounded-xl p-6 shadow-lg smooth-transition hover:scale-[1.01]">  
        <div class="space-y-4">  
          <div class="flex items-center justify-between">  
            <h3 class="font-semibold">  
              <i class="fas fa-magic text-emerald-600 mr-2"></i>  
              输入文本  
            </h3>  
            <span class="text-sm text-gray-500" id="charCount">0/2000 字</span>  
          </div>  
          <textarea   
            id="inputText"  
            class="w-full p-4 border rounded-lg resize-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"  
            rows="8"  
            placeholder="粘贴或输入需要摘要的文本..."  
          ></textarea>  
        </div>  
      </div>  

      <!-- 控制区 -->  
      <div class="space-y-6">  
        <div class="glass-panel rounded-xl p-6 shadow-lg smooth-transition hover:scale-[1.01]">  
          <div class="space-y-4">  
            <div class="flex items-center justify-between">  
              <div class="flex items-center gap-2">  
                <i class="fas fa-cog text-emerald-600"></i>  
                <span class="font-medium">生成设置</span>  
              </div>  
              <button   
                id="generateBtn"  
                class="px-4 py-2 bg-emerald-600 text-white rounded-lg smooth-transition hover:bg-emerald-700 disabled:opacity-50"  
                onclick="generateSummary()"  
              >  
                <span id="btnText">开始生成</span>  
                <i id="loadingIcon" class="hidden animate-spin ml-2 fas fa-spinner"></i>  
              </button>  
            </div>  
            
            <div class="space-y-2">  
              <div class="flex justify-between text-sm">  
                <span>摘要长度</span>  
                <span id="lengthValue">150 字</span>  
              </div>  
              <input  
                type="range"  
                id="lengthSlider"  
                class="w-full"  
                min="50"  
                max="500"  
                value="150"  
                oninput="updateLength(this.value)"  
              >  
            </div>  
          </div>  
        </div>  

        <!-- 结果区 -->  
        <div id="resultPanel" class="hidden glass-panel rounded-xl p-6 shadow-lg relative">  
          <div class="absolute top-4 right-4 flex gap-2">  
            <button   
              id="copyBtn"  
              class="p-2 hover:bg-gray-100 rounded-full tooltip"  
              onclick="copySummary()"  
              data-tooltip="复制内容"  
            >  
              <i class="far fa-copy text-gray-500"></i>  
            </button>  
            <button  
              id="saveBtn"  
              class="p-2 hover:bg-gray-100 rounded-full tooltip"  
              onclick="saveSummary()"  
              data-tooltip="保存文件"  
            >  
              <i class="far fa-save text-gray-500"></i>  
            </button>  
            <button  
              id="editBtn"  
              class="p-2 hover:bg-gray-100 rounded-full tooltip"  
              onclick="toggleEdit()"  
              data-tooltip="编辑摘要"  
            >  
              <i class="fas fa-edit text-gray-500"></i>  
            </button>  
          </div>  

          <div class="space-y-4">  
            <h3 class="font-semibold">生成摘要</h3>  
            <div class="relative">  
              <textarea   
                id="summaryContent"   
                class="w-full p-3 border rounded-lg bg-white/90 resize-none focus:ring-2 focus:ring-emerald-500"  
                rows="8"  
                placeholder="摘要将在此显示..."  
                readonly  
                oninput="updateCharCounter(this.value.length)"  
              ></textarea>  
              <div class="absolute bottom-2 right-2 bg-white/80 px-2 py-1 rounded text-sm text-gray-500">  
                <span id="summaryCounter">0</span> 字  
              </div>  
            </div>  
            <div class="text-sm text-gray-500 border-t pt-3">  
              <div class="flex gap-4" id="stats"></div>  
              <div class="mt-2 flex gap-2" id="formatOptions">  
                <button   
                  class="px-2 py-1 rounded hover:bg-gray-100"  
                  onclick="toggleFont()"  
                >  
                  <i class="fas fa-font mr-1"></i>字体切换  
                </button>  
                <button  
                  class="px-2 py-1 rounded hover:bg-gray-100"  
                  onclick="toggleContrast()"  
                >  
                  <i class="fas fa-adjust mr-1"></i>高对比度  
                </button>  
              </div>  
            </div>  
          </div>  
        </div>  
      </div>  
    </div>  
  </div>  

  <script>  
    // 输入限制  
    document.getElementById('inputText').addEventListener('input', function(e) {  
      const maxLength = 2000;  
      if (e.target.value.length > maxLength) {  
        e.target.value = e.target.value.substring(0, maxLength);  
      }  
      document.getElementById('charCount').textContent =   
          `${e.target.value.length}/2000 字`;  
    });  

    // 更新摘要长度  
    function updateLength(value) {  
      document.getElementById('lengthValue').textContent = `${value} 字`;  
    }  

    // 生成摘要  
    async function generateSummary() {  
      const input = document.getElementById('inputText').value.trim();  
      const btn = document.getElementById('generateBtn');  
      const loadingIcon = document.getElementById('loadingIcon');  
      const btnText = document.getElementById('btnText');  

      if (!input) {  
        alert('请输入需要摘要的内容');  
        return;  
      }  

      btn.disabled = true;  
      btnText.textContent = '生成中...';  
      loadingIcon.classList.remove('hidden');  

      try {  
        const response = await fetch('http://localhost:5000/summarize', {  
          method: 'POST',  
          headers: { 'Content-Type': 'application/json' },  
          body: JSON.stringify({  
            text: input.substring(0, 2000),  
            max_length: document.getElementById('lengthSlider').value  
          })  
        });  

        const data = await response.json();  
        if (data.error) throw new Error(data.error);  

        showResult(data);  
      } catch (err) {  
        alert('生成失败: ' + err.message);  
      } finally {  
        btn.disabled = false;  
        btnText.textContent = '开始生成';  
        loadingIcon.classList.add('hidden');  
      }  
    }  

    // 显示摘要结果  
    function showResult(data) {  
      const resultPanel = document.getElementById('resultPanel');  
      const stats = document.getElementById('stats');  
      const reduction = Math.round((1 - data.summary_length / data.original_length) * 100);  

      document.getElementById('summaryContent').value = data.summary;  
      stats.innerHTML = `  
          <span>原始长度: ${data.original_length}字</span>  
          <span>压缩率: ${reduction}%</span>  
      `;  
      resultPanel.classList.remove('hidden');  
      updateCharCounter(data.summary.length);  
    }  

    // 字数统计  
    function updateCharCounter(length) {  
      document.getElementById('summaryCounter').textContent = length;  
    }  

    // 切换字体  
    let currentFont = 1;  
    const FONT_OPTIONS = ['text-sm', 'text-base', 'text-lg'];  
    function toggleFont() {  
      const textarea = document.getElementById('summaryContent');  
      textarea.classList.remove(FONT_OPTIONS[currentFont]);  
      currentFont = (currentFont + 1) % FONT_OPTIONS.length;  
      textarea.classList.add(FONT_OPTIONS[currentFont]);  
    }  

    // 切换高对比度模式  
    function toggleContrast() {  
      document.body.classList.toggle('high-contrast');  
      document.querySelectorAll('.glass-panel').forEach(panel => {  
        panel.classList.toggle('bg-gray-100');  
      });  
    }  

    // 保存为文件  
    async function saveSummary() {  
      const text = document.getElementById('summaryContent').value;  
      const blob = new Blob([text], { type: 'text/plain;charset=utf-8' });  
      const url = URL.createObjectURL(blob);  

      const a = document.createElement('a');  
      a.href = url;  
      a.download = `摘要_${new Date().toLocaleString().replace(/[/:]/g, '-')}.txt`;  
      document.body.appendChild(a);  
      a.click();  
      document.body.removeChild(a);  
    }  

    // 复制摘要  
    async function copySummary() {  
      const text = document.getElementById('summaryContent').value;  
      const btn = document.getElementById('copyBtn');  

      try {  
        await navigator.clipboard.writeText(text);  
        btn.innerHTML = '<i class="fas fa-check text-emerald-600"></i>';  
        setTimeout(() => {  
          btn.innerHTML = '<i class="far fa-copy text-gray-500"></i>';  
        }, 2000);  
      } catch (err) {  
        alert('复制失败');  
      }  
    }  

    // 编辑摘要功能：切换 textarea 的只读状态  
    function toggleEdit() {  
      const textarea = document.getElementById('summaryContent');  
      const editBtn = document.getElementById('editBtn');  
      if (textarea.hasAttribute('readonly')) {  
        textarea.removeAttribute('readonly');  
        editBtn.innerHTML = '<i class="fas fa-check text-emerald-600"></i>';  
        editBtn.setAttribute("data-tooltip", "完成编辑");  
      } else {  
        textarea.setAttribute('readonly', true);  
        editBtn.innerHTML = '<i class="fas fa-edit text-gray-500"></i>';  
        editBtn.setAttribute("data-tooltip", "编辑摘要");  
      }  
    }  
  </script>  
</body>  
</html>