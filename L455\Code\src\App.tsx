import React, { useState } from 'react';
import { FileText, Send, Loader2 } from 'lucide-react';
import { supabase } from './lib/supabase';

function App() {
  const [text, setText] = useState('');
  const [result, setResult] = useState('');
  const [loading, setLoading] = useState(false);

  const handleExtract = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase.functions.invoke('extract-text', {
        body: { text },
      });

      if (error) throw error;
      setResult(data.extractedText);
    } catch (error) {
      console.error('Error:', error);
      setResult('Error processing text. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 to-gray-800 text-white p-8">
      <div className="max-w-4xl mx-auto">
        <header className="flex items-center gap-3 mb-8">
          <FileText className="w-8 h-8" />
          <h1 className="text-3xl font-bold">Text Extraction System</h1>
        </header>

        <div className="space-y-6">
          <div className="space-y-2">
            <label htmlFor="input" className="block text-sm font-medium">
              Input Text
            </label>
            <textarea
              id="input"
              value={text}
              onChange={(e) => setText(e.target.value)}
              className="w-full h-40 px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none text-white"
              placeholder="Enter your text here..."
            />
          </div>

          <button
            onClick={handleExtract}
            disabled={loading || !text.trim()}
            className="flex items-center justify-center gap-2 px-6 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-800 disabled:cursor-not-allowed rounded-lg font-medium transition-colors w-full sm:w-auto"
          >
            {loading ? (
              <Loader2 className="w-5 h-5 animate-spin" />
            ) : (
              <Send className="w-5 h-5" />
            )}
            {loading ? 'Processing...' : 'Extract Text'}
          </button>

          {result && (
            <div className="space-y-2">
              <h2 className="text-xl font-semibold">Result</h2>
              <div className="bg-gray-800 border border-gray-700 rounded-lg p-4">
                <pre className="whitespace-pre-wrap font-mono text-sm">
                  {result}
                </pre>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default App;