const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

Deno.serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const { text } = await req.json();

    // Basic text extraction logic
    // You can enhance this with more sophisticated processing
    const extractedText = text
      .replace(/[^\w\s]/g, '') // Remove punctuation
      .split(/\s+/) // Split into words
      .filter(word => word.length > 3) // Filter out short words
      .join(' ');

    const data = {
      extractedText,
      timestamp: new Date().toISOString(),
    };

    return new Response(
      JSON.stringify(data),
      {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json',
        },
      },
    );
  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        status: 400,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json',
        },
      },
    );
  }
});