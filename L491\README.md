# WKU AI Helper

This application provides a web interface for querying a JSONL knowledge base using the nomic-embed-text:v1.5 embedding model and DeepSeek API for answering questions.

## Requirements

- Python 3.7+
- Ollama with nomic-embed-text:v1.5 model
- DeepSeek API key
- Flask backend
- Web browser

## Setup

1. Install required packages:

```bash
pip install -r requirements.txt
```

2. Ensure Ollama is running and has the nomic-embed-text model:

```bash
ollama pull nomic-embed-text:v1.5
```

3. Make sure your JSONL data file is in the `data/` directory (named `wku.jsonl`).

4. Update the DeepSeek API key in `backend/model_interface.py` if needed.

## Running the Application

1. First, build the FAISS index (if not already built):

```bash
cd L491
python backend/build_faiss.py
```

2. Start the Flask backend:

```bash
cd L491
python backend/app.py
```

3. Open the frontend in a web browser by opening `frontend/index.html` directly in your browser.

4. You can now ask questions related to your knowledge base!

## How It Works

1. The application uses Ollama to generate embeddings for your JSONL data.
2. These embeddings are stored in a FAISS index for fast retrieval.
3. When a user asks a question, the system:
   - Converts the question to an embedding
   - Finds the most relevant documents in the FAISS index
   - Sends the relevant context + question to DeepSeek API
   - Returns the response to the user

## Troubleshooting

- Make sure Ollama is running: `ollama serve`
- Ensure your JSONL data is properly formatted
- Check that the DeepSeek API key is valid
- Verify that all paths in the code match your actual file structure 