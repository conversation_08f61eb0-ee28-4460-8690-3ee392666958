<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API响应测试</title>
    <style>
        body {
            font-family: monospace;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        button {
            background: #1a73e8;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1557b0;
        }
        .raw-response {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .formatted-response {
            background: white;
            border: 1px solid #e9ecef;
            padding: 15px;
            border-radius: 4px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
        }
        .error {
            color: red;
            background: #ffe6e6;
            padding: 10px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>API响应测试</h1>
    
    <div class="container">
        <h2>测试查询</h2>
        <button onclick="testQuery('Tell me about WKU')">查询: Tell me about WKU</button>
        <button onclick="testQuery('What is the overview of WKU?')">查询: What is the overview of WKU?</button>
        <button onclick="testQuery('介绍一下WKU')">查询: 介绍一下WKU</button>
        <button onclick="clearResults()">清空结果</button>
    </div>
    
    <div class="container">
        <h2>原始API响应</h2>
        <div id="raw-response" class="raw-response">等待查询...</div>
    </div>
    
    <div class="container">
        <h2>格式化后的响应</h2>
        <div id="formatted-response" class="formatted-response">等待查询...</div>
    </div>
    
    <div class="container">
        <h2>错误信息</h2>
        <div id="error-info" class="error" style="display: none;"></div>
    </div>

    <script>
        async function testQuery(query) {
            document.getElementById('raw-response').textContent = '正在查询...';
            document.getElementById('formatted-response').textContent = '正在处理...';
            document.getElementById('error-info').style.display = 'none';
            
            try {
                const response = await fetch('http://127.0.0.1:5001/query', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ query: query })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                
                // 显示原始响应
                document.getElementById('raw-response').textContent = 
                    `查询: ${query}\n\n原始响应:\n${data.response}`;
                
                // 显示格式化后的响应
                const formatted = formatResponse(data.response);
                document.getElementById('formatted-response').innerHTML = formatted;
                
            } catch (error) {
                console.error('Error:', error);
                document.getElementById('error-info').textContent = 
                    `错误: ${error.message}`;
                document.getElementById('error-info').style.display = 'block';
                document.getElementById('raw-response').textContent = '查询失败';
                document.getElementById('formatted-response').textContent = '查询失败';
            }
        }
        
        function formatResponse(text) {
            console.log('开始处理文本:', text);
            
            text = text.trim();
            
            // 保护代码块
            const codeBlocks = [];
            text = text.replace(/```[\s\S]*?```/g, (match, index) => {
                codeBlocks.push(match);
                return `__CODE_BLOCK_${codeBlocks.length - 1}__`;
            });
            
            // 保护行内代码
            const inlineCodes = [];
            text = text.replace(/`([^`]+)`/g, (match, code) => {
                inlineCodes.push(`<code>${code}</code>`);
                return `__INLINE_CODE_${inlineCodes.length - 1}__`;
            });
            
            // 清理多余的星号
            text = text.replace(/^\*+\s*/gm, '');
            text = text.replace(/\*+\s*$/gm, '');
            text = text.replace(/\*+(\s+)/g, '$1');
            
            // 简化的标题处理
            text = text.replace(/^(#{1,6})\s*(.+?)$/gm, (match, hashes, title) => {
                const level = hashes.length;
                const cleanTitle = title.trim();
                console.log(`处理标题: ${hashes} "${cleanTitle}"`);
                
                if (level === 1) return `<h1 style="color: #1a73e8; word-break: normal; white-space: nowrap; overflow: visible;">${cleanTitle}</h1>`;
                if (level === 2) return `<h2 style="color: #1a73e8; word-break: normal; white-space: nowrap; overflow: visible;">${cleanTitle}</h2>`;
                if (level === 3) return `<h3 style="color: #1967d2; word-break: normal; white-space: nowrap; overflow: visible;">${cleanTitle}</h3>`;
                return `<h4 style="color: #1967d2; word-break: normal; white-space: nowrap; overflow: visible;">${cleanTitle}</h4>`;
            });
            
            // 处理粗体和斜体
            text = text.replace(/\*\*\*((?:[^*]|\*(?!\*))+?)\*\*\*/g, '<strong><em>$1</em></strong>');
            text = text.replace(/\*\*((?:[^*]|\*(?!\*))+?)\*\*/g, '<strong>$1</strong>');
            text = text.replace(/(?<!\*)\*([^*\n]+?)\*(?!\*)/g, '<em>$1</em>');
            
            // 处理引用
            text = text.replace(/^>\s+(.*?)$/gm, '<blockquote>$1</blockquote>');
            
            // 清理残留的星号
            text = text.replace(/(?<![a-zA-Z])\*(?![a-zA-Z*])/g, '');
            text = text.replace(/\s*\*\s*/g, ' ');
            text = text.replace(/\*+/g, '');
            
            // 处理段落
            const lines = text.split('\n');
            const processedLines = [];
            
            for (let line of lines) {
                line = line.trim();
                if (!line) {
                    processedLines.push('');
                    continue;
                }
                
                if (line.match(/^<[h1-6|blockquote|ul|ol|li]/)) {
                    processedLines.push(line);
                } else if (line.match(/^[-*]\s/)) {
                    const content = line.replace(/^[-*]\s+/, '');
                    processedLines.push(`<li>${content}</li>`);
                } else if (line.match(/^\d+\.\s/)) {
                    const content = line.replace(/^\d+\.\s+/, '');
                    processedLines.push(`<li>${content}</li>`);
                } else {
                    processedLines.push(`<p>${line}</p>`);
                }
            }
            
            let result = processedLines.join('');
            
            // 恢复代码块
            codeBlocks.forEach((block, index) => {
                let code = block.replace(/```(\w*)\n?([\s\S]*?)```/g, '<pre><code>$2</code></pre>');
                result = result.replace(`__CODE_BLOCK_${index}__`, code);
            });
            
            // 恢复行内代码
            inlineCodes.forEach((code, index) => {
                result = result.replace(`__INLINE_CODE_${index}__`, code);
            });
            
            console.log('处理完成的结果:', result);
            return result;
        }
        
        function clearResults() {
            document.getElementById('raw-response').textContent = '等待查询...';
            document.getElementById('formatted-response').textContent = '等待查询...';
            document.getElementById('error-info').style.display = 'none';
        }
    </script>
</body>
</html>
