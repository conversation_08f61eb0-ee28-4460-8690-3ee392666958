from flask import Flask, request, jsonify
from flask_cors import CORS
import faiss
import pickle
import requests
import os
import json
import numpy as np
from model_interface import call_deepseek
import re
import signal
import sys
import atexit

# 创建Flask应用
app = Flask(__name__)
CORS(app)  # 启用跨域支持

def get_embedding(text):
    response = requests.post(
        'http://localhost:11434/api/embeddings',
        json={
            "model": "nomic-embed-text:v1.5",  # 使用 nomic-embed-text:v1.5
            "prompt": text
        }
    )
    data = response.json()
    return data["embedding"]

# 确保目录存在
os.makedirs('backend/faiss_index', exist_ok=True)

# 检查索引文件是否存在，如果不存在则尝试使用vector_db.py中的方法
try:
    # 尝试加载 FAISS 向量索引和文本数据
    index_path = 'backend/faiss_index/index.faiss'
    texts_path = 'backend/faiss_index/texts.pkl'

    # 如果文件不存在，尝试使用其他可能的路径
    if not os.path.exists(index_path):
        index_path = 'backend/faiss_index.bin'

    if not os.path.exists(texts_path):
        texts_path = 'backend/docs.json'
        with open(texts_path, 'r', encoding='utf-8') as f:
            texts = json.load(f)
    else:
        with open(texts_path, 'rb') as f:
            texts = pickle.load(f)

    index = faiss.read_index(index_path)
    print(f"成功加载索引: {index_path}")

    # 如果使用的是docs.json，确保texts是正确的格式
    if texts_path.endswith('.json'):
        # 确保texts是列表格式
        if isinstance(texts, list):
            print(f"成功加载文本数据: {len(texts)}条记录")
        else:
            print("警告: 文本数据格式不是预期的列表格式")

except Exception as e:
    print(f"加载索引或文本数据时出错: {e}")
    print("请先运行build_faiss.py构建索引")
    index = None
    texts = []

# 尝试导入sentence_transformers作为备用嵌入方法
try:
    from sentence_transformers import SentenceTransformer
    # 尝试加载模型，如果失败则跳过
    try:
        model = SentenceTransformer("all-MiniLM-L6-v2")
        print("成功加载SentenceTransformer模型")
    except Exception as e:
        print(f"加载SentenceTransformer模型失败: {e}")
        print("将仅使用Ollama API进行嵌入")
        model = None

    def embed_with_transformer(texts):
        if model is not None:
            return model.encode(texts)
        else:
            raise NotImplementedError("SentenceTransformer模型未加载")

except ImportError:
    print("未安装sentence_transformers，将仅使用Ollama API")
    model = None

    def embed_with_transformer(texts):
        raise NotImplementedError("SentenceTransformer未安装")

# 添加语言检测函数
def detect_language(text):
    # 检测文本中是否包含中文字符
    if re.search(r'[\u4e00-\u9fff]', text):
        return 'zh'
    else:
        return 'en'

@app.route('/query', methods=['POST'])
def query():
    user_input = request.json.get('query')

    if not user_input:
        return jsonify({'error': '请输入查询内容'}), 400

    try:
        # 检测语言
        lang = detect_language(user_input)

        # Get embedding for query text
        query_embedding = np.array([get_embedding(user_input)], dtype=np.float32)

        # Perform search with FAISS
        k = min(5, len(texts))  # Ensure k is not larger than the number of texts
        if k > 0:
            _, indices = index.search(query_embedding, k)

            # Get relevant texts
            context_texts = [texts[i] for i in indices[0]]
            context = "\n\n".join(context_texts)

            # Form prompt with context based on detected language
            if lang == 'zh':
                prompt = f"""基于以下信息：

{context}

请用中文回答这个问题：{user_input}

请按照以下格式要求：
1. 使用清晰的标题结构（## 主标题，### 副标题）
2. 用**粗体**突出重要信息
3. 使用项目符号列表或编号列表组织信息
4. 保持段落简洁明了，每段专注一个要点
5. 适当使用*斜体*强调关键词
6. 确保内容逻辑清晰，层次分明

请提供详细、专业且格式良好的回答。"""
            else:
                prompt = f"""Based on the following information:

{context}

Please answer this question in English: {user_input}

Please follow these formatting requirements:
1. Use clear heading structure (## Main headings, ### Subheadings)
2. Use **bold** to highlight important information
3. Use bullet points or numbered lists to organize information
4. Keep paragraphs concise and focused on one point each
5. Use *italics* appropriately for emphasis
6. Ensure logical flow and clear hierarchy

Please provide a detailed, professional, and well-formatted response."""

            # Call LLM with the context and question
            response = call_deepseek(prompt)
        else:
            if lang == 'zh':
                response = "没有可用的数据来回答这个问题。"
            else:
                response = "No data available to answer the question."

        return jsonify({'response': response})
    except Exception as e:
        import traceback
        error_trace = traceback.format_exc()
        print(f"查询处理错误: {error_trace}")
        return jsonify({'error': f'处理查询时出错: {str(e)}'}), 500

@app.route('/health', methods=['GET'])
def health_check():
    return jsonify({'status': 'ok', 'index_loaded': index is not None})

def signal_handler(sig, frame):
    """处理Ctrl+C信号"""
    print('\n收到退出信号，正在关闭服务器...')
    cleanup()
    sys.exit(0)

def cleanup():
    """清理资源"""
    try:
        # 这里可以添加需要清理的资源
        # 比如关闭数据库连接、文件句柄等
        print('正在清理资源...')
        # 如果有其他需要清理的资源，在这里添加
    except Exception as e:
        print(f'清理资源时出错: {e}')
    finally:
        print('服务器已正常关闭')

if __name__ == '__main__':
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # 注册退出时的清理函数
    atexit.register(cleanup)

    print('启动WKU AI Helper服务器...')
    print('按 Ctrl+C 停止服务器')

    try:
        app.run(debug=True, host='0.0.0.0', port=5001, use_reloader=False)
    except KeyboardInterrupt:
        print('\n收到键盘中断，正在关闭...')
    except Exception as e:
        print(f'服务器运行出错: {e}')
    finally:
        print('服务器已停止')
