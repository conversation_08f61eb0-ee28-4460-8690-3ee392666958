import json
import faiss
import numpy as np
import os
import pickle
import requests

def get_embedding(text):
    response = requests.post(
        'http://localhost:11434/api/embeddings',
        json={
            "model": "nomic-embed-text:v1.5",  # Use nomic-embed-text:v1.5
            "prompt": text
        }
    )
    data = response.json()
    return data["embedding"]

def build_index():
    # Create directory for FAISS index
    os.makedirs('backend/faiss_index', exist_ok=True)
    
    # Read the JSONL file
    jsonl_path = "data/wku.jsonl"
    with open(jsonl_path, "r", encoding="utf-8") as f:
        docs = [json.loads(line) for line in f]
    
    # Extract texts from documents
    texts = [doc["text"] for doc in docs]
    
    # Get embeddings for all texts
    embeddings = []
    for text in texts:
        embedding = get_embedding(text)
        embeddings.append(embedding)
    
    # Convert embeddings to numpy array
    embeddings_np = np.array(embeddings, dtype=np.float32)
    
    # Create FAISS index
    dimension = len(embeddings[0])
    index = faiss.IndexFlatL2(dimension)
    index.add(embeddings_np)
    
    # Save FAISS index and texts
    faiss.write_index(index, "backend/faiss_index/index.faiss")
    with open("backend/faiss_index/texts.pkl", "wb") as f:
        pickle.dump(texts, f)
    
    print("✅ FAISS index built successfully")

if __name__ == "__main__":
    build_index()
