# backend/model_interface.py
import requests

API_URL = "https://api.deepseek.com/v1/chat/completions"
API_KEY = "***********************************"  # ← 替换为你自己的 key

def call_deepseek(prompt):
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json"
    }
    data = {
        "model": "deepseek-chat",
        "messages": [
            {"role": "user", "content": prompt}
        ]
    }

    response = requests.post(API_URL, headers=headers, json=data)
    if response.status_code == 200:
        return response.json()['choices'][0]['message']['content']
    else:
        return f"调用模型出错: {response.text}"
