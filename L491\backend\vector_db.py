import faiss
import numpy as np
import json
import pickle
import requests
import os

def get_embedding(text):
    response = requests.post(
        'http://localhost:11434/api/embeddings',
        json={
            "model": "nomic-embed-text:v1.5",  # Use nomic-embed-text:v1.5
            "prompt": text
        }
    )
    data = response.json()
    return data["embedding"]

def load_vector_db():
    # Check if index exists
    if not os.path.exists('backend/faiss_index/index.faiss'):
        from build_faiss import build_index
        build_index()
    
    # Load FAISS index and texts
    index = faiss.read_index('backend/faiss_index/index.faiss')
    with open('backend/faiss_index/texts.pkl', 'rb') as f:
        texts = pickle.load(f)
    
    return index, texts

def retrieve_context(query: str, top_k: int = 3):
    index, texts = load_vector_db()
    
    # Get embedding for query
    query_embedding = np.array([get_embedding(query)], dtype=np.float32)
    
    # Ensure top_k is not larger than the number of texts
    top_k = min(top_k, len(texts))
    
    if top_k > 0:
        # Search in the index
        _, indices = index.search(query_embedding, top_k)
        
        # Return relevant texts
        return [texts[i] for i in indices[0]]
    else:
        return []
