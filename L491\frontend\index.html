<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WKU's AI Helper</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        h1 {
            color: white;
            text-align: center;
            margin-bottom: 30px;
            font-weight: 300;
            font-size: 2.5em;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        .chat-container {
            background-color: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            width: 100%;
            max-width: none;
            box-sizing: border-box;
            overflow: visible;
        }
        #user-query {
            width: 100%;
            padding: 16px;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            margin-bottom: 20px;
            font-size: 16px;
            font-family: inherit;
            resize: vertical;
            transition: border-color 0.3s ease;
            box-sizing: border-box;
        }
        #user-query:focus {
            outline: none;
            border-color: #1a73e8;
            box-shadow: 0 0 0 3px rgba(26, 115, 232, 0.1);
        }
        button {
            background: linear-gradient(135deg, #1a73e8, #1557b0);
            color: white;
            border: none;
            padding: 14px 28px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(26, 115, 232, 0.3);
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(26, 115, 232, 0.4);
        }
        button:active {
            transform: translateY(0);
        }
        #response {
            margin-top: 30px;
            padding: 24px;
            border-radius: 12px;
            background-color: #f8f9fa;
            min-height: 120px;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
            width: 100%;
            max-width: none;
            box-sizing: border-box;
            overflow: visible;
        }
        #response.has-content {
            background-color: white;
            border: 1px solid #e3f2fd;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }
        .formatted-response {
            background: white;
            border: 1px solid #e9ecef;
            padding: 15px;
            border-radius: 4px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
        }
        .loading {
            text-align: center;
            color: #666;
        }
    </style>
</head>
<body>
    <h1>WKU's AI Helper</h1>
    <div class="chat-container">
        <textarea id="user-query" placeholder="Type your questions about WKU..." rows="4"></textarea><br>
        <button onclick="sendQuery()">Send Question</button>
        <div id="response"></div>
    </div>

    <script>
        function formatResponse(text) {
            console.log('开始处理文本:', text);

            text = text.trim();

            // 保护代码块
            const codeBlocks = [];
            text = text.replace(/```[\s\S]*?```/g, (match, index) => {
                codeBlocks.push(match);
                return `__CODE_BLOCK_${codeBlocks.length - 1}__`;
            });

            // 保护行内代码
            const inlineCodes = [];
            text = text.replace(/`([^`]+)`/g, (match, code) => {
                inlineCodes.push(`<code>${code}</code>`);
                return `__INLINE_CODE_${inlineCodes.length - 1}__`;
            });

            // 清理多余的星号
            text = text.replace(/^\*+\s*/gm, '');
            text = text.replace(/\*+\s*$/gm, '');
            text = text.replace(/\*+(\s+)/g, '$1');

            // 简化的标题处理
            text = text.replace(/^(#{1,6})\s*(.+?)$/gm, (match, hashes, title) => {
                const level = hashes.length;
                const cleanTitle = title.trim();
                console.log(`处理标题: ${hashes} "${cleanTitle}"`);

                if (level === 1) return `<h1 style="color: #1a73e8; word-break: normal; white-space: nowrap; overflow: visible;">${cleanTitle}</h1>`;
                if (level === 2) return `<h2 style="color: #1a73e8; word-break: normal; white-space: nowrap; overflow: visible;">${cleanTitle}</h2>`;
                if (level === 3) return `<h3 style="color: #1967d2; word-break: normal; white-space: nowrap; overflow: visible;">${cleanTitle}</h3>`;
                return `<h4 style="color: #1967d2; word-break: normal; white-space: nowrap; overflow: visible;">${cleanTitle}</h4>`;
            });

            // 处理粗体和斜体
            text = text.replace(/\*\*\*((?:[^*]|\*(?!\*))+?)\*\*\*/g, '<strong><em>$1</em></strong>');
            text = text.replace(/\*\*((?:[^*]|\*(?!\*))+?)\*\*/g, '<strong>$1</strong>');
            text = text.replace(/(?<!\*)\*([^*\n]+?)\*(?!\*)/g, '<em>$1</em>');

            // 处理引用
            text = text.replace(/^>\s+(.*?)$/gm, '<blockquote>$1</blockquote>');

            // 清理残留的星号
            text = text.replace(/(?<![a-zA-Z])\*(?![a-zA-Z*])/g, '');
            text = text.replace(/\s*\*\s*/g, ' ');
            text = text.replace(/\*+/g, '');

            // 处理段落
            const lines = text.split('\n');
            const processedLines = [];

            for (let line of lines) {
                line = line.trim();
                if (!line) {
                    processedLines.push('');
                    continue;
                }

                if (line.match(/^<[h1-6|blockquote|ul|ol|li]/)) {
                    processedLines.push(line);
                } else if (line.match(/^[-*]\s/)) {
                    const content = line.replace(/^[-*]\s+/, '');
                    processedLines.push(`<li>${content}</li>`);
                } else if (line.match(/^\d+\.\s/)) {
                    const content = line.replace(/^\d+\.\s+/, '');
                    processedLines.push(`<li>${content}</li>`);
                } else {
                    processedLines.push(`<p>${line}</p>`);
                }
            }

            let result = processedLines.join('');

            // 恢复代码块
            codeBlocks.forEach((block, index) => {
                let code = block.replace(/```(\w*)\n?([\s\S]*?)```/g, '<pre><code>$2</code></pre>');
                result = result.replace(`__CODE_BLOCK_${index}__`, code);
            });

            // 恢复行内代码
            inlineCodes.forEach((code, index) => {
                result = result.replace(`__INLINE_CODE_${index}__`, code);
            });

            console.log('处理完成的结果:', result);
            return result;
        }

        function sendQuery() {
            const query = document.getElementById("user-query").value;
            if (!query.trim()) {
                alert("Please enter a question");
                return;
            }

            const responseDiv = document.getElementById("response");
            responseDiv.innerHTML = '<p class="loading">Thinking...</p>';

            fetch('http://127.0.0.1:5001/query', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ query: query })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                console.log('原始响应:', data.response);
                const formattedResponse = formatResponse(data.response);
                console.log('格式化后的响应:', formattedResponse);
                // 完全按照api_test.html的简洁方式显示
                responseDiv.innerHTML = `<div class="formatted-response">${formattedResponse}</div>`;
                responseDiv.classList.add('has-content');
            })
            .catch(error => {
                console.error('Error:', error);
                responseDiv.innerHTML = `<p style="color: red;">Error: ${error.message || 'Failed to get response from server'}</p>`;
            });
        }

        // Allow Enter key to submit the query
        document.getElementById("user-query").addEventListener("keydown", function(event) {
            if (event.key === "Enter" && !event.shiftKey) {
                event.preventDefault();
                sendQuery();
            }
        });
    </script>
</body>
</html>