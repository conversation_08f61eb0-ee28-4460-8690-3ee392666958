// frontend/script.js
async function sendQuery() {
    const query = document.getElementById("user-query").value;
    if (!query.trim()) {
        alert("Please enter a question");
        return;
    }
    
    const responseDiv = document.getElementById("response");
    responseDiv.innerHTML = '<p class="loading">Thinking...</p>';
    
    try {
        const response = await fetch("http://127.0.0.1:5000/query", {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify({ query })
        });

        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        
        const data = await response.json();
        responseDiv.innerHTML = `<h3>AI Response:</h3><p>${data.response}</p>`;
    } catch (error) {
        console.error('Error:', error);
        responseDiv.innerHTML = `<p style="color: red;">Error: ${error.message || 'Failed to get response from server'}</p>`;
    }
}

// Allow Enter key to submit the query
document.getElementById("user-query")?.addEventListener("keydown", function(event) {
    if (event.key === "Enter" && !event.shiftKey) {
        event.preventDefault();
        sendQuery();
    }
});