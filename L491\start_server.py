#!/usr/bin/env python3
"""
WKU AI Helper服务器启动脚本
提供更好的进程管理和信号处理
"""

import os
import sys
import signal
import subprocess
import time
import atexit

def cleanup():
    """清理函数"""
    print("\n服务器启动脚本已退出")

def signal_handler(sig, frame):
    """信号处理函数"""
    print(f"\n收到信号 {sig}，正在退出...")
    sys.exit(0)

def main():
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    atexit.register(cleanup)
    
    # 获取脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    print("=" * 50)
    print("🚀 启动 WKU AI Helper 服务器")
    print("=" * 50)
    
    # 检查后端目录是否存在
    backend_dir = os.path.join(script_dir, 'backend')
    if not os.path.exists(backend_dir):
        print("❌ 错误: backend 目录不存在")
        sys.exit(1)
    
    # 检查app.py是否存在
    app_path = os.path.join(backend_dir, 'app.py')
    if not os.path.exists(app_path):
        print("❌ 错误: backend/app.py 文件不存在")
        sys.exit(1)
    
    print(f"📁 工作目录: {script_dir}")
    print(f"🐍 Python版本: {sys.version}")
    print("⏳ 正在启动服务器...")
    
    try:
        # 启动Flask应用
        env = os.environ.copy()
        env['PYTHONUNBUFFERED'] = '1'  # 确保输出立即显示
        
        process = subprocess.Popen(
            [sys.executable, 'backend/app.py'],
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1,
            env=env
        )
        
        print(f"✅ 服务器进程已启动 (PID: {process.pid})")
        print("🔗 服务器地址: http://127.0.0.1:5001")
        print("🌐 前端页面: 打开 frontend/index.html")
        print("\n按 Ctrl+C 停止服务器\n")
        
        # 实时显示输出
        for line in process.stdout:
            print(line.rstrip())
            
        # 等待进程结束
        process.wait()
        
    except KeyboardInterrupt:
        print("\n⏹️  收到键盘中断信号")
        if 'process' in locals():
            print("🔄 正在停止服务器进程...")
            process.terminate()
            try:
                process.wait(timeout=5)
                print("✅ 服务器已正常停止")
            except subprocess.TimeoutExpired:
                print("⚠️  强制终止服务器进程...")
                process.kill()
                process.wait()
                print("✅ 服务器已强制停止")
    except Exception as e:
        print(f"❌ 启动服务器时出错: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()