# WKU AI Helper 使用说明

## 🚀 快速启动

### 方法1：使用改进的启动脚本（推荐）
```bash
python start_server.py
```

### 方法2：直接启动Flask应用
```bash
python backend/app.py
```

## ⏹️ 停止服务器

现在可以正常使用 **Ctrl+C** 来停止服务器，问题已解决！

### 改进内容

1. **信号处理优化**
   - 添加了 `SIGINT` 和 `SIGTERM` 信号处理器
   - 支持优雅的服务器关闭
   - 可以正常响应 Ctrl+C 中断信号

2. **资源清理**
   - 自动清理服务器资源
   - 防止进程僵死
   - 显示清晰的关闭状态信息

3. **启动脚本**
   - 提供了更智能的启动脚本 `start_server.py`
   - 更好的进程管理
   - 实时输出显示

## 🌐 访问方式

- **后端API**: http://127.0.0.1:5001
- **前端页面**: 直接在浏览器中打开 `frontend/index.html`
- **健康检查**: http://127.0.0.1:5001/health

## 🛠️ 故障排除

如果仍然遇到问题，可以使用以下命令强制清理残留进程：

```bash
# 查找并终止残留进程
pkill -f "python.*app.py"

# 或者使用端口查找
lsof -i :5001 | grep python | awk '{print $2}' | xargs kill
```

## ✅ 测试

运行测试脚本验证服务器功能：
```bash
python test_server.py
```

现在你可以放心使用 Ctrl+C 来停止服务器了！🎉

## 📁 项目文件结构详解

### 🔧 核心后端文件

#### `backend/app.py` - 主服务器应用 ⭐
**作用**: Flask Web服务器的核心文件，整个项目的心脏
**详细功能**:
- 🌐 提供HTTP API接口：
  - `/query` - 处理用户问题查询
  - `/health` - 服务器健康检查
- 📚 加载和管理FAISS向量索引
- 🔍 处理用户查询请求的完整流程
- 🤖 调用Ollama API获取文本嵌入向量
- 🎯 使用FAISS进行高效的相似度搜索
- 💬 调用DeepSeek API生成智能回答
- 🌍 支持中英文双语自动检测和回复
- 🛡️ 优雅的信号处理和资源清理机制
- 🔄 跨域支持，允许前端访问

#### `backend/model_interface.py` - AI模型接口
**作用**: DeepSeek API的专用调用接口
**详细功能**:
- 🔑 管理DeepSeek API密钥和认证
- 📡 封装API调用逻辑，简化使用
- 🎨 格式化请求和响应数据
- ⚡ 处理API错误和异常情况
- 💡 返回AI生成的智能回答

#### `backend/build_faiss.py` - 向量索引构建器
**作用**: 构建FAISS向量数据库的专用工具
**详细功能**:
- 📖 读取`data/wku.jsonl`中的知识库数据
- 🔄 批量调用Ollama API为每条文本生成嵌入向量
- 🏗️ 创建高效的FAISS索引用于快速相似度搜索
- 💾 保存索引文件(`index.faiss`)和文本数据(`texts.pkl`)
- 📁 自动创建必要的目录结构
- ✅ 提供构建完成的确认信息

#### `backend/vector_db.py` - 向量数据库工具
**作用**: 向量数据库的辅助工具和备用接口
**详细功能**:
- 🔧 提供向量数据库加载功能
- 🎯 封装嵌入向量获取逻辑
- 🔄 作为备用的数据库操作接口
- 🛠️ 自动检查索引文件是否存在
- 📦 统一的数据加载接口

### 🌐 前端文件

#### `frontend/index.html` - 主前端页面 ⭐
**作用**: 用户交互的主界面，项目的门面
**详细功能**:
- 💬 提供美观的聊天式问答界面
- 🎨 支持Markdown格式的回答显示，包括：
  - 标题层级显示
  - 粗体和斜体文本
  - 项目符号和编号列表
  - 代码块和行内代码
  - 链接和引用
- 📱 响应式设计，适配不同屏幕尺寸
- ⚡ 实时与后端API通信
- 🎯 智能的加载状态显示
- ⌨️ 支持Enter键快速提交
- 🎪 现代化的CSS动画效果

#### `frontend/script.js` - 前端JavaScript逻辑（备用）
**作用**: 独立的前端交互逻辑文件
**详细功能**:
- 🖱️ 处理用户输入和点击事件
- 📡 发送AJAX请求到后端API
- 📺 动态更新页面内容显示AI回答
- ⚠️ **注意**: 端口设置为5000，与主应用的5001不同
- 🔧 可作为调试或替代方案使用

### 📊 数据文件

#### `data/wku.jsonl` - 知识库数据 ⭐
**作用**: 项目的核心知识库，所有AI回答的数据源
**详细内容**:
- 🏫 温州肯恩大学的全面信息（80条精选记录）
- 📚 涵盖内容：
  - 学校基本介绍和历史
  - 学院设置和专业信息
  - 宿舍详细信息和价格
  - 校园商店和餐厅指南
  - 师资力量和学术成就
  - 国际合作和交流项目
- 📝 JSONL格式，每行一个完整的JSON对象
- 🏷️ 标准字段：`id`(唯一标识)、`source`(数据来源)、`text`(文本内容)

### 🚀 启动和配置文件

#### `start_server.py` - 智能启动脚本（推荐使用）⭐
**作用**: 改进的服务器启动器，提供最佳用户体验
**详细功能**:
- 🎛️ 智能的进程管理和监控
- 🛡️ 优雅的信号处理（支持Ctrl+C）
- 📺 实时输出显示和状态反馈
- 🔍 自动检查文件和目录完整性
- 🚨 详细的错误提示和故障诊断
- 🔄 自动设置环境变量
- ⏹️ 安全的服务器关闭流程

#### `requirements.txt` - Python依赖列表
**作用**: 项目依赖管理文件
**包含的关键依赖**:
- `flask` - 轻量级Web框架，提供HTTP服务
- `flask-cors` - 跨域资源共享支持
- `faiss-cpu` - Facebook的高效向量搜索引擎
- `sentence-transformers` - 预训练的文本嵌入模型
- `requests` - 简洁的HTTP请求库

### 📚 文档文件

#### `README.md` - 英文项目说明
**作用**: 项目的官方英文文档
**内容包括**:
- 📋 项目概述和技术架构
- 🛠️ 详细的安装和配置步骤
- 🚀 运行指南和使用方法
- 🔧 故障排除和常见问题
- 🌍 面向国际用户的完整说明

#### `使用说明.md` - 中文使用指南（当前文件）
**作用**: 详细的中文使用说明和文档
**内容包括**:
- 🚀 快速启动指南
- 📁 完整的文件结构说明
- 🛠️ 故障排除方案
- 💡 使用技巧和最佳实践

#### `color_test.html` - 颜色测试页面
**作用**: 前端样式开发和测试工具
**功能**:
- 🎨 测试和预览不同的颜色方案
- 🖼️ 调试前端视觉效果
- 🔧 开发阶段的样式验证工具

### 🗂️ 自动生成的文件夹

#### `backend/faiss_index/` - 向量索引存储目录
**作用**: 存储构建好的向量索引和相关数据
**包含文件**:
- `index.faiss` - FAISS向量索引文件（二进制格式）
- `texts.pkl` - 对应的文本数据（Python pickle格式）
- 📊 这些文件由`build_faiss.py`自动生成
- ⚡ 提供毫秒级的相似度搜索性能

#### `backend/__pycache__/` - Python缓存目录
**作用**: Python解释器自动生成的字节码缓存
**包含**:
- `.pyc`文件 - 编译后的Python字节码
- 🚀 提高程序启动和运行速度
- 🔄 Python自动管理，无需手动操作

## 🔄 完整工作流程

1. **📊 数据准备**: `data/wku.jsonl` 提供结构化的知识库数据
2. **🏗️ 索引构建**: `build_faiss.py` 将文本转换为向量并创建搜索索引
3. **🚀 服务启动**: `start_server.py` 启动后端服务器
4. **🌐 用户交互**: `frontend/index.html` 提供友好的Web界面
5. **🔍 查询处理**: `app.py` 协调各组件处理用户请求
6. **🤖 智能回答**: 通过DeepSeek API生成专业回答

## 💡 技术架构说明

这是一个完整的**RAG（检索增强生成）系统**，专门为温州肯恩大学信息查询设计：

- **🔍 检索层**: 使用FAISS进行高效向量搜索
- **🤖 生成层**: 使用DeepSeek API进行智能回答生成
- **🌐 服务层**: Flask提供稳定的Web API服务
- **💻 展示层**: 现代化的Web前端界面
- **📊 数据层**: 结构化的JSONL知识库