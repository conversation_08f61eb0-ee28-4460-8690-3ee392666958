from flask import Flask, render_template, request, jsonify, send_from_directory
import os
import time
import uuid
import numpy as np
from PIL import Image

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import torchvision.transforms as transforms
import torchvision.models as models
import copy
import io
import json
import datetime
from skimage.metrics import structural_similarity as ssim
import cv2

# 导入数据库模块
from database import TransferHistoryDB, test_connection

app = Flask(__name__)

# 配置上传文件夹
UPLOAD_FOLDER = 'static/uploads'
RESULT_FOLDER = 'static/results'
HISTORY_FOLDER = 'static/history'
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['RESULT_FOLDER'] = RESULT_FOLDER
app.config['HISTORY_FOLDER'] = HISTORY_FOLDER
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 限制上传大小为16MB

# 确保上传和结果目录存在
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(RESULT_FOLDER, exist_ok=True)
os.makedirs(HISTORY_FOLDER, exist_ok=True)


# GAN网络定义
class ResidualBlock(nn.Module):
    """残差块，用于生成器"""
    def __init__(self, in_features):
        super(ResidualBlock, self).__init__()

        self.conv_block = nn.Sequential(
            nn.ReflectionPad2d(1),
            nn.Conv2d(in_features, in_features, 3),
            nn.InstanceNorm2d(in_features),
            nn.ReLU(inplace=True),
            nn.ReflectionPad2d(1),
            nn.Conv2d(in_features, in_features, 3),
            nn.InstanceNorm2d(in_features)
        )

    def forward(self, x):
        return x + self.conv_block(x)


class Generator(nn.Module):
    """CycleGAN生成器"""
    def __init__(self, input_nc=3, output_nc=3, n_residual_blocks=9):
        super(Generator, self).__init__()

        # 初始卷积层
        model = [
            nn.ReflectionPad2d(3),
            nn.Conv2d(input_nc, 64, 7),
            nn.InstanceNorm2d(64),
            nn.ReLU(inplace=True)
        ]

        # 下采样
        in_features = 64
        out_features = in_features * 2
        for _ in range(2):
            model += [
                nn.Conv2d(in_features, out_features, 3, stride=2, padding=1),
                nn.InstanceNorm2d(out_features),
                nn.ReLU(inplace=True)
            ]
            in_features = out_features
            out_features = in_features * 2

        # 残差块
        for _ in range(n_residual_blocks):
            model += [ResidualBlock(in_features)]

        # 上采样
        out_features = in_features // 2
        for _ in range(2):
            model += [
                nn.ConvTranspose2d(in_features, out_features, 3, stride=2, padding=1, output_padding=1),
                nn.InstanceNorm2d(out_features),
                nn.ReLU(inplace=True)
            ]
            in_features = out_features
            out_features = in_features // 2

        # 输出层
        model += [
            nn.ReflectionPad2d(3),
            nn.Conv2d(64, output_nc, 7),
            nn.Tanh()
        ]

        self.model = nn.Sequential(*model)

    def forward(self, x):
        return self.model(x)


class Discriminator(nn.Module):
    """PatchGAN判别器"""
    def __init__(self, input_nc=3):
        super(Discriminator, self).__init__()

        def discriminator_block(in_filters, out_filters, normalize=True):
            layers = [nn.Conv2d(in_filters, out_filters, 4, stride=2, padding=1)]
            if normalize:
                layers.append(nn.InstanceNorm2d(out_filters))
            layers.append(nn.LeakyReLU(0.2, inplace=True))
            return layers

        self.model = nn.Sequential(
            *discriminator_block(input_nc, 64, normalize=False),
            *discriminator_block(64, 128),
            *discriminator_block(128, 256),
            *discriminator_block(256, 512),
            nn.ZeroPad2d((1, 0, 1, 0)),
            nn.Conv2d(512, 1, 4, padding=1)
        )

    def forward(self, img):
        return self.model(img)


# SSIM计算函数
def calculate_ssim(image1_path, image2_path):
    """计算两幅图像的结构相似性指数(SSIM)"""
    try:
        image1 = cv2.imread(image1_path)
        image2 = cv2.imread(image2_path)

        if image1 is None or image2 is None:
            raise ValueError("无法读取图像文件")

        if image1.shape != image2.shape:
            min_height = min(image1.shape[0], image2.shape[0])
            min_width = min(image1.shape[1], image2.shape[1])
            image1 = cv2.resize(image1, (min_width, min_height))
            image2 = cv2.resize(image2, (min_width, min_height))

        gray1 = cv2.cvtColor(image1, cv2.COLOR_BGR2GRAY)
        gray2 = cv2.cvtColor(image2, cv2.COLOR_BGR2GRAY)

        score, _ = ssim(gray1, gray2, full=True)
        return round(float(score), 4)
    except Exception as e:
        print(f"SSIM计算失败: {str(e)}")
        return None


class StyleTransferGAN:
    """结合VGG和GAN的风格迁移系统"""
    def __init__(self):
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        print(f"使用设备: {self.device}")

        # 加载VGG19用于特征提取和损失计算
        self.vgg = models.vgg19(weights='DEFAULT').features.to(self.device).eval()
        for param in self.vgg.parameters():
            param.requires_grad_(False)

        # 初始化GAN网络
        self.generator = Generator().to(self.device)
        self.discriminator = Discriminator().to(self.device)

        # 图片处理尺寸
        self.imsize = 256  # GAN通常使用256x256
        self.original_size = None

        # 数据预处理
        self.loader = transforms.Compose([
            transforms.Resize((self.imsize, self.imsize)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5])  # GAN常用的归一化
        ])

        # VGG特征提取的归一化
        self.vgg_normalize = transforms.Normalize(mean=[0.485, 0.456, 0.406],
                                                 std=[0.229, 0.224, 0.225])

        # 特征层定义
        self.content_layers = ['conv4_2']
        self.style_layers = ['conv1_1', 'conv2_1', 'conv3_1', 'conv4_1', 'conv5_1']

        # 损失权重
        self.content_weights = {'conv4_2': 1.0}
        self.style_weights = {
            'conv1_1': 1.0,
            'conv2_1': 0.8,
            'conv3_1': 0.6,
            'conv4_1': 0.4,
            'conv5_1': 0.2
        }

        # 尝试加载预训练的GAN模型（如果存在）
        self.load_pretrained_models()

    def load_pretrained_models(self):
        """加载预训练的GAN模型"""
        try:
            # 尝试加载不同的预训练模型
            model_paths = [
                'model_directory/style_monet.pth',
                'model_directory/generator.pth'
            ]

            loaded = False
            for generator_path in model_paths:
                if os.path.exists(generator_path):
                    try:
                        print(f"尝试加载预训练模型: {generator_path}")

                        # 加载模型权重
                        checkpoint = torch.load(generator_path, map_location=self.device)

                        # 处理不同的checkpoint格式
                        state_dict = None
                        if isinstance(checkpoint, dict):
                            # 尝试不同的键名
                            for key in ['netG_A', 'netG_B', 'generator', 'model', 'state_dict']:
                                if key in checkpoint:
                                    state_dict = checkpoint[key]
                                    print(f"找到生成器权重，键名: {key}")
                                    break

                            # 如果没找到特定键，直接使用整个字典
                            if state_dict is None:
                                state_dict = checkpoint
                                print("使用整个checkpoint作为state_dict")
                        else:
                            state_dict = checkpoint

                        # 过滤和适配权重
                        model_dict = self.generator.state_dict()
                        filtered_dict = {}

                        print(f"模型期望的参数数量: {len(model_dict)}")
                        print(f"预训练模型的参数数量: {len(state_dict)}")

                        for k, v in state_dict.items():
                            # 移除可能的前缀
                            key = k
                            for prefix in ['module.', 'netG_A.', 'netG_B.', 'generator.']:
                                if key.startswith(prefix):
                                    key = key[len(prefix):]
                                    break

                            if key in model_dict:
                                if v.shape == model_dict[key].shape:
                                    filtered_dict[key] = v
                                else:
                                    print(f"参数 {key} 形状不匹配: 期望 {model_dict[key].shape}, 实际 {v.shape}")
                            else:
                                # 尝试部分匹配
                                for model_key in model_dict.keys():
                                    if model_key.endswith(key) or key.endswith(model_key):
                                        if v.shape == model_dict[model_key].shape:
                                            filtered_dict[model_key] = v
                                            print(f"部分匹配: {key} -> {model_key}")
                                            break

                        # 加载过滤后的权重
                        if filtered_dict:
                            missing_keys, unexpected_keys = self.generator.load_state_dict(filtered_dict, strict=False)
                            print(f"已加载预训练模型: {generator_path}")
                            print(f"成功加载 {len(filtered_dict)}/{len(model_dict)} 个参数")

                            if missing_keys:
                                print(f"缺失的参数: {len(missing_keys)} 个")
                            if unexpected_keys:
                                print(f"意外的参数: {len(unexpected_keys)} 个")

                            # 检查是否加载了足够的参数
                            if len(filtered_dict) >= len(model_dict) * 0.8:  # 至少80%的参数
                                loaded = True
                                print("预训练模型加载成功！")
                                break
                            else:
                                print("加载的参数太少，继续尝试其他模型...")
                        else:
                            print(f"模型 {generator_path} 结构完全不匹配，跳过")
                    except Exception as e:
                        print(f"加载模型 {generator_path} 失败: {e}")
                        continue

            if not loaded:
                print("警告：未找到兼容的预训练模型，将使用随机初始化的权重")
                print("这可能导致SSIM分数过高，建议下载正确的预训练模型")

        except Exception as e:
            print(f"加载预训练模型失败: {e}")
            print("将使用随机初始化的权重继续运行")

    def image_loader(self, image_path):
        """加载图像"""
        image = Image.open(image_path).convert('RGB')
        self.original_size = image.size
        print(f"加载图片 {image_path} 成功，原始尺寸: {self.original_size}")

        image = self.loader(image).unsqueeze(0)
        return image.to(self.device, torch.float)

    def save_image(self, tensor, path):
        """保存图像"""
        # 反归一化 (从[-1,1]转换到[0,1])
        image = tensor.cpu().clone().squeeze(0)
        image = (image + 1) / 2.0
        image = image.clamp(0, 1)

        # 转换为PIL图像
        image = transforms.ToPILImage()(image)

        # 恢复到原始尺寸
        if self.original_size:
            image = image.resize(self.original_size, Image.LANCZOS)

        image.save(path)
        print(f"已保存图片到: {path}，恢复尺寸: {image.size}")
        return image

    def gram_matrix(self, input):
        """计算Gram矩阵，用于捕获风格特征"""
        b, c, h, w = input.size()
        features = input.view(b * c, h * w)
        gram = torch.mm(features, features.t())
        return gram.div(b * c * h * w)

    def get_features(self, image, layers=None):
        """从VGG网络中提取特征"""
        if layers is None:
            layers = {
                '0': 'conv1_1',
                '5': 'conv2_1',
                '10': 'conv3_1',
                '19': 'conv4_1',
                '21': 'conv4_2',
                '28': 'conv5_1'
            }

        # 将图像从GAN的[-1,1]范围转换到VGG的[0,1]范围，然后应用VGG归一化
        vgg_input = (image + 1) / 2.0  # [-1,1] -> [0,1]
        vgg_input = self.vgg_normalize(vgg_input)

        features = {}
        x = vgg_input
        for name, layer in self.vgg._modules.items():
            x = layer(x)
            if name in layers:
                features[layers[name]] = x
        return features

    def adversarial_loss(self, pred, target_is_real):
        """对抗损失"""
        if target_is_real:
            target = torch.ones_like(pred)
        else:
            target = torch.zeros_like(pred)
        return F.mse_loss(pred, target)

    def cycle_consistency_loss(self, real_img, cycled_img):
        """循环一致性损失"""
        return F.l1_loss(cycled_img, real_img)

    def identity_loss(self, real_img, same_img):
        """身份损失"""
        return F.l1_loss(same_img, real_img)

    def perceptual_loss(self, generated, target):
        """感知损失（基于VGG特征）"""
        gen_features = self.get_features(generated)
        target_features = self.get_features(target)

        loss = 0
        for layer in self.content_layers:
            if layer in gen_features and layer in target_features:
                loss += F.mse_loss(gen_features[layer], target_features[layer])

        return loss

    def style_loss(self, generated, style):
        """风格损失（基于Gram矩阵）"""
        gen_features = self.get_features(generated)
        style_features = self.get_features(style)

        loss = 0
        for layer in self.style_layers:
            if layer in gen_features and layer in style_features:
                gen_gram = self.gram_matrix(gen_features[layer])
                style_gram = self.gram_matrix(style_features[layer])
                layer_loss = self.style_weights.get(layer, 1.0) * F.mse_loss(gen_gram, style_gram)
                loss += layer_loss

        return loss

    def transfer_style_gan(self, content_path, style_path, output_path,
                          num_epochs=50, progress_callback=None,
                          use_pretrained=True, train_mode=False):
        """
        VGG19+GAN混合风格迁移方法

        Args:
            content_path: 内容图片路径
            style_path: 风格图片路径
            output_path: 输出路径
            num_epochs: 训练轮数
            progress_callback: 进度回调函数
            use_pretrained: 是否使用预训练模型
            train_mode: 是否进行训练（False表示仅推理）
        """
        print("\n=== 开始VGG19+GAN混合风格迁移 ===")
        print(f"参数设置: 轮数={num_epochs}, 使用预训练={use_pretrained}, 训练模式={train_mode}")

        if progress_callback:
            progress_callback(5, "正在加载图片...")

        # 加载图片
        content = self.image_loader(content_path)
        style = self.image_loader(style_path)

        if progress_callback:
            progress_callback(10, "正在初始化混合模型...")

        # 使用新的混合方法
        return self.hybrid_vgg_gan_transfer(content, style, output_path,
                                          num_epochs, progress_callback,
                                          use_pretrained, train_mode)

    def hybrid_vgg_gan_transfer(self, content, style, output_path,
                               num_epochs, progress_callback,
                               use_pretrained, train_mode):
        """
        VGG19+GAN混合风格迁移的核心方法

        策略：
        1. 使用VGG19进行精确的风格特征提取
        2. 使用GAN生成器进行高质量图像生成
        3. 结合两者优势，降低SSIM分数
        """
        start_time = time.time()

        if progress_callback:
            progress_callback(15, "分析风格特征...")

        # 第一步：使用VGG19进行风格分析
        vgg_stylized = self.vgg_style_transfer(content, style, num_epochs//2)

        if progress_callback:
            progress_callback(40, "GAN生成器处理...")

        # 第二步：使用GAN生成器增强质量
        if use_pretrained:
            # 使用预训练GAN作为质量增强器
            gan_enhanced = self.generator(vgg_stylized)
        else:
            # 直接使用内容图像
            gan_enhanced = self.generator(content)

        if progress_callback:
            progress_callback(70, "混合优化...")

        # 第三步：智能混合两种结果
        final_result = self.intelligent_blend(vgg_stylized, gan_enhanced, content, style)

        if progress_callback:
            progress_callback(90, "保存最终结果...")

        final_image = self.save_image(final_result, output_path)

        if progress_callback:
            progress_callback(100, "VGG19+GAN混合风格迁移完成！")

        print(f"\n混合风格迁移完成！总用时: {time.time() - start_time:.1f} 秒")
        return final_image

    def vgg_style_transfer(self, content, style, num_steps):
        """使用VGG19进行传统风格迁移"""
        print("开始VGG19风格迁移...")

        # 创建可优化的图像
        input_img = content.clone().requires_grad_(True)

        # 优化器
        optimizer = torch.optim.LBFGS([input_img])

        # 提取目标特征
        style_features = self.get_features(style)
        content_features = self.get_features(content)

        # 计算目标Gram矩阵
        style_grams = {layer: self.gram_matrix(style_features[layer])
                      for layer in self.style_layers if layer in style_features}

        run = [0]
        while run[0] <= num_steps:
            def closure():
                # 清零梯度
                optimizer.zero_grad()

                # 获取当前特征
                input_features = self.get_features(input_img)

                # 内容损失
                content_loss = 0
                for layer in self.content_layers:
                    if layer in input_features and layer in content_features:
                        content_loss += F.mse_loss(input_features[layer], content_features[layer])

                # 风格损失
                style_loss = 0
                for layer in self.style_layers:
                    if layer in input_features and layer in style_grams:
                        input_gram = self.gram_matrix(input_features[layer])
                        style_loss += F.mse_loss(input_gram, style_grams[layer])

                # 总损失
                total_loss = content_loss + style_loss * 1000000  # 风格权重
                total_loss.backward()

                run[0] += 1
                if run[0] % 10 == 0:
                    print(f'VGG步骤 {run[0]}: 内容损失={content_loss.item():.4f}, 风格损失={style_loss.item():.4f}')

                return total_loss

            optimizer.step(closure)

            # 限制像素值范围
            with torch.no_grad():
                input_img.clamp_(-1, 1)

        print("VGG19风格迁移完成")
        return input_img.detach()

    def intelligent_blend(self, vgg_result, gan_result, content, style, alpha=0.6):
        """智能混合VGG和GAN结果"""
        print("开始智能混合...")

        # 计算各结果与内容图的相似度
        vgg_content_sim = self.calculate_similarity(vgg_result, content)
        gan_content_sim = self.calculate_similarity(gan_result, content)

        # 计算各结果的风格强度
        vgg_style_strength = self.calculate_style_strength(vgg_result, style)
        gan_style_strength = self.calculate_style_strength(gan_result, style)

        print(f"VGG内容相似度: {vgg_content_sim:.4f}, 风格强度: {vgg_style_strength:.4f}")
        print(f"GAN内容相似度: {gan_content_sim:.4f}, 风格强度: {gan_style_strength:.4f}")

        # 动态调整混合权重
        if vgg_style_strength > gan_style_strength:
            # VGG风格更强，增加VGG权重
            blend_weight = alpha + 0.2
        else:
            # GAN质量更好，增加GAN权重
            blend_weight = alpha - 0.2

        blend_weight = max(0.3, min(0.8, blend_weight))  # 限制在合理范围

        print(f"使用混合权重: VGG={blend_weight:.2f}, GAN={1-blend_weight:.2f}")

        # 混合结果
        blended = vgg_result * blend_weight + gan_result * (1 - blend_weight)

        print("智能混合完成")
        return blended

    def calculate_similarity(self, img1, img2):
        """计算两图像的特征相似度"""
        features1 = self.get_features(img1)
        features2 = self.get_features(img2)

        similarity = 0
        count = 0
        for layer in ['conv4_2']:  # 使用内容层
            if layer in features1 and layer in features2:
                sim = F.cosine_similarity(
                    features1[layer].flatten(),
                    features2[layer].flatten(),
                    dim=0
                )
                similarity += sim.item()
                count += 1

        return similarity / count if count > 0 else 0

    def calculate_style_strength(self, img, style_ref):
        """计算图像的风格强度"""
        img_features = self.get_features(img)
        style_features = self.get_features(style_ref)

        style_strength = 0
        count = 0
        for layer in self.style_layers:
            if layer in img_features and layer in style_features:
                img_gram = self.gram_matrix(img_features[layer])
                style_gram = self.gram_matrix(style_features[layer])

                # 计算Gram矩阵相似度
                similarity = F.cosine_similarity(
                    img_gram.flatten(),
                    style_gram.flatten(),
                    dim=0
                )
                style_strength += similarity.item()
                count += 1

        return style_strength / count if count > 0 else 0

    def inference_mode(self, content, style, output_path, progress_callback=None):
        """推理模式：使用预训练模型直接生成"""
        if progress_callback:
            progress_callback(20, "使用预训练模型进行推理...")

        start_time = time.time()

        with torch.no_grad():
            self.generator.eval()

            if progress_callback:
                progress_callback(50, "正在生成风格迁移图像...")

            # 直接使用生成器生成结果
            # 注意：标准CycleGAN只使用内容图像作为输入
            # 风格信息已经编码在预训练的生成器权重中
            result = self.generator(content)

            # 记录风格图像信息（用于日志）
            print(f"使用风格参考: {style.shape}")

            if progress_callback:
                progress_callback(90, "正在保存结果...")

            final_image = self.save_image(result, output_path)

            if progress_callback:
                progress_callback(100, "推理完成！")

        print(f"\nGAN推理完成！总用时: {time.time() - start_time:.1f} 秒")
        return final_image

    def improved_inference_mode(self, content, style, output_path, progress_callback=None):
        """改进的推理模式：使用简化的风格增强"""
        if progress_callback:
            progress_callback(20, "使用改进的推理模式...")

        start_time = time.time()

        # 记录风格图像信息
        print(f"使用风格参考图像: {style.shape}")

        with torch.no_grad():
            self.generator.eval()

            if progress_callback:
                progress_callback(40, "生成风格迁移图像...")

            # 使用生成器生成初始结果
            generated = self.generator(content)

            if progress_callback:
                progress_callback(70, "应用简化风格增强...")

            # 应用简化的风格增强
            enhanced_result = self.simple_style_enhancement(generated, alpha=0.2)

            if progress_callback:
                progress_callback(90, "保存最终结果...")

            final_image = self.save_image(enhanced_result, output_path)

            if progress_callback:
                progress_callback(100, "改进推理完成！")

        print(f"\n改进GAN推理完成！总用时: {time.time() - start_time:.1f} 秒")
        return final_image

    def simple_style_enhancement(self, generated, alpha=0.2):
        """简化的风格增强方法，避免复杂的特征比较"""
        try:
            # 简单的颜色和对比度调整
            enhanced = generated.clone()

            # 轻微增强对比度和饱和度
            enhanced = torch.clamp(enhanced * 1.1 - 0.05, -1, 1)

            # 与原始生成结果混合
            enhanced = generated * (1 - alpha) + enhanced * alpha

            print("应用简化风格增强完成")
            return enhanced

        except Exception as e:
            print(f"简化风格增强失败，返回原始结果: {e}")
            return generated

    def apply_style_enhancement(self, generated, style_features, content, alpha=0.3):
        """应用风格增强，通过特征匹配改善风格迁移效果"""
        try:
            # 获取生成图像的特征
            gen_features = self.get_features(generated)
            content_features = self.get_features(content)

            # 创建增强的特征表示
            enhanced = generated.clone()

            # 对每个风格层进行特征调整
            for layer in self.style_layers:
                if layer in gen_features and layer in style_features and layer in content_features:
                    try:
                        # 计算风格差异（只比较Gram矩阵）
                        gen_gram = self.gram_matrix(gen_features[layer])
                        style_gram = self.gram_matrix(style_features[layer])
                        content_gram = self.gram_matrix(content_features[layer])

                        # 计算风格迁移强度（只比较相同维度的Gram矩阵）
                        style_diff = torch.norm(style_gram - gen_gram)
                        content_diff = torch.norm(content_gram - gen_gram)  # 修正：比较Gram矩阵而不是原始特征

                        # 应用风格调整
                        style_weight = self.style_weights.get(layer, 1.0) * alpha

                        # 只在风格差异较大且内容保持较好时进行调整
                        if style_diff > 0.1 and content_diff < 1.0:
                            # 计算调整强度，结合层权重
                            adjustment_strength = min(style_diff.item() * style_weight * 0.05, 0.1)  # 降低调整强度

                            # 应用轻微的风格调整
                            enhanced = enhanced * (1 - adjustment_strength) + \
                                      generated * adjustment_strength

                    except RuntimeError as e:
                        print(f"层 {layer} 特征增强失败: {e}")
                        continue  # 跳过有问题的层，继续处理其他层

            return enhanced

        except Exception as e:
            print(f"风格增强失败，返回原始生成结果: {e}")
            return generated  # 如果增强失败，返回原始生成结果


# 全局变量存储模型，避免重复加载
style_transfer_gan_model = None

def get_gan_model():
    global style_transfer_gan_model
    if style_transfer_gan_model is None:
        style_transfer_gan_model = StyleTransferGAN()
    return style_transfer_gan_model


# 图像预处理函数
def preprocess_image(image_path, enhance_contrast=False, reduce_noise=True):
    """对输入图像进行预处理，可选择增强对比度和降噪"""
    try:
        from PIL import Image, ImageEnhance, ImageFilter
        img = Image.open(image_path).convert('RGB')

        if reduce_noise:
            img = img.filter(ImageFilter.GaussianBlur(radius=0.5))

        if enhance_contrast:
            enhancer = ImageEnhance.Contrast(img)
            img = enhancer.enhance(1.2)

        processed_path = f"{image_path.rsplit('.', 1)[0]}_processed.{image_path.rsplit('.', 1)[1]}"
        img.save(processed_path)
        print(f"图像预处理完成: {processed_path}")
        return processed_path
    except Exception as e:
        print(f"图像预处理失败: {str(e)}")
        return image_path


# 颜色增强函数
def enhance_colors(style_path, saturation=1.3, brightness=1.0):
    """增强风格图像的颜色饱和度，使风格转移更鲜明"""
    try:
        from PIL import Image, ImageEnhance
        img = Image.open(style_path).convert('RGB')

        enhancer = ImageEnhance.Color(img)
        img = enhancer.enhance(saturation)

        if brightness != 1.0:
            enhancer = ImageEnhance.Brightness(img)
            img = enhancer.enhance(brightness)

        enhanced_path = f"{style_path.rsplit('.', 1)[0]}_enhanced.{style_path.rsplit('.', 1)[1]}"
        img.save(enhanced_path)
        print(f"颜色增强完成: {enhanced_path}")
        return enhanced_path
    except Exception as e:
        print(f"颜色增强失败: {str(e)}")
        return style_path


# 历史记录管理 - 使用数据库
def save_history_to_db(task_id, content_path, style_path, result_path, parameters,
                      version_number=1, processing_time=None, ssim_score=None,
                      rating=None, comment=None, model_type='gan'):
    """保存历史记录到数据库"""
    try:
        # 生成下载链接（相对于static目录）
        content_url = content_path.replace('static/', '') if content_path.startswith('static/') else content_path
        style_url = style_path.replace('static/', '') if style_path.startswith('static/') else style_path
        result_url = result_path.replace('static/', '') if result_path.startswith('static/') else result_path

        # 创建或更新主记录
        TransferHistoryDB.create_history_record(
            task_id=task_id,
            model_type=model_type,
            thumbnail_url=result_url
        )

        # 创建详细记录
        model_info = {
            'model_type': model_type,
            'device': 'cuda' if torch.cuda.is_available() else 'cpu',
            'timestamp': datetime.datetime.now().isoformat()
        }

        TransferHistoryDB.create_detail_record(
            task_id=task_id,
            version_number=version_number,
            content_image_url=content_url,
            style_image_url=style_url,
            result_image_url=result_url,
            content_image_path=content_path,
            style_image_path=style_path,
            result_image_path=result_path,
            parameters=parameters,
            processing_time=processing_time,
            ssim_score=ssim_score,
            model_info=model_info
        )

        # 如果有评分和评论，更新它们
        if rating is not None:
            TransferHistoryDB.update_rating_comment(task_id, version_number, rating, comment)

        # 更新总版本数
        TransferHistoryDB.update_total_versions(task_id)

        print(f"历史记录已保存到数据库: {task_id}, 版本: {version_number}")
        return True

    except Exception as e:
        print(f"保存历史记录到数据库失败: {e}")
        return False

# 兼容旧的save_history函数
def save_history(task_id, content_path, style_path, result_path, parameters, rating=None, comment=None,
                 version_display=None, ssim_score=None, gan_mode=False):
    """兼容旧版本的历史记录保存函数"""
    # 从version_display中提取版本号
    version_number = 1
    if version_display and 'version' in version_display.lower():
        try:
            import re
            match = re.search(r'(\d+)', version_display)
            if match:
                version_number = int(match.group(1))
        except:
            version_number = 1

    model_type = 'gan' if gan_mode else 'vgg'

    return save_history_to_db(
        task_id=task_id,
        content_path=content_path,
        style_path=style_path,
        result_path=result_path,
        parameters=parameters,
        version_number=version_number,
        ssim_score=ssim_score,
        rating=rating,
        comment=comment,
        model_type=model_type
    )


@app.route('/')
def index():
    return render_template('index_gan.html')


@app.route('/history')
def history():
    """历史记录列表页面"""
    try:
        # 从数据库获取历史记录列表
        history_list = TransferHistoryDB.get_history_list(limit=50)

        # 转换数据格式以兼容现有模板
        formatted_history = []
        for item in history_list:
            formatted_item = {
                'id': item['task_id'],
                'timestamp': item['created_time'].strftime('%Y-%m-%d %H:%M:%S') if item['created_time'] else '',
                'title': item['title'] or f"记录_{item['task_id'][:8]}",
                'model_type': item['model_type'],
                'total_versions': item['version_count'],
                'latest_rating': item['latest_rating'],
                'has_comment': bool(item['has_comment']),
                'thumbnail_url': item['thumbnail_url'],
                'is_favorite': bool(item['is_favorite']),
                'task_id': item['task_id']  # 用于详情页面链接
            }
            formatted_history.append(formatted_item)

        return render_template('history_list.html', history=formatted_history)

    except Exception as e:
        print(f"获取历史记录失败: {e}")
        # 如果数据库出错，返回空列表
        return render_template('history_list.html', history=[])


@app.route('/history/<task_id>')
def history_detail(task_id):
    """历史记录详情页面"""
    try:
        # 从数据库获取详细信息
        detail_data = TransferHistoryDB.get_history_detail(task_id)

        if not detail_data:
            return render_template('error.html', message='记录不存在'), 404

        history = detail_data['history']
        details = detail_data['details']

        # 格式化数据
        formatted_history = {
            'task_id': history['task_id'],
            'title': history['title'],
            'created_time': history['created_time'].strftime('%Y-%m-%d %H:%M:%S') if history['created_time'] else '',
            'model_type': history['model_type'],
            'total_versions': history['total_versions'],
            'latest_rating': history['latest_rating'],
            'has_comment': bool(history['has_comment']),
            'is_favorite': bool(history['is_favorite'])
        }

        # 格式化版本详情
        formatted_details = []
        for detail in details:
            formatted_detail = {
                'version_number': detail['version_number'],
                'version_name': detail['version_name'] or f"版本 {detail['version_number']}",
                'content_image_url': detail['content_image_url'],
                'style_image_url': detail['style_image_url'],
                'result_image_url': detail['result_image_url'],
                'parameters': detail['parameters'] or {},
                'processing_time': detail['processing_time'],
                'ssim_score': float(detail['ssim_score']) if detail['ssim_score'] else None,
                'user_rating': detail['user_rating'],
                'user_comment': detail['user_comment'],
                'model_info': detail['model_info'] or {},
                'created_at': detail['created_at'].strftime('%Y-%m-%d %H:%M:%S') if detail['created_at'] else ''
            }
            formatted_details.append(formatted_detail)

        return render_template('history_detail.html',
                             history=formatted_history,
                             details=formatted_details)

    except Exception as e:
        print(f"获取历史记录详情失败: {e}")
        return render_template('error.html', message='获取记录详情失败'), 500


@app.route('/upload', methods=['POST'])
def upload_file():
    if 'content_image' not in request.files or 'style_image' not in request.files:
        return jsonify({'error': '请同时上传内容图像和风格图像'}), 400

    content_file = request.files['content_image']
    style_file = request.files['style_image']

    if content_file.filename == '' or style_file.filename == '':
        return jsonify({'error': '请选择文件'}), 400

    content_filename = str(uuid.uuid4()) + os.path.splitext(content_file.filename)[1]
    style_filename = str(uuid.uuid4()) + os.path.splitext(style_file.filename)[1]

    content_path = os.path.join(app.config['UPLOAD_FOLDER'], content_filename)
    style_path = os.path.join(app.config['UPLOAD_FOLDER'], style_filename)

    content_file.save(content_path)
    style_file.save(style_path)

    task_id = str(uuid.uuid4())

    task_data = {
        'id': task_id,
        'content_path': content_path,
        'style_path': style_path,
        'progress': 0,
        'status': '已上传图片，等待处理...',
        'result_path': None,
        'parameters': {
            'epochs': 50,
            'train_mode': False,
            'use_pretrained': True,
            'gan_mode': True
        },
        'tips': 'GAN模式: 推荐使用预训练模型进行快速推理，或选择训练模式获得更好的定制效果'
    }

    app.config.setdefault('TASKS', {})[task_id] = task_data

    return jsonify({
        'success': True,
        'task_id': task_id,
        'content_url': content_path.replace('static/', ''),
        'style_url': style_path.replace('static/', ''),
        'parameters': task_data['parameters'],
        'tips': task_data['tips']
    })


@app.route('/process/<task_id>', methods=['POST'])
def process_task(task_id):
    tasks = app.config.get('TASKS', {})
    if task_id not in tasks:
        return jsonify({'error': '任务不存在'}), 404

    task = tasks[task_id]

    # 获取GAN特有的参数
    data = request.json if request.is_json else {}
    epochs = data.get('epochs', 50)
    train_mode = data.get('train_mode', False)
    use_pretrained = data.get('use_pretrained', True)

    # 获取图像增强选项
    enhance_contrast = data.get('enhance_contrast', False)
    reduce_noise = data.get('reduce_noise', True)
    enhance_colors_enabled = data.get('enhance_colors', False)
    color_saturation = data.get('color_saturation', 1.3)

    # 参数验证
    if epochs < 10:
        epochs = 10
        task['status'] = "训练轮数不能小于10，已自动调整"
    elif epochs > 200:
        epochs = 200
        task['status'] = "训练轮数不能超过200，已自动限制"

    # 更新任务参数
    task['parameters'] = {
        'epochs': epochs,
        'train_mode': train_mode,
        'use_pretrained': use_pretrained,
        'enhance_contrast': enhance_contrast,
        'reduce_noise': reduce_noise,
        'enhance_colors': enhance_colors_enabled,
        'color_saturation': color_saturation,
        'gan_mode': True
    }

    # 重置任务进度
    task['progress'] = 0
    task['status'] = '准备开始GAN处理...'
    task['start_time'] = time.time()

    # 版本管理
    if 'version' not in task:
        task['version'] = 1
    else:
        task['version'] += 1

    version = task['version']
    result_filename = f"{task_id}_gan_v{version}.png"
    result_path = os.path.join(app.config['RESULT_FOLDER'], result_filename)

    def update_progress(progress, status, interim=False):
        task['progress'] = progress
        task['status'] = status

        if interim:
            task['interim_updated'] = True
            task['interim_timestamp'] = time.time()

        if progress % 10 == 0 or progress >= 95 or interim:
            print(f"GAN进度: {progress}%, 状态: {status}")

    try:
        # 预处理图像
        update_progress(2, "正在预处理图像...")

        content_path = task['content_path']
        if enhance_contrast or reduce_noise:
            processed_content_path = preprocess_image(
                content_path,
                enhance_contrast=enhance_contrast,
                reduce_noise=reduce_noise
            )
            if processed_content_path != content_path:
                content_path = processed_content_path
                task['processed_content_path'] = processed_content_path

        style_path = task['style_path']
        if enhance_colors_enabled:
            enhanced_style_path = enhance_colors(
                style_path,
                saturation=color_saturation
            )
            if enhanced_style_path != style_path:
                style_path = enhanced_style_path
                task['enhanced_style_path'] = enhanced_style_path

        # 获取GAN模型
        update_progress(5, "正在加载GAN模型...")
        model = get_gan_model()

        # 执行GAN风格迁移
        model.transfer_style_gan(
            content_path=content_path,
            style_path=style_path,
            output_path=result_path,
            num_epochs=epochs,
            progress_callback=update_progress,
            use_pretrained=use_pretrained,
            train_mode=train_mode
        )

        # 计算处理时间
        process_time = time.time() - task['start_time']

        # 计算SSIM
        ssim_score = calculate_ssim(task['content_path'], result_path)

        # 更新任务状态
        task['progress'] = 100
        task['status'] = 'GAN处理完成'
        task['result_path'] = result_path.replace('static/', '')
        task['process_time'] = process_time
        task['version_display'] = f"GAN版本 {version}"

        # 记录处理参数
        task['applied_parameters'] = {
            'epochs': epochs,
            'train_mode': train_mode,
            'use_pretrained': use_pretrained,
            'version': version,
            'version_display': f"GAN版本 {version}",
            'process_time': int(process_time),
            'enhance_contrast': enhance_contrast,
            'reduce_noise': reduce_noise,
            'enhance_colors': enhance_colors_enabled,
            'color_saturation': color_saturation,
            'gan_mode': True,
            'ssim': ssim_score if ssim_score is not None else "计算失败",
            'timestamp': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

        # 保存到历史记录数据库
        save_history_to_db(
            task_id=task_id,
            content_path=task['content_path'],
            style_path=task['style_path'],
            result_path=task['result_path'],
            parameters=task['parameters'],
            version_number=version,
            processing_time=int(process_time),
            ssim_score=ssim_score,
            model_type='gan'
        )

        return jsonify({
            'success': True,
            'result_url': task['result_path'],
            'progress': 100,
            'status': 'GAN处理完成',
            'version': version,
            'version_display': f"GAN版本 {version}",
            'parameters': task['parameters'],
            'process_time': int(process_time),
            'ssim_score': ssim_score if ssim_score is not None else None,
            'gan_mode': True
        })

    except Exception as e:
        task['status'] = f'GAN处理错误: {str(e)}'
        print(f"GAN处理失败: {str(e)}")
        import traceback
        print(traceback.format_exc())
        return jsonify({
            'error': str(e),
            'status': task['status'],
            'progress': task['progress']
        }), 500


@app.route('/progress/<task_id>', methods=['GET'])
def get_progress(task_id):
    tasks = app.config.get('TASKS', {})
    if task_id not in tasks:
        return jsonify({'error': '任务不存在'}), 404

    task = tasks[task_id]

    # 计算预估剩余时间
    remaining_time = None
    if 'start_time' in task and task['progress'] > 0 and task['progress'] < 100:
        elapsed = time.time() - task['start_time']
        if elapsed > 0:
            remaining_time = int((elapsed / task['progress']) * (100 - task['progress']))

    response = {
        'progress': task['progress'],
        'status': task['status'],
        'result_url': task.get('result_path'),
        'version': task.get('version', 1),
        'version_display': task.get('version_display', f"GAN版本 {task.get('version', 1)}"),
        'gan_mode': True
    }

    if 'applied_parameters' in task:
        response['parameters'] = task['applied_parameters']

    if remaining_time is not None:
        response['remaining_time'] = remaining_time

    return jsonify(response)


@app.route('/rating', methods=['POST'])
def submit_rating():
    data = request.json
    task_id = data.get('task_id')
    rating = data.get('rating')
    comment = data.get('comment', '')
    version_number = data.get('version_number', 1)
    parameters = data.get('parameters', {})

    if not task_id or not rating:
        return jsonify({'error': '缺少必要参数'}), 400

    try:
        # 更新数据库中的评分和评论
        TransferHistoryDB.update_rating_comment(task_id, version_number, rating, comment)

        # 保存评分到文件（保持兼容性）
        rating_file = os.path.join('static', 'ratings.txt')
        with open(rating_file, 'a', encoding='utf-8') as f:
            f.write(f"任务ID: {task_id}, 版本: {version_number}, 评分: {rating}, 评论: {comment}, " +
                    f"GAN参数: 轮数={parameters.get('epochs', 50)}, " +
                    f"训练模式={parameters.get('train_mode', False)}, " +
                    f"使用预训练={parameters.get('use_pretrained', True)}\n")

        return jsonify({'success': True, 'message': '评分已保存'})

    except Exception as e:
        print(f"保存评分失败: {e}")
        return jsonify({'error': '保存评分失败'}), 500


@app.route('/api/favorite/<task_id>', methods=['POST'])
def toggle_favorite(task_id):
    """切换收藏状态"""
    try:
        success = TransferHistoryDB.toggle_favorite(task_id)
        if success:
            return jsonify({'success': True, 'message': '收藏状态已更新'})
        else:
            return jsonify({'error': '记录不存在'}), 404
    except Exception as e:
        print(f"切换收藏状态失败: {e}")
        return jsonify({'error': '操作失败'}), 500


@app.route('/api/delete/<task_id>', methods=['DELETE'])
def delete_history(task_id):
    """删除历史记录"""
    try:
        success = TransferHistoryDB.delete_history(task_id)
        if success:
            return jsonify({'success': True, 'message': '记录已删除'})
        else:
            return jsonify({'error': '记录不存在'}), 404
    except Exception as e:
        print(f"删除记录失败: {e}")
        return jsonify({'error': '删除失败'}), 500


if __name__ == '__main__':
    # 启动前测试数据库连接
    print("正在测试数据库连接...")
    if test_connection():
        print("数据库连接成功，启动应用...")
        app.run(host='0.0.0.0', port=5001, debug=True)  # 使用不同端口避免冲突
    else:
        print("数据库连接失败，请检查配置！")
        print("应用将继续启动，但数据库功能可能不可用。")
