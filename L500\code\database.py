import pymysql
import json
from datetime import datetime
import os
from contextlib import contextmanager

# 数据库配置
DB_CONFIG = {
    'host': 'dbconn.sealosbja.site',
    'port': 46334,
    'user': 'root',
    'password': '9sscmtk2',
    'database': 'mydb',
    'charset': 'utf8mb4',
    'autocommit': True
}

@contextmanager
def get_db_connection():
    """获取数据库连接的上下文管理器"""
    connection = None
    try:
        connection = pymysql.connect(**DB_CONFIG)
        yield connection
    except Exception as e:
        print(f"数据库连接错误: {e}")
        if connection:
            connection.rollback()
        raise
    finally:
        if connection:
            connection.close()

class TransferHistoryDB:
    """风格迁移历史记录数据库操作类"""
    
    @staticmethod
    def create_history_record(task_id, title=None, model_type='gan', thumbnail_url=None):
        """创建历史记录主表记录"""
        with get_db_connection() as conn:
            cursor = conn.cursor()
            
            sql = """
            INSERT INTO transfer_history 
            (task_id, title, created_time, model_type, thumbnail_url) 
            VALUES (%s, %s, %s, %s, %s)
            ON DUPLICATE KEY UPDATE
            updated_at = CURRENT_TIMESTAMP
            """
            
            created_time = datetime.now()
            if not title:
                title = f"风格迁移_{created_time.strftime('%Y%m%d_%H%M%S')}"
            
            cursor.execute(sql, (task_id, title, created_time, model_type, thumbnail_url))
            conn.commit()
            return cursor.lastrowid

    @staticmethod
    def create_detail_record(task_id, version_number, content_image_url, style_image_url, 
                           result_image_url, content_image_path, style_image_path, 
                           result_image_path, parameters, processing_time=None, 
                           ssim_score=None, model_info=None):
        """创建详细记录表记录"""
        with get_db_connection() as conn:
            cursor = conn.cursor()
            
            sql = """
            INSERT INTO transfer_details 
            (task_id, version_number, content_image_url, style_image_url, result_image_url,
             content_image_path, style_image_path, result_image_path, parameters, 
             processing_time, ssim_score, model_info) 
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            ON DUPLICATE KEY UPDATE
            result_image_url = VALUES(result_image_url),
            result_image_path = VALUES(result_image_path),
            parameters = VALUES(parameters),
            processing_time = VALUES(processing_time),
            ssim_score = VALUES(ssim_score),
            model_info = VALUES(model_info),
            updated_at = CURRENT_TIMESTAMP
            """
            
            # 确保parameters是JSON字符串
            if isinstance(parameters, dict):
                parameters = json.dumps(parameters, ensure_ascii=False)
            if isinstance(model_info, dict):
                model_info = json.dumps(model_info, ensure_ascii=False)
            
            cursor.execute(sql, (
                task_id, version_number, content_image_url, style_image_url, 
                result_image_url, content_image_path, style_image_path, 
                result_image_path, parameters, processing_time, ssim_score, model_info
            ))
            conn.commit()
            return cursor.lastrowid

    @staticmethod
    def update_rating_comment(task_id, version_number, rating, comment):
        """更新评分和评论"""
        with get_db_connection() as conn:
            cursor = conn.cursor()
            
            # 更新详细记录表
            detail_sql = """
            UPDATE transfer_details 
            SET user_rating = %s, user_comment = %s, updated_at = CURRENT_TIMESTAMP
            WHERE task_id = %s AND version_number = %s
            """
            cursor.execute(detail_sql, (rating, comment, task_id, version_number))
            
            # 更新主表的最新评分和评论标记
            history_sql = """
            UPDATE transfer_history 
            SET latest_rating = %s, has_comment = %s, updated_at = CURRENT_TIMESTAMP
            WHERE task_id = %s
            """
            has_comment = 1 if comment and comment.strip() else 0
            cursor.execute(history_sql, (rating, has_comment, task_id))
            
            conn.commit()

    @staticmethod
    def get_history_list(limit=50, offset=0):
        """获取历史记录列表"""
        with get_db_connection() as conn:
            cursor = conn.cursor(pymysql.cursors.DictCursor)
            
            sql = """
            SELECT h.*, 
                   COUNT(d.id) as version_count,
                   MAX(d.created_at) as last_version_time
            FROM transfer_history h
            LEFT JOIN transfer_details d ON h.task_id = d.task_id
            GROUP BY h.id
            ORDER BY h.created_time DESC
            LIMIT %s OFFSET %s
            """
            
            cursor.execute(sql, (limit, offset))
            return cursor.fetchall()

    @staticmethod
    def get_history_detail(task_id):
        """获取指定任务的详细信息"""
        with get_db_connection() as conn:
            cursor = conn.cursor(pymysql.cursors.DictCursor)
            
            # 获取主记录信息
            history_sql = """
            SELECT * FROM transfer_history WHERE task_id = %s
            """
            cursor.execute(history_sql, (task_id,))
            history = cursor.fetchone()
            
            if not history:
                return None
            
            # 获取所有版本详情
            details_sql = """
            SELECT * FROM transfer_details 
            WHERE task_id = %s 
            ORDER BY version_number ASC
            """
            cursor.execute(details_sql, (task_id,))
            details = cursor.fetchall()
            
            # 解析JSON字段
            for detail in details:
                if detail['parameters']:
                    try:
                        detail['parameters'] = json.loads(detail['parameters'])
                    except:
                        detail['parameters'] = {}
                if detail['model_info']:
                    try:
                        detail['model_info'] = json.loads(detail['model_info'])
                    except:
                        detail['model_info'] = {}
            
            return {
                'history': history,
                'details': details
            }

    @staticmethod
    def update_total_versions(task_id):
        """更新总版本数"""
        with get_db_connection() as conn:
            cursor = conn.cursor()
            
            # 计算版本数
            count_sql = "SELECT COUNT(*) as count FROM transfer_details WHERE task_id = %s"
            cursor.execute(count_sql, (task_id,))
            count = cursor.fetchone()[0]
            
            # 更新主表
            update_sql = """
            UPDATE transfer_history 
            SET total_versions = %s, updated_at = CURRENT_TIMESTAMP
            WHERE task_id = %s
            """
            cursor.execute(update_sql, (count, task_id))
            conn.commit()

    @staticmethod
    def delete_history(task_id):
        """删除历史记录（级联删除详情）"""
        with get_db_connection() as conn:
            cursor = conn.cursor()
            
            sql = "DELETE FROM transfer_history WHERE task_id = %s"
            cursor.execute(sql, (task_id,))
            conn.commit()
            return cursor.rowcount > 0

    @staticmethod
    def toggle_favorite(task_id):
        """切换收藏状态"""
        with get_db_connection() as conn:
            cursor = conn.cursor()
            
            sql = """
            UPDATE transfer_history 
            SET is_favorite = 1 - is_favorite, updated_at = CURRENT_TIMESTAMP
            WHERE task_id = %s
            """
            cursor.execute(sql, (task_id,))
            conn.commit()
            return cursor.rowcount > 0

def test_connection():
    """测试数据库连接"""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            print("数据库连接成功！")
            return True
    except Exception as e:
        print(f"数据库连接失败: {e}")
        return False

if __name__ == "__main__":
    # 测试数据库连接
    test_connection()
