#!/usr/bin/env python3
"""
下载CycleGAN预训练模型脚本

这个脚本会下载适合风格迁移的预训练模型，解决SSIM分数过高的问题。
"""

import os
import urllib.request
import sys
from pathlib import Path

# 预训练模型URL和信息
PRETRAINED_MODELS = {
    'monet2photo': {
        'url': 'https://github.com/junyanz/pytorch-CycleGAN-and-pix2pix/releases/download/v0.1/monet2photo_pretrained.zip',
        'description': '莫奈风格转照片风格',
        'filename': 'monet2photo_pretrained.zip',
        'generator_file': 'latest_net_G_A.pth'
    },
    'style_monet': {
        'url': 'https://github.com/junyanz/pytorch-CycleGAN-and-pix2pix/releases/download/v0.1/style_monet_pretrained.zip',
        'description': '照片转莫奈风格',
        'filename': 'style_monet_pretrained.zip',
        'generator_file': 'latest_net_G_A.pth'
    },
    'vangogh2photo': {
        'url': 'https://github.com/junyanz/pytorch-CycleGAN-and-pix2pix/releases/download/v0.1/vangogh2photo_pretrained.zip',
        'description': '梵高风格转照片风格',
        'filename': 'vangogh2photo_pretrained.zip',
        'generator_file': 'latest_net_G_A.pth'
    },
    'cezanne2photo': {
        'url': 'https://github.com/junyanz/pytorch-CycleGAN-and-pix2pix/releases/download/v0.1/cezanne2photo_pretrained.zip',
        'description': '塞尚风格转照片风格',
        'filename': 'cezanne2photo_pretrained.zip',
        'generator_file': 'latest_net_G_A.pth'
    }
}

def download_file(url, filename, description):
    """下载文件并显示进度"""
    print(f"正在下载 {description}...")
    print(f"URL: {url}")
    
    def progress_hook(block_num, block_size, total_size):
        if total_size > 0:
            percent = min(100, (block_num * block_size * 100) // total_size)
            sys.stdout.write(f"\r下载进度: {percent}%")
            sys.stdout.flush()
    
    try:
        urllib.request.urlretrieve(url, filename, progress_hook)
        print(f"\n✓ 下载完成: {filename}")
        return True
    except Exception as e:
        print(f"\n✗ 下载失败: {e}")
        return False

def extract_zip(zip_file, extract_to):
    """解压ZIP文件"""
    try:
        import zipfile
        with zipfile.ZipFile(zip_file, 'r') as zip_ref:
            zip_ref.extractall(extract_to)
        print(f"✓ 解压完成: {zip_file}")
        return True
    except Exception as e:
        print(f"✗ 解压失败: {e}")
        return False

def setup_model_directory():
    """设置模型目录"""
    model_dir = Path("model_directory")
    model_dir.mkdir(exist_ok=True)
    return model_dir

def download_model(model_name, model_info, model_dir):
    """下载并设置单个模型"""
    print(f"\n{'='*50}")
    print(f"处理模型: {model_name}")
    print(f"描述: {model_info['description']}")
    print(f"{'='*50}")
    
    # 下载文件
    zip_path = model_dir / model_info['filename']
    if not download_file(model_info['url'], str(zip_path), model_info['description']):
        return False
    
    # 解压文件
    extract_dir = model_dir / model_name
    extract_dir.mkdir(exist_ok=True)
    
    if not extract_zip(str(zip_path), str(extract_dir)):
        return False
    
    # 查找生成器文件
    generator_files = list(extract_dir.rglob("*net_G*.pth"))
    if generator_files:
        # 复制生成器文件到主目录
        src_file = generator_files[0]
        dst_file = model_dir / f"{model_name}.pth"
        
        import shutil
        shutil.copy2(str(src_file), str(dst_file))
        print(f"✓ 生成器模型已复制到: {dst_file}")
        
        # 如果是style_monet，也复制一份作为默认模型
        if model_name == 'style_monet':
            default_file = model_dir / "generator.pth"
            shutil.copy2(str(src_file), str(default_file))
            print(f"✓ 默认生成器模型: {default_file}")
    
    # 清理ZIP文件
    try:
        os.remove(str(zip_path))
        print(f"✓ 清理临时文件: {zip_path}")
    except:
        pass
    
    return True

def main():
    """主函数"""
    print("CycleGAN预训练模型下载器")
    print("=" * 50)
    print("这个脚本将下载适合风格迁移的预训练模型")
    print("解决SSIM分数过高的问题")
    print()
    
    # 设置模型目录
    model_dir = setup_model_directory()
    print(f"模型目录: {model_dir.absolute()}")
    
    # 显示可用模型
    print("\n可用的预训练模型:")
    for i, (name, info) in enumerate(PRETRAINED_MODELS.items(), 1):
        print(f"{i}. {name}: {info['description']}")
    
    print("\n选择要下载的模型:")
    print("0. 下载所有模型")
    print("1-4. 下载特定模型")
    print("q. 退出")
    
    choice = input("\n请输入选择 (0-4 或 q): ").strip().lower()
    
    if choice == 'q':
        print("退出下载器")
        return
    
    try:
        if choice == '0':
            # 下载所有模型
            print("\n开始下载所有模型...")
            success_count = 0
            for name, info in PRETRAINED_MODELS.items():
                if download_model(name, info, model_dir):
                    success_count += 1
            
            print(f"\n{'='*50}")
            print(f"下载完成! 成功: {success_count}/{len(PRETRAINED_MODELS)}")
            
        else:
            # 下载特定模型
            choice_idx = int(choice) - 1
            model_names = list(PRETRAINED_MODELS.keys())
            
            if 0 <= choice_idx < len(model_names):
                model_name = model_names[choice_idx]
                model_info = PRETRAINED_MODELS[model_name]
                
                if download_model(model_name, model_info, model_dir):
                    print(f"\n✓ 模型 {model_name} 下载成功!")
                else:
                    print(f"\n✗ 模型 {model_name} 下载失败!")
            else:
                print("无效的选择!")
                
    except ValueError:
        print("无效的输入!")
    except KeyboardInterrupt:
        print("\n\n下载被用户中断")
    except Exception as e:
        print(f"\n下载过程中出现错误: {e}")
    
    print("\n使用说明:")
    print("1. 下载完成后，重启 app_gan.py")
    print("2. 选择 '推理模式' 和 '启用预训练'")
    print("3. 现在SSIM分数应该会降低到合理范围 (0.3-0.7)")
    print("4. 如果仍然过高，尝试 '训练模式' 进行微调")

if __name__ == "__main__":
    main()
