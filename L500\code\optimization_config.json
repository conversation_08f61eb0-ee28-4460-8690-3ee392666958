{"loss_weights": {"lambda_adversarial": 1.0, "lambda_style": 50.0, "lambda_content": 10.0, "lambda_identity": 0.5, "lambda_cycle": 10.0}, "training_params": {"epochs": 100, "learning_rate": 0.0002, "batch_size": 1, "save_interval": 10}, "target_ssim_range": {"min": 0.6, "max": 0.8, "ideal": 0.7}, "recommendations": ["使用预训练模型作为起点", "监控SSIM变化趋势", "如果SSIM>0.9，降低identity权重", "如果SSIM<0.5，增加content权重", "定期保存检查点以便回滚"]}