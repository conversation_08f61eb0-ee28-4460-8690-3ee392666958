body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.image-preview {
    position: relative;
    width: 100%;
    height: 300px;
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    background-color: #f8f9fa;
}

.image-preview img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.placeholder-icon {
    font-size: 3rem;
    color: #adb5bd;
}

.console-box {
    background-color: #212529;
    color: #f8f9fa;
    font-family: 'Courier New', monospace;
    border-radius: 5px;
    height: 150px;
    overflow-y: auto;
}

.console-box p {
    margin-bottom: 5px;
    font-size: 0.9rem;
    white-space: pre-wrap;
    word-break: break-all;
}

.star-rating {
    display: inline-block;
    font-size: 1.5rem;
    cursor: pointer;
}

.star-rating i {
    margin-right: 5px;
    color: #ffc107;
}

.star-rating i:hover,
.star-rating i.fas {
    transform: scale(1.1);
}

/* 历史记录页面样式 */
.history-thumbnail {
    height: 100px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.history-result {
    height: 150px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.history-thumbnail img,
.history-result img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

/* 评分和评论显示样式 */
.star-rating-display i {
    font-size: 0.9rem;
    margin-right: 0.1rem;
}

.comment-text {
    max-height: 60px;
    overflow-y: auto;
    white-space: pre-line;
    background-color: #fff;
    border-radius: 4px;
    padding: 5px;
}

/* 结果预览区样式 */
#result-preview {
    max-height: 400px;
    overflow-y: auto;
    padding: 10px;
    border-radius: 4px;
    border: 1px solid #dee2e6;
    background-color: white;
    margin-bottom: 15px;
}

/* 结果区域的样式 */
#result-preview img {
    width: 100%;
    height: auto;
    object-fit: contain;
    margin-bottom: 15px;
}

/* 确保结果区域内的图片卡片样式一致 */
#result-preview .mb-3 {
    margin-bottom: 20px !important;
}

#result-preview .position-relative {
    background-color: #f8f9fa;
    border-radius: 4px;
    overflow: hidden;
}

/* 版本标签样式 */
.rounded-bottom-left {
    border-top-right-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
    border-bottom-left-radius: 0.25rem !important;
}

/* 版本控制器样式 */
.version-controller {
    display: flex;
    align-items: center;
    margin: 15px 0;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #dee2e6;
}

.version-controller .version-info {
    margin: 0 15px;
    font-weight: 500;
}

.version-controller button {
    min-width: 40px;
}

.version-controller button:disabled {
    cursor: not-allowed;
    opacity: 0.5;
}

/* 版本导航按钮样式 */
#prev-version, #next-version {
    transition: all 0.2s ease;
}

#prev-version:hover:not(:disabled),
#next-version:hover:not(:disabled) {
    background-color: #0d6efd;
    color: white;
}

/* 当前版本显示样式 */
.version-display {
    background-color: #e9ecef;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.9rem;
    font-weight: 500;
}

/* 结果操作区域样式 */
#result-actions {
    margin-top: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #dee2e6;
}

#version-info {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 10px;
    margin-top: 10px;
    text-align: left;
    font-size: 0.85rem;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

#version-info div {
    margin-bottom: 3px;
}

#version-info strong {
    color: #495057;
}

/* 处理中标签 */
#processing-label {
    font-size: 0.8rem;
    z-index: 10;
    opacity: 0.9;
}

/* 实时预览标记动画 */
@keyframes pulse {
    0% { opacity: 0.7; }
    50% { opacity: 1; }
    100% { opacity: 0.7; }
}

#realtime-badge {
    animation: pulse 1.5s infinite;
} 