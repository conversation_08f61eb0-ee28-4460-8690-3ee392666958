document.addEventListener('DOMContentLoaded', function() {
    // 全局变量
    let currentTaskId = null;
    let contentFile = null;
    let styleFile = null;
    let resultUrl = null;
    let pollingInterval = null;
    let userRating = 0;

    // 版本控制变量
    let resultVersions = [];
    let currentVersionIndex = 0;
    const MAX_VERSION_HISTORY = 10;

    // GAN参数设置变量
    let epochs = 50;
    let trainMode = false;
    let usePretrained = true;

    // 元素选择器
    const contentImageInput = document.getElementById('content-image');
    const styleImageInput = document.getElementById('style-image');
    const contentPreview = document.getElementById('content-preview');
    const stylePreview = document.getElementById('style-preview');
    const resultPreview = document.getElementById('result-preview');
    const transferBtn = document.getElementById('transfer-btn');
    const progressContainer = document.getElementById('progress-container');
    const progressBar = document.getElementById('progress-bar');
    const statusMessage = document.getElementById('status-message');
    const consoleOutput = document.getElementById('console-output');
    const ratingContainer = document.getElementById('rating-container');
    const submitRatingBtn = document.getElementById('submit-rating');
    const resultActions = document.getElementById('result-actions');
    const downloadBtn = document.getElementById('download-btn');
    const starRating = document.querySelectorAll('.star-rating i');
    const ratingValue = document.getElementById('rating-value');
    const commentTextarea = document.getElementById('comment');

    // 版本控制元素
    const prevVersionBtn = document.getElementById('prev-version');
    const nextVersionBtn = document.getElementById('next-version');
    const currentVersionSpan = document.getElementById('current-version');
    const totalVersionsSpan = document.getElementById('total-versions');

    // GAN参数元素
    const epochsSlider = document.getElementById('epochs');
    const epochsValue = document.getElementById('epochs-value');
    const inferenceMode = document.getElementById('inference-mode');
    const trainModeRadio = document.getElementById('train-mode');
    const usePretrainedCheckbox = document.getElementById('use-pretrained');
    const enhanceContrastCheckbox = document.getElementById('enhance-contrast');
    const reduceNoiseCheckbox = document.getElementById('reduce-noise');

    // 版本控制事件
    prevVersionBtn.addEventListener('click', function() {
        if (currentVersionIndex > 0) {
            currentVersionIndex--;
            updateVersionDisplay();
        }
    });

    nextVersionBtn.addEventListener('click', function() {
        if (currentVersionIndex < resultVersions.length - 1) {
            currentVersionIndex++;
            updateVersionDisplay();
        }
    });

    // 更新版本显示
    function updateVersionDisplay() {
        currentVersionSpan.textContent = currentVersionIndex + 1;
        totalVersionsSpan.textContent = resultVersions.length;

        prevVersionBtn.disabled = currentVersionIndex === 0;
        nextVersionBtn.disabled = currentVersionIndex === resultVersions.length - 1;

        if (resultVersions.length > 0) {
            const currentVersion = resultVersions[currentVersionIndex];
            resultPreview.innerHTML = '';

            const img = document.createElement('img');
            img.src = `/static/${currentVersion.url}${!currentVersion.loaded ? '?t=' + Date.now() : ''}`;
            img.alt = `GAN结果图片 (版本 ${currentVersion.version})`;
            img.className = 'img-fluid';
            img.onload = function() {
                currentVersion.loaded = true;
            };
            resultPreview.appendChild(img);

            // 添加版本信息显示
            const versionInfoDiv = document.createElement('div');
            versionInfoDiv.id = 'version-info';
            versionInfoDiv.className = 'small text-muted mt-2 bg-light p-2 rounded border-success';

            if (currentVersion.parameters) {
                versionInfoDiv.innerHTML = `
                    <div><strong><i class="fas fa-robot text-success"></i> GAN版本 ${currentVersion.version} 参数:</strong></div>
                    <div>训练轮数: ${currentVersion.parameters.epochs || '未知'}</div>
                    <div>训练模式: ${currentVersion.parameters.trainMode ? '是' : '否'}</div>
                    <div>使用预训练: ${currentVersion.parameters.usePretrained ? '是' : '否'}</div>
                    <div class="text-end mt-1"><small>${new Date(currentVersion.timestamp || Date.now()).toLocaleString()}</small></div>
                `;
            } else {
                versionInfoDiv.innerHTML = `<div><i class="fas fa-robot text-success"></i> GAN版本 ${currentVersion.version} - 参数信息不可用</div>`;
            }

            resultPreview.appendChild(versionInfoDiv);

            downloadBtn.href = `/static/${currentVersion.url}`;
            downloadBtn.download = `gan_stylized_image_v${currentVersion.version}_${Date.now()}.png`;
        }
    }

    // 添加结果版本
    function addResultVersion(url, version) {
        resultVersions.push({
            url: url,
            version: version,
            timestamp: Date.now(),
            loaded: false,
            parameters: {
                epochs: epochs,
                trainMode: trainMode,
                usePretrained: usePretrained
            }
        });

        if (resultVersions.length > MAX_VERSION_HISTORY) {
            resultVersions.shift();
        }

        currentVersionIndex = resultVersions.length - 1;
        updateVersionDisplay();
        resultActions.classList.remove('d-none');

        // 保存到本地存储
        saveVersionsToLocalStorage();
    }

    // 保存版本到本地存储
    function saveVersionsToLocalStorage() {
        try {
            const versionsToSave = resultVersions.map(v => ({
                url: v.url,
                version: v.version,
                timestamp: v.timestamp,
                parameters: v.parameters
            }));

            localStorage.setItem('ganStyleTransferVersions', JSON.stringify({
                taskId: currentTaskId,
                versions: versionsToSave
            }));
        } catch (e) {
            console.error('保存GAN版本到本地存储失败:', e);
        }
    }

    // 从本地存储加载版本历史
    function loadVersionsFromLocalStorage() {
        try {
            const savedData = localStorage.getItem('ganStyleTransferVersions');
            if (savedData) {
                const data = JSON.parse(savedData);
                if (data.taskId === currentTaskId) {
                    resultVersions = data.versions;
                    currentVersionIndex = resultVersions.length - 1;
                    updateVersionDisplay();
                    resultActions.classList.remove('d-none');
                }
            }
        } catch (e) {
            console.error('从本地存储加载GAN版本失败:', e);
        }
    }

    // GAN参数事件监听
    epochsSlider.addEventListener('input', function() {
        epochs = parseInt(this.value);
        epochsValue.textContent = epochs;
    });

    inferenceMode.addEventListener('change', function() {
        if (this.checked) {
            trainMode = false;
            addConsoleMessage('切换到推理模式：使用预训练模型快速生成');
        }
    });

    trainModeRadio.addEventListener('change', function() {
        if (this.checked) {
            trainMode = true;
            addConsoleMessage('切换到训练模式：将针对当前图片对进行训练，耗时较长但质量更高');
        }
    });

    usePretrainedCheckbox.addEventListener('change', function() {
        usePretrained = this.checked;
        addConsoleMessage(`${usePretrained ? '启用' : '禁用'}预训练模型`);
    });

    // 初始化星级评分系统
    starRating.forEach(star => {
        star.addEventListener('click', function() {
            const rating = parseInt(this.getAttribute('data-rating'));
            setRating(rating);
        });
    });

    function setRating(rating) {
        userRating = rating;
        ratingValue.textContent = `${rating}分`;
        submitRatingBtn.disabled = false;

        starRating.forEach(star => {
            const starValue = parseInt(star.getAttribute('data-rating'));
            if (starValue <= rating) {
                star.classList.remove('far');
                star.classList.add('fas');
            } else {
                star.classList.remove('fas');
                star.classList.add('far');
            }
        });
    }

    // 内容图片上传处理
    contentImageInput.addEventListener('change', function(e) {
        if (this.files && this.files[0]) {
            contentFile = this.files[0];
            const reader = new FileReader();

            reader.onload = function(e) {
                contentPreview.innerHTML = `<img src="${e.target.result}" alt="内容图片预览">`;
                checkEnableTransferButton();
            };

            reader.readAsDataURL(contentFile);
        }
    });

    // 风格图片上传处理
    styleImageInput.addEventListener('change', function(e) {
        if (this.files && this.files[0]) {
            styleFile = this.files[0];
            const reader = new FileReader();

            reader.onload = function(e) {
                stylePreview.innerHTML = `<img src="${e.target.result}" alt="风格图片预览">`;
                checkEnableTransferButton();
            };

            reader.readAsDataURL(styleFile);
        }
    });

    // 检查是否可以启用转换按钮
    function checkEnableTransferButton() {
        transferBtn.disabled = !(contentFile && styleFile);
    }

    // 开始GAN风格迁移处理
    transferBtn.addEventListener('click', function() {
        if (!contentFile || !styleFile) return;

        // 重置按钮状态
        if (transferBtn.classList.contains('btn-warning')) {
            transferBtn.innerHTML = '<i class="fas fa-robot"></i> 开始VGG19+GAN混合迁移';
            transferBtn.classList.remove('btn-warning');
            transferBtn.classList.add('btn-success');
        }

        progressContainer.classList.remove('d-none');

        // 如果已经有任务ID，说明是在重新处理
        if (currentTaskId) {
            addConsoleMessage('使用已上传图片，调整GAN参数重新开始处理...');
            addConsoleMessage(`当前GAN参数 - 轮数: ${epochs}, 模式: ${trainMode ? '训练' : '推理'}, 预训练: ${usePretrained ? '是' : '否'}`);
            startGANProcessing(currentTaskId);
            return;
        }

        // 首次处理，需要上传图片
        const formData = new FormData();
        formData.append('content_image', contentFile);
        formData.append('style_image', styleFile);

        fetch('/upload', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                currentTaskId = data.task_id;

                addConsoleMessage('已上传图片到GAN系统，准备处理...');
                addConsoleMessage(`任务ID: ${currentTaskId}`);
                addConsoleMessage(`GAN参数设置 - 轮数: ${epochs}, 模式: ${trainMode ? '训练' : '推理'}, 预训练: ${usePretrained ? '是' : '否'}`);

                startGANProcessing(currentTaskId);
            } else {
                addConsoleMessage(`错误: ${data.error}`);
            }
        })
        .catch(error => {
            addConsoleMessage(`上传错误: ${error.message}`);
        });
    });

    // 开始GAN处理
    function startGANProcessing(taskId) {
        const requestData = {
            epochs: epochs,
            train_mode: trainMode,
            use_pretrained: usePretrained,
            enhance_contrast: enhanceContrastCheckbox.checked,
            reduce_noise: reduceNoiseCheckbox.checked,
            enhance_colors: false,
            color_saturation: 1.3
        };

        fetch(`/process/${taskId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                addConsoleMessage('GAN处理已开始，正在监控进度...');
                startProgressPolling(taskId);
            } else {
                addConsoleMessage(`GAN处理启动失败: ${data.error}`);
            }
        })
        .catch(error => {
            addConsoleMessage(`GAN处理请求错误: ${error.message}`);
        });
    }

    // 开始进度轮询
    function startProgressPolling(taskId) {
        if (pollingInterval) {
            clearInterval(pollingInterval);
        }

        pollingInterval = setInterval(() => {
            fetch(`/progress/${taskId}`)
                .then(response => response.json())
                .then(data => {
                    updateProgress(data.progress, data.status);

                    if (data.remaining_time) {
                        const minutes = Math.floor(data.remaining_time / 60);
                        const seconds = data.remaining_time % 60;
                        addConsoleMessage(`预计剩余时间: ${minutes}分${seconds}秒`);
                    }

                    if (data.progress >= 100) {
                        clearInterval(pollingInterval);
                        pollingInterval = null;

                        if (data.result_url) {
                            resultUrl = data.result_url;
                            addResultVersion(data.result_url, data.version || 1);
                            addConsoleMessage('VGG19+GAN混合风格迁移完成！');

                            // 显示评分界面
                            setTimeout(() => {
                                ratingContainer.classList.remove('d-none');
                            }, 1000);

                            // 更新按钮状态
                            transferBtn.innerHTML = '<i class="fas fa-redo"></i> 重新生成混合版本';
                            transferBtn.classList.remove('btn-success');
                            transferBtn.classList.add('btn-warning');
                        }
                    }
                })
                .catch(error => {
                    addConsoleMessage(`进度查询错误: ${error.message}`);
                });
        }, 2000);
    }

    // 更新进度条
    function updateProgress(progress, status) {
        progressBar.style.width = `${progress}%`;
        progressBar.textContent = `${progress}%`;
        statusMessage.textContent = status;

        if (progress >= 100) {
            progressBar.classList.remove('progress-bar-animated');
        }
    }

    // 添加控制台消息
    function addConsoleMessage(message) {
        const timestamp = new Date().toLocaleTimeString();
        const messageElement = document.createElement('p');
        messageElement.innerHTML = `<span class="text-muted">[${timestamp}]</span> ${message}`;
        consoleOutput.appendChild(messageElement);
        consoleOutput.scrollTop = consoleOutput.scrollHeight;
    }

    // 提交评分
    submitRatingBtn.addEventListener('click', function() {
        if (userRating === 0) return;

        const comment = commentTextarea.value.trim();

        const ratingData = {
            task_id: currentTaskId,
            rating: userRating,
            comment: comment,
            version_number: currentVersionIndex + 1, // 当前显示的版本号
            parameters: {
                epochs: epochs,
                train_mode: trainMode,
                use_pretrained: usePretrained
            }
        };

        fetch('/rating', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(ratingData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                addConsoleMessage(`评分已提交: ${userRating}星`);
                if (comment) {
                    addConsoleMessage(`评论: ${comment}`);
                }

                // 隐藏评分界面
                ratingContainer.classList.add('d-none');

                // 重置评分
                setRating(0);
                commentTextarea.value = '';
                submitRatingBtn.disabled = true;
            } else {
                addConsoleMessage('评分提交失败');
            }
        })
        .catch(error => {
            addConsoleMessage(`评分提交错误: ${error.message}`);
        });
    });

    // 初始化控制台消息
    addConsoleMessage('VGG19+GAN混合风格迁移系统已就绪');
    addConsoleMessage('新系统智能融合VGG19精确风格迁移和GAN高质量生成，显著降低SSIM分数');
    addConsoleMessage('请上传内容图片和风格图片，然后调整参数开始处理');

    // 初始化参数显示
    epochsValue.textContent = epochs;
});
