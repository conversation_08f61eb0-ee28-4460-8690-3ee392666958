<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ history.title }} - 详细记录</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="/static/css/style.css">
    <style>
        .version-card {
            transition: transform 0.2s;
            border-left: 4px solid #007bff;
        }
        .version-card:hover {
            transform: translateY(-2px);
        }
        .image-container {
            height: 200px;
            overflow: hidden;
            border-radius: 8px;
            background-color: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .image-container img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }
        .parameter-badge {
            font-size: 0.75rem;
            margin: 2px;
        }
        .rating-section {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
        }
        .star-rating {
            font-size: 1.2rem;
        }
        .comment-box {
            background-color: white;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 10px;
            min-height: 60px;
        }
        .star-clickable {
            cursor: pointer;
            font-size: 1.5rem;
            margin-right: 5px;
            color: #ddd;
            transition: color 0.2s;
        }
        .star-clickable:hover,
        .star-clickable.active {
            color: #ffc107;
        }
        .star-rating-edit {
            margin: 10px 0;
        }
        .rating-edit {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            border: 1px solid #dee2e6;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <!-- 页面头部 -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="fw-bold">
                            <i class="fas fa-file-alt text-primary"></i> {{ history.title }}
                        </h1>
                        <p class="lead">
                            <span class="badge {{ 'bg-success' if history.model_type == 'gan' else 'bg-primary' }} me-2">
                                {{ 'GAN模式' if history.model_type == 'gan' else 'VGG模式' }}
                            </span>
                            创建时间: {{ history.created_time }}
                        </p>
                    </div>
                    <div>
                        <a href="/history" class="btn btn-outline-secondary me-2">
                            <i class="fas fa-arrow-left"></i> 返回列表
                        </a>
                        <button class="btn {{ 'btn-warning' if history.is_favorite else 'btn-outline-warning' }}"
                                onclick="toggleFavorite('{{ history.task_id }}', this)">
                            <i class="fas fa-star"></i> {{ '已收藏' if history.is_favorite else '收藏' }}
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 概览信息 -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-3">
                                <h5 class="text-primary">{{ history.total_versions }}</h5>
                                <small class="text-muted">总版本数</small>
                            </div>
                            <div class="col-md-3">
                                <h5 class="text-success">
                                    {% if history.latest_rating %}
                                        {{ history.latest_rating }}/5
                                    {% else %}
                                        未评分
                                    {% endif %}
                                </h5>
                                <small class="text-muted">最新评分</small>
                            </div>
                            <div class="col-md-3">
                                <h5 class="text-info">
                                    {% if history.has_comment %}
                                        <i class="fas fa-check text-success"></i>
                                    {% else %}
                                        <i class="fas fa-times text-muted"></i>
                                    {% endif %}
                                </h5>
                                <small class="text-muted">有评论</small>
                            </div>
                            <div class="col-md-3">
                                <h5 class="text-warning">{{ history.model_type.upper() }}</h5>
                                <small class="text-muted">模型类型</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 版本详情 -->
        <div class="row">
            <div class="col-md-12">
                <h3 class="mb-3">
                    <i class="fas fa-layer-group"></i> 版本详情
                    <small class="text-muted">({{ details|length }} 个版本)</small>
                </h3>
            </div>
        </div>

        {% for detail in details %}
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card version-card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-code-branch text-primary"></i>
                                {{ detail.version_name }}
                            </h5>
                            <div>
                                <span class="badge bg-secondary">版本 {{ detail.version_number }}</span>
                                <small class="text-muted ms-2">{{ detail.created_at }}</small>
                            </div>
                        </div>
                    </div>

                    <div class="card-body">
                        <!-- 图片展示 -->
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <h6 class="text-center mb-2">内容图片</h6>
                                <div class="image-container">
                                    <img src="/static/{{ detail.content_image_url }}" alt="内容图片" class="img-fluid">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <h6 class="text-center mb-2">风格图片</h6>
                                <div class="image-container">
                                    <img src="/static/{{ detail.style_image_url }}" alt="风格图片" class="img-fluid">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <h6 class="text-center mb-2">结果图片</h6>
                                <div class="image-container">
                                    {% if detail.result_image_url %}
                                        <img src="/static/{{ detail.result_image_url }}" alt="结果图片" class="img-fluid">
                                    {% else %}
                                        <div class="text-center text-muted">
                                            <i class="fas fa-image fa-2x"></i>
                                            <p class="small mt-2">暂无结果</p>
                                        </div>
                                    {% endif %}
                                </div>
                                {% if detail.result_image_url %}
                                <div class="text-center mt-2">
                                    <a href="/static/{{ detail.result_image_url }}" download
                                       class="btn btn-sm btn-success">
                                        <i class="fas fa-download"></i> 下载
                                    </a>
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- 参数信息 -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <h6><i class="fas fa-cogs"></i> 生成参数</h6>
                                <div class="mb-2">
                                    {% if detail.parameters %}
                                        {% for key, value in detail.parameters.items() %}
                                            <span class="badge bg-primary parameter-badge">{{ key }}: {{ value }}</span>
                                        {% endfor %}
                                    {% else %}
                                        <span class="text-muted">无参数信息</span>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="fas fa-chart-line"></i> 性能指标</h6>
                                <div class="row">
                                    <div class="col-6">
                                        <small class="text-muted">处理时间</small>
                                        <div class="fw-bold">
                                            {% if detail.processing_time %}
                                                {{ detail.processing_time }}秒
                                            {% else %}
                                                未知
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">SSIM评分</small>
                                        <div class="fw-bold">
                                            {% if detail.ssim_score %}
                                                {{ "%.4f"|format(detail.ssim_score) }}
                                            {% else %}
                                                未计算
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 用户评分和评论 -->
                        <div class="rating-section">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6><i class="fas fa-star"></i> 用户反馈</h6>
                                <button class="btn btn-sm btn-outline-primary"
                                        data-task-id="{{ history.task_id }}"
                                        data-version="{{ detail.version_number }}"
                                        data-rating="{{ detail.user_rating or 0 }}"
                                        data-comment="{{ detail.user_comment or '' }}"
                                        onclick="editRatingFromButton(this)">
                                    <i class="fas fa-edit"></i> 编辑反馈
                                </button>
                            </div>

                            <!-- 显示模式 -->
                            <div id="rating-display-{{ detail.version_number }}" class="rating-display">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-2">
                                            <strong>评分:</strong>
                                            {% if detail.user_rating %}
                                                <div class="star-rating d-inline-block ms-2">
                                                    {% for i in range(1, 6) %}
                                                        {% if i <= detail.user_rating %}
                                                            <i class="fas fa-star text-warning"></i>
                                                        {% else %}
                                                            <i class="far fa-star text-warning"></i>
                                                        {% endif %}
                                                    {% endfor %}
                                                </div>
                                                <span class="ms-2">({{ detail.user_rating }}/5)</span>
                                            {% else %}
                                                <span class="text-muted ms-2">暂无评分</span>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-2">
                                            <strong>评论:</strong>
                                            <div class="comment-box mt-1">
                                                {% if detail.user_comment %}
                                                    {{ detail.user_comment }}
                                                {% else %}
                                                    <span class="text-muted">暂无评论</span>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 编辑模式 -->
                            <div id="rating-edit-{{ detail.version_number }}" class="rating-edit" style="display: none;">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label"><strong>评分:</strong></label>
                                            <div class="star-rating-edit" data-version="{{ detail.version_number }}">
                                                {% for i in range(1, 6) %}
                                                    <i class="fas fa-star star-clickable"
                                                       data-rating="{{ i }}"
                                                       data-version="{{ detail.version_number }}"></i>
                                                {% endfor %}
                                            </div>
                                            <input type="hidden" id="rating-value-{{ detail.version_number }}" value="{{ detail.user_rating or 0 }}">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label"><strong>评论:</strong></label>
                                            <textarea class="form-control"
                                                      id="comment-text-{{ detail.version_number }}"
                                                      rows="3"
                                                      placeholder="请输入您的评论...">{{ detail.user_comment or '' }}</textarea>
                                        </div>
                                    </div>
                                </div>
                                <div class="text-end">
                                    <button class="btn btn-secondary me-2"
                                            onclick="cancelEditRating({{ detail.version_number }})">
                                        <i class="fas fa-times"></i> 取消
                                    </button>
                                    <button class="btn btn-primary"
                                            onclick="saveRating('{{ history.task_id }}', {{ detail.version_number }})">
                                        <i class="fas fa-save"></i> 保存
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}

        {% if not details %}
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-warning text-center">
                    <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                    <h4>暂无版本详情</h4>
                    <p>该记录暂时没有详细的版本信息。</p>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 切换收藏状态
        function toggleFavorite(taskId, button) {
            fetch(`/api/favorite/${taskId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 切换按钮样式和文本
                    if (button.classList.contains('btn-warning')) {
                        button.classList.remove('btn-warning');
                        button.classList.add('btn-outline-warning');
                        button.innerHTML = '<i class="fas fa-star"></i> 收藏';
                    } else {
                        button.classList.remove('btn-outline-warning');
                        button.classList.add('btn-warning');
                        button.innerHTML = '<i class="fas fa-star"></i> 已收藏';
                    }
                } else {
                    alert('操作失败: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('操作失败');
            });
        }

        // 从按钮数据属性编辑评分和评论
        function editRatingFromButton(button) {
            const taskId = button.getAttribute('data-task-id');
            const versionNumber = parseInt(button.getAttribute('data-version'));
            const currentRating = parseInt(button.getAttribute('data-rating'));
            const currentComment = button.getAttribute('data-comment');

            editRating(taskId, versionNumber, currentRating, currentComment);
        }

        // 编辑评分和评论
        function editRating(taskId, versionNumber, currentRating, currentComment) {
            // 隐藏显示模式，显示编辑模式
            document.getElementById(`rating-display-${versionNumber}`).style.display = 'none';
            document.getElementById(`rating-edit-${versionNumber}`).style.display = 'block';

            // 设置当前评分
            updateStarRating(versionNumber, currentRating);

            // 设置当前评论
            document.getElementById(`comment-text-${versionNumber}`).value = currentComment;
        }

        // 取消编辑
        function cancelEditRating(versionNumber) {
            // 显示显示模式，隐藏编辑模式
            document.getElementById(`rating-display-${versionNumber}`).style.display = 'block';
            document.getElementById(`rating-edit-${versionNumber}`).style.display = 'none';
        }

        // 保存评分和评论
        function saveRating(taskId, versionNumber) {
            const rating = parseInt(document.getElementById(`rating-value-${versionNumber}`).value);
            const comment = document.getElementById(`comment-text-${versionNumber}`).value;

            if (rating === 0) {
                alert('请选择评分！');
                return;
            }

            fetch('/rating', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    task_id: taskId,
                    version_number: versionNumber,
                    rating: rating,
                    comment: comment
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('评分已保存！');
                    // 刷新页面以显示更新后的评分
                    location.reload();
                } else {
                    alert('保存失败: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('保存失败');
            });
        }

        // 更新星级评分显示
        function updateStarRating(versionNumber, rating) {
            const stars = document.querySelectorAll(`[data-version="${versionNumber}"] .star-clickable`);
            stars.forEach((star, index) => {
                if (index < rating) {
                    star.classList.add('active');
                } else {
                    star.classList.remove('active');
                }
            });
            document.getElementById(`rating-value-${versionNumber}`).value = rating;
        }

        // 页面加载完成后初始化星级评分点击事件
        document.addEventListener('DOMContentLoaded', function() {
            // 为所有可点击的星星添加事件监听器
            document.querySelectorAll('.star-clickable').forEach(star => {
                star.addEventListener('click', function() {
                    const rating = parseInt(this.getAttribute('data-rating'));
                    const versionNumber = this.getAttribute('data-version');
                    updateStarRating(versionNumber, rating);
                });

                // 鼠标悬停效果
                star.addEventListener('mouseenter', function() {
                    const rating = parseInt(this.getAttribute('data-rating'));
                    const versionNumber = this.getAttribute('data-version');
                    const stars = document.querySelectorAll(`[data-version="${versionNumber}"] .star-clickable`);

                    stars.forEach((s, index) => {
                        if (index < rating) {
                            s.style.color = '#ffc107';
                        } else {
                            s.style.color = '#ddd';
                        }
                    });
                });

                // 鼠标离开时恢复当前评分状态
                star.addEventListener('mouseleave', function() {
                    const versionNumber = this.getAttribute('data-version');
                    const currentRating = parseInt(document.getElementById(`rating-value-${versionNumber}`).value);
                    updateStarRating(versionNumber, currentRating);
                });
            });
        });
    </script>
</body>
</html>
