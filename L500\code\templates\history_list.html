<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>风格迁移历史记录</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="/static/css/style.css">
    <style>
        .history-card {
            transition: transform 0.2s, box-shadow 0.2s;
            cursor: pointer;
        }
        .history-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        .model-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }
        .thumbnail-container {
            height: 120px;
            overflow: hidden;
            border-radius: 8px;
            background-color: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .thumbnail-container img {
            max-width: 100%;
            max-height: 100%;
            object-fit: cover;
        }
        .favorite-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 10;
            width: 32px;
            height: 32px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .card-header-content {
            padding-right: 50px; /* 为收藏按钮留出空间 */
        }
        .model-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            margin-top: 5px; /* 稍微下移避免重叠 */
        }
        .stats-row {
            background-color: #f8f9fa;
            border-radius: 6px;
            padding: 8px;
            margin: 8px 0;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="fw-bold">
                            <i class="fas fa-history text-primary"></i> 风格迁移历史记录
                        </h1>
                        <p class="lead">查看您的风格迁移历史记录和结果</p>
                    </div>
                    <div>
                        <a href="/" class="btn btn-success me-2">
                            <i class="fas fa-robot"></i> GAN模式
                        </a>
                        <a href="/" class="btn btn-primary">
                            <i class="fas fa-home"></i> 返回首页
                        </a>
                    </div>
                </div>
            </div>
        </div>

        {% if history %}
        <div class="row mb-3">
            <div class="col-md-12">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    共找到 <strong>{{ history|length }}</strong> 条记录。点击任意记录查看详细信息和所有版本。
                </div>
            </div>
        </div>

        <div class="row">
            {% for item in history %}
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card history-card h-100 position-relative"
                     onclick="window.location.href='/history/{{ item.task_id }}'">

                    <!-- 收藏按钮 -->
                    <button class="btn btn-sm favorite-btn {{ 'btn-warning' if item.is_favorite else 'btn-outline-secondary' }}"
                            onclick="event.stopPropagation(); toggleFavorite('{{ item.task_id }}', this)">
                        <i class="fas fa-star"></i>
                    </button>

                    <div class="card-header">
                        <div class="card-header-content">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <h6 class="card-title mb-1">{{ item.title }}</h6>
                                    <small class="text-muted">{{ item.timestamp }}</small>
                                </div>
                                <div class="text-end">
                                    <span class="badge model-badge {{ 'bg-success' if item.model_type == 'gan' else 'bg-primary' }}">
                                        {{ 'GAN模式' if item.model_type == 'gan' else 'VGG模式' }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-body">
                        <!-- 缩略图 -->
                        <div class="thumbnail-container mb-3">
                            {% if item.thumbnail_url %}
                                <img src="/static/{{ item.thumbnail_url }}" alt="结果预览" class="img-fluid">
                            {% else %}
                                <div class="text-center text-muted">
                                    <i class="fas fa-image fa-2x"></i>
                                    <p class="small mt-2">暂无预览</p>
                                </div>
                            {% endif %}
                        </div>

                        <!-- 统计信息 -->
                        <div class="stats-row">
                            <div class="row text-center">
                                <div class="col-4">
                                    <div class="small text-muted">版本数</div>
                                    <div class="fw-bold">{{ item.total_versions }}</div>
                                </div>
                                <div class="col-4">
                                    <div class="small text-muted">评分</div>
                                    <div class="fw-bold">
                                        {% if item.latest_rating %}
                                            {% for i in range(1, 6) %}
                                                {% if i <= item.latest_rating %}
                                                    <i class="fas fa-star text-warning"></i>
                                                {% else %}
                                                    <i class="far fa-star text-warning"></i>
                                                {% endif %}
                                            {% endfor %}
                                        {% else %}
                                            <span class="text-muted">未评分</span>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="small text-muted">评论</div>
                                    <div class="fw-bold">
                                        {% if item.has_comment %}
                                            <i class="fas fa-comment text-success"></i>
                                        {% else %}
                                            <i class="far fa-comment text-muted"></i>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer">
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">
                                <i class="fas fa-clock"></i> {{ item.timestamp }}
                            </small>
                            <div>
                                <button class="btn btn-sm btn-outline-primary"
                                        onclick="event.stopPropagation(); window.location.href='/history/{{ item.task_id }}'">
                                    <i class="fas fa-eye"></i> 查看详情
                                </button>
                                <button class="btn btn-sm btn-outline-danger"
                                        onclick="event.stopPropagation(); deleteRecord('{{ item.task_id }}')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-info text-center">
                    <i class="fas fa-info-circle fa-3x mb-3"></i>
                    <h4>暂无历史记录</h4>
                    <p>您还没有进行过风格迁移。</p>
                    <a href="/" class="btn btn-success">
                        <i class="fas fa-robot"></i> 开始创作
                    </a>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 切换收藏状态
        function toggleFavorite(taskId, button) {
            fetch(`/api/favorite/${taskId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 切换按钮样式
                    if (button.classList.contains('btn-warning')) {
                        button.classList.remove('btn-warning');
                        button.classList.add('btn-outline-secondary');
                    } else {
                        button.classList.remove('btn-outline-secondary');
                        button.classList.add('btn-warning');
                    }
                } else {
                    alert('操作失败: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('操作失败');
            });
        }

        // 删除记录
        function deleteRecord(taskId) {
            if (confirm('确定要删除这条记录吗？此操作不可恢复。')) {
                fetch(`/api/delete/${taskId}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload(); // 刷新页面
                    } else {
                        alert('删除失败: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('删除失败');
                });
            }
        }
    </script>
</body>
</html>
