<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GAN增强风格迁移系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="/static/css/style.css">
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-md-12">
                <div class="text-center mb-4">
                    <h1 class="fw-bold">🎨 VGG19+GAN混合风格迁移系统</h1>
                    <p class="lead">智能融合VGG19精确风格迁移和GAN高质量生成，实现更强大的AI艺术创作</p>
                    <div class="mt-2">
                        <span class="badge bg-success me-2">
                            <i class="fas fa-robot"></i> GAN模式
                        </span>
                        <a href="/history" class="btn btn-outline-primary">
                            <i class="fas fa-history"></i> 查看历史记录
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-4">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">内容图片</h5>
                    </div>
                    <div class="card-body text-center">
                        <div id="content-preview" class="image-preview mb-3">
                            <i class="fas fa-image placeholder-icon"></i>
                        </div>
                        <div class="d-grid gap-2">
                            <input type="file" id="content-image" class="d-none" accept="image/*">
                            <button class="btn btn-primary" onclick="document.getElementById('content-image').click()">
                                选择内容图片
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">风格图片</h5>
                    </div>
                    <div class="card-body text-center">
                        <div id="style-preview" class="image-preview mb-3">
                            <i class="fas fa-image placeholder-icon"></i>
                        </div>
                        <div class="d-grid gap-2">
                            <input type="file" id="style-image" class="d-none" accept="image/*">
                            <button class="btn btn-primary" onclick="document.getElementById('style-image').click()">
                                选择风格图片
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card mb-3">
                    <div class="card-header bg-success text-white">
                        <i class="fas fa-robot"></i> GAN生成结果
                    </div>
                    <div class="card-body text-center">
                        <div id="result-preview" class="image-preview">
                            <i class="fas fa-image fa-3x text-muted mb-2"></i>
                            <p class="text-muted">GAN风格迁移结果将会在这里显示</p>
                        </div>

                        <div id="version-controls" class="mt-3 mb-3">
                            <div class="d-flex justify-content-between align-items-center border rounded p-2 bg-light">
                                <button id="prev-version" class="btn btn-sm btn-outline-success" disabled>
                                    <i class="fas fa-chevron-left"></i> 上一版本
                                </button>
                                <span class="px-3 py-1 bg-white rounded border">
                                    GAN版本 <span id="current-version" class="fw-bold">1</span>/<span id="total-versions">1</span>
                                </span>
                                <button id="next-version" class="btn btn-sm btn-outline-success" disabled>
                                    下一版本 <i class="fas fa-chevron-right"></i>
                                </button>
                            </div>
                            <div class="mt-2 text-start small text-muted">
                                <i class="fas fa-info-circle"></i> GAN模式支持训练和推理两种模式，可生成多个版本对比
                            </div>
                        </div>

                        <div id="result-actions" class="d-none mt-3">
                            <a id="download-btn" href="#" class="btn btn-success w-100" download="gan_stylized_image.png">
                                <i class="fas fa-download"></i> 下载当前版本
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-cogs"></i> GAN参数设置
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="epochs" class="form-label">训练轮数</label>
                                    <input type="range" class="form-range" id="epochs" min="10" max="200" step="10" value="50">
                                    <div class="d-flex justify-content-between">
                                        <small>快速</small>
                                        <small id="epochs-value">50</small>
                                        <small>精细</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">处理模式</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="mode" id="inference-mode" value="inference" checked>
                                        <label class="form-check-label" for="inference-mode">
                                            <i class="fas fa-bolt text-warning"></i> 推理模式（快速）
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="mode" id="train-mode" value="train">
                                        <label class="form-check-label" for="train-mode">
                                            <i class="fas fa-graduation-cap text-info"></i> 训练模式（高质量）
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">预训练模型</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="use-pretrained" checked>
                                        <label class="form-check-label" for="use-pretrained">
                                            <i class="fas fa-download text-primary"></i> 使用预训练权重
                                        </label>
                                    </div>
                                    <small class="text-muted">推荐开启以获得更好效果</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">图像增强</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="enhance-contrast">
                                        <label class="form-check-label" for="enhance-contrast">
                                            增强对比度
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="reduce-noise" checked>
                                        <label class="form-check-label" for="reduce-noise">
                                            降噪处理
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-info mt-3">
                            <h6><i class="fas fa-lightbulb"></i> VGG19+GAN混合模式说明：</h6>
                            <ul class="mb-0 small">
                                <li><strong>智能混合</strong>：自动融合VGG19精确风格迁移和GAN高质量生成</li>
                                <li><strong>推理模式</strong>：使用预训练模型快速生成，适合快速预览</li>
                                <li><strong>训练模式</strong>：针对当前图片对进行优化，质量更高但耗时更长</li>
                                <li><strong>预训练权重</strong>：基于大量数据训练的模型，建议保持开启</li>
                                <li><strong>SSIM优化</strong>：新方法显著降低SSIM分数，提升风格迁移效果</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-md-12">
                <div class="d-grid">
                    <button id="transfer-btn" class="btn btn-lg btn-success" disabled>
                        <i class="fas fa-robot"></i> 开始GAN风格迁移
                    </button>
                    <small class="text-center mt-2 text-muted">
                        <i class="fas fa-info-circle"></i> GAN模式结合了传统风格迁移和生成对抗网络的优势，请耐心等待处理完成。
                    </small>
                </div>
            </div>
        </div>

        <div id="progress-container" class="row mb-4 d-none">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="fas fa-robot text-success"></i> GAN处理进度
                        </h5>
                        <div class="progress mb-2">
                            <div id="progress-bar" class="progress-bar progress-bar-striped progress-bar-animated bg-success"
                                 role="progressbar" style="width: 0%"></div>
                        </div>
                        <p id="status-message" class="text-muted">等待处理...</p>
                        <div id="console-output" class="console-box mt-3 p-3">
                            <p>GAN系统准备就绪，等待开始处理...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div id="rating-container" class="row mb-4 d-none">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-star"></i> GAN结果评分
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-success mb-3">
                            <i class="fas fa-robot"></i> 您的GAN风格迁移已完成！请对结果进行评分，这将帮助改进模型性能。
                        </div>
                        <div class="mb-3">
                            <label class="form-label">对GAN生成结果的满意度：</label>
                            <div class="star-rating">
                                <i class="far fa-star" data-rating="1"></i>
                                <i class="far fa-star" data-rating="2"></i>
                                <i class="far fa-star" data-rating="3"></i>
                                <i class="far fa-star" data-rating="4"></i>
                                <i class="far fa-star" data-rating="5"></i>
                            </div>
                            <span id="rating-value" class="ms-2">0分</span>
                        </div>
                        <div class="mb-3">
                            <label for="comment" class="form-label">评论反馈：</label>
                            <textarea id="comment" class="form-control" rows="3"
                                      placeholder="请分享您对GAN生成结果的看法..."></textarea>
                        </div>
                        <button id="submit-rating" class="btn btn-success" disabled>
                            <i class="fas fa-paper-plane"></i> 提交评分
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/main_gan.js"></script>
</body>
</html>
