from flask import Flask, render_template, request, jsonify
import torch
import pandas as pd
import numpy as np
import os
import json
import random

from model import FakeReviewDetector
from preprocess import Preprocessor
from utils import predict

app = Flask(__name__)

# Global variables
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
model = None
preprocessor = None
test_samples = None

def load_model():
    """Load the trained model and preprocessor"""
    global model, preprocessor
    
    # Load preprocessor
    preprocessor = Preprocessor.load('preprocessor.pkl')
    
    # Define model parameters
    vocab_size = len(preprocessor.word_to_idx)
    embedding_dim = preprocessor.embeddings.shape[1]
    hidden_dim = 256
    output_dim = 2  # Binary classification
    n_layers = 2
    dropout = 0.5
    pad_idx = preprocessor.word_to_idx['<PAD>']
    
    # Create model
    model = FakeReviewDetector(
        vocab_size=vocab_size,
        embedding_dim=embedding_dim,
        hidden_dim=hidden_dim,
        output_dim=output_dim,
        n_layers=n_layers,
        dropout=dropout,
        pad_idx=pad_idx,
        pretrained_embeddings=torch.FloatTensor(preprocessor.embeddings)
    ).to(device)
    
    # Load trained model weights
    model.load_state_dict(torch.load('best_model.pt', map_location=device))
    model.eval()

def load_test_samples():
    """Load some test samples from the OpSpam dataset"""
    global test_samples
    
    base_path = 'data/op_spam_v1.4'
    samples = []
    
    # Load some genuine reviews
    truthful_pos_path = os.path.join(base_path, 'positive_polarity', 'truthful_from_TripAdvisor')
    folders = os.listdir(truthful_pos_path)[:2]  # Just use a few folders
    for folder in folders:
        folder_path = os.path.join(truthful_pos_path, folder)
        if os.path.isdir(folder_path):
            files = os.listdir(folder_path)[:5]  # Take 5 samples from each folder
            for filename in files:
                file_path = os.path.join(folder_path, filename)
                with open(file_path, 'r', encoding='latin-1') as f:
                    samples.append({
                        'text': f.read().strip(),
                        'actual_label': 'GENUINE'
                    })
    
    # Load some fake reviews
    deceptive_pos_path = os.path.join(base_path, 'positive_polarity', 'deceptive_from_MTurk')
    folders = os.listdir(deceptive_pos_path)[:2]  # Just use a few folders
    for folder in folders:
        folder_path = os.path.join(deceptive_pos_path, folder)
        if os.path.isdir(folder_path):
            files = os.listdir(folder_path)[:5]  # Take 5 samples from each folder
            for filename in files:
                file_path = os.path.join(folder_path, filename)
                with open(file_path, 'r', encoding='latin-1') as f:
                    samples.append({
                        'text': f.read().strip(),
                        'actual_label': 'FAKE'
                    })
    
    test_samples = samples
    return samples

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/detect', methods=['POST'])
def detect_fake_review():
    data = request.get_json()
    review_text = data.get('review', '')
    
    if not review_text:
        return jsonify({'error': 'No review text provided'}), 400
    
    # Make prediction
    label, probability = predict(model, review_text, preprocessor, device)
    
    result = {
        'is_fake': bool(label),
        'probability': float(probability),
        'prediction': 'FAKE' if label else 'GENUINE'
    }
    
    return jsonify(result)

@app.route('/get_sample', methods=['GET'])
def get_sample():
    """Get a random sample from the test set"""
    sample = random.choice(test_samples)
    return jsonify(sample)

if __name__ == '__main__':
    # Load the model and test samples before starting the app
    load_model()
    load_test_samples()
    app.run(debug=True) 