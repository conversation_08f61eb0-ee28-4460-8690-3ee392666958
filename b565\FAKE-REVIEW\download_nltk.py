import nltk
import ssl

try:
    _create_unverified_https_context = ssl._create_unverified_context
except AttributeError:
    pass
else:
    ssl._create_default_https_context = _create_unverified_https_context

def download_nltk_resources():
    print("开始下载NLTK资源...")
    
    # 下载基础资源
    resources = [
        'punkt',          # 分词器
        'stopwords',      # 停用词
        'wordnet',        # 词典
        'averaged_perceptron_tagger'  # 词性标注
    ]
    
    for resource in resources:
        print(f"正在下载 {resource}...")
        nltk.download(resource)
    
    print("NLTK资源下载完成！")

if __name__ == "__main__":
    download_nltk_resources() 