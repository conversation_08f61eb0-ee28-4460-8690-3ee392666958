import os
import re
import nltk
from nltk.corpus import stopwords
from collections import Counter
import matplotlib.pyplot as plt
from wordcloud import WordCloud
import numpy as np
from PIL import Image

# 确保下载必要的NLTK数据
nltk.download('punkt')
nltk.download('stopwords')

def read_reviews(directory):
    """读取指定目录下的所有评论文件"""
    reviews = []

    # 遍历正面评论
    pos_dir = os.path.join(directory, 'positive_polarity')
    for subdir in ['deceptive_from_MTurk', 'truthful_from_TripAdvisor']:
        path = os.path.join(pos_dir, subdir)
        if os.path.exists(path):
            for fold in os.listdir(path):
                fold_path = os.path.join(path, fold)
                if os.path.isdir(fold_path):
                    for file in os.listdir(fold_path):
                        if file.endswith('.txt'):
                            with open(os.path.join(fold_path, file), 'r', encoding='utf-8') as f:
                                reviews.append(f.read())

    # 遍历负面评论
    neg_dir = os.path.join(directory, 'negative_polarity')
    for subdir in ['deceptive_from_MTurk', 'truthful_from_Web']:
        path = os.path.join(neg_dir, subdir)
        if os.path.exists(path):
            for fold in os.listdir(path):
                fold_path = os.path.join(path, fold)
                if os.path.isdir(fold_path):
                    for file in os.listdir(fold_path):
                        if file.endswith('.txt'):
                            with open(os.path.join(fold_path, file), 'r', encoding='utf-8') as f:
                                reviews.append(f.read())

    return reviews

def preprocess_text(text):
    """预处理文本：小写化、去除标点符号、去除停用词"""
    # 转换为小写
    text = text.lower()

    # 去除标点符号和数字
    text = re.sub(r'[^\w\s]', '', text)
    text = re.sub(r'\d+', '', text)

    # 简单分词（使用空格分割）
    tokens = text.split()

    # 去除停用词
    stop_words = set(stopwords.words('english'))
    filtered_tokens = [word for word in tokens if word not in stop_words and len(word) > 2]

    return filtered_tokens

def generate_wordcloud(text, output_path):
    """生成词云并保存"""
    # 创建词云对象
    wordcloud = WordCloud(
        width=800,
        height=400,
        background_color='white',
        max_words=200,
        contour_width=3,
        contour_color='steelblue',
        colormap='viridis'
    ).generate(text)

    # 显示词云
    plt.figure(figsize=(10, 5))
    plt.imshow(wordcloud, interpolation='bilinear')
    plt.axis('off')

    # 保存词云图像
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()

    print(f"词云已保存到 {output_path}")

def main():
    # 数据目录
    data_dir = os.path.join('data', 'op_spam_v1.4')

    # 读取所有评论
    print("正在读取评论...")
    reviews = read_reviews(data_dir)
    print(f"共读取了 {len(reviews)} 条评论")

    # 预处理文本
    print("正在预处理文本...")
    all_tokens = []
    for review in reviews:
        all_tokens.extend(preprocess_text(review))

    # 统计词频
    word_counts = Counter(all_tokens)
    print(f"共有 {len(word_counts)} 个不同的单词")

    # 打印最常见的20个单词
    print("\n最常见的20个单词:")
    for word, count in word_counts.most_common(20):
        print(f"{word}: {count}")

    # 将所有单词连接成一个字符串，用于生成词云
    text = ' '.join(all_tokens)

    # 生成并保存词云
    output_path = 'review_wordcloud.png'
    print("\n正在生成词云...")
    generate_wordcloud(text, output_path)

if __name__ == "__main__":
    main()
