<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>虚假评论检测系统</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #4361ee;
            --primary-hover: #3a56d4;
            --secondary-color: #f72585;
            --light-color: #f8f9fa;
            --dark-color: #212529;
            --genuine-color: #06d6a0;
            --fake-color: #ef476f;
            --gray-color: #6c757d;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Roboto', sans-serif;
            line-height: 1.6;
            color: var(--dark-color);
            background-color: var(--light-color);
            padding: 0;
            margin: 0;
        }
        
        .header {
            background-color: var(--primary-color);
            color: white;
            padding: 1.5rem 0;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        
        .review-card {
            background-color: white;
            border-radius: 8px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
        }
        
        h1 {
            font-weight: 500;
            font-size: 1.75rem;
            margin-bottom: 0.5rem;
        }
        
        h2 {
            font-weight: 400;
            font-size: 1.25rem;
            color: var(--gray-color);
            margin-bottom: 1.5rem;
        }
        
        .review-textarea {
            width: 100%;
            height: 180px;
            padding: 1rem;
            margin-bottom: 1rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: inherit;
            font-size: 1rem;
            resize: none;
        }
        
        .review-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(67, 97, 238, 0.2);
        }
        
        .btn-group {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 4px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background-color: var(--primary-hover);
        }
        
        .btn-secondary {
            background-color: #e9ecef;
            color: var(--dark-color);
        }
        
        .btn-secondary:hover {
            background-color: #dde2e6;
        }
        
        .result-card {
            display: none;
            margin-top: 2rem;
            padding: 1.5rem;
            border-radius: 8px;
            text-align: center;
        }
        
        .result-genuine {
            background-color: rgba(6, 214, 160, 0.1);
            border: 1px solid var(--genuine-color);
        }
        
        .result-fake {
            background-color: rgba(239, 71, 111, 0.1);
            border: 1px solid var(--fake-color);
        }
        
        .result-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        .genuine-icon {
            color: var(--genuine-color);
        }
        
        .fake-icon {
            color: var(--fake-color);
        }
        
        .result-title {
            font-size: 1.5rem;
            font-weight: 500;
            margin-bottom: 0.5rem;
        }
        
        .genuine-title {
            color: var(--genuine-color);
        }
        
        .fake-title {
            color: var(--fake-color);
        }
        
        .result-score {
            font-size: 1.1rem;
            color: var(--gray-color);
            margin-bottom: 1rem;
        }
        
        .loader {
            display: none;
            text-align: center;
            margin: 2rem 0;
        }
        
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(0, 0, 0, 0.1);
            border-radius: 50%;
            border-top-color: var(--primary-color);
            animation: spin 1s ease-in-out infinite;
            margin: 0 auto;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .sample-info {
            margin-top: 1rem;
            padding: 1rem;
            background-color: #f8f9fa;
            border-radius: 4px;
            font-size: 0.9rem;
            color: var(--gray-color);
            display: none;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>虚假评论检测系统</h1>
        <h2>基于双层LSTM和GloVe词向量的深度学习模型</h2>
    </div>
    
    <div class="container">
        <div class="review-card">
            <h2>输入评论文本或选择测试样本</h2>
            <textarea id="reviewTextarea" class="review-textarea" placeholder="在此输入评论文本..."></textarea>
            
            <div class="btn-group">
                <button id="loadSampleBtn" class="btn btn-secondary">加载测试样本</button>
                <button id="analyzeBtn" class="btn btn-primary">分析评论</button>
            </div>
            
            <div id="sampleInfo" class="sample-info">
                <p><strong>样本来源：</strong><span id="sampleSource"></span></p>
            </div>
        </div>
        
        <div id="loader" class="loader">
            <div class="spinner"></div>
            <p>正在分析评论...</p>
        </div>
        
        <div id="resultGenuine" class="result-card result-genuine">
            <div class="result-icon genuine-icon">✓</div>
            <h3 class="result-title genuine-title">真实评论</h3>
            <p class="result-score" id="genuineScore"></p>
            <p>系统认为这是一条真实评论。这条评论的语言模式、情感表达和内容结构符合真实用户的评论特征。</p>
        </div>
        
        <div id="resultFake" class="result-card result-fake">
            <div class="result-icon fake-icon">✗</div>
            <h3 class="result-title fake-title">虚假评论</h3>
            <p class="result-score" id="fakeScore"></p>
            <p>系统认为这是一条虚假评论。这条评论的语言模式、情感表达或内容结构显示出虚假评论的特征。</p>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const reviewTextarea = document.getElementById('reviewTextarea');
            const loadSampleBtn = document.getElementById('loadSampleBtn');
            const analyzeBtn = document.getElementById('analyzeBtn');
            const loader = document.getElementById('loader');
            const resultGenuine = document.getElementById('resultGenuine');
            const resultFake = document.getElementById('resultFake');
            const genuineScore = document.getElementById('genuineScore');
            const fakeScore = document.getElementById('fakeScore');
            const sampleInfo = document.getElementById('sampleInfo');
            const sampleSource = document.getElementById('sampleSource');
            
            // 加载测试样本
            loadSampleBtn.addEventListener('click', function() {
                loader.style.display = 'block';
                resultGenuine.style.display = 'none';
                resultFake.style.display = 'none';
                
                fetch('/get_sample')
                    .then(response => response.json())
                    .then(data => {
                        loader.style.display = 'none';
                        reviewTextarea.value = data.text;
                        sampleSource.textContent = data.actual_label;
                        sampleInfo.style.display = 'block';
                    })
                    .catch(error => {
                        loader.style.display = 'none';
                        alert('加载样本失败: ' + error);
                    });
            });
            
            // 分析评论
            analyzeBtn.addEventListener('click', function() {
                const reviewText = reviewTextarea.value.trim();
                
                if (!reviewText) {
                    alert('请输入评论文本或加载测试样本');
                    return;
                }
                
                // 显示加载动画
                loader.style.display = 'block';
                resultGenuine.style.display = 'none';
                resultFake.style.display = 'none';
                
                // 发送请求到后端
                fetch('/detect', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ review: reviewText })
                })
                .then(response => response.json())
                .then(data => {
                    // 隐藏加载动画
                    loader.style.display = 'none';
                    
                    // 显示结果
                    if (data.is_fake) {
                        fakeScore.textContent = `置信度: ${(data.probability * 100).toFixed(2)}%`;
                        resultFake.style.display = 'block';
                    } else {
                        genuineScore.textContent = `置信度: ${((1 - data.probability) * 100).toFixed(2)}%`;
                        resultGenuine.style.display = 'block';
                    }
                })
                .catch(error => {
                    loader.style.display = 'none';
                    alert('分析评论失败: ' + error);
                });
            });
        });
    </script>
</body>
</html> 