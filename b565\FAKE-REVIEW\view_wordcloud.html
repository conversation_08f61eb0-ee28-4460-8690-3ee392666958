<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>评论词云</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            text-align: center;
        }
        h1 {
            color: #333;
            margin-bottom: 30px;
        }
        .wordcloud-container {
            margin-top: 20px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            padding: 10px;
        }
        img {
            max-width: 100%;
            height: auto;
        }
        .info {
            margin-top: 30px;
            text-align: left;
            background-color: #f9f9f9;
            padding: 20px;
            border-radius: 5px;
        }
        .word-list {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
        }
        .word-item {
            width: 30%;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <h1>酒店评论词云</h1>
    
    <div class="wordcloud-container">
        <img src="review_wordcloud.png" alt="酒店评论词云">
    </div>
    
    <div class="info">
        <h2>词云说明</h2>
        <p>这个词云是基于b565/FAKE-REVIEW/data/op_spam_v1.4目录中的1600条酒店评论生成的。词云中的单词大小表示其在评论中出现的频率。</p>
        
        <h3>最常见的20个单词：</h3>
        <div class="word-list">
            <div class="word-item">hotel: 3282</div>
            <div class="word-item">room: 2766</div>
            <div class="word-item">chicago: 1498</div>
            <div class="word-item">stay: 1300</div>
            <div class="word-item">would: 916</div>
            <div class="word-item">great: 856</div>
            <div class="word-item">staff: 833</div>
            <div class="word-item">service: 805</div>
            <div class="word-item">one: 703</div>
            <div class="word-item">rooms: 687</div>
            <div class="word-item">stayed: 663</div>
            <div class="word-item">like: 552</div>
            <div class="word-item">night: 552</div>
            <div class="word-item">get: 540</div>
            <div class="word-item">time: 519</div>
            <div class="word-item">desk: 499</div>
            <div class="word-item">nice: 488</div>
            <div class="word-item">location: 480</div>
            <div class="word-item">even: 466</div>
            <div class="word-item">could: 454</div>
        </div>
        
        <h3>数据来源</h3>
        <p>数据来自b565/FAKE-REVIEW/data/op_spam_v1.4目录，包含正面和负面的酒店评论，每个类别又分为真实和虚假评论。</p>
    </div>
</body>
</html>
