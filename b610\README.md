# 携程酒店评论爬虫

## 概述
这是一个用于获取携程（Trip.com）酒店评论数据的Python爬虫工具。

## 文件说明
- `trip_comments_scraper.py` - 基础版本爬虫
- `trip_comments_scraper_v2.py` - 增强版本，支持多种请求格式和端点测试
- `requirements.txt` - 依赖包列表

## 安装依赖
```bash
pip install -r requirements.txt
```

## 使用方法

### 方法1：直接运行
```bash
python trip_comments_scraper_v2.py
```

### 方法2：自定义参数
```python
from trip_comments_scraper_v2 import TripCommentsScraper

# 使用您自己的cookie和token
scraper = TripCommentsScraper(
    custom_cookies="您的cookie字符串",
    custom_token="您的phantom-token"
)

# 获取评论
comments_data = scraper.try_multiple_request_formats(
    hotel_id="451196",  # 酒店ID
    page=1,             # 页码
    page_size=20        # 每页数量
)
```

## 如何获取最新的请求参数

### 1. 打开浏览器开发者工具
1. 在Chrome/Edge中按F12打开开发者工具
2. 点击"Network"(网络)标签页
3. 访问携程酒店页面：https://www.trip.com/hotels/beijing-hotel-detail-451196/

### 2. 查找评论请求
1. 在酒店页面滚动到评论区域
2. 在Network面板中查找包含"comment"的请求
3. 找到类似`ctgetHotelComment`的API请求

### 3. 复制请求信息
1. 右键点击该请求
2. 选择"Copy" -> "Copy as cURL"或"Copy as PowerShell"
3. 或者查看Headers标签页，复制：
   - Cookie字符串
   - Phantom-Token值
   - Request Payload（请求体）

### 4. 更新代码
将获取到的信息更新到代码中：
```python
scraper = TripCommentsScraper(
    custom_cookies="从浏览器复制的完整cookie字符串",
    custom_token="从浏览器复制的phantom-token值"
)
```

## 常见问题

### Q: 为什么返回404错误？
A: 可能的原因：
1. API端点已更改
2. 需要最新的认证信息
3. 存在反爬虫保护

### Q: 如何更新cookie和token？
A: 
1. 使用浏览器访问携程网站
2. 在开发者工具中查看最新的请求
3. 复制最新的cookie和phantom-token

### Q: 如何处理不同的响应格式？
A: 代码已经支持多种响应格式，会自动尝试从不同的字段中提取评论数据。

## 注意事项
1. 请遵守网站的robots.txt和使用条款
2. 避免过于频繁的请求，建议添加延时
3. 定期更新认证信息
4. 仅用于学习和研究目的

## 输出文件
- `raw_response.json` - 原始API响应数据
- `extracted_comments.json` - 提取后的评论数据

## 支持的数据字段
- comment_id: 评论ID
- user_name: 用户名
- rating: 评分
- content: 评论内容
- check_in_date: 入住日期
- comment_date: 评论日期
- room_type: 房间类型
- helpful_count: 有用数量
- is_translated: 是否翻译 