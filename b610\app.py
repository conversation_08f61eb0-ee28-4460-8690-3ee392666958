from flask import Flask, render_template, request, jsonify, session, redirect, url_for
import torch
import pandas as pd
import numpy as np
import os
import json
import random
import mysql.connector
from mysql.connector import Error
import hashlib
from functools import wraps
import datetime
import requests
import time

from model import FakeReviewDetector
from preprocess import Preprocessor
from utils import predict

app = Flask(__name__)
app.secret_key = 'paj8sjd0a9sd0asdjaslkdjalsjd0a9wje09'  # 用于会话管理，应使用随机生成的强密钥

# 数据库配置
DB_CONFIG = {
    'host': '**************',
    'port': 3306,
    'user': 'danzi',
    'password': '123456',
    'database': 'danzi'
}

# Global variables
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
model = None
preprocessor = None
test_samples = None

def load_model():
    """Load the trained model and preprocessor"""
    global model, preprocessor
    
    # Load preprocessor
    preprocessor = Preprocessor.load('preprocessor.pkl')
    
    # Define model parameters
    vocab_size = len(preprocessor.word_to_idx)
    embedding_dim = preprocessor.embeddings.shape[1]
    hidden_dim = 256
    output_dim = 2  # Binary classification
    n_layers = 2
    dropout = 0.5
    pad_idx = preprocessor.word_to_idx['<PAD>']
    
    # Create model
    model = FakeReviewDetector(
        vocab_size=vocab_size,
        embedding_dim=embedding_dim,
        hidden_dim=hidden_dim,
        output_dim=output_dim,
        n_layers=n_layers,
        dropout=dropout,
        pad_idx=pad_idx,
        pretrained_embeddings=torch.FloatTensor(preprocessor.embeddings)
    ).to(device)
    
    # Load trained model weights
    model.load_state_dict(torch.load('best_model.pt', map_location=device))
    model.eval()

def load_test_samples():
    """Load some test samples from the OpSpam dataset"""
    global test_samples
    
    base_path = 'data/op_spam_v1.4'
    samples = []
    
    # Load some genuine reviews
    truthful_pos_path = os.path.join(base_path, 'positive_polarity', 'truthful_from_TripAdvisor')
    folders = os.listdir(truthful_pos_path)[:2]  # Just use a few folders
    for folder in folders:
        folder_path = os.path.join(truthful_pos_path, folder)
        if os.path.isdir(folder_path):
            files = os.listdir(folder_path)[:5]  # Take 5 samples from each folder
            for filename in files:
                file_path = os.path.join(folder_path, filename)
                with open(file_path, 'r', encoding='latin-1') as f:
                    samples.append({
                        'text': f.read().strip(),
                        'actual_label': 'GENUINE'
                    })
    
    # Load some fake reviews
    deceptive_pos_path = os.path.join(base_path, 'positive_polarity', 'deceptive_from_MTurk')
    folders = os.listdir(deceptive_pos_path)[:2]  # Just use a few folders
    for folder in folders:
        folder_path = os.path.join(deceptive_pos_path, folder)
        if os.path.isdir(folder_path):
            files = os.listdir(folder_path)[:5]  # Take 5 samples from each folder
            for filename in files:
                file_path = os.path.join(folder_path, filename)
                with open(file_path, 'r', encoding='latin-1') as f:
                    samples.append({
                        'text': f.read().strip(),
                        'actual_label': 'FAKE'
                    })
    
    test_samples = samples
    return samples

# 认证相关函数
def hash_password(password, salt=None):
    """使用SHA-256和随机盐值加密密码"""
    if salt is None:
        salt = os.urandom(16).hex()  # 生成32字节的随机盐值
        
    # 组合密码和盐值
    salted_password = password + salt
    
    # 使用SHA-256加密
    hashed = hashlib.sha256(salted_password.encode()).hexdigest()
    
    return hashed, salt

def get_db_connection():
    """获取数据库连接"""
    try:
        conn = mysql.connector.connect(**DB_CONFIG)
        return conn
    except Error as e:
        print(f"数据库连接错误: {e}")
        return None

def login_required(f):
    """确保用户已登录的装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

def crawl_hotel_comments_by_id(hotel_id, page_count=1):
    """根据酒店ID爬取评论"""
    url = "https://www.trip.com/restapi/soa2/28820/ctgetHotelComment"

    headers = {
        "accept": "application/json",
        "accept-encoding": "gzip, deflate, br, zstd",
        "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "content-type": "application/json",
        "cookie": "UBT_VID=1749090982181.c6c3HHbqyi8x; ibu_online_jump_site_result={\"site_url\":[],\"suggestion\":[\"zh-hk\",\"\",\"zh-cn\"]}; cookiePricesDisplayed=HKD; _abtest_userid=1436a11c-f103-4b53-a61a-f265ff81301c; ubtc_trip_pwa=0; _gid=GA1.2.2056404189.1749090985; _gcl_au=1.1.355452720.1749090985; _RSG=xp4qYAcw7W6MwMApa7_Tk8; _RDG=2807475fb7561526de1be11cce2acea0b1; _RGUID=6807ea62-a2d0-4cfb-8b9d-fc7933296e96; GUID=09034026419290576641; ibulanguage=EN; devicePixelRatio=1.25; cticket=BE07832BC634773F3F08F61DCF13D72250A83E4B1DA4967875AC87A4C65D8715; login_type=0; DUID=u=FF39AA5AAFFA176EF286211E085E338F1B03469F2A4A79E8CDB5AEEA82BC4740&v=0; IsNonUser=F; ibu_h5_isquick=0; _udl=B65C4933EEDF6B5BDE1A8622319894C3; login_uid=\"\"; oldLocale=en-HK; IBU_showtotalamt=0; _fbp=fb.1.1749091102084.955434062150384326; GUID=09034026419290576641; nfes_isSupportWebP=1; intl_ht1=h4%3D1_451196; _fwb=45ycDb5Xb2qXmaJqIXdn6b.1749091569009; ibulocale=en_us; __utma=1.1002885612.1749090985.1749092443.1749092443.1; __utmc=1; __utmz=1.1749092443.1.1.utmcsr=(direct)|utmccn=(direct)|utmcmd=(none); __utmb=1.1.10.1749092443; _ga_37RNVFDP1J=GS2.2.s1749090985$o1$g1$t1749092443$j60$l0$h0; ibu_online_home_language_match={\"isRedirect\":false,\"isShowSuggestion\":false,\"lastVisited\":true,\"region\":\"cn\",\"redirectSymbol\":false}; ibu_country=US; _resDomain=https%3A%2F%2Faw-s.tripcdn.com; _bfa=1.1749090982181.c6c3HHbqyi8x.1.1749092448192.1749093315002.1.11.10320668147; wcs_bt=s_33fb334966e9:1749093332; _ga=GA1.1.1002885612.1749090985; _ga_X437DZ73MR=GS2.1.s1749090985$o1$g1$t1749093332$j60$l0$h0; __rtbh.lid=%7B%22eventType%22%3A%22lid%22%2C%22id%22%3A%22OosPMzDCjgoUTqZUcMew%22%2C%22expiryDate%22%3A%222026-06-05T03%3A15%3A33.622Z%22%7D; _uetsid=2563a64041b611f086e12b2da5c02d6d; _uetvid=25639fa041b611f0bc9003dc3c8961ed; cto_bundle=e7Ha1196UlV2aUp3TmZWWXdTeTRsRjhMcG5OT3B2MzI4dnpuQ2ZhWGlSelMwNnVUM2JhR2hCYU84YTJPcjJMeXBWYVI3Q2VxcVRLNmFXa2h0SmxzbDhKcWYyM09lNzNPVCUyRjVLVjBaTDNGdVdhUGZjNkVzOWJrJTJCQUxUaklJdDJVczBMalQ0bHhiNGkyN3J2RU9LT1dMaXBlcEN3JTNEJTNE; _RF1=**************",
        "cookieorigin": "https://www.trip.com",
        "origin": "https://www.trip.com",
        "phantom-token": "1004-common-591vA3Ed8K5Ai04W6XwXajpDjbgvknETpE4aJsUvXZRhNjaQWMqJS5iDURShjgsv38e6BRhQJlDi6AYloWa7ioOyctY71yZtwSkWXlwMTWkQeb3KG4I9YpZIHUImNWqTRGby8bE0QvlTWmTvXti3dy7gyO6izXYGzYk6WmwDNinXJqY1tIZjGajkMKSMjMFjq4E0zI8EkLizQYqEc4YpDyhE7qY4XyldKnpKZmKTcIQ1jdowAze5HE4DImbwFhiDtR6EPQYcsydkKGZiD3jUpjTZWXQy1ENSYFPynZJlGjGDeObI9dedXYa0xPce4PKpNisgjGPj4cWLFeqhyh8jB0jDFEQGeMSWcleLbE31JBzRXaRFHvsfwUE7pYp5yPpKTdESOY7ajOHeB9YchxQkeQkK4kiX6jHTjd4WSZefTJUFjaoeX9IASehDYpOx1leDbJGpe06E4UYT4RZ3jfZYadeaTym0jbUw9Ojg5j0ZjkPKfJqTYLYsLjM4IONjZPRMcyOdE1Hv4NWqhvD6W5cvMmj1aYBURhGjMsRf9I5twA4w74woHiGhvfbv49JNPJcMIoZenBj0ael6JoLx35wGYa3EogEQAr4PvcOjhcio6J59W6UEg1j9tyA5eQhES1wOdYLowF3e14EGnWPaj3SEsneQSvl5E4ZJA5Rkpe8nJLqYzmjgfy1nJkBjmOjt1E8mRtQWkojnAE34Jo0r9UxdY9orfGJUE6bjTaYgDYXQwB0w41vUtY8Aw11xHtiM6vLYcmxSmr6aYQqYs3EAhx0lRSFIOYPOrNbwM9e0dvUleBaYqciMqYsdJbfjo1y7YgkjMzE8fJzpedMEa5jQZWGQWSqY4ZjgYAkWUqe07YXsrDXKS4eBlEb1WfwXGWNbxcYdMxHUyOBwSbRGhJcmiDbw4zenSjfUwq8EcNvl4JHZJmY0SxMSiAUrXHROPJqUil7wnfe1ajmgRSHw6bKGkw5ykYA6wpLK53W1OiX7vBLET1Wlzeg1iTY4ZwFAvQOelpYBFi86wSGRGoEUqW41E7frGqiAYDqW6be6pJ17JBzw8JD3efYqE0vT9wA1jTHwghv9ajFFKmzIf4IkYAAx3FRhHeX6Y00xPGW6TEcYpaeAUW9XITXjpswPcv9tjQHEFaEp8IkYhhKsBRLHjf3iqbW3kWoQJ9Swt6rFYQHrDhrb7RTmjLdi0tiHTxSlWdBjsrLXxzkYdfwNPItrXygqWHqjHMxcAJUoxqfYZj91I1zY6fRPYzvDyPayg7RzqWBHifoYpdWD1wQOwt1jG9RUgEXSyZkvNqw87",
        "priority": "u=1, i",
        "referer": f"https://www.trip.com/hotels/beijing-hotel-detail-{hotel_id}/",
        "sec-ch-ua": "\"Microsoft Edge\";v=\"129\", \"Not=A?Brand\";v=\"8\", \"Chromium\";v=\"129\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"Windows\"",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
    }

    all_comments = []

    for page in range(1, page_count + 1):
        data = {
            "hotelId": hotel_id,
            "pageIndex": page,
            "pageSize": 10,
            "repeatComment": 1,
            "needStaticInfo": False,
            "functionOptions": [
                "IntegratedTARating",
                "hidePicAndVideoAgg",
                "TripReviewsToServerOnline",
                "IntegratedExpediaList",
                "tripShuffled",
                "taAdvisorCount",
                "filterComment",
                "noShowNewExpedia"
            ],
            "head": {
                "platform": "PC",
                "cver": "0",
                "cid": "1749090982181.c6c3HHbqyi8x",
                "bu": "IBU",
                "group": "trip",
                "aid": "",
                "currency": "HKD",
                "guid": "",
                "isSSR": False,
                "locale": "en-US",
                "ouid": "",
                "pageId": "10320668147",
                "sid": "",
                "timezone": "8",
                "vid": "1749090982181.c6c3HHbqyi8x"
            }
        }

        try:
            response = requests.post(url, headers=headers, json=data, timeout=30)

            if response.status_code == 200:
                json_data = response.json()

                if 'data' in json_data and 'commentList' in json_data['data']:
                    comments = json_data['data']['commentList']

                    for comment in comments:
                        comment_data = {
                            'content': comment.get('content', ''),
                            'user_name': comment.get('userInfo', {}).get('nickName', 'Anonymous User') if comment.get('userInfo') else 'Anonymous User'
                        }
                        all_comments.append(comment_data)

            # 添加延时避免请求过快
            if page < page_count:
                time.sleep(2)

        except Exception as e:
            print(f"爬取第{page}页时出错: {e}")
            continue

    return all_comments

@app.route('/')
@login_required
def index():
    """主页，需要登录才能访问"""
    return render_template('index.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    """用户登录"""
    if request.method == 'GET':
        # 如果用户已登录，重定向到主页
        if 'user_id' in session:
            return redirect(url_for('index'))
        return render_template('login.html')
    
    # 处理POST请求（AJAX登录）
    if request.content_type == 'application/json':
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')
    else:
        # 处理传统表单提交
        username = request.form.get('username')
        password = request.form.get('password')
    
    if not username or not password:
        return jsonify({'success': False, 'message': '用户名和密码不能为空'})
    
    conn = get_db_connection()
    if not conn:
        return jsonify({'success': False, 'message': '服务器错误，请稍后再试'})
    
    try:
        cursor = conn.cursor(dictionary=True)
        
        # 查询用户
        cursor.execute("SELECT * FROM users WHERE username = %s", (username,))
        user = cursor.fetchone()
        
        if not user:
            return jsonify({'success': False, 'message': '用户名或密码错误'})
        
        # 验证密码
        hashed_password, _ = hash_password(password, user['salt'])
        if hashed_password != user['password_hash']:
            return jsonify({'success': False, 'message': '用户名或密码错误'})
        
        # 更新最后登录时间
        cursor.execute("UPDATE users SET last_login = %s WHERE id = %s", 
                      (datetime.datetime.now(), user['id']))
        conn.commit()
        
        # 设置会话
        session['user_id'] = user['id']
        session['username'] = user['username']
        
        return jsonify({'success': True})
    
    except Error as e:
        return jsonify({'success': False, 'message': f'登录失败: {str(e)}'})
    finally:
        if conn.is_connected():
            cursor.close()
            conn.close()

@app.route('/register', methods=['GET', 'POST'])
def register():
    """用户注册"""
    if request.method == 'GET':
        # 如果用户已登录，重定向到主页
        if 'user_id' in session:
            return redirect(url_for('index'))
        return render_template('register.html')
    
    # 处理POST请求（AJAX注册）
    if request.content_type == 'application/json':
        data = request.get_json()
        username = data.get('username')
        email = data.get('email')
        password = data.get('password')
    else:
        # 处理传统表单提交
        username = request.form.get('username')
        email = request.form.get('email')
        password = request.form.get('password')
    
    if not username or not email or not password:
        return jsonify({'success': False, 'message': '所有字段都必须填写'})
    
    conn = get_db_connection()
    if not conn:
        return jsonify({'success': False, 'message': '服务器错误，请稍后再试'})
    
    try:
        cursor = conn.cursor()
        
        # 检查用户名和邮箱是否已被使用
        cursor.execute("SELECT id FROM users WHERE username = %s OR email = %s", (username, email))
        existing_user = cursor.fetchone()
        
        if existing_user:
            return jsonify({'success': False, 'message': '用户名或邮箱已被使用'})
        
        # 创建新用户
        password_hash, salt = hash_password(password)
        
        cursor.execute("""
        INSERT INTO users (username, email, password_hash, salt)
        VALUES (%s, %s, %s, %s)
        """, (username, email, password_hash, salt))
        
        conn.commit()
        
        return jsonify({'success': True})
    
    except Error as e:
        return jsonify({'success': False, 'message': f'注册失败: {str(e)}'})
    finally:
        if conn.is_connected():
            cursor.close()
            conn.close()

@app.route('/logout')
def logout():
    """用户退出登录"""
    session.pop('user_id', None)
    session.pop('username', None)
    return redirect(url_for('login'))

@app.route('/detect', methods=['POST'])
@login_required
def detect_fake_review():
    data = request.get_json()
    review_text = data.get('review', '')
    
    if not review_text:
        return jsonify({'error': 'No review text provided'}), 400
    
    # Make prediction
    label, probability = predict(model, review_text, preprocessor, device)
    
    result = {
        'is_fake': bool(label),
        'probability': float(probability),
        'prediction': 'FAKE' if label else 'GENUINE'
    }
    
    return jsonify(result)

@app.route('/get_sample', methods=['GET'])
@login_required
def get_sample():
    """Get a random sample from the test set"""
    sample = random.choice(test_samples)
    return jsonify(sample)

@app.route('/crawl_hotel_comments', methods=['POST'])
@login_required
def crawl_hotel_comments():
    """爬取酒店评论"""
    data = request.get_json()
    hotel_name = data.get('hotel_name', '').strip()
    page_count = data.get('page_count', 1)

    if not hotel_name:
        return jsonify({'success': False, 'message': '酒店名称不能为空'})

    # 从数据库查询酒店ID
    conn = get_db_connection()
    if not conn:
        return jsonify({'success': False, 'message': '数据库连接失败'})

    try:
        cursor = conn.cursor(dictionary=True)

        # 模糊查询酒店名称
        cursor.execute("SELECT hotel_id, hotel_name FROM hotel_comments WHERE hotel_name LIKE %s LIMIT 1",
                      (f'%{hotel_name}%',))
        hotel = cursor.fetchone()

        if not hotel:
            return jsonify({'success': False, 'message': f'未找到酒店: {hotel_name}'})

        hotel_id = hotel['hotel_id']
        actual_hotel_name = hotel['hotel_name']

        # 爬取评论
        comments = crawl_hotel_comments_by_id(hotel_id, page_count)

        if not comments:
            return jsonify({'success': False, 'message': '未能获取到评论数据'})

        # 将评论保存到数据库
        comments_json = json.dumps(comments, ensure_ascii=False)

        cursor.execute("""
        UPDATE hotel_comments
        SET comment = %s, updated_at = CURRENT_TIMESTAMP
        WHERE hotel_id = %s
        """, (comments_json, hotel_id))

        conn.commit()

        return jsonify({
            'success': True,
            'message': '评论爬取成功',
            'hotel_name': actual_hotel_name,
            'hotel_id': hotel_id,
            'comments_count': len(comments),
            'comments': comments
        })

    except Error as e:
        return jsonify({'success': False, 'message': f'数据库操作失败: {str(e)}'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'爬取失败: {str(e)}'})
    finally:
        if conn.is_connected():
            cursor.close()
            conn.close()

@app.route('/get_hotel_comments/<hotel_name>', methods=['GET'])
@login_required
def get_hotel_comments(hotel_name):
    """获取酒店评论"""
    conn = get_db_connection()
    if not conn:
        return jsonify({'success': False, 'message': '数据库连接失败'})

    try:
        cursor = conn.cursor(dictionary=True)

        cursor.execute("SELECT hotel_name, comment FROM hotel_comments WHERE hotel_name LIKE %s LIMIT 1",
                      (f'%{hotel_name}%',))
        hotel = cursor.fetchone()

        if not hotel:
            return jsonify({'success': False, 'message': f'未找到酒店: {hotel_name}'})

        comments = []
        if hotel['comment']:
            try:
                comments = json.loads(hotel['comment'])
            except json.JSONDecodeError:
                comments = []

        return jsonify({
            'success': True,
            'hotel_name': hotel['hotel_name'],
            'comments': comments
        })

    except Error as e:
        return jsonify({'success': False, 'message': f'数据库查询失败: {str(e)}'})
    finally:
        if conn.is_connected():
            cursor.close()
            conn.close()

if __name__ == '__main__':
    # Load the model and test samples before starting the app
    load_model()
    load_test_samples()
    app.run(debug=True) 