#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库中的评论数据
"""

import mysql.connector
from mysql.connector import Error
import json

# 数据库配置
DB_CONFIG = {
    'host': '**************',
    'port': 3306,
    'user': 'danzi',
    'password': '123456',
    'database': 'danzi'
}

def check_hotel_comments():
    """检查数据库中的酒店评论"""
    try:
        conn = mysql.connector.connect(**DB_CONFIG)
        
        if conn.is_connected():
            cursor = conn.cursor(dictionary=True)
            
            # 查询所有酒店及其评论状态
            cursor.execute("""
            SELECT hotel_name, hotel_id, 
                   CASE 
                       WHEN comment IS NULL THEN '无评论'
                       WHEN comment = '' THEN '评论为空'
                       ELSE '有评论'
                   END as comment_status,
                   CHAR_LENGTH(comment) as comment_length
            FROM hotel_comments 
            ORDER BY hotel_id
            """)
            
            hotels = cursor.fetchall()
            
            print("=== 酒店评论状态 ===")
            for hotel in hotels:
                print(f"酒店: {hotel['hotel_name']}")
                print(f"ID: {hotel['hotel_id']}")
                print(f"评论状态: {hotel['comment_status']}")
                print(f"评论长度: {hotel['comment_length']} 字符")
                print("-" * 50)
            
            # 查询有评论的酒店详情
            cursor.execute("""
            SELECT hotel_name, hotel_id, comment 
            FROM hotel_comments 
            WHERE comment IS NOT NULL AND comment != ''
            """)
            
            hotels_with_comments = cursor.fetchall()
            
            print("\n=== 有评论的酒店详情 ===")
            for hotel in hotels_with_comments:
                print(f"\n酒店: {hotel['hotel_name']} (ID: {hotel['hotel_id']})")
                
                try:
                    comments = json.loads(hotel['comment'])
                    print(f"评论数量: {len(comments)}")
                    
                    # 显示前3条评论
                    for i, comment in enumerate(comments[:3], 1):
                        print(f"\n评论 {i}:")
                        print(f"用户: {comment.get('user_name', 'Unknown')}")
                        content = comment.get('content', '')
                        if len(content) > 100:
                            content = content[:100] + "..."
                        print(f"内容: {content}")
                    
                    if len(comments) > 3:
                        print(f"... 还有 {len(comments) - 3} 条评论")
                        
                except json.JSONDecodeError:
                    print("评论数据格式错误")
                
                print("=" * 80)
                
    except Error as e:
        print(f"数据库错误: {e}")
    finally:
        if conn is not None and conn.is_connected():
            cursor.close()
            conn.close()

if __name__ == "__main__":
    check_hotel_comments()
