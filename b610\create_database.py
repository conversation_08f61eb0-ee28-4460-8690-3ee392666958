import mysql.connector
from mysql.connector import Error
import hashlib
import os

# 数据库配置
DB_CONFIG = {
    'host': '**************',
    'port': 3306,
    'user': 'danzi',
    'password': '123456',
    'database': 'danzi'
}

def create_database():
    """创建数据库和用户表"""
    
    # 首先不指定数据库名称连接
    conn = None
    try:
        conn = mysql.connector.connect(
            host=DB_CONFIG['host'],
            port=DB_CONFIG['port'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password']
        )
        
        if conn.is_connected():
            cursor = conn.cursor()
            
            # 创建数据库（如果不存在）
            cursor.execute(f"CREATE DATABASE IF NOT EXISTS {DB_CONFIG['database']} DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            print(f"数据库 '{DB_CONFIG['database']}' 创建成功或已存在")
            
            # 选择数据库
            cursor.execute(f"USE {DB_CONFIG['database']}")
            
            # 创建用户表
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) NOT NULL UNIQUE,
                email VARCHAR(100) NOT NULL UNIQUE,
                password_hash VARCHAR(128) NOT NULL,
                salt VARCHAR(32) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login TIMESTAMP NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """)
            print("用户表创建成功或已存在")

            # 创建酒店评论表
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS hotel_comments (
                id INT AUTO_INCREMENT PRIMARY KEY,
                hotel_name VARCHAR(255) NOT NULL,
                hotel_id BIGINT NOT NULL,
                comment TEXT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_hotel_id (hotel_id),
                INDEX idx_hotel_name (hotel_name)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """)
            print("酒店评论表创建成功或已存在")

            # 添加用户表索引
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_username ON users (username)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_email ON users (email)")
            
            # 提交更改
            conn.commit()
            
    except Error as e:
        print(f"数据库错误: {e}")
    finally:
        if conn is not None and conn.is_connected():
            cursor.close()
            conn.close()

def hash_password(password, salt=None):
    """
    使用SHA-256和随机盐值加密密码
    """
    if salt is None:
        salt = os.urandom(16).hex()  # 生成32字节的随机盐值
        
    # 组合密码和盐值
    salted_password = password + salt
    
    # 使用SHA-256加密
    hashed = hashlib.sha256(salted_password.encode()).hexdigest()
    
    return hashed, salt

def create_test_user():
    """创建测试用户"""
    try:
        conn = mysql.connector.connect(**DB_CONFIG)

        if conn.is_connected():
            cursor = conn.cursor()

            # 检查测试用户是否已存在
            cursor.execute("SELECT id FROM users WHERE username = 'test'")
            user = cursor.fetchone()

            if not user:
                # 创建一个测试用户
                password_hash, salt = hash_password("test123")

                cursor.execute("""
                INSERT INTO users (username, email, password_hash, salt)
                VALUES (%s, %s, %s, %s)
                """, ("test", "<EMAIL>", password_hash, salt))

                conn.commit()
                print("测试用户创建成功")
            else:
                print("测试用户已存在")

    except Error as e:
        print(f"数据库错误: {e}")
    finally:
        if conn is not None and conn.is_connected():
            cursor.close()
            conn.close()

def insert_hotel_data():
    """插入酒店基础数据"""
    hotels_data = [
        {"hotelName": "VOYAGE INTERNATIONAL HOTEL", "hotelId": 80920781},
        {"hotelName": "Jingli Hotel (Beijing Heshenghui Dajiaoting Subway Station Branch)", "hotelId": 25748541},
        {"hotelName": "Jingli Hongyizhan Hotel (Beijing Dongzhimen Sanlitun Branch)", "hotelId": 1013461},
        {"hotelName": "CitiGO Hotel Sanyuanqiao Beijing", "hotelId": 608516},
        {"hotelName": "Radisson Hotel Beijing Daxing Airport", "hotelId": 113612942},
        {"hotelName": "TRUE GO S Hotel (Beijing New International Exhibition Center Capital Airport)", "hotelId": 44461848},
        {"hotelName": "Yitingzhenshe Hotel (Beijing Qianmen)", "hotelId": 8986081},
        {"hotelName": "TRUE GO Hotel (Beijing New International Exhibition Center Capital Airport)", "hotelId": 1379329},
        {"hotelName": "TRUE GO Hotel (Beijing Workers' Stadium Sanlitun Taikoo Li)", "hotelId": 72922538},
        {"hotelName": "Huayi Collection Hotel (Beijing Xinguozhan Capital Airport)", "hotelId": 9116792},
        {"hotelName": "PALACE GARDEN Tai He Fu Hotel", "hotelId": 821582},
        {"hotelName": "Beijing Suwen Hotel", "hotelId": 125452087}
    ]

    try:
        conn = mysql.connector.connect(**DB_CONFIG)

        if conn.is_connected():
            cursor = conn.cursor()

            # 插入酒店数据（如果不存在）
            inserted_count = 0
            for hotel in hotels_data:
                # 检查酒店是否已存在
                cursor.execute("SELECT id FROM hotel_comments WHERE hotel_id = %s", (hotel["hotelId"],))
                existing_hotel = cursor.fetchone()

                if not existing_hotel:
                    cursor.execute("""
                    INSERT INTO hotel_comments (hotel_name, hotel_id, comment)
                    VALUES (%s, %s, %s)
                    """, (hotel["hotelName"], hotel["hotelId"], None))
                    inserted_count += 1

            conn.commit()
            print(f"成功插入 {inserted_count} 个酒店数据")

            # 显示插入的数据
            cursor.execute("SELECT hotel_name, hotel_id FROM hotel_comments ORDER BY id")
            hotels = cursor.fetchall()
            print(f"\n当前数据库中的酒店列表（共{len(hotels)}个）：")
            for i, (name, hotel_id) in enumerate(hotels, 1):
                print(f"{i}. {name} (ID: {hotel_id})")

    except Error as e:
        print(f"数据库错误: {e}")
    finally:
        if conn is not None and conn.is_connected():
            cursor.close()
            conn.close()

if __name__ == "__main__":
    create_database()
    create_test_user()
    insert_hotel_data()
    print("数据库初始化完成")