import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np
import json
import re
from collections import Counter
import pandas as pd
from matplotlib.font_manager import FontProperties

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def clean_username(username):
    """清理用户名中的特殊字符"""
    # 移除零宽连接符和其他不可见字符
    cleaned = username.replace('\u200d', '')  # 零宽连接符
    cleaned = cleaned.replace('\u200c', '')  # 零宽非连接符
    cleaned = cleaned.replace('\u200b', '')  # 零宽空格
    cleaned = cleaned.replace('\ufeff', '')  # 字节顺序标记
    return cleaned.strip()

def load_sample_comments():
    """加载示例评论数据"""
    try:
        with open('hotel_comments/page_1_comments.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
            comments = []
            for comment in data['data']['commentList'][:5]:  # 取前5条评论
                comments.append({
                    'content': comment['content'],
                    'user_name': clean_username(comment['userInfo']['nickName']),
                    'rating': comment['rating']
                })
            return comments
    except:
        # 如果文件不存在，使用示例数据
        return [
            {
                'content': "Very good staff and very good hotel. Tasty breakfast! \nLoved it!",
                'user_name': "Guest User",
                'rating': 10
            },
            {
                'content': "Great location. Getting around with buses or e-hailing was convenient. Metro station is a bit far by foot. The rooms were big and clean.",
                'user_name': "KS",
                'rating': 10
            },
            {
                'content': "Clean and good customer service.",
                'user_name': "Soo",
                'rating': 7
            },
            {
                'content': "it was mostly good",
                'user_name': "Guest User",
                'rating': 8
            },
            {
                'content': "New and clean hotel.",
                'user_name': "Guest User",
                'rating': 10
            }
        ]

def clean_text_demo(text):
    """演示文本清理过程"""
    # 模拟停用词
    stop_words = {'the', 'and', 'a', 'an', 'is', 'was', 'were', 'are', 'to', 'of', 'in', 'on', 'at', 'by', 'for', 'with', 'it'}
    
    # 步骤1: 转换为小写
    step1 = str(text).lower()
    
    # 步骤2: 移除标点符号
    step2 = re.sub(r'[^\w\s]', '', step1)
    
    # 步骤3: 分词
    step3 = step2.split()
    
    # 步骤4: 移除停用词
    step4 = [token for token in step3 if token not in stop_words]
    
    return {
        'original': text,
        'lowercase': step1,
        'no_punctuation': step2,
        'tokenized': step3,
        'no_stopwords': step4
    }

def text_to_sequence_demo(tokens, max_length=20):
    """演示文本转序列过程"""
    # 模拟词汇表
    vocab = {'<PAD>': 0, '<UNK>': 1, 'good': 2, 'hotel': 3, 'staff': 4, 'very': 5, 'tasty': 6, 'breakfast': 7, 
             'loved': 8, 'great': 9, 'location': 10, 'getting': 11, 'around': 12, 'buses': 13, 'convenient': 14,
             'metro': 15, 'station': 16, 'bit': 17, 'far': 18, 'foot': 19, 'rooms': 20, 'big': 21, 'clean': 22,
             'customer': 23, 'service': 24, 'mostly': 25, 'new': 26}
    
    # 截断到最大长度
    tokens = tokens[:max_length]
    
    # 转换为索引
    sequence = [vocab.get(token, 1) for token in tokens]  # 1 表示<UNK>
    
    # 填充到固定长度
    if len(sequence) < max_length:
        sequence += [0] * (max_length - len(sequence))  # 0 表示<PAD>
    
    return sequence, tokens

def create_preprocessing_before_image():
    """创建预处理前的图片"""
    fig, ax = plt.subplots(figsize=(14, 10))
    
    # 加载示例评论
    comments = load_sample_comments()
    
    # 设置标题
    ax.text(0.5, 0.95, '评论文本数据 - 预处理前', fontsize=20, fontweight='bold', 
            ha='center', va='top', transform=ax.transAxes)
    
    # 绘制原始评论数据
    y_positions = np.linspace(0.85, 0.15, len(comments))
    
    for i, (comment, y_pos) in enumerate(zip(comments, y_positions)):
        # 评论框
        rect = patches.Rectangle((0.05, y_pos-0.06), 0.9, 0.12, 
                               linewidth=2, edgecolor='#2E86AB', facecolor='#F8F9FA', alpha=0.8)
        ax.add_patch(rect)
        
        # 用户名和评分
        ax.text(0.07, y_pos+0.03, f"用户: {comment['user_name']}", fontsize=11, fontweight='bold', color='#2E86AB')
        ax.text(0.85, y_pos+0.03, f"评分: {comment['rating']}/10", fontsize=11, fontweight='bold', color='#E74C3C')
        
        # 评论内容（显示原始格式，包含标点符号、大小写等）
        content = comment['content']
        if len(content) > 80:
            content = content[:80] + "..."
        
        ax.text(0.07, y_pos-0.02, f"评论: {content}", fontsize=10, color='#2C3E50', 
                wrap=True, ha='left', va='center')
        
        # 标注原始数据特征
        if i == 0:
            # 箭头指向特征
            ax.annotate('包含标点符号', xy=(0.3, y_pos), xytext=(0.95, y_pos+0.05),
                       arrowprops=dict(arrowstyle='->', color='red', lw=1.5),
                       fontsize=9, color='red', ha='left')
            ax.annotate('大小写混合', xy=(0.2, y_pos-0.02), xytext=(0.95, y_pos),
                       arrowprops=dict(arrowstyle='->', color='red', lw=1.5),
                       fontsize=9, color='red', ha='left')
            ax.annotate('包含换行符', xy=(0.4, y_pos-0.02), xytext=(0.95, y_pos-0.05),
                       arrowprops=dict(arrowstyle='->', color='red', lw=1.5),
                       fontsize=9, color='red', ha='left')
    
    # 添加数据特征说明
    ax.text(0.05, 0.08, '原始数据特征:', fontsize=14, fontweight='bold', color='#E74C3C')
    features = [
        '* 包含各种标点符号 (!,.,?)',
        '* 大小写字母混合',
        '* 包含换行符和空格',
        '* 文本长度不一致',
        '* 包含停用词 (the, and, is, etc.)',
        '* 数据格式不统一'
    ]
    
    for i, feature in enumerate(features):
        ax.text(0.05, 0.05 - i*0.015, feature, fontsize=11, color='#2C3E50')
    
    ax.set_xlim(0, 1)
    ax.set_ylim(0, 1)
    ax.axis('off')
    
    plt.tight_layout()
    plt.savefig('评论文本预处理前.png', dpi=300, bbox_inches='tight', facecolor='white')
    plt.close()

    print("✅ 预处理前图片已生成: 评论文本预处理前.png")

def create_preprocessing_after_image():
    """创建预处理后的图片"""
    fig, ax = plt.subplots(figsize=(14, 10))
    
    # 加载示例评论
    comments = load_sample_comments()
    
    # 设置标题
    ax.text(0.5, 0.95, '评论文本数据 - 预处理后', fontsize=20, fontweight='bold', 
            ha='center', va='top', transform=ax.transAxes)
    
    # 处理每条评论
    y_positions = np.linspace(0.85, 0.25, len(comments))
    
    for i, (comment, y_pos) in enumerate(zip(comments, y_positions)):
        # 清理文本
        cleaned = clean_text_demo(comment['content'])
        sequence, tokens = text_to_sequence_demo(cleaned['no_stopwords'])
        
        # 评论框
        rect = patches.Rectangle((0.05, y_pos-0.08), 0.9, 0.15, 
                               linewidth=2, edgecolor='#27AE60', facecolor='#E8F8F5', alpha=0.8)
        ax.add_patch(rect)
        
        # 用户名和评分
        ax.text(0.07, y_pos+0.05, f"用户: {comment['user_name']}", fontsize=11, fontweight='bold', color='#27AE60')
        ax.text(0.85, y_pos+0.05, f"评分: {comment['rating']}/10", fontsize=11, fontweight='bold', color='#E74C3C')
        
        # 清理后的词汇
        tokens_str = ' '.join(cleaned['no_stopwords'][:10])  # 显示前10个词
        if len(cleaned['no_stopwords']) > 10:
            tokens_str += "..."
        ax.text(0.07, y_pos+0.01, f"清理后词汇: {tokens_str}", fontsize=10, color='#2C3E50')
        
        # 数字序列
        sequence_str = str(sequence[:15])  # 显示前15个数字
        if len(sequence) > 15:
            sequence_str = sequence_str[:-1] + "...]"
        ax.text(0.07, y_pos-0.03, f"数字序列: {sequence_str}", fontsize=10, color='#8E44AD')
        
        # 序列长度
        ax.text(0.07, y_pos-0.06, f"序列长度: {len(sequence)} (固定长度: 20)", fontsize=9, color='#7F8C8D')
    
    # 添加预处理步骤说明
    ax.text(0.05, 0.18, '预处理步骤:', fontsize=14, fontweight='bold', color='#27AE60')
    steps = [
        '1. 转换为小写字母',
        '2. 移除标点符号和特殊字符',
        '3. 按空格分词',
        '4. 移除停用词 (the, and, is, etc.)',
        '5. 词汇映射为数字索引',
        '6. 填充/截断到固定长度',
        '7. 生成词汇表和嵌入向量'
    ]
    
    for i, step in enumerate(steps):
        ax.text(0.05, 0.15 - i*0.015, step, fontsize=11, color='#2C3E50')
    
    # 添加特殊标记说明
    ax.text(0.55, 0.18, '特殊标记:', fontsize=14, fontweight='bold', color='#8E44AD')
    tokens_info = [
        '<PAD> = 0 (填充标记)',
        '<UNK> = 1 (未知词标记)',
        'good = 2, hotel = 3, staff = 4',
        'very = 5, tasty = 6, ...'
    ]
    
    for i, info in enumerate(tokens_info):
        ax.text(0.55, 0.15 - i*0.015, info, fontsize=11, color='#2C3E50')
    
    ax.set_xlim(0, 1)
    ax.set_ylim(0, 1)
    ax.axis('off')
    
    plt.tight_layout()
    plt.savefig('评论文本预处理后.png', dpi=300, bbox_inches='tight', facecolor='white')
    plt.close()

    print("✅ 预处理后图片已生成: 评论文本预处理后.png")

def main():
    """主函数"""
    print("🚀 开始生成评论文本预处理对比图片...")
    
    # 生成预处理前的图片
    create_preprocessing_before_image()
    
    # 生成预处理后的图片
    create_preprocessing_after_image()
    
    print("🎉 所有图片生成完成！")
    print("\n📁 生成的文件:")
    print("   - 评论文本预处理前.png")
    print("   - 评论文本预处理后.png")

if __name__ == "__main__":
    main()
