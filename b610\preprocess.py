import torch
import numpy as np
import pandas as pd
import re
import nltk
from collections import Counter
import os
import pickle

# 简化分词，不依赖punkt_tab
class Preprocessor:
    def __init__(self, glove_path, max_seq_length=100, min_freq=5):
        self.glove_path = glove_path
        self.max_seq_length = max_seq_length
        self.min_freq = min_freq
        self.word_to_idx = {}
        self.idx_to_word = {}
        self.embeddings = None
        
        # 尝试加载停用词，如果失败则使用空集合
        try:
            from nltk.corpus import stopwords
            self.stop_words = set(stopwords.words('english'))
        except:
            print("警告: 无法加载NLTK停用词，将使用空集合")
            self.stop_words = set()
    
    def clean_text(self, text):
        """清理并标记文本（简化版）"""
        text = str(text).lower()
        text = re.sub(r'[^\w\s]', '', text)  # 移除标点符号
        # 使用简单的空格分词，避免依赖NLTK的word_tokenize
        tokens = text.split()
        tokens = [token for token in tokens if token not in self.stop_words]
        return tokens
    
    def build_vocab(self, texts):
        """从文本构建词汇表"""
        # 统计词频
        counter = Counter()
        for text in texts:
            tokens = self.clean_text(text)
            counter.update(tokens)
        
        # 按频率过滤
        words = [word for word, count in counter.items() if count >= self.min_freq]
        
        # 创建词到索引的映射
        self.word_to_idx = {word: idx + 2 for idx, word in enumerate(words)}  # +2 为pad和unk标记
        self.word_to_idx['<PAD>'] = 0
        self.word_to_idx['<UNK>'] = 1
        
        # 创建索引到词的映射
        self.idx_to_word = {idx: word for word, idx in self.word_to_idx.items()}
    
    def load_glove_embeddings(self):
        """加载GloVe嵌入"""
        print(f"从{self.glove_path}加载GloVe嵌入")
        embedding_dim = None
        word_to_vec = {}
        
        with open(self.glove_path, 'r', encoding='utf-8') as f:
            for line in f:
                values = line.split()
                word = values[0]
                embedding_dim = len(values) - 1
                vector = np.asarray(values[1:], dtype='float32')
                word_to_vec[word] = vector
        
        # 使用随机值初始化嵌入矩阵
        vocab_size = len(self.word_to_idx)
        self.embeddings = np.random.uniform(-0.25, 0.25, (vocab_size, embedding_dim))
        
        # 将<PAD>标记嵌入设置为零
        self.embeddings[0] = np.zeros(embedding_dim)
        
        # 复制词汇表中词的GloVe嵌入
        for word, idx in self.word_to_idx.items():
            if word in word_to_vec:
                self.embeddings[idx] = word_to_vec[word]
        
        print(f"加载了{len(word_to_vec)}个GloVe词向量，维度为{embedding_dim}")
        return torch.FloatTensor(self.embeddings)
    
    def text_to_sequence(self, text):
        """将文本转换为词索引序列"""
        tokens = self.clean_text(text)
        # 截断或填充序列到max_seq_length
        tokens = tokens[:self.max_seq_length]
        # 将标记转换为索引
        sequence = [self.word_to_idx.get(token, 1) for token in tokens]  # 1 表示<UNK>
        # 如有必要填充序列
        if len(sequence) < self.max_seq_length:
            sequence += [0] * (self.max_seq_length - len(sequence))  # 0 表示<PAD>
        return sequence
    
    def preprocess_df(self, df, text_column, label_column=None):
        """预处理带有评论的DataFrame"""
        sequences = []
        lengths = []
        labels = None
        
        if label_column:
            labels = df[label_column].values
        
        for text in df[text_column]:
            tokens = self.clean_text(text)
            length = min(len(tokens), self.max_seq_length)
            lengths.append(length)
            sequence = self.text_to_sequence(text)
            sequences.append(sequence)
        
        sequences = torch.LongTensor(sequences)
        lengths = torch.LongTensor(lengths)
        
        if labels is not None:
            labels = torch.LongTensor(labels)
            return sequences, lengths, labels
        
        return sequences, lengths
    
    def save(self, path):
        """保存预处理器状态到磁盘"""
        with open(path, 'wb') as f:
            pickle.dump({
                'word_to_idx': self.word_to_idx,
                'idx_to_word': self.idx_to_word,
                'embeddings': self.embeddings,
                'max_seq_length': self.max_seq_length
            }, f)
    
    @classmethod
    def load(cls, path, glove_path=None):
        """从磁盘加载预处理器"""
        with open(path, 'rb') as f:
            data = pickle.load(f)
        
        preprocessor = cls(glove_path if glove_path else '', data['max_seq_length'])
        preprocessor.word_to_idx = data['word_to_idx']
        preprocessor.idx_to_word = data['idx_to_word']
        preprocessor.embeddings = data['embeddings']
        
        return preprocessor 