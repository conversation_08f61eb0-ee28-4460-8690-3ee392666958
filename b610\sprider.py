import requests
import json
import time
import os

# 携程酒店评论API URL
url = "https://www.trip.com/restapi/soa2/28820/ctgetHotelComment"

# 完整的请求头，基于你提供的浏览器抓包数据
headers = {
    "accept": "application/json",
    "accept-encoding": "gzip, deflate, br, zstd",
    "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
    "content-type": "application/json",
    "cookie": "UBT_VID=1749090982181.c6c3HHbqyi8x; ibu_online_jump_site_result={\"site_url\":[],\"suggestion\":[\"zh-hk\",\"\",\"zh-cn\"]}; cookiePricesDisplayed=HKD; _abtest_userid=1436a11c-f103-4b53-a61a-f265ff81301c; ubtc_trip_pwa=0; _gid=GA1.2.2056404189.1749090985; _gcl_au=1.1.355452720.1749090985; _RSG=xp4qYAcw7W6MwMApa7_Tk8; _RDG=2807475fb7561526de1be11cce2acea0b1; _RGUID=6807ea62-a2d0-4cfb-8b9d-fc7933296e96; GUID=09034026419290576641; ibulanguage=EN; devicePixelRatio=1.25; cticket=BE07832BC634773F3F08F61DCF13D72250A83E4B1DA4967875AC87A4C65D8715; login_type=0; DUID=u=FF39AA5AAFFA176EF286211E085E338F1B03469F2A4A79E8CDB5AEEA82BC4740&v=0; IsNonUser=F; ibu_h5_isquick=0; _udl=B65C4933EEDF6B5BDE1A8622319894C3; login_uid=\"\"; oldLocale=en-HK; IBU_showtotalamt=0; _fbp=fb.1.1749091102084.955434062150384326; GUID=09034026419290576641; nfes_isSupportWebP=1; intl_ht1=h4%3D1_451196; _fwb=45ycDb5Xb2qXmaJqIXdn6b.1749091569009; ibulocale=en_us; __utma=1.1002885612.1749090985.1749092443.1749092443.1; __utmc=1; __utmz=1.1749092443.1.1.utmcsr=(direct)|utmccn=(direct)|utmcmd=(none); __utmb=1.1.10.1749092443; _ga_37RNVFDP1J=GS2.2.s1749090985$o1$g1$t1749092443$j60$l0$h0; ibu_online_home_language_match={\"isRedirect\":false,\"isShowSuggestion\":false,\"lastVisited\":true,\"region\":\"cn\",\"redirectSymbol\":false}; ibu_country=US; _resDomain=https%3A%2F%2Faw-s.tripcdn.com; _bfa=1.1749090982181.c6c3HHbqyi8x.1.1749092448192.1749093315002.1.11.10320668147; wcs_bt=s_33fb334966e9:1749093332; _ga=GA1.1.1002885612.1749090985; _ga_X437DZ73MR=GS2.1.s1749090985$o1$g1$t1749093332$j60$l0$h0; __rtbh.lid=%7B%22eventType%22%3A%22lid%22%2C%22id%22%3A%22OosPMzDCjgoUTqZUcMew%22%2C%22expiryDate%22%3A%222026-06-05T03%3A15%3A33.622Z%22%7D; _uetsid=2563a64041b611f086e12b2da5c02d6d; _uetvid=25639fa041b611f0bc9003dc3c8961ed; cto_bundle=e7Ha1196UlV2aUp3TmZWWXdTeTRsRjhMcG5OT3B2MzI4dnpuQ2ZhWGlSelMwNnVUM2JhR2hCYU84YTJPcjJMeXBWYVI3Q2VxcVRLNmFXa2h0SmxzbDhKcWYyM09lNzNPVCUyRjVLVjBaTDNGdVdhUGZjNkVzOWJrJTJCQUxUaklJdDJVczBMalQ0bHhiNGkyN3J2RU9LT1dMaXBlcEN3JTNEJTNE; _RF1=**************",
    "cookieorigin": "https://www.trip.com",
    "origin": "https://www.trip.com",
    "phantom-token": "1004-common-591vA3Ed8K5Ai04W6XwXajpDjbgvknETpE4aJsUvXZRhNjaQWMqJS5iDURShjgsv38e6BRhQJlDi6AYloWa7ioOyctY71yZtwSkWXlwMTWkQeb3KG4I9YpZIHUImNWqTRGby8bE0QvlTWmTvXti3dy7gyO6izXYGzYk6WmwDNinXJqY1tIZjGajkMKSMjMFjq4E0zI8EkLizQYqEc4YpDyhE7qY4XyldKnpKZmKTcIQ1jdowAze5HE4DImbwFhiDtR6EPQYcsydkKGZiD3jUpjTZWXQy1ENSYFPynZJlGjGDeObI9dedXYa0xPce4PKpNisgjGPj4cWLFeqhyh8jB0jDFEQGeMSWcleLbE31JBzRXaRFHvsfwUE7pYp5yPpKTdESOY7ajOHeB9YchxQkeQkK4kiX6jHTjd4WSZefTJUFjaoeX9IASehDYpOx1leDbJGpe06E4UYT4RZ3jfZYadeaTym0jbUw9Ojg5j0ZjkPKfJqTYLYsLjM4IONjZPRMcyOdE1Hv4NWqhvD6W5cvMmj1aYBURhGjMsRf9I5twA4w74woHiGhvfbv49JNPJcMIoZenBj0ael6JoLx35wGYa3EogEQAr4PvcOjhcio6J59W6UEg1j9tyA5eQhES1wOdYLowF3e14EGnWPaj3SEsneQSvl5E4ZJA5Rkpe8nJLqYzmjgfy1nJkBjmOjt1E8mRtQWkojnAE34Jo0r9UxdY9orfGJUE6bjTaYgDYXQwB0w41vUtY8Aw11xHtiM6vLYcmxSmr6aYQqYs3EAhx0lRSFIOYPOrNbwM9e0dvUleBaYqciMqYsdJbfjo1y7YgkjMzE8fJzpedMEa5jQZWGQWSqY4ZjgYAkWUqe07YXsrDXKS4eBlEb1WfwXGWNbxcYdMxHUyOBwSbRGhJcmiDbw4zenSjfUwq8EcNvl4JHZJmY0SxMSiAUrXHROPJqUil7wnfe1ajmgRSHw6bKGkw5ykYA6wpLK53W1OiX7vBLET1Wlzeg1iTY4ZwFAvQOelpYBFi86wSGRGoEUqW41E7frGqiAYDqW6be6pJ17JBzw8JD3efYqE0vT9wA1jTHwghv9ajFFKmzIf4IkYAAx3FRhHeX6Y00xPGW6TEcYpaeAUW9XITXjpswPcv9tjQHEFaEp8IkYhhKsBRLHjf3iqbW3kWoQJ9Swt6rFYQHrDhrb7RTmjLdi0tiHTxSlWdBjsrLXxzkYdfwNPItrXygqWHqjHMxcAJUoxqfYZj91I1zY6fRPYzvDyPayg7RzqWBHifoYpdWD1wQOwt1jG9RUgEXSyZkvNqw87",
    "priority": "u=1, i",
    "referer": "https://www.trip.com/hotels/beijing-hotel-detail-451196/hanting-hotel-beijing-qianmen-street",
    "sec-ch-ua": "\"Microsoft Edge\";v=\"129\", \"Not=A?Brand\";v=\"8\", \"Chromium\";v=\"129\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\"",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
}

# 根据你提供的实际请求负载构建正确的请求体
data = {
    "hotelId": 451196,
    "pageIndex": 1,
    "pageSize": 10,
    "repeatComment": 1,
    "needStaticInfo": False,
    "functionOptions": [
        "IntegratedTARating",
        "hidePicAndVideoAgg",
        "TripReviewsToServerOnline",
        "IntegratedExpediaList",
        "tripShuffled",
        "taAdvisorCount",
        "filterComment",
        "noShowNewExpedia"
    ],
    "head": {
        "platform": "PC",
        "cver": "0",
        "cid": "1749090982181.c6c3HHbqyi8x",
        "bu": "IBU",
        "group": "trip",
        "aid": "",
        "currency": "HKD",
        "guid": "",
        "isSSR": False,
        "locale": "en-US",
        "ouid": "",
        "pageId": "10320668147",
        "sid": "",
        "timezone": "8",
        "vid": "1749090982181.c6c3HHbqyi8x"
    }
}

print("正在使用正确的请求格式获取携程酒店评论数据...")
print(f"请求URL: {url}")
print(f"请求数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
print("由于是国外网站，请耐心等待...")

try:
    # 设置较长的超时时间，因为是国外网站可能比较慢
    response = requests.post(url, headers=headers, json=data, timeout=30)

    print(f"\n响应状态码: {response.status_code}")
    print(f"响应头: {dict(response.headers)}")

    if response.status_code == 200:
        print("请求成功！")
        # 尝试解析JSON响应
        try:
            json_data = response.json()
            print("\n=== 响应数据解析 ===")

            # 检查响应状态
            if 'ResponseStatus' in json_data:
                status = json_data['ResponseStatus']
                print(f"API响应状态: {status.get('Ack', 'Unknown')}")
                if status.get('Ack') == 'Success':
                    print("API调用成功！")
                else:
                    print("API调用失败")
                    if 'Errors' in status and status['Errors']:
                        print(f"错误信息: {status['Errors']}")

            # 解析评论数据
            if 'data' in json_data and 'commentList' in json_data['data']:
                comments = json_data['data']['commentList']
                total_count = json_data['data'].get('totalCount', 0)
                all_comment_count = json_data['data'].get('allCommentCount', 0)

                print(f"\n=== 评论统计 ===")
                print(f"总评论数: {all_comment_count}")
                print(f"当前页评论数: {len(comments)}")
                print(f"筛选后总数: {total_count}")

                print(f"\n=== 评论详情 ===")
                for i, comment in enumerate(comments, 1):
                    print(f"\n--- 评论 {i} ---")
                    print(f"评论ID: {comment.get('id', 'N/A')}")
                    print(f"评分: {comment.get('rating', 'N/A')}/10")
                    print(f"评价等级: {comment.get('commentLevel', 'N/A')}")
                    print(f"入住日期: {comment.get('checkInDate', 'N/A')}")
                    print(f"创建日期: {comment.get('createDate', 'N/A')}")
                    print(f"房型: {comment.get('roomTypeName', 'N/A')}")
                    print(f"旅行类型: {comment.get('travelTypeText', 'N/A')}")
                    print(f"语言: {comment.get('language', 'N/A')}")
                    print(f"评论内容: {comment.get('content', 'N/A')}")
                    if comment.get('userInfo'):
                        user_info = comment['userInfo']
                        print(f"用户信息: {user_info}")
                    print("-" * 50)
            else:
                print("未找到评论数据")

            # 保存完整响应到文件
            with open('hotel_comments_response.json', 'w', encoding='utf-8') as f:
                json.dump(json_data, f, ensure_ascii=False, indent=2)
            print(f"\n完整响应数据已保存到: hotel_comments_response.json")

        except json.JSONDecodeError:
            print("\n响应不是有效的JSON格式，原始响应:")
            print(response.text[:1000])  # 只显示前1000个字符
    elif response.status_code == 403:
        print("403 Forbidden - 可能需要有效的认证token或被反爬虫机制阻止")
        print("响应内容:")
        print(response.text[:500])
    elif response.status_code == 404:
        print("404 Not Found - API端点不存在或参数错误")
        print("响应内容:")
        print(response.text[:500])
    else:
        print(f"请求失败，状态码: {response.status_code}")
        print("响应内容:")
        print(response.text[:500])

except requests.exceptions.Timeout:
    print("请求超时，请检查网络连接或稍后重试")
except requests.exceptions.RequestException as e:
    print(f"请求出错: {e}")
except Exception as e:
    print(f"发生未知错误: {e}")

print("\n" + "="*80)
print("爬虫增强功能：批量获取多页评论数据")
print("="*80)

def get_multiple_pages_comments(hotel_id=451196, max_pages=5, page_size=20):
    """
    获取多页评论数据

    Args:
        hotel_id: 酒店ID
        max_pages: 最大页数
        page_size: 每页评论数量
    """
    all_comments = []

    # 创建保存目录
    if not os.path.exists('hotel_comments'):
        os.makedirs('hotel_comments')

    for page in range(1, max_pages + 1):
        print(f"\n--- 正在获取第 {page} 页评论 ---")

        # 构建请求数据
        page_data = data.copy()
        page_data['pageIndex'] = page
        page_data['pageSize'] = page_size

        try:
            response = requests.post(url, headers=headers, json=page_data, timeout=30)

            if response.status_code == 200:
                json_data = response.json()

                if 'data' in json_data and 'commentList' in json_data['data']:
                    comments = json_data['data']['commentList']
                    print(f"成功获取 {len(comments)} 条评论")

                    # 添加到总列表
                    all_comments.extend(comments)

                    # 保存单页数据
                    page_filename = f'hotel_comments/page_{page}_comments.json'
                    with open(page_filename, 'w', encoding='utf-8') as f:
                        json.dump(json_data, f, ensure_ascii=False, indent=2)
                    print(f"第 {page} 页数据已保存到: {page_filename}")

                    # 如果评论数量少于请求的数量，说明已经到最后一页
                    if len(comments) < page_size:
                        print(f"已到达最后一页（第 {page} 页）")
                        break
                else:
                    print(f"第 {page} 页没有找到评论数据")
                    break
            else:
                print(f"第 {page} 页请求失败，状态码: {response.status_code}")
                break

        except Exception as e:
            print(f"第 {page} 页请求出错: {e}")
            break

        # 等待一下，避免请求过于频繁
        if page < max_pages:
            print("等待3秒后继续...")
            time.sleep(3)

    # 保存所有评论到一个文件
    if all_comments:
        all_comments_data = {
            "total_comments": len(all_comments),
            "hotel_id": hotel_id,
            "comments": all_comments
        }

        all_filename = f'hotel_comments/all_comments_{hotel_id}.json'
        with open(all_filename, 'w', encoding='utf-8') as f:
            json.dump(all_comments_data, f, ensure_ascii=False, indent=2)

        print(f"\n=== 汇总统计 ===")
        print(f"总共获取评论数: {len(all_comments)}")
        print(f"所有评论已保存到: {all_filename}")

        # 简单统计
        ratings = [comment.get('rating', 0) for comment in all_comments]
        if ratings:
            avg_rating = sum(ratings) / len(ratings)
            print(f"平均评分: {avg_rating:.2f}/10")

        languages = {}
        for comment in all_comments:
            lang = comment.get('language', 'unknown')
            languages[lang] = languages.get(lang, 0) + 1
        print(f"评论语言分布: {languages}")

    return all_comments

# 询问用户是否要获取多页数据
user_input = input("\n是否要获取多页评论数据？(y/n): ").lower().strip()
if user_input in ['y', 'yes', '是', '1']:
    try:
        pages = int(input("请输入要获取的页数 (默认5页): ") or "5")
        page_size = int(input("请输入每页评论数量 (默认20条): ") or "20")

        print(f"\n开始获取 {pages} 页评论数据，每页 {page_size} 条...")
        comments = get_multiple_pages_comments(max_pages=pages, page_size=page_size)

    except ValueError:
        print("输入无效，使用默认设置获取5页数据...")
        comments = get_multiple_pages_comments()
    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        print(f"批量获取过程中出错: {e}")
else:
    print("跳过批量获取，程序结束。")



