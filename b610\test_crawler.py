#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试爬虫功能
"""

import requests
import json

def test_crawl_hotel_comments():
    """测试爬取酒店评论功能"""
    
    # 登录获取session
    login_url = "http://127.0.0.1:5000/login"
    login_data = {
        "username": "test",
        "password": "test123"
    }
    
    session = requests.Session()
    
    # 登录
    print("正在登录...")
    login_response = session.post(login_url, json=login_data)
    
    if login_response.status_code == 200:
        login_result = login_response.json()
        if login_result.get('success'):
            print("登录成功！")
        else:
            print(f"登录失败: {login_result.get('message')}")
            return
    else:
        print(f"登录请求失败，状态码: {login_response.status_code}")
        return
    
    # 测试爬取评论
    crawl_url = "http://127.0.0.1:5000/crawl_hotel_comments"
    crawl_data = {
        "hotel_name": "VOYAGE",  # 测试搜索VOYAGE酒店
        "page_count": 1
    }
    
    print("正在测试爬取酒店评论...")
    crawl_response = session.post(crawl_url, json=crawl_data)
    
    if crawl_response.status_code == 200:
        crawl_result = crawl_response.json()
        print(f"爬取结果: {json.dumps(crawl_result, ensure_ascii=False, indent=2)}")
    else:
        print(f"爬取请求失败，状态码: {crawl_response.status_code}")
        print(f"响应内容: {crawl_response.text}")

if __name__ == "__main__":
    test_crawl_hotel_comments()
