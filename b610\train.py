import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import os
import requests
import zipfile
import io

# 尝试运行NLTK下载脚本
try:
    import download_nltk
    download_nltk.download_nltk_resources()
except:
    print("警告：无法运行NLTK下载脚本，可能会影响文本处理功能")

from model import FakeReviewDetector
from preprocess import Preprocessor
from utils import prepare_data, train_model

def check_opspam_dataset():
    """Check if the OpSpam dataset exists"""
    return os.path.exists('data/op_spam_v1.4')

def download_glove_embeddings():
    """Download GloVe embeddings"""
    print("Downloading GloVe embeddings...")
    
    if os.path.exists('data/glove.6B.100d.txt'):
        print("GloVe embeddings already exist. Skipping download.")
        return 'data/glove.6B.100d.txt'
    
    # Create data directory if not exists
    os.makedirs('data', exist_ok=True)
    
    # Download GloVe embeddings
    url = "http://nlp.stanford.edu/data/glove.6B.zip"
    response = requests.get(url)
    z = zipfile.ZipFile(io.BytesIO(response.content))
    z.extractall('data')
    
    print("GloVe embeddings downloaded and extracted successfully!")
    return 'data/glove.6B.100d.txt'

def prepare_opspam_dataset():
    """Prepare the OpSpam dataset for training"""
    # Path to the dataset
    base_path = 'data/op_spam_v1.4'
    
    if not check_opspam_dataset():
        raise FileNotFoundError("OpSpam dataset not found. Please download it first.")
    
    reviews = []
    labels = []
    
    # Process positive polarity files
    # Truthful reviews (from TripAdvisor) - GENUINE
    truthful_pos_path = os.path.join(base_path, 'positive_polarity', 'truthful_from_TripAdvisor')
    for folder in os.listdir(truthful_pos_path):
        folder_path = os.path.join(truthful_pos_path, folder)
        if os.path.isdir(folder_path):
            for filename in os.listdir(folder_path):
                file_path = os.path.join(folder_path, filename)
                with open(file_path, 'r', encoding='latin-1') as f:
                    reviews.append(f.read().strip())
                    labels.append(0)  # 0 for genuine reviews
    
    # Deceptive reviews (from MTurk) - FAKE
    deceptive_pos_path = os.path.join(base_path, 'positive_polarity', 'deceptive_from_MTurk')
    for folder in os.listdir(deceptive_pos_path):
        folder_path = os.path.join(deceptive_pos_path, folder)
        if os.path.isdir(folder_path):
            for filename in os.listdir(folder_path):
                file_path = os.path.join(folder_path, filename)
                with open(file_path, 'r', encoding='latin-1') as f:
                    reviews.append(f.read().strip())
                    labels.append(1)  # 1 for fake reviews
    
    # Process negative polarity files
    # Truthful reviews (from Web) - GENUINE
    truthful_neg_path = os.path.join(base_path, 'negative_polarity', 'truthful_from_Web')
    for folder in os.listdir(truthful_neg_path):
        folder_path = os.path.join(truthful_neg_path, folder)
        if os.path.isdir(folder_path):
            for filename in os.listdir(folder_path):
                file_path = os.path.join(folder_path, filename)
                with open(file_path, 'r', encoding='latin-1') as f:
                    reviews.append(f.read().strip())
                    labels.append(0)  # 0 for genuine reviews
    
    # Deceptive reviews (from MTurk) - FAKE
    deceptive_neg_path = os.path.join(base_path, 'negative_polarity', 'deceptive_from_MTurk')
    for folder in os.listdir(deceptive_neg_path):
        folder_path = os.path.join(deceptive_neg_path, folder)
        if os.path.isdir(folder_path):
            for filename in os.listdir(folder_path):
                file_path = os.path.join(folder_path, filename)
                with open(file_path, 'r', encoding='latin-1') as f:
                    reviews.append(f.read().strip())
                    labels.append(1)  # 1 for fake reviews
    
    # Create DataFrame
    df = pd.DataFrame({
        'review': reviews,
        'label': labels
    })
    
    print(f"Dataset prepared: {len(df)} reviews ({df['label'].sum()} fake, {len(df) - df['label'].sum()} genuine)")
    return df

def main():
    # Prepare the dataset
    print("Preparing OpSpam dataset...")
    df = prepare_opspam_dataset()
    
    # Download or use existing GloVe embeddings
    glove_path = download_glove_embeddings()
    
    # Initialize preprocessor
    preprocessor = Preprocessor(glove_path=glove_path, max_seq_length=100)
    
    # Build vocabulary
    preprocessor.build_vocab(df['review'])
    
    # Load GloVe embeddings
    pretrained_embeddings = preprocessor.load_glove_embeddings()
    
    # Prepare data for training
    train_loader, val_loader = prepare_data(
        df=df,
        preprocessor=preprocessor,
        text_column='review',
        label_column='label',
        batch_size=32
    )
    
    # Save preprocessor
    preprocessor.save('preprocessor.pkl')
    
    # Set device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Define model parameters
    vocab_size = len(preprocessor.word_to_idx)
    embedding_dim = pretrained_embeddings.shape[1]
    hidden_dim = 256
    output_dim = 2  # Binary classification
    n_layers = 2
    dropout = 0.5
    pad_idx = preprocessor.word_to_idx['<PAD>']
    
    # Create model
    model = FakeReviewDetector(
        vocab_size=vocab_size,
        embedding_dim=embedding_dim,
        hidden_dim=hidden_dim,
        output_dim=output_dim,
        n_layers=n_layers,
        dropout=dropout,
        pad_idx=pad_idx,
        pretrained_embeddings=pretrained_embeddings
    ).to(device)
    
    # Define optimizer and loss function
    optimizer = optim.Adam(model.parameters())
    criterion = nn.CrossEntropyLoss()
    
    # Train the model
    trained_model = train_model(
        model=model,
        train_loader=train_loader,
        val_loader=val_loader,
        optimizer=optimizer,
        criterion=criterion,
        device=device,
        epochs=15,
        patience=3
    )
    
    print("Training completed successfully!")

if __name__ == "__main__":
    main() 