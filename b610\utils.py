import torch
import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from torch.utils.data import Dataset, DataLoader

class ReviewDataset(Dataset):
    def __init__(self, sequences, lengths, labels=None):
        self.sequences = sequences
        self.lengths = lengths
        self.labels = labels
        
    def __len__(self):
        return len(self.sequences)
    
    def __getitem__(self, idx):
        if self.labels is not None:
            return self.sequences[idx], self.lengths[idx], self.labels[idx]
        return self.sequences[idx], self.lengths[idx]

def prepare_data(df, preprocessor, text_column, label_column, test_size=0.2, batch_size=64, random_state=42):
    """Prepare the data for training and evaluation"""
    # Split the data
    train_df, val_df = train_test_split(df, test_size=test_size, random_state=random_state, stratify=df[label_column])
    
    # Preprocess the data
    train_sequences, train_lengths, train_labels = preprocessor.preprocess_df(train_df, text_column, label_column)
    val_sequences, val_lengths, val_labels = preprocessor.preprocess_df(val_df, text_column, label_column)
    
    # Create datasets
    train_dataset = ReviewDataset(train_sequences, train_lengths, train_labels)
    val_dataset = ReviewDataset(val_sequences, val_lengths, val_labels)
    
    # Create data loaders
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size)
    
    return train_loader, val_loader

def train_model(model, train_loader, val_loader, optimizer, criterion, device, epochs=10, patience=5):
    """Train the model"""
    best_val_loss = float('inf')
    epochs_without_improvement = 0
    
    for epoch in range(epochs):
        # Training
        model.train()
        train_loss = 0.0
        train_correct = 0
        train_total = 0
        
        for sequences, lengths, labels in train_loader:
            sequences = sequences.to(device)
            labels = labels.to(device)
            
            optimizer.zero_grad()
            outputs = model(sequences, lengths)
            loss = criterion(outputs, labels)
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item() * sequences.size(0)
            _, preds = torch.max(outputs, 1)
            train_correct += (preds == labels).sum().item()
            train_total += labels.size(0)
        
        train_loss = train_loss / len(train_loader.dataset)
        train_acc = train_correct / train_total
        
        # Validation
        model.eval()
        val_loss = 0.0
        val_correct = 0
        val_total = 0
        
        with torch.no_grad():
            for sequences, lengths, labels in val_loader:
                sequences = sequences.to(device)
                labels = labels.to(device)
                
                outputs = model(sequences, lengths)
                loss = criterion(outputs, labels)
                
                val_loss += loss.item() * sequences.size(0)
                _, preds = torch.max(outputs, 1)
                val_correct += (preds == labels).sum().item()
                val_total += labels.size(0)
        
        val_loss = val_loss / len(val_loader.dataset)
        val_acc = val_correct / val_total
        
        print(f'Epoch {epoch+1}/{epochs}')
        print(f'Train Loss: {train_loss:.4f} | Train Acc: {train_acc:.4f}')
        print(f'Val Loss: {val_loss:.4f} | Val Acc: {val_acc:.4f}')
        
        # Early stopping
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            torch.save(model.state_dict(), 'best_model.pt')
            epochs_without_improvement = 0
        else:
            epochs_without_improvement += 1
            if epochs_without_improvement >= patience:
                print(f'Early stopping after {epoch+1} epochs')
                break
    
    # Load the best model
    model.load_state_dict(torch.load('best_model.pt'))
    return model

def predict(model, text, preprocessor, device):
    """Make a prediction on a single text input"""
    model.eval()
    
    # Preprocess the text
    sequence = preprocessor.text_to_sequence(text)
    sequence = torch.LongTensor([sequence]).to(device)
    length = torch.LongTensor([min(len(preprocessor.clean_text(text)), preprocessor.max_seq_length)]).to(device)
    
    # Make prediction
    with torch.no_grad():
        output = model(sequence, length)
        _, pred = torch.max(output, 1)
    
    return pred.item(), torch.softmax(output, dim=1)[0][1].item() 