import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler
import matplotlib

# 设置中文字体
matplotlib.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
matplotlib.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

# 您的数据（根据Excel表格）
data = {
    '序号': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41],
    '省份': ['合肥市', '芜湖市', '马鞍山市', '滁州市', '蚌埠市', '安庆市', '淮南市', '淮北市', '宿州市', '六安市', '亳州市', '宣城市', '铜陵市', '阜阳市', '黄山市', '池州市', '南京市', '无锡市', '徐州市', '常州市', '苏州市', '南通市', '连云港市', '淮安市', '盐城市', '扬州市', '镇江市', '泰州市', '宿迁市', '杭州市', '宁波市', '温州市', '嘉兴市', '湖州市', '绍兴市', '金华市', '衢州市', '舟山市', '台州市', '丽水市', '上海市'],
    '东经(x)': [117.23, 118.43, 118.51, 118.33, 117.39, 117.12, 117, 116.8, 116.97, 116.52, 115.78, 118.76, 117.81, 115.82, 118.34, 117.5, 118.8, 120.31, 117.29, 119.98, 120.59, 120.9, 119.22, 119.11, 120.16, 119.41, 119.43, 119.92, 118.28, 120.21, 121.63, 120.7, 120.76, 120.09, 120.59, 119.65, 118.86, 122.21, 121.42, 119.92, 121.48],
    '北纬(y)': [31.82, 31.35, 31.67, 32.26, 32.92, 30.53, 32.63, 33.96, 33.65, 31.74, 33.85, 30.94, 30.95, 32.89, 29.72, 30.68, 32.06, 31.49, 34.21, 31.81, 31.3, 31.98, 34.6, 33.55, 33.35, 32.4, 32.19, 32.46, 33.96, 30.25, 29.86, 28, 30.75, 30.89, 30.05, 29.08, 28.97, 29.99, 28.66, 28.47, 31.23],
    '运输量(万吨)': [28546, 7192, 4568, 19192, 31600, 6285, 9388, 12379, 20555, 17595, 30557, 11870, 3356, 33638, 3857, 5402, 20834, 17444, 26453, 9770, 23320, 11541, 10828, 4939, 10923, 5039, 5381, 4981, 8482, 35091, 45310, 14561, 16339, 13216, 17856, 15856, 12059, 12181, 17773, 5693, 44846]
}

# 创建DataFrame
df = pd.DataFrame(data)

# 提取经纬度坐标
coordinates = df[['东经(x)', '北纬(y)']].values

# 数据标准化
scaler = StandardScaler()
coordinates_scaled = scaler.fit_transform(coordinates)

# 手肘法确定最佳K值
k_range = range(2, 11)  # 测试k从2到10
sse = []  # 误差平方和

print("正在计算不同K值的聚类结果...")

for k in k_range:
    kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)
    kmeans.fit(coordinates_scaled)
    sse.append(kmeans.inertia_)
    print(f"K={k}, SSE={kmeans.inertia_:.2f}")

# 绘制手肘图
plt.figure(figsize=(10, 6))
plt.plot(k_range, sse, 'o-', linewidth=2, markersize=8)
plt.title('不同K值聚类偏差图', fontsize=16, fontweight='bold')
plt.xlabel('分类数K值', fontsize=14)
plt.ylabel('误差平方和', fontsize=14)
plt.grid(True, alpha=0.3)
plt.xticks(k_range)

# 美化图表
plt.gca().spines['top'].set_visible(False)
plt.gca().spines['right'].set_visible(False)
plt.tight_layout()

# 显示图表
plt.show()

# 计算肘部点（最佳K值）
def find_elbow_point(x, y):
    """
    使用二阶导数方法找到肘部点
    """
    # 计算一阶导数
    first_derivative = np.diff(y)
    # 计算二阶导数
    second_derivative = np.diff(first_derivative)
    
    # 找到二阶导数最大的点
    elbow_point = np.argmax(second_derivative) + 2  # +2是因为计算了两次差分
    return elbow_point

# 使用更简单的方法：计算斜率变化
def find_elbow_simple(k_values, sse_values):
    """
    简单的肘部检测方法：寻找斜率变化最大的点
    """
    slopes = []
    for i in range(1, len(sse_values)):
        slope = (sse_values[i] - sse_values[i-1]) / (k_values[i] - k_values[i-1])
        slopes.append(abs(slope))
    
    # 计算斜率的变化率
    slope_changes = []
    for i in range(1, len(slopes)):
        change = abs(slopes[i] - slopes[i-1])
        slope_changes.append(change)
    
    # 找到斜率变化最大的点
    max_change_idx = np.argmax(slope_changes)
    optimal_k = k_values[max_change_idx + 2]  # +2是因为索引偏移
    
    return optimal_k

# 计算最佳K值
optimal_k = find_elbow_simple(list(k_range), sse)

print(f"\n=== 聚类分析结果 ===")
print(f"建议的最佳聚类数 K = {optimal_k}")

# 打印各K值对应的SSE，便于分析
print(f"\n各K值对应的误差平方和(SSE):")
for k, s in zip(k_range, sse):
    print(f"K={k}: SSE={s:.2f}")

# 计算相邻K值的SSE减少量
print(f"\n相邻K值的SSE减少量:")
for i in range(1, len(sse)):
    reduction = sse[i-1] - sse[i]
    reduction_rate = reduction / sse[i-1] * 100
    print(f"K={k_range[i-1]}→K={k_range[i]}: 减少{reduction:.2f} ({reduction_rate:.1f}%)")

# 根据论文建议，当K在[4,5]时曲线趋于平稳
print(f"\n根据论文方法分析:")
print(f"当K=4时，SSE={sse[2]:.2f}")
print(f"当K=5时，SSE={sse[3]:.2f}")
print(f"SSE减少量: {sse[2]-sse[3]:.2f}")
print(f"建议选择 K=4 作为最佳聚类数")

# 验证K=4的聚类效果
kmeans_final = KMeans(n_clusters=4, random_state=42, n_init=10)
cluster_labels = kmeans_final.fit_predict(coordinates_scaled)

# 添加聚类标签到数据框
df['聚类标签'] = cluster_labels + 1  # +1使标签从1开始

print(f"\n=== K=4的聚类结果 ===")
for i in range(4):
    cities_in_cluster = df[df['聚类标签'] == i+1]['省份'].tolist()
    print(f"第{i+1}类城市 ({len(cities_in_cluster)}个): {', '.join(cities_in_cluster)}")