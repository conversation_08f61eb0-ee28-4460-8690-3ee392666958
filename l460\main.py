# -*- coding: utf-8 -*-
"""
三张照片全景拼接（纯 OpenCV 版 SIFT）
路径示例：D:\全景拼接\1.jpg 2.jpg 3.jpg
"""
import cv2
import numpy as np
import os
from matplotlib import pyplot as plt

# ---------- 工具函数 ---------- #
def detect_and_describe(img, sift=None):
    """SIFT 检测 + 描述"""
    if sift is None:
        sift = cv2.SIFT_create()
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    kp, des = sift.detectAndCompute(gray, None)
    return kp, des

def match_features(des1, des2, ratio=0.75):
    """Lowe ratio test 过滤匹配"""
    matcher = cv2.BFMatcher()
    raw = matcher.knnMatch(des1, des2, k=2)
    good = [m for m, n in raw if m.distance < ratio * n.distance]
    return good

def compute_homography(kp1, kp2, matches):
    """RANSAC 估计单应矩阵 H (kp1→kp2)"""
    if len(matches) < 4:
        return None
    src = np.float32([kp1[m.queryIdx].pt for m in matches]).reshape(-1, 1, 2)
    dst = np.float32([kp2[m.trainIdx].pt for m in matches]).reshape(-1, 1, 2)
    H, _ = cv2.findHomography(src, dst, cv2.RANSAC, 5.0)
    return H

def warp_and_merge(img_warp, img_ref, H):
    """
    把 img_warp 通过 H 映射到 img_ref 坐标系，再拼进同一画布
    返回 new_canvas
    """
    h1, w1 = img_warp.shape[:2]
    h2, w2 = img_ref.shape[:2]

    # img_warp 四角经 H 透视到新坐标
    corners_warp = cv2.perspectiveTransform(
        np.float32([[0, 0], [0, h1], [w1, 0], [w1, h1]]).reshape(-1, 1, 2), H
    )
    # img_ref 四角
    corners_ref = np.float32([[0, 0], [0, h2], [w2, 0], [w2, h2]]).reshape(-1, 1, 2)

    all_corners = np.vstack((corners_warp, corners_ref))
    [xmin, ymin] = np.int32(all_corners.min(axis=0).ravel() - 0.5)
    [xmax, ymax] = np.int32(all_corners.max(axis=0).ravel() + 0.5)

    # 平移量，保证最小坐标为 0
    tx, ty = -xmin, -ymin
    T = np.array([[1, 0, tx],
                  [0, 1, ty],
                  [0, 0, 1]], dtype=float)

    # 先把待拼图透视+平移
    canvas = cv2.warpPerspective(img_warp, T @ H, (xmax - xmin, ymax - ymin))
    # 再把参考图贴进去
    canvas[ty:ty + h2, tx:tx + w2] = img_ref
    return canvas

# ---------- 主流程 ---------- #
def stitch_three(img_paths):
    imgs = [cv2.imread(p) for p in img_paths]
    if any(im is None for im in imgs):
        raise IOError("有图片读入失败，请检查路径")

    sift = cv2.SIFT_create()

    # 0-1 配对
    kp0, des0 = detect_and_describe(imgs[0], sift)
    kp1, des1 = detect_and_describe(imgs[1], sift)
    matches01 = match_features(des0, des1)
    H01 = compute_homography(kp0, kp1, matches01)

    # 2-1 配对
    kp2, des2 = detect_and_describe(imgs[2], sift)
    matches21 = match_features(des2, des1)
    H21 = compute_homography(kp2, kp1, matches21)

    if H01 is None or H21 is None:
        raise RuntimeError("单应矩阵估计失败，可能是匹配点过少")

    # 先拼左边图 (0) ➜ 中图 (1)
    pano_left = warp_and_merge(imgs[0], imgs[1], H01)
    # 再把右边图 (2) 拼进 pano_left
    panorama = warp_and_merge(imgs[2], pano_left, H21)
    return panorama

if __name__ == "__main__":
    folder = r"D:\Code-money\l460"     # 修改成自己的文件夹
    img_paths = [os.path.join(folder, f"{i+1}.jpg") for i in range(3)]
    pano = stitch_three(img_paths)

    cv2.imwrite(os.path.join(folder, "result_cv2.png"), pano)
    plt.imshow(cv2.cvtColor(pano, cv2.COLOR_BGR2RGB))
    plt.axis("off"); plt.tight_layout()
    plt.show()
