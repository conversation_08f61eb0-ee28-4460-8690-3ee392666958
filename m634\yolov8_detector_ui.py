#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import cv2
import numpy as np
from pathlib import Path
from PyQt5.QtWidgets import (QApplication, QMainWindow, QPushButton, QFileDialog,
                            QLabel, QVBoxLayout, QHBoxLayout, QWidget, QGroupBox,
                            QFormLayout, QLineEdit, QComboBox,
                            QMessageBox, QSplitter, QGridLayout, QCheckBox,
                            QStatusBar, QFrame, QSlider, QProgressBar)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QSize
from PyQt5.QtGui import QFont, QPixmap, QImage, QIcon, QPalette, QColor
import torch
from ultralytics import YOLO

# 设置样式表
STYLE_SHEET = """
QMainWindow {
    background-color: #f5f5f5;
}
QGroupBox {
    border: 1px solid #cccccc;
    border-radius: 5px;
    margin-top: 10px;
    font-weight: bold;
    background-color: #ffffff;
}
QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 5px 0 5px;
}
QPushButton {
    background-color: #4a86e8;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    font-weight: bold;
}
QPushButton:hover {
    background-color: #3a76d8;
}
QPushButton:pressed {
    background-color: #2a66c8;
}
QLabel {
    color: #333333;
}
QComboBox, QDoubleSpinBox, QLineEdit {
    border: 1px solid #cccccc;
    border-radius: 3px;
    padding: 4px;
    background-color: white;
}
QStatusBar {
    background-color: #e6e6e6;
}
"""

class PredictionThread(QThread):
    """用于在后台运行预测的线程"""
    prediction_complete = pyqtSignal(object)
    progress_update = pyqtSignal(int)

    def __init__(self, model, image_path):
        super().__init__()
        self.model = model
        self.image_path = image_path

    def run(self):
        try:
            # 更新进度
            self.progress_update.emit(30)

            # 运行预测
            results = self.model.predict(
                source=self.image_path,
                conf=0.25,  # 使用默认值
                iou=0.45,   # 使用默认值
                verbose=False
            )

            # 更新进度
            self.progress_update.emit(90)

            # 发送结果
            self.prediction_complete.emit(results)

            # 完成
            self.progress_update.emit(100)
        except Exception as e:
            print(f"预测过程中出错: {e}")
            self.prediction_complete.emit(None)
            self.progress_update.emit(0)

class YOLOv8DetectorUI(QMainWindow):
    """YOLOv8检测器的PyQt5界面"""

    def __init__(self):
        super().__init__()
        self.model = None
        self.image_path = None
        self.results = None
        self.init_ui()

    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle('YOLOv8 目标检测器')
        self.setGeometry(100, 100, 1200, 800)
        self.setStyleSheet(STYLE_SHEET)

        # 创建主窗口部件
        main_widget = QWidget()
        self.setCentralWidget(main_widget)

        # 创建主布局
        main_layout = QHBoxLayout(main_widget)

        # 创建左侧控制面板
        control_panel = self.create_control_panel()

        # 创建右侧图像显示区域
        image_panel = self.create_image_panel()

        # 添加到主布局
        main_layout.addWidget(control_panel, 1)  # 1:3比例
        main_layout.addWidget(image_panel, 3)

        # 创建状态栏
        self.statusBar = QStatusBar()
        self.setStatusBar(self.statusBar)
        self.statusBar.showMessage('准备就绪')

        # 创建进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setFixedWidth(150)
        self.statusBar.addPermanentWidget(self.progress_bar)

    def create_control_panel(self):
        """创建左侧控制面板"""
        control_panel = QWidget()
        layout = QVBoxLayout(control_panel)

        # 模型设置组
        model_group = QGroupBox("模型设置")
        model_layout = QFormLayout(model_group)

        # 模型路径
        self.model_path_edit = QLineEdit()
        self.model_path_edit.setReadOnly(True)
        self.model_path_edit.setPlaceholderText("选择YOLOv8模型文件...")

        browse_model_btn = QPushButton("浏览...")
        browse_model_btn.clicked.connect(self.browse_model)

        model_path_layout = QHBoxLayout()
        model_path_layout.addWidget(self.model_path_edit)
        model_path_layout.addWidget(browse_model_btn)

        model_layout.addRow("模型路径:", model_path_layout)



        # 加载模型按钮
        self.load_model_btn = QPushButton("加载模型")
        self.load_model_btn.clicked.connect(self.load_model)
        model_layout.addRow("", self.load_model_btn)

        layout.addWidget(model_group)

        # 图像设置组
        image_group = QGroupBox("图像设置")
        image_layout = QFormLayout(image_group)

        # 图像路径
        self.image_path_edit = QLineEdit()
        self.image_path_edit.setReadOnly(True)
        self.image_path_edit.setPlaceholderText("选择图像文件...")

        browse_image_btn = QPushButton("浏览...")
        browse_image_btn.clicked.connect(self.browse_image)

        image_path_layout = QHBoxLayout()
        image_path_layout.addWidget(self.image_path_edit)
        image_path_layout.addWidget(browse_image_btn)

        image_layout.addRow("图像路径:", image_path_layout)

        layout.addWidget(image_group)

        # 操作组
        operation_group = QGroupBox("操作")
        operation_layout = QVBoxLayout(operation_group)

        # 预测按钮
        self.predict_btn = QPushButton("开始预测")
        self.predict_btn.setEnabled(False)
        self.predict_btn.clicked.connect(self.predict_image)
        operation_layout.addWidget(self.predict_btn)

        # 清除按钮
        clear_btn = QPushButton("清除结果")
        clear_btn.clicked.connect(self.clear_results)
        operation_layout.addWidget(clear_btn)

        layout.addWidget(operation_group)

        # 添加弹性空间
        layout.addStretch()

        return control_panel

    def create_image_panel(self):
        """创建右侧图像显示区域"""
        image_panel = QWidget()
        layout = QVBoxLayout(image_panel)

        # 创建图像标签
        self.original_image_label = QLabel("原始图像")
        self.original_image_label.setAlignment(Qt.AlignCenter)
        self.original_image_label.setMinimumSize(400, 300)
        self.original_image_label.setFrameShape(QFrame.Box)
        self.original_image_label.setStyleSheet("background-color: #e0e0e0;")

        self.result_image_label = QLabel("预测结果")
        self.result_image_label.setAlignment(Qt.AlignCenter)
        self.result_image_label.setMinimumSize(400, 300)
        self.result_image_label.setFrameShape(QFrame.Box)
        self.result_image_label.setStyleSheet("background-color: #e0e0e0;")

        # 添加到布局
        layout.addWidget(self.original_image_label)
        layout.addWidget(self.result_image_label)

        return image_panel

    def browse_model(self):
        """浏览并选择模型文件"""
        file_dialog = QFileDialog()
        model_path, _ = file_dialog.getOpenFileName(
            self, "选择YOLOv8模型文件", "", "模型文件 (*.pt *.pth *.weights);;所有文件 (*)"
        )

        if model_path:
            self.model_path_edit.setText(model_path)
            self.statusBar.showMessage(f"已选择模型: {os.path.basename(model_path)}")

    def browse_image(self):
        """浏览并选择图像文件"""
        file_dialog = QFileDialog()
        image_path, _ = file_dialog.getOpenFileName(
            self, "选择图像文件", "", "图像文件 (*.jpg *.jpeg *.png *.bmp);;所有文件 (*)"
        )

        if image_path:
            self.image_path = image_path
            self.image_path_edit.setText(image_path)
            self.statusBar.showMessage(f"已选择图像: {os.path.basename(image_path)}")

            # 显示原始图像
            self.display_original_image()

            # 如果模型已加载，启用预测按钮
            if self.model is not None:
                self.predict_btn.setEnabled(True)

    def load_model(self):
        """加载YOLOv8模型"""
        model_path = self.model_path_edit.text()

        if not model_path:
            QMessageBox.warning(self, "警告", "请先选择模型文件")
            return

        try:
            self.statusBar.showMessage("正在加载模型...")
            self.model = YOLO(model_path)
            self.statusBar.showMessage(f"模型加载成功: {os.path.basename(model_path)}")

            # 如果已选择图像，启用预测按钮
            if self.image_path:
                self.predict_btn.setEnabled(True)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"模型加载失败: {str(e)}")
            self.statusBar.showMessage("模型加载失败")

    def predict_image(self):
        """预测图像"""
        if self.model is None:
            QMessageBox.warning(self, "警告", "请先加载模型")
            return

        if not self.image_path:
            QMessageBox.warning(self, "警告", "请先选择图像")
            return

        # 创建并启动预测线程
        self.prediction_thread = PredictionThread(self.model, self.image_path)
        self.prediction_thread.prediction_complete.connect(self.handle_prediction_result)
        self.prediction_thread.progress_update.connect(self.update_progress)

        # 禁用预测按钮
        self.predict_btn.setEnabled(False)
        self.statusBar.showMessage("正在预测...")
        self.progress_bar.setValue(10)

        # 启动线程
        self.prediction_thread.start()

    def handle_prediction_result(self, results):
        """处理预测结果"""
        if results is None:
            QMessageBox.critical(self, "错误", "预测过程中出错")
            self.statusBar.showMessage("预测失败")
            self.predict_btn.setEnabled(True)
            return

        self.results = results

        # 显示结果图像
        self.display_result_image()

        # 重新启用预测按钮
        self.predict_btn.setEnabled(True)
        self.statusBar.showMessage("预测完成")

    def update_progress(self, value):
        """更新进度条"""
        self.progress_bar.setValue(value)

    def display_original_image(self):
        """显示原始图像"""
        if not self.image_path:
            return

        # 读取图像
        image = cv2.imread(self.image_path)
        if image is None:
            QMessageBox.critical(self, "错误", "无法读取图像")
            return

        # 转换为RGB
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

        # 调整图像大小以适应标签
        h, w, c = image.shape
        label_size = self.original_image_label.size()

        # 保持纵横比
        aspect_ratio = w / h
        if label_size.width() / label_size.height() > aspect_ratio:
            new_h = label_size.height()
            new_w = int(new_h * aspect_ratio)
        else:
            new_w = label_size.width()
            new_h = int(new_w / aspect_ratio)

        # 调整大小
        image = cv2.resize(image, (new_w, new_h))

        # 转换为QImage
        h, w, c = image.shape
        bytes_per_line = c * w
        q_image = QImage(image.data, w, h, bytes_per_line, QImage.Format_RGB888)

        # 显示图像
        pixmap = QPixmap.fromImage(q_image)
        self.original_image_label.setPixmap(pixmap)

    def display_result_image(self):
        """显示预测结果图像"""
        if self.results is None or len(self.results) == 0:
            return

        # 获取结果图像
        result_image = self.results[0].plot()

        # 转换为RGB
        result_image = cv2.cvtColor(result_image, cv2.COLOR_BGR2RGB)

        # 调整图像大小以适应标签
        h, w, c = result_image.shape
        label_size = self.result_image_label.size()

        # 保持纵横比
        aspect_ratio = w / h
        if label_size.width() / label_size.height() > aspect_ratio:
            new_h = label_size.height()
            new_w = int(new_h * aspect_ratio)
        else:
            new_w = label_size.width()
            new_h = int(new_w / aspect_ratio)

        # 调整大小
        result_image = cv2.resize(result_image, (new_w, new_h))

        # 转换为QImage
        h, w, c = result_image.shape
        bytes_per_line = c * w
        q_image = QImage(result_image.data, w, h, bytes_per_line, QImage.Format_RGB888)

        # 显示图像
        pixmap = QPixmap.fromImage(q_image)
        self.result_image_label.setPixmap(pixmap)

    def clear_results(self):
        """清除结果"""
        self.results = None
        self.result_image_label.clear()
        self.result_image_label.setText("预测结果")
        self.statusBar.showMessage("结果已清除")
        self.progress_bar.setValue(0)

def main():
    app = QApplication(sys.argv)
    window = YOLOv8DetectorUI()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
