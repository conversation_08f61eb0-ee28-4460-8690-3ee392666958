import sys
import os
import cv2
import numpy as np
from PyQt5.QtWidgets import (QApplication, QMainWindow, QLabel, QPushButton, 
                            QVBoxLayout, QHBoxLayout, QComboBox, QFileDialog, 
                            QWidget, QSlider, QCheckBox, QMessageBox, QProgressBar)
from PyQt5.QtGui import QPixmap, QImage
from PyQt5.QtCore import Qt, QThread, pyqtSignal
import yaml
from ultralytics import YOLO

class PredictionThread(QThread):
    update_image = pyqtSignal(np.ndarray)
    finished = pyqtSignal()
    error = pyqtSignal(str)
    
    def __init__(self, model_path, image_path, conf_thres=0.25, class_names=None):
        super().__init__()
        self.model_path = model_path
        self.image_path = image_path
        self.conf_thres = conf_thres
        self.class_names = class_names
        
    def run(self):
        try:
            # 加载模型
            if self.model_path.endswith('.yaml'):
                # 如果是YAML文件，需要先创建模型
                # 注意：这种方式只能用于推理，不能用于预测，除非您已经训练过模型
                # 实际上，您应该使用训练好的.pt文件而不是.yaml文件
                try:
                    # 尝试加载预训练权重（如果存在）
                    pt_path = self.model_path.replace('.yaml', '.pt')
                    if os.path.exists(pt_path):
                        model = YOLO(pt_path)
                    else:
                        # 如果没有预训练权重，会报错
                        self.error.emit("没有找到预训练权重文件，无法使用YAML文件直接预测。请先训练模型或提供.pt文件。")
                        return
                except Exception as e:
                    self.error.emit(f"加载模型失败: {str(e)}")
                    return
            else:
                # 加载.pt模型文件
                model = YOLO(self.model_path)
            
            # 读取图像
            if not os.path.exists(self.image_path):
                self.error.emit(f"图像文件不存在: {self.image_path}")
                return
                
            # 进行预测
            results = model.predict(self.image_path, conf=self.conf_thres)
            
            # 处理结果
            for result in results:
                # 获取原始图像
                img = cv2.imread(self.image_path)
                
                # 如果提供了自定义类别名称，使用它们
                if self.class_names:
                    # 绘制检测框
                    boxes = result.boxes
                    for box in boxes:
                        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy().astype(int)
                        conf = box.conf[0].cpu().numpy()
                        cls_id = int(box.cls[0].cpu().numpy())
                        
                        # 获取类别名称
                        if cls_id < len(self.class_names):
                            cls_name = self.class_names[cls_id]
                        else:
                            cls_name = f"Class {cls_id}"
                        
                        # 绘制边界框
                        color = (0, 255, 0)  # 绿色
                        cv2.rectangle(img, (x1, y1), (x2, y2), color, 2)
                        
                        # 添加标签
                        label = f"{cls_name} {conf:.2f}"
                        (label_width, label_height), _ = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 2)
                        cv2.rectangle(img, (x1, y1 - label_height - 10), (x1 + label_width, y1), color, -1)
                        cv2.putText(img, label, (x1, y1 - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 2)
                else:
                    # 使用YOLO的内置绘制功能
                    img = result.plot()
                
                # 发送处理后的图像
                self.update_image.emit(img)
                break  # 只处理第一个结果
            
            self.finished.emit()
        except Exception as e:
            self.error.emit(f"预测过程中发生错误: {str(e)}")


class YOLOv8GUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("YOLOv8 预测界面")
        self.setGeometry(100, 100, 1000, 800)
        
        # 存储类别名称
        self.class_names = None
        
        # 创建主窗口部件和布局
        main_widget = QWidget()
        main_layout = QVBoxLayout()
        
        # 创建顶部控制面板
        control_layout = QHBoxLayout()
        
        # 模型选择
        self.model_combo = QComboBox()
        self.model_combo.addItems([
            "yolov8_CA.yaml",
            "yolov8_CBAM.yaml",
            "yolov8_CBAMandCA.yaml",
            "yolov8_my_CBAM.yaml",
            "yolov8_SEAttention.yaml"
        ])
        control_layout.addWidget(QLabel("选择模型:"))
        control_layout.addWidget(self.model_combo)
        
        # 数据集配置文件选择
        self.dataset_btn = QPushButton("加载数据集配置")
        self.dataset_btn.clicked.connect(self.load_dataset_config)
        control_layout.addWidget(self.dataset_btn)
        
        # 置信度阈值滑块
        self.conf_slider = QSlider(Qt.Horizontal)
        self.conf_slider.setMinimum(1)
        self.conf_slider.setMaximum(100)
        self.conf_slider.setValue(25)  # 默认0.25
        self.conf_slider.setTickPosition(QSlider.TicksBelow)
        self.conf_slider.setTickInterval(10)
        
        self.conf_label = QLabel("置信度阈值: 0.25")
        self.conf_slider.valueChanged.connect(self.update_conf_label)
        
        conf_layout = QVBoxLayout()
        conf_layout.addWidget(self.conf_label)
        conf_layout.addWidget(self.conf_slider)
        
        control_layout.addLayout(conf_layout)
        
        # 添加控制面板到主布局
        main_layout.addLayout(control_layout)
        
        # 创建图像选择和预测按钮
        btn_layout = QHBoxLayout()
        
        self.select_img_btn = QPushButton("选择图像")
        self.select_img_btn.clicked.connect(self.select_image)
        btn_layout.addWidget(self.select_img_btn)
        
        self.predict_btn = QPushButton("开始预测")
        self.predict_btn.clicked.connect(self.start_prediction)
        self.predict_btn.setEnabled(False)  # 初始禁用
        btn_layout.addWidget(self.predict_btn)
        
        main_layout.addLayout(btn_layout)
        
        # 创建进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        main_layout.addWidget(self.progress_bar)
        
        # 创建图像显示区域
        self.image_label = QLabel("请选择图像")
        self.image_label.setAlignment(Qt.AlignCenter)
        self.image_label.setStyleSheet("border: 2px dashed #aaa; background-color: #f0f0f0;")
        main_layout.addWidget(self.image_label)
        
        # 设置主布局
        main_widget.setLayout(main_layout)
        self.setCentralWidget(main_widget)
        
        # 初始化变量
        self.image_path = None
        self.prediction_thread = None
    
    def update_conf_label(self):
        value = self.conf_slider.value() / 100
        self.conf_label.setText(f"置信度阈值: {value:.2f}")
    
    def load_dataset_config(self):
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择数据集配置文件", "", "YAML Files (*.yaml);;All Files (*)"
        )
        
        if file_path:
            try:
                with open(file_path, 'r') as f:
                    config = yaml.safe_load(f)
                
                if 'names' in config:
                    self.class_names = config['names']
                    class_list = ", ".join([f"{i}: {name}" for i, name in self.class_names.items()])
                    QMessageBox.information(self, "加载成功", f"成功加载类别信息:\n{class_list}")
                else:
                    QMessageBox.warning(self, "加载失败", "配置文件中没有找到类别信息(names)")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"加载配置文件时出错: {str(e)}")
    
    def select_image(self):
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择图像", "", "Images (*.png *.jpg *.jpeg);;All Files (*)"
        )
        
        if file_path:
            self.image_path = file_path
            # 显示原始图像
            pixmap = QPixmap(file_path)
            
            # 调整图像大小以适应标签
            pixmap = pixmap.scaled(self.image_label.width(), self.image_label.height(), 
                                  Qt.KeepAspectRatio, Qt.SmoothTransformation)
            
            self.image_label.setPixmap(pixmap)
            self.predict_btn.setEnabled(True)
    
    def start_prediction(self):
        if not self.image_path:
            QMessageBox.warning(self, "警告", "请先选择图像")
            return
        
        # 获取选择的模型
        model_name = self.model_combo.currentText()
        model_path = os.path.join("ultralytics-main", model_name)
        
        # 检查模型文件是否存在
        if not os.path.exists(model_path):
            # 尝试查找对应的.pt文件
            pt_path = model_path.replace('.yaml', '.pt')
            if os.path.exists(pt_path):
                model_path = pt_path
            else:
                QMessageBox.critical(self, "错误", f"模型文件不存在: {model_path}")
                return
        
        # 获取置信度阈值
        conf_thres = self.conf_slider.value() / 100
        
        # 禁用按钮，显示进度条
        self.predict_btn.setEnabled(False)
        self.select_img_btn.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 不确定进度
        
        # 创建并启动预测线程
        self.prediction_thread = PredictionThread(model_path, self.image_path, conf_thres, self.class_names)
        self.prediction_thread.update_image.connect(self.update_result_image)
        self.prediction_thread.finished.connect(self.prediction_finished)
        self.prediction_thread.error.connect(self.show_error)
        self.prediction_thread.start()
    
    def update_result_image(self, img):
        # 将OpenCV图像转换为QPixmap并显示
        height, width, channel = img.shape
        bytes_per_line = 3 * width
        q_img = QImage(img.data, width, height, bytes_per_line, QImage.Format_RGB888).rgbSwapped()
        pixmap = QPixmap.fromImage(q_img)
        
        # 调整图像大小以适应标签
        pixmap = pixmap.scaled(self.image_label.width(), self.image_label.height(), 
                              Qt.KeepAspectRatio, Qt.SmoothTransformation)
        
        self.image_label.setPixmap(pixmap)
    
    def prediction_finished(self):
        # 恢复按钮状态，隐藏进度条
        self.predict_btn.setEnabled(True)
        self.select_img_btn.setEnabled(True)
        self.progress_bar.setVisible(False)
    
    def show_error(self, error_msg):
        QMessageBox.critical(self, "错误", error_msg)
        self.prediction_finished()


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = YOLOv8GUI()
    window.show()
    sys.exit(app.exec_())
