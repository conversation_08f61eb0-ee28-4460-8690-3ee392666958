import numpy as np
import scipy.constants as const
from scipy.integrate import trapz, trapezoid # Updated import
from scipy.interpolate import interp1d
import matplotlib.pyplot as plt
from PyMieScatt import MieQ # Using MieQ
from matplotlib.ticker import MultipleLocator # For minor ticks

# --- 物理和材料常数 ---
RHO_DUST = 2500.0  # 颗粒密度 (kg/m^3)
C_R = 7.6e-4       # 辐射压力常数 (kg/m^2)
T_SUN = 5778.0     # 太阳有效温度 (K)

# --- 普朗克函数 B(lambda, T) ---
def planck_lambda(wav_m, T):
    wav_m = np.maximum(wav_m, 1e-12) # Prevent division by zero or log of zero if wav_m can be zero
    a = 2.0 * const.h * const.c**2
    b = const.h * const.c / (wav_m * const.k * T)
    intensity = np.zeros_like(wav_m)
    # Avoid overflow in np.exp(b) for large b
    mask_normal = b < 700 # exp(709) is near float64 max
    intensity[mask_normal] = a / (wav_m[mask_normal]**5 * (np.exp(b[mask_normal]) - 1.0))
    # For very large b, exp(b) - 1 approx exp(b), so intensity approx a / (wav_m**5 * exp(b))
    # This part is usually negligible for solar spectrum but good for robustness
    # mask_large_b = b >= 700
    # intensity[mask_large_b] = a / (wav_m[mask_large_b]**5 * np.exp(b[mask_large_b])) # This could still overflow
    return intensity

# --- 加载和插值光学常数 ---
def load_optical_constants(filepath):
    print(f"Attempting to load optical constants from: {filepath}")
    try:
        data = np.loadtxt(filepath)
        print(f"  Successfully read data with shape: {data.shape}")

        # Assuming wavelength is in column 0, n-1 in column 3, k in column 4
        # Sort by wavelength to ensure interp1d works correctly
        sort_indices = np.argsort(data[:, 0])
        wav_um_data_sorted = data[sort_indices, 0]
        n_minus_1_data_sorted = data[sort_indices, 3]
        k_data_sorted = data[sort_indices, 4]

        # Check for and handle duplicate wavelengths if necessary (use first occurrence)
        unique_wavs, unique_indices = np.unique(wav_um_data_sorted, return_index=True)
        if len(unique_wavs) < len(wav_um_data_sorted):
            print(f"  Warning: Duplicate wavelengths found. Using values from first occurrences after sorting.")
            wav_um_data_final = wav_um_data_sorted[unique_indices]
            n_minus_1_data_final = n_minus_1_data_sorted[unique_indices]
            k_data_final = k_data_sorted[unique_indices]
        else:
            wav_um_data_final = wav_um_data_sorted
            n_minus_1_data_final = n_minus_1_data_sorted
            k_data_final = k_data_sorted

        print(f"  Number of unique wavelength points for interpolation: {len(wav_um_data_final)}")
        n_data_final = n_minus_1_data_final + 1.0 # Convert n-1 to n
        n_interp_func = interp1d(wav_um_data_final, n_data_final, kind='linear', fill_value="extrapolate", bounds_error=False)
        k_interp_func = interp1d(wav_um_data_final, k_data_final, kind='linear', fill_value="extrapolate", bounds_error=False)

        print(f"  Optical constants processing complete.")
        print(f"  Data file wavelength range (after processing): {wav_um_data_final.min():.2e} um to {wav_um_data_final.max():.2e} um")
        # test_wl_um = np.array([0.05, 0.1, 0.5, 1.0, 5.0, 10.0, 50.0, 100.0, 500.0, 1000.0])
        # print("  Interpolation test:")
        # for wl_test in test_wl_um:
        #     n_test = n_interp_func(wl_test)
        #     k_test = k_interp_func(wl_test)
        #     print(f"    wl={wl_test:>7.2f} um: n={n_test:.4f}, k={k_test:.4e}")
        return n_interp_func, k_interp_func
    except FileNotFoundError:
        print(f"Error: Optical constants file not found at {filepath}")
    except Exception as e:
        print(f"Error loading/processing optical constants from {filepath}: {e}")
    print("  Using placeholder constant optical constants as fallback.")
    placeholder_n = lambda x: 1.7 # Example constant n
    placeholder_k = lambda x: 0.01 # Example constant k
    return placeholder_n, placeholder_k

# --- 主计算 ---
def calculate_beta_vs_mass(radii_m, lambda_nm_array, n_interp, k_interp):
    masses_kg = []
    betas = []
    lambda_m_array = lambda_nm_array * 1e-9 # Convert nm to m
    lambda_um_array = lambda_nm_array * 1e-3 # Convert nm to um (for optical constants)
    solar_spectrum_values = planck_lambda(lambda_m_array, T_SUN)
    solar_spectrum_integral = trapezoid(solar_spectrum_values, lambda_m_array) # Use trapezoid


    if solar_spectrum_integral == 0 or np.isnan(solar_spectrum_integral) or solar_spectrum_integral < 1e-10: # Added a small threshold
        print(f"Error/Warning: Solar spectrum integral is very small or invalid ({solar_spectrum_integral:.2e}). This will affect Qpr_avg.")
    # print(f"Solar spectrum integral (denominator for Qpr_avg): {solar_spectrum_integral:.3e}")
    if np.any(np.isnan(solar_spectrum_values)) or np.any(np.isinf(solar_spectrum_values)):
        print("Warning: NaN or Inf found in solar_spectrum_values.")

    for s_idx, s_m in enumerate(radii_m):
        # is_first_radius_loop = (s_idx == 0) # For targeted debugging
        # if is_first_radius_loop:
        #     print(f"\n--- Debugging for FIRST particle radius: s_m = {s_m:.3e} m ({s_m*1e6:.3f} um) ---")

        if s_m <= 0: continue # Skip non-physical radii
        q_pr_values = []

        for i, lam_m in enumerate(lambda_m_array):
            lam_um = lambda_um_array[i]
            n_val = n_interp(lam_um)
            k_val = k_interp(lam_um)
            # Basic sanity checks for interpolated optical constants
            if np.isnan(n_val) or np.isinf(n_val) or n_val < 0: n_val = 1.0 # Fallback for bad n
            if np.isnan(k_val) or np.isinf(k_val) or k_val < 0: k_val = 0.0 # Fallback for bad k
            m_complex_for_pymie = complex(n_val, k_val)
            Qpr_val = 0.0
            diameter_m = 2.0 * s_m

            if diameter_m > 1e-12 and lam_m > 1e-12: # Basic validity checks
                try:
                    # MieQ directly takes m, wavelength (in m), diameter (in m)
                    scattering_properties = MieQ(m=m_complex_for_pymie,
                                                 wavelength=lam_m,
                                                 diameter=diameter_m,
                                                 nMedium=1.0) # Assuming vacuum/air
                    if isinstance(scattering_properties, tuple) and len(scattering_properties) >= 5:
                        Qpr_val = scattering_properties[4] # Qpr is typically the 5th element
                        if np.isnan(Qpr_val) or np.isinf(Qpr_val): Qpr_val = 0.0 # Handle bad Qpr
                    else:
                        # if is_first_radius_loop and i % (len(lambda_m_array)//10) == 0 : # Debug print
                        #     print(f"  Warning (s={s_m*1e6:.2f}um, lam={lam_um:.2f}um): MieQ unexpected output. Output: {scattering_properties}. Setting Qpr=0.")
                        Qpr_val = 0.0
                except Exception as e_mie:
                    # if is_first_radius_loop and i % (len(lambda_m_array)//10) == 0 : # Debug print
                    #     print(f"  Error (s={s_m*1e6:.2f}um, lam={lam_um:.2f}um): MieQ calc error: {e_mie}. m={m_complex_for_pymie}, diam={diameter_m:.2e}m, wl={lam_m:.2e}m. Setting Qpr=0.")
                    Qpr_val = 0.0
            q_pr_values.append(Qpr_val)
            # if is_first_radius_loop and i % (len(lambda_m_array)//20) == 0: # More detailed debug print
            #      print(f"    wl={lam_um:7.2f}um, n={n_val:.3f}, k={k_val:.3e}, m={m_complex_for_pymie.real:.3f}+{m_complex_for_pymie.imag:.3e}j, diam={diameter_m*1e6:7.2f}um, Qpr_val={Qpr_val:.3e}")

        q_pr_array = np.array(q_pr_values)
        # if is_first_radius_loop:
        #     print(f"  For s_m = {s_m:.3e} m:")
        #     print(f"    q_pr_array stats: min={q_pr_array.min():.3e}, max={q_pr_array.max():.3e}, mean={q_pr_array.mean():.3e}, any_nan={np.any(np.isnan(q_pr_array))}")
        #     print(f"    solar_spectrum_values stats: min={solar_spectrum_values.min():.3e}, max={solar_spectrum_values.max():.3e}, mean={solar_spectrum_values.mean():.3e}, any_nan={np.any(np.isnan(solar_spectrum_values))}")

        integrand = q_pr_array * solar_spectrum_values
        if np.any(np.isnan(integrand)) or np.any(np.isinf(integrand)):
            print(f"  Warning (s={s_m*1e6:.2f}um): NaN/Inf in integrand. Replacing with 0.")
            integrand = np.nan_to_num(integrand, nan=0.0, posinf=0.0, neginf=0.0)

        if solar_spectrum_integral != 0:
            q_pr_avg = trapezoid(integrand, lambda_m_array) / solar_spectrum_integral # Use trapezoid
        else:
            q_pr_avg = 0
            # print(f"  Warning (s={s_m*1e6:.2f}um): solar_spectrum_integral is zero, setting q_pr_avg to 0.")

        g_over_m = 3.0 / (4.0 * s_m * RHO_DUST) # Geometrical cross section over mass
        beta = C_R * q_pr_avg * g_over_m
        mass_kg = (4.0/3.0) * np.pi * s_m**3 * RHO_DUST
        masses_kg.append(mass_kg)
        betas.append(beta)
        # print(f"Result for s_m={s_m*1e6:7.3f} um (mass={mass_kg:.2e} kg): Qpr_avg={q_pr_avg:7.4f}, beta={beta:7.4f}")
        # if is_first_radius_loop:
        #      print(f"--- End Debugging for FIRST particle radius ---")
    return np.array(masses_kg), np.array(betas)

# --- 设置参数并运行 ---
optical_constants_filepath = 'callindex.txt' # Make sure this file exists or provide the correct path
print(f"Using optical constants file: {optical_constants_filepath}")
n_interp_func, k_interp_func = load_optical_constants(optical_constants_filepath)

# Define mass range for the plot (e.g., 10^-23 to 10^-7 kg from your reference image)
# Convert this mass range to a particle size (radius) range
s_min_m = (3 * 1e-23 / (4 * np.pi * RHO_DUST))**(1/3)
s_max_m = (3 * 1e-7  / (4 * np.pi * RHO_DUST))**(1/3)
# Generate particle radii logarithmically for smoother curve over orders of magnitude
particle_radii_m_for_calculation = np.logspace(np.log10(s_min_m), np.log10(s_max_m), 100) # Increased points for smoother curve
print(f"\nParticle radii for calculation (m): min={particle_radii_m_for_calculation.min():.2e}, max={particle_radii_m_for_calculation.max():.2e}, count={len(particle_radii_m_for_calculation)}")

# Define wavelength range for integrating Qpr over the solar spectrum (e.g., UV to far-IR)
# Common range for solar spectrum relevant to dust: ~10 nm to 100,000 nm (0.01 um to 100 um)
wavelengths_nm = np.logspace(np.log10(10), np.log10(100000), 300) # 300 points from 10nm to 100um
print(f"Wavelengths for integration (nm): min={wavelengths_nm.min():.1f}, max={wavelengths_nm.max():.1f}, count={len(wavelengths_nm)}\n")

calculated_masses_kg, calculated_betas = calculate_beta_vs_mass(
    particle_radii_m_for_calculation,
    wavelengths_nm,
    n_interp_func,
    k_interp_func
)

# --- 绘图 ---
if len(calculated_masses_kg) > 0 and len(calculated_betas) > 0:
    # Figure size and aspect ratio (reference image plot area ~1.428 W/H)
    plt.figure(figsize=(7, 5)) # Adjusted for aspect ratio (7 / 1.428 ~ 4.9)

    plt.plot(calculated_masses_kg, calculated_betas, linestyle='-', color='black', linewidth=1.5)

    plt.xscale('log')
    plt.xlabel('Mass (kg)', fontsize=14)
    plt.ylabel('β ratio', fontsize=14)
    # No title to match reference

    # X-axis ticks and limits
    # Reference ticks: 10^-22, 10^-20, ..., 10^-8
    x_ticks_values = [10.0**p for p in np.arange(-22, -8+1, 2)] # Use 10.0 for float exponentiation
    x_tick_labels = [f'$10^{{{int(np.log10(t))}}}$' for t in x_ticks_values]
    plt.xticks(x_ticks_values, labels=x_tick_labels, fontsize=12)
    # X-limits: reference shows 10^-8 tick at the very right edge,
    # and 10^-22 tick slightly offset from the left edge.
    plt.xlim(10.0**(-22.15), 10.0**(-8.0)) # Use 10.0 for float exponentiation

    # Y-axis ticks and limits
    # Reference ticks: .2, .4, ..., 1.4
    y_ticks_values = np.arange(0.2, 1.401, 0.2) # 0.2, 0.4, ..., 1.4
    # Custom Y-tick labels to match reference (.2 instead of 0.2, but 1.0 as 1.0)
    y_tick_labels_custom = []
    for val in y_ticks_values:
        if val == 1.0:
            y_tick_labels_custom.append("1.0")
        elif 0 < val < 1.0 : # Handles 0.2 to 0.8
            y_tick_labels_custom.append(f"{val:.1f}".lstrip('0'))
        else: # Handles 1.2, 1.4
            y_tick_labels_custom.append(f"{val:.1f}")
    plt.yticks(y_ticks_values, labels=y_tick_labels_custom, fontsize=12)

    # Y-limits: reference shows axis from 0.0 up to ~1.5 (1.4 is last tick)
    plt.ylim(0.0, 1.5)

    # Minor ticks for Y-axis (5 intervals between major ticks means step of 0.04)
    plt.gca().yaxis.set_minor_locator(MultipleLocator(0.04))

    # Grid: Reference image has no grid
    plt.grid(False)

    # Spine and Tick appearance
    ax = plt.gca()
    spine_line_width = 1.0  # Thickness of the plot border lines
    major_tick_len = 6
    minor_tick_len = 3
    tick_width = spine_line_width # Make ticks as thick as spines

    # Ensure all spines are visible and set their width
    for spine_pos in ['top', 'bottom', 'left', 'right']:
        ax.spines[spine_pos].set_visible(True)
        ax.spines[spine_pos].set_linewidth(spine_line_width)

    # X-axis major ticks (no minor x-ticks visible in reference)
    ax.tick_params(axis='x', which='major', direction='inout', top=True, bottom=True,
                   length=major_tick_len, width=tick_width)

    # Y-axis major and minor ticks
    ax.tick_params(axis='y', which='major', direction='inout', right=True, left=True,
                   length=major_tick_len, width=tick_width)
    ax.tick_params(axis='y', which='minor', direction='inout', right=True, left=True,
                   length=minor_tick_len, width=tick_width * 0.75) # Minor ticks can be thinner

    plt.tight_layout(pad=1.0) # Adjust padding around the plot
    plt.show()
else:
    print("\nNo data was calculated to plot. Please check console output for errors.")