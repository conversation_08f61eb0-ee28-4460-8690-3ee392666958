# 图像隐写加密系统

这是一个基于LSB算法的图像隐写加密系统，能够将文本信息隐藏在图像中，同时提供高级别的加密保护。系统采用了ChaCha20流加密和AES-256-GCM认证加密的双重加密架构，保证数据的安全性。系统支持多种启动方式，适应不同的运行环境。

## 功能特点

- **图像隐写**：使用改进的LSB算法，在保持图像视觉质量的同时隐藏文本信息
- **双重加密**：使用ChaCha20流加密和AES-256-GCM认证加密提供多层加密保护
- **密钥管理**：支持自动生成安全密钥或使用自定义密钥
- **攻击测试**：内置多种攻击测试方法，评估隐写系统的安全性和抗攻击能力
- **多平台支持**：提供三种不同的启动方式，适应不同的运行环境
- **简单易用**：提供直观的Web界面进行操作，无需复杂配置

## 系统要求

- Python 3.6+
- 必要的Python库（根据启动方式不同，部分库可选）：
  - fastapi - 用于main.py（FastAPI版本）
  - uvicorn - 用于main.py（FastAPI版本）
  - python-multipart - 用于处理文件上传
  - pillow (PIL) - 用于图像处理
  - numpy - 用于数组操作和图像处理
  - pycryptodome - 用于加密算法实现
  - jinja2 - 用于HTML模板渲染
  - flask - 用于main_flask.py（Flask版本）
  - 无需额外依赖 - main_simple.py使用Python内置http.server

## 快速开始

系统提供三种不同的启动方式，可以根据您的环境和需求选择最合适的方式。

### 方法1：使用智能启动脚本（推荐）

```bash
python run.py
```

智能启动脚本会自动检测您的环境并选择最适合的运行方式：
- 如果已安装fastapi和uvicorn，将使用FastAPI版本（main.py）
- 如果已安装flask但未安装fastapi，将使用Flask版本（main_flask.py）
- 如果以上依赖都未安装，将使用内置HTTP服务器版本（main_simple.py）或提示您安装依赖

### 方法2：直接运行特定版本

根据您的环境和需求选择合适的脚本：

```bash
# FastAPI版本（功能最完整，性能最好）
python main.py

# Flask版本（功能完整，兼容性好）
python main_flask.py

# 内置HTTP服务器版本（无需额外依赖，基本功能可用）
python main_simple.py
```

### 启动方式对比

| 启动方式 | 文件 | 依赖要求 | 特点 |
|---------|------|---------|------|
| FastAPI | main.py | fastapi, uvicorn | 性能最佳，异步处理，完整功能支持 |
| Flask | main_flask.py | flask | 兼容性好，同步处理，完整功能支持 |
| 内置HTTP服务器 | main_simple.py | 无额外依赖 | 最小依赖，基本功能可用，界面简化 |

无论使用哪种方式启动，服务器将运行在 http://localhost:8000

## 使用指南

### 加密图像

1. 打开 http://localhost:8000
2. 选择"加密模式"
3. 上传要用于隐写的图像文件
4. 输入要隐藏的文本
5. （可选）选择使用自定义密钥
6. 点击"加密文件"
7. 复制生成的密钥并下载加密后的图像

### 解密图像

1. 打开 http://localhost:8000
2. 选择"解密模式"
3. 上传加密的图像文件
4. 输入密钥
5. 点击"解密文件"
6. 查看解密后的文本

### 攻击测试

1. 打开 http://localhost:8000/attack-test
2. 上传一个已加密的图像文件
3. 输入用于加密的密钥
4. 从下拉菜单中选择攻击类型：
   - JPEG压缩攻击（可设置压缩质量）
   - 随机裁剪攻击（可设置裁剪比例）
   - 高斯噪声攻击（可设置噪声强度）
   - 隐写分析攻击
   - 文本操纵攻击（可输入新文本）
   - 密钥攻击（可设置修改位数）
5. 根据选择的攻击类型，设置相应的攻击参数
6. 点击"执行攻击测试"按钮
7. 查看攻击结果，包括：
   - 原始加密图像与攻击后图像的对比
   - 攻击状态和详细信息
   - 能否从攻击后的图像中提取出隐写文本
   - 提取出的文本内容（如果可提取）

## 注意事项

- 请妥善保管密钥，一旦丢失将无法恢复隐藏在图像中的信息
- 加密图像必须保存为PNG格式，以避免信息丢失
- 隐写容量取决于图像大小，较大的图像可以隐藏更多文本
- 建议使用原始版本的main.py，它具有最好的性能和安全性

## 系统架构

系统采用"双加密引擎+多模态隐写"的技术体系：

1. **第一层加密**：ChaCha20流加密（256位密钥，64位随机nonce）
2. **第二层加密**：AES-256-GCM认证加密（生成16字节认证标签和16字节初始化向量）
3. **密钥派生**：PBKDF2-HMAC-SHA256算法，配置100,000次迭代强化密钥强度
4. **数据完整性**：Blake2b哈希 + GCM认证标签的双验证机制
5. **隐写算法**：改进的LSB算法，每个RGB通道嵌入2位数据

## 攻击测试原理

攻击测试模块用于评估隐写系统的安全性和抗攻击能力。系统实现了多种攻击方式，模拟现实环境中可能遇到的各种威胁，以测试隐写信息的鲁棒性。

### 攻击类型及原理

1. **JPEG压缩攻击**
   - **原理**：JPEG是一种有损压缩格式，会丢弃人眼不敏感的高频信息。LSB隐写在像素值的最低位，正好属于高频信息范畴。
   - **实现方式**：将PNG图像转换为JPEG格式并设定压缩质量，然后再转回PNG格式。
   - **攻击效果**：压缩过程会破坏LSB中的隐写数据，压缩质量越低，破坏越严重。
   - **安全意义**：测试隐写系统对图像格式转换的抵抗能力。

2. **随机裁剪攻击**
   - **原理**：图像裁剪会移除部分包含隐写信息的像素。
   - **实现方式**：随机选择图像的一部分区域进行裁剪，然后将裁剪后的图像调整回原始大小。
   - **攻击效果**：裁剪会永久丢失部分隐写数据，导致无法完整提取信息。
   - **安全意义**：测试隐写系统对图像物理修改的抵抗能力。

3. **高斯噪声攻击**
   - **原理**：向图像添加随机噪声会改变像素值，包括LSB位。
   - **实现方式**：生成符合高斯分布的随机噪声，并添加到图像的每个像素通道中。
   - **攻击效果**：噪声会随机改变像素值，破坏LSB中的隐写数据。
   - **安全意义**：测试隐写系统对图像质量退化的抵抗能力。

4. **隐写分析攻击**
   - **原理**：通过统计分析检测图像中是否存在隐写信息，并尝试破坏它。
   - **实现方式**：分析图像的LSB分布，检测异常模式，然后随机化可疑区域的LSB。
   - **攻击效果**：有针对性地破坏可能包含隐写信息的区域。
   - **安全意义**：测试隐写系统对专业隐写分析工具的抵抗能力。

5. **文本操纵攻击**
   - **原理**：在已知密钥的情况下，修改隐写文本内容。
   - **实现方式**：使用正确密钥解密隐写数据，修改文本内容，然后重新加密并嵌入图像。
   - **攻击效果**：成功修改隐写文本，但不破坏隐写机制本身。
   - **安全意义**：测试系统在密钥泄露情况下的数据完整性保护能力。

6. **密钥攻击**
   - **原理**：尝试通过修改密钥的部分位来破解加密。
   - **实现方式**：基于原始密钥生成多个稍有变化的密钥，尝试解密隐写数据。
   - **攻击效果**：测试密钥的敏感性和加密算法的强度。
   - **安全意义**：评估系统对密钥部分泄露或猜测的抵抗能力。

### 攻击测试结果评估

每次攻击测试后，系统会自动评估攻击效果：

1. **提取成功率**：尝试从攻击后的图像中提取隐写文本，判断是否能成功解密。
2. **数据完整性**：比较提取的文本与原始隐写文本的差异程度。
3. **视觉质量**：评估攻击对图像视觉质量的影响程度。

### 攻击测试系统核心代码实现

#### 攻击测试类结构

```python
class AttackTester:
    """图像隐写攻击测试类"""

    def __init__(self, steg_handler: SteganographyHandler = None):
        """初始化攻击测试器"""
        self.steg_handler = steg_handler if steg_handler else SteganographyHandler()

    # 各种攻击方法实现
    def jpeg_compression_attack(self, image_path, output_path, quality):
        # JPEG压缩攻击实现

    def random_crop_attack(self, image_path, output_path, crop_percentage):
        # 随机裁剪攻击实现

    def gaussian_noise_attack(self, image_path, output_path, mean, sigma):
        # 高斯噪声攻击实现

    def steganalysis_attack(self, image_path, output_path, intensity):
        # 隐写分析攻击实现

    def text_manipulation_attack(self, image_path, output_path, original_key, manipulation_type, params):
        # 文本操纵攻击实现

    def key_attack(self, image_path, output_path, original_key, attack_type):
        # 密钥攻击实现

    def run_attack(self, attack_type, image_path, output_path, params):
        # 统一攻击执行入口

    def evaluate_attack(self, attack_result, original_key):
        # 攻击效果评估
```

#### JPEG压缩攻击实现

```python
def jpeg_compression_attack(self, image_path: str, output_path: str, quality: int = 75) -> str:
    """
    JPEG压缩攻击 - 通过有损压缩破坏LSB中的隐写信息
    """
    try:
        # 读取图像
        img = Image.open(image_path)

        # 保存为JPEG格式（有损压缩）
        jpeg_path = output_path.replace('.png', '.jpg')
        img.save(jpeg_path, 'JPEG', quality=quality)

        # 重新读取JPEG图像
        compressed_img = Image.open(jpeg_path)

        # 保存为PNG格式（用于后续处理）
        compressed_img.save(output_path)

        # 删除临时JPEG文件
        if os.path.exists(jpeg_path):
            os.remove(jpeg_path)

        return output_path
    except Exception as e:
        logging.error(f"JPEG压缩攻击失败: {str(e)}")
        raise ValueError(f"JPEG压缩攻击失败: {str(e)}")
```

#### 随机裁剪攻击实现

```python
def random_crop_attack(self, image_path: str, output_path: str, crop_percentage: int = 10) -> str:
    """
    随机裁剪攻击 - 随机裁剪图像的一部分，破坏部分隐写信息
    """
    try:
        # 读取图像
        img = Image.open(image_path)
        width, height = img.size

        # 计算裁剪区域
        crop_pixels_x = int(width * crop_percentage / 100)
        crop_pixels_y = int(height * crop_percentage / 100)

        # 随机选择裁剪起点
        start_x = random.randint(0, crop_pixels_x)
        start_y = random.randint(0, crop_pixels_y)

        # 裁剪图像
        cropped_img = img.crop((start_x, start_y, width, height))

        # 创建新图像并粘贴裁剪后的图像
        new_img = Image.new(img.mode, (width, height), (255, 255, 255))
        new_img.paste(cropped_img, (0, 0))

        # 保存结果
        new_img.save(output_path)

        return output_path
    except Exception as e:
        logging.error(f"随机裁剪攻击失败: {str(e)}")
        raise ValueError(f"随机裁剪攻击失败: {str(e)}")
```

#### 高斯噪声攻击实现

```python
def gaussian_noise_attack(self, image_path: str, output_path: str, mean: float = 0, sigma: float = 10) -> str:
    """
    高斯噪声攻击 - 向图像添加高斯噪声，干扰LSB中的隐写信息
    """
    try:
        # 读取图像
        img = Image.open(image_path)
        img_array = np.array(img)

        # 生成高斯噪声
        noise = np.random.normal(mean, sigma, img_array.shape).astype(np.int16)

        # 添加噪声
        noisy_img_array = img_array.astype(np.int16) + noise

        # 裁剪值到有效范围
        noisy_img_array = np.clip(noisy_img_array, 0, 255).astype(np.uint8)

        # 保存结果
        noisy_img = Image.fromarray(noisy_img_array)
        noisy_img.save(output_path)

        return output_path
    except Exception as e:
        logging.error(f"高斯噪声攻击失败: {str(e)}")
        raise ValueError(f"高斯噪声攻击失败: {str(e)}")
```

#### 文本操纵攻击实现

```python
def text_manipulation_attack(self, image_path: str, output_path: str, original_key: bytes,
                             manipulation_type: str = 'random', params: Dict[str, Any] = None) -> Tuple[str, Optional[str]]:
    """
    文本操纵攻击 - 尝试修改隐写在图像中的文本
    """
    try:
        # 首先尝试提取原始文本
        original_text = self.steg_handler.extract_text_from_image(image_path, original_key)

        # 根据操纵类型修改文本
        if manipulation_type == 'custom' and custom_text:
            # 使用自定义文本完全替换原始文本
            modified_text = custom_text
        elif manipulation_type == 'append':
            # 在文本末尾添加内容
            append_text = custom_text if custom_text else " [HACKED]"
            modified_text = original_text + append_text
        elif manipulation_type == 'truncate':
            # 截断文本
            modified_text = original_text[:len(original_text)//2]
        else:  # random
            # 随机修改文本中的一个字符
            char_pos = random.randint(0, len(original_text) - 1)
            char_list = list(original_text)
            char_list[char_pos] = chr(ord(char_list[char_pos]) + 1)
            modified_text = ''.join(char_list)

        # 使用相同的密钥重新嵌入修改后的文本
        self.steg_handler.embed_text_in_image(image_path, modified_text, original_key, output_path)

        return output_path, modified_text
    except Exception as e:
        logging.error(f"文本操纵攻击失败: {str(e)}")
        # 确保即使失败也返回一个有效的输出路径
        if not os.path.exists(output_path):
            shutil.copy(image_path, output_path)
        return output_path, None
```

#### 隐写分析攻击实现

```python
def steganalysis_attack(self, image_path: str, output_path: str, intensity: int = 1) -> str:
    """
    图像隐写分析攻击 - 模拟隐写分析攻击，通过修改LSB位来破坏隐写信息
    """
    try:
        # 读取图像
        img = Image.open(image_path)
        img_array = np.array(img)

        # 根据强度计算修改比例
        modify_ratio = intensity * 0.1  # 10% - 50%

        # 随机选择像素进行LSB修改
        height, width, channels = img_array.shape
        total_pixels = height * width * channels
        pixels_to_modify = int(total_pixels * modify_ratio)

        for _ in range(pixels_to_modify):
            # 随机选择像素位置
            h = random.randint(0, height - 1)
            w = random.randint(0, width - 1)
            c = random.randint(0, channels - 1)

            # 修改LSB
            img_array[h, w, c] = img_array[h, w, c] ^ 1  # 翻转最低位

        # 保存结果
        modified_img = Image.fromarray(img_array)
        modified_img.save(output_path)

        return output_path
    except Exception as e:
        logging.error(f"图像隐写分析攻击失败: {str(e)}")
        raise ValueError(f"图像隐写分析攻击失败: {str(e)}")
```

#### 密钥攻击实现

```python
def key_attack(self, image_path: str, output_path: str, original_key: bytes,
               attack_type: str = 'bit_flip') -> Tuple[str, bytes]:
    """
    密钥攻击 - 尝试修改或破解密钥
    """
    try:
        # 复制原始图像到输出路径
        img = Image.open(image_path)
        img.save(output_path)

        # 根据攻击类型生成攻击密钥
        if attack_type == 'bit_flip':
            # 随机翻转密钥中的一个位
            key_array = bytearray(original_key)
            bit_pos = random.randint(0, len(key_array) * 8 - 1)
            byte_pos = bit_pos // 8
            bit_offset = bit_pos % 8
            key_array[byte_pos] ^= (1 << bit_offset)
            attacked_key = bytes(key_array)
        elif attack_type == 'similar_key':
            # 生成一个与原始密钥相似的密钥
            key_array = bytearray(original_key)
            # 修改最后几个字节
            for i in range(1, 4):
                if len(key_array) - i >= 0:
                    key_array[len(key_array) - i] = random.randint(0, 255)
            attacked_key = bytes(key_array)
        elif attack_type == 'brute_force':
            # 模拟暴力破解 - 实际上只是随机生成一个新密钥
            attacked_key = os.urandom(len(original_key))
        else:
            raise ValueError(f"不支持的密钥攻击类型: {attack_type}")

        return output_path, attacked_key
    except Exception as e:
        logging.error(f"密钥攻击失败: {str(e)}")
        raise ValueError(f"密钥攻击失败: {str(e)}")
```

#### 攻击效果评估实现

```python
def evaluate_attack(self, attack_result: Dict[str, Any], original_key: bytes) -> Dict[str, Any]:
    """
    评估攻击效果
    """
    evaluation = {
        "extraction_success": False,
        "original_text": None,
        "extracted_text": None,
        "integrity_preserved": False,
        "message": ""
    }

    try:
        # 尝试使用原始密钥从攻击后的图像中提取文本
        try:
            extracted_text = self.steg_handler.extract_text_from_image(
                attack_result["output_path"], original_key
            )
            evaluation["extraction_success"] = True
            evaluation["extracted_text"] = extracted_text
            evaluation["message"] = "成功从攻击后的图像中提取文本"

            # 如果是文本操纵攻击，比较提取的文本和修改后的文本
            if attack_result.get("attack_type") == "text_manipulation" and attack_result.get("modified_text"):
                if extracted_text == attack_result["modified_text"]:
                    evaluation["integrity_preserved"] = True
                    evaluation["message"] += "，文本操纵攻击成功"
                else:
                    evaluation["message"] += "，但文本与预期的修改不符"

            # 如果是密钥攻击，尝试使用攻击后的密钥提取
            elif attack_result.get("attack_type") == "key_attack" and attack_result.get("attacked_key"):
                try:
                    attacked_key = base64.b64decode(attack_result["attacked_key"])
                    attacked_extracted_text = self.steg_handler.extract_text_from_image(
                        attack_result["output_path"], attacked_key
                    )
                    if attacked_extracted_text:
                        evaluation["message"] += f"，使用攻击后的密钥也能提取文本"
                except Exception:
                    evaluation["message"] += "，使用攻击后的密钥无法提取文本"

        except Exception as e:
            evaluation["extraction_success"] = False
            evaluation["message"] = f"无法从攻击后的图像中提取文本: {str(e)}"

    except Exception as e:
        evaluation["message"] = f"评估失败: {str(e)}"
        logging.error(f"评估失败: {str(e)}")

    return evaluation
```

### 安全性结论

通过实际测试，本系统的隐写加密方案表现出以下安全特性：

- **物理攻击抵抗**：JPEG压缩、随机裁剪、高斯噪声和隐写分析等物理攻击会有效破坏隐写信息，使其无法提取，这反而证明了系统不会在图像被修改后泄露信息。
- **密钥依赖性**：只有文本操纵攻击和密钥攻击在特定条件下可能提取部分信息，这证明了系统的安全性主要依赖于密钥保护，而非隐写算法本身的隐蔽性。
- **双重加密有效性**：即使在LSB数据被部分破坏的情况下，双层加密机制也能防止部分信息泄露，体现了"深度防御"的安全设计理念。

## 故障排除

如果您在运行系统时遇到问题：

1. **依赖问题**：尝试手动安装依赖
   ```bash
   pip install -r requirements.txt
   ```

2. **权限问题**：确保有权限创建和修改目录和文件

3. **端口冲突**：如果8000端口被占用，可以修改脚本中的端口号

## 许可证

本项目基于MIT许可证开源