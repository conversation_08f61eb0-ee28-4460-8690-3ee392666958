import os
import random
import base64
import hashlib
import shutil
import numpy as np
from PIL import Image
from typing import Tuple, Dict, Any, Optional
import logging
from steganography import SteganographyHandler

class AttackTester:
    """图像隐写攻击测试类"""

    def __init__(self, steg_handler: SteganographyHandler = None):
        """初始化攻击测试器"""
        self.steg_handler = steg_handler if steg_handler else SteganographyHandler()

    def jpeg_compression_attack(self, image_path: str, output_path: str, quality: int = 75) -> str:
        """
        JPEG压缩攻击 - 通过有损压缩破坏LSB中的隐写信息

        参数:
            image_path: 输入图像路径
            output_path: 输出图像路径
            quality: JPEG压缩质量 (1-100)

        返回:
            输出图像路径
        """
        try:
            # 读取图像
            img = Image.open(image_path)

            # 保存为JPEG格式（有损压缩）
            jpeg_path = output_path.replace('.png', '.jpg')
            img.save(jpeg_path, 'JPEG', quality=quality)

            # 重新读取JPEG图像
            compressed_img = Image.open(jpeg_path)

            # 保存为PNG格式（用于后续处理）
            compressed_img.save(output_path)

            # 删除临时JPEG文件
            if os.path.exists(jpeg_path):
                os.remove(jpeg_path)

            return output_path
        except Exception as e:
            logging.error(f"JPEG压缩攻击失败: {str(e)}")
            raise ValueError(f"JPEG压缩攻击失败: {str(e)}")

    def random_crop_attack(self, image_path: str, output_path: str, crop_percentage: int = 10) -> str:
        """
        随机裁剪攻击 - 随机裁剪图像的一部分，破坏部分隐写信息

        参数:
            image_path: 输入图像路径
            output_path: 输出图像路径
            crop_percentage: 裁剪百分比

        返回:
            输出图像路径
        """
        try:
            # 读取图像
            img = Image.open(image_path)
            width, height = img.size

            # 计算裁剪区域
            crop_pixels_x = int(width * crop_percentage / 100)
            crop_pixels_y = int(height * crop_percentage / 100)

            # 随机选择裁剪起点
            start_x = random.randint(0, crop_pixels_x)
            start_y = random.randint(0, crop_pixels_y)

            # 裁剪图像
            cropped_img = img.crop((start_x, start_y, width, height))

            # 创建新图像并粘贴裁剪后的图像
            new_img = Image.new(img.mode, (width, height), (255, 255, 255))
            new_img.paste(cropped_img, (0, 0))

            # 保存结果
            new_img.save(output_path)

            return output_path
        except Exception as e:
            logging.error(f"随机裁剪攻击失败: {str(e)}")
            raise ValueError(f"随机裁剪攻击失败: {str(e)}")

    def gaussian_noise_attack(self, image_path: str, output_path: str, mean: float = 0, sigma: float = 10) -> str:
        """
        高斯噪声攻击 - 向图像添加高斯噪声，干扰LSB中的隐写信息

        参数:
            image_path: 输入图像路径
            output_path: 输出图像路径
            mean: 高斯噪声的均值
            sigma: 高斯噪声的标准差

        返回:
            输出图像路径
        """
        try:
            # 读取图像
            img = Image.open(image_path)
            img_array = np.array(img)

            # 生成高斯噪声
            noise = np.random.normal(mean, sigma, img_array.shape).astype(np.int16)

            # 添加噪声
            noisy_img_array = img_array.astype(np.int16) + noise

            # 裁剪值到有效范围
            noisy_img_array = np.clip(noisy_img_array, 0, 255).astype(np.uint8)

            # 保存结果
            noisy_img = Image.fromarray(noisy_img_array)
            noisy_img.save(output_path)

            return output_path
        except Exception as e:
            logging.error(f"高斯噪声攻击失败: {str(e)}")
            raise ValueError(f"高斯噪声攻击失败: {str(e)}")

    def steganalysis_attack(self, image_path: str, output_path: str, intensity: int = 1) -> str:
        """
        图像隐写分析攻击 - 模拟隐写分析攻击，通过修改LSB位来破坏隐写信息

        参数:
            image_path: 输入图像路径
            output_path: 输出图像路径
            intensity: 攻击强度 (1-5)，影响修改的LSB位数量

        返回:
            输出图像路径
        """
        try:
            # 读取图像
            img = Image.open(image_path)
            img_array = np.array(img)

            # 根据强度计算修改比例
            modify_ratio = intensity * 0.1  # 10% - 50%

            # 随机选择像素进行LSB修改
            height, width, channels = img_array.shape
            total_pixels = height * width * channels
            pixels_to_modify = int(total_pixels * modify_ratio)

            for _ in range(pixels_to_modify):
                # 随机选择像素位置
                h = random.randint(0, height - 1)
                w = random.randint(0, width - 1)
                c = random.randint(0, channels - 1)

                # 修改LSB
                img_array[h, w, c] = img_array[h, w, c] ^ 1  # 翻转最低位

            # 保存结果
            modified_img = Image.fromarray(img_array)
            modified_img.save(output_path)

            return output_path
        except Exception as e:
            logging.error(f"图像隐写分析攻击失败: {str(e)}")
            raise ValueError(f"图像隐写分析攻击失败: {str(e)}")

    def text_manipulation_attack(self, image_path: str, output_path: str, original_key: bytes,
                                 manipulation_type: str = 'random', params: Dict[str, Any] = None) -> Tuple[str, Optional[str]]:
        """
        文本操纵攻击 - 尝试修改隐写在图像中的文本

        参数:
            image_path: 输入图像路径
            output_path: 输出图像路径
            original_key: 原始密钥
            manipulation_type: 操纵类型 ('random', 'append', 'truncate')

        返回:
            (输出图像路径, 修改后的文本)
        """
        try:
            # 确保输入图像存在
            if not os.path.exists(image_path):
                logging.error(f"输入图像不存在: {image_path}")
                return image_path, None

            # 确保输出目录存在
            output_dir = os.path.dirname(output_path)
            if not os.path.exists(output_dir):
                os.makedirs(output_dir, exist_ok=True)

            # 首先尝试提取原始文本
            try:
                original_text = self.steg_handler.extract_text_from_image(image_path, original_key)
                logging.info(f"成功提取原始文本: {original_text[:30]}...")
            except Exception as e:
                logging.error(f"无法提取原始文本: {str(e)}")
                # 复制原始图像到输出路径，以便后续处理
                shutil.copy(image_path, output_path)
                return output_path, None

            # 获取自定义文本参数
            if params is None:
                params = {}
            custom_text = params.get('custom_text', '')

            # 根据操纵类型修改文本
            if manipulation_type == 'custom' and custom_text:
                # 使用自定义文本完全替换原始文本
                modified_text = custom_text
                logging.info(f"使用自定义文本: {custom_text}")
            elif manipulation_type == 'append':
                # 在文本末尾添加内容
                append_text = custom_text if custom_text and manipulation_type == 'custom' else " [HACKED]"
                modified_text = original_text + append_text
            elif manipulation_type == 'truncate':
                # 截断文本
                if len(original_text) > 10:
                    modified_text = original_text[:len(original_text)//2]
                else:
                    modified_text = original_text[:-1]
            else:  # random
                # 随机修改文本中的一个字符
                if len(original_text) > 0:
                    char_pos = random.randint(0, len(original_text) - 1)
                    char_list = list(original_text)
                    char_list[char_pos] = chr(ord(char_list[char_pos]) + 1)
                    modified_text = ''.join(char_list)
                else:
                    modified_text = "HACKED"

            logging.info(f"修改后的文本: {modified_text[:30]}...")

            # 先复制原始图像到临时文件
            temp_image_path = output_path + ".temp"
            shutil.copy(image_path, temp_image_path)

            # 使用相同的密钥重新嵌入修改后的文本
            try:
                self.steg_handler.embed_text_in_image(temp_image_path, modified_text, original_key, output_path)

                # 验证输出文件是否存在
                if not os.path.exists(output_path):
                    logging.error(f"输出文件未创建: {output_path}")
                    return image_path, modified_text

                # 验证是否可以从输出文件中提取文本
                try:
                    test_extract = self.steg_handler.extract_text_from_image(output_path, original_key)
                    logging.info(f"验证提取成功: {test_extract[:30]}...")
                except Exception as e:
                    logging.error(f"验证提取失败: {str(e)}")
            except Exception as e:
                logging.error(f"嵌入修改后文本失败: {str(e)}")
                # 如果嵌入失败，使用原始图像
                shutil.copy(image_path, output_path)

            # 清理临时文件
            if os.path.exists(temp_image_path):
                try:
                    os.remove(temp_image_path)
                except:
                    pass

            return output_path, modified_text
        except Exception as e:
            logging.error(f"文本操纵攻击失败: {str(e)}")
            # 确保即使失败也返回一个有效的输出路径
            if not os.path.exists(output_path):
                shutil.copy(image_path, output_path)
            return output_path, None

    def key_attack(self, image_path: str, output_path: str, original_key: bytes,
                   attack_type: str = 'bit_flip') -> Tuple[str, bytes]:
        """
        密钥攻击 - 尝试修改或破解密钥

        参数:
            image_path: 输入图像路径
            output_path: 输出图像路径
            original_key: 原始密钥
            attack_type: 攻击类型 ('bit_flip', 'brute_force', 'similar_key')

        返回:
            (输出图像路径, 攻击后的密钥)
        """
        try:
            # 复制原始图像到输出路径
            img = Image.open(image_path)
            img.save(output_path)

            # 根据攻击类型生成攻击密钥
            if attack_type == 'bit_flip':
                # 随机翻转密钥中的一个位
                key_array = bytearray(original_key)
                bit_pos = random.randint(0, len(key_array) * 8 - 1)
                byte_pos = bit_pos // 8
                bit_offset = bit_pos % 8
                key_array[byte_pos] ^= (1 << bit_offset)
                attacked_key = bytes(key_array)

            elif attack_type == 'similar_key':
                # 生成一个与原始密钥相似的密钥
                key_array = bytearray(original_key)
                # 修改最后几个字节
                for i in range(1, 4):
                    if len(key_array) - i >= 0:
                        key_array[len(key_array) - i] = random.randint(0, 255)
                attacked_key = bytes(key_array)

            elif attack_type == 'brute_force':
                # 模拟暴力破解 - 实际上只是随机生成一个新密钥
                # 注意：真正的暴力破解需要尝试大量密钥，这里只是演示
                attacked_key = os.urandom(len(original_key))

            else:
                raise ValueError(f"不支持的密钥攻击类型: {attack_type}")

            return output_path, attacked_key
        except Exception as e:
            logging.error(f"密钥攻击失败: {str(e)}")
            raise ValueError(f"密钥攻击失败: {str(e)}")

    def run_attack(self, attack_type: str, image_path: str, output_path: str,
                   params: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        运行指定类型的攻击

        参数:
            attack_type: 攻击类型
            image_path: 输入图像路径
            output_path: 输出图像路径
            params: 攻击参数

        返回:
            包含攻击结果的字典
        """
        if params is None:
            params = {}

        result = {
            "attack_type": attack_type,
            "success": False,
            "output_path": None,
            "message": "",
            "modified_text": None,
            "attacked_key": None
        }

        try:
            if attack_type == "jpeg_compression":
                quality = params.get("quality", 75)
                result["output_path"] = self.jpeg_compression_attack(image_path, output_path, quality)
                result["message"] = f"JPEG压缩攻击完成 (质量: {quality})"
                result["success"] = True

            elif attack_type == "random_crop":
                crop_percentage = params.get("crop_percentage", 10)
                result["output_path"] = self.random_crop_attack(image_path, output_path, crop_percentage)
                result["message"] = f"随机裁剪攻击完成 (裁剪比例: {crop_percentage}%)"
                result["success"] = True

            elif attack_type == "gaussian_noise":
                mean = params.get("mean", 0)
                sigma = params.get("sigma", 10)
                result["output_path"] = self.gaussian_noise_attack(image_path, output_path, mean, sigma)
                result["message"] = f"高斯噪声攻击完成 (均值: {mean}, 标准差: {sigma})"
                result["success"] = True

            elif attack_type == "steganalysis":
                intensity = params.get("intensity", 3)
                result["output_path"] = self.steganalysis_attack(image_path, output_path, intensity)
                result["message"] = f"图像隐写分析攻击完成 (强度: {intensity})"
                result["success"] = True

            elif attack_type == "text_manipulation":
                original_key = params.get("key")
                if not original_key:
                    raise ValueError("文本操纵攻击需要提供原始密钥")

                manipulation_type = params.get("manipulation_type", "random")
                custom_text = params.get("custom_text", "")

                # 记录参数
                logging.info(f"文本操纵攻击参数: 类型={manipulation_type}, 自定义文本={custom_text}")

                output_path, modified_text = self.text_manipulation_attack(
                    image_path, output_path, original_key, manipulation_type, params
                )

                if output_path and os.path.exists(output_path):
                    result["output_path"] = output_path
                    result["modified_text"] = modified_text
                    result["message"] = f"文本操纵攻击完成 (类型: {manipulation_type})"
                    if manipulation_type == 'custom' and custom_text:
                        result["message"] += f", 自定义文本: {custom_text[:20]}..."
                    result["success"] = True
                else:
                    logging.error(f"文本操纵攻击失败: 输出文件不存在 {output_path}")
                    result["message"] = "文本操纵攻击失败: 无法创建输出文件"

            elif attack_type == "key_attack":
                original_key = params.get("key")
                if not original_key:
                    raise ValueError("密钥攻击需要提供原始密钥")

                attack_type = params.get("key_attack_type", "bit_flip")
                output_path, attacked_key = self.key_attack(
                    image_path, output_path, original_key, attack_type
                )
                result["output_path"] = output_path
                result["attacked_key"] = base64.b64encode(attacked_key).decode()
                result["message"] = f"密钥攻击完成 (类型: {attack_type})"
                result["success"] = True

            else:
                raise ValueError(f"不支持的攻击类型: {attack_type}")

        except Exception as e:
            result["message"] = f"攻击失败: {str(e)}"
            logging.error(f"攻击失败: {str(e)}")

        return result

    def evaluate_attack(self, attack_result: Dict[str, Any], original_key: bytes) -> Dict[str, Any]:
        """
        评估攻击效果

        参数:
            attack_result: 攻击结果字典
            original_key: 原始密钥

        返回:
            包含评估结果的字典
        """
        evaluation = {
            "extraction_success": False,
            "original_text": None,
            "extracted_text": None,
            "integrity_preserved": False,
            "message": ""
        }

        try:
            # 如果攻击不成功，直接返回
            if not attack_result.get("success", False) or not attack_result.get("output_path"):
                evaluation["message"] = "攻击未成功完成，无法评估"
                return evaluation

            # 尝试使用原始密钥从攻击后的图像中提取文本
            try:
                extracted_text = self.steg_handler.extract_text_from_image(
                    attack_result["output_path"], original_key
                )
                evaluation["extraction_success"] = True
                evaluation["extracted_text"] = extracted_text
                evaluation["message"] = "成功从攻击后的图像中提取文本"

                # 如果是文本操纵攻击，比较提取的文本和修改后的文本
                if attack_result.get("attack_type") == "text_manipulation" and attack_result.get("modified_text"):
                    if extracted_text == attack_result["modified_text"]:
                        evaluation["integrity_preserved"] = True
                        evaluation["message"] += "，文本操纵攻击成功"
                    else:
                        evaluation["message"] += "，但文本与预期的修改不符"

                # 如果是密钥攻击，尝试使用攻击后的密钥提取
                elif attack_result.get("attack_type") == "key_attack" and attack_result.get("attacked_key"):
                    try:
                        attacked_key = base64.b64decode(attack_result["attacked_key"])
                        attacked_extracted_text = self.steg_handler.extract_text_from_image(
                            attack_result["output_path"], attacked_key
                        )
                        if attacked_extracted_text:
                            evaluation["message"] += f"，使用攻击后的密钥也能提取文本: {attacked_extracted_text[:20]}..."
                    except Exception:
                        evaluation["message"] += "，使用攻击后的密钥无法提取文本"

            except Exception as e:
                evaluation["extraction_success"] = False
                evaluation["message"] = f"无法从攻击后的图像中提取文本: {str(e)}"

        except Exception as e:
            evaluation["message"] = f"评估失败: {str(e)}"
            logging.error(f"评估失败: {str(e)}")

        return evaluation
