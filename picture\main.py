from fastapi import FastAPI, File, UploadFile, Form, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import FileResponse, JSONResponse
import uvicorn
import os
import shutil
import logging
import json
from datetime import datetime
from steganography import <PERSON>eganography<PERSON>andler
from attack_tests import AttackTester
import base64
from Crypto.Protocol.KDF import PBKDF2
from Crypto.Random import get_random_bytes
import uuid

app = FastAPI(title="图像隐写加密系统")
steg_handler = SteganographyHandler()
attack_tester = AttackTester(steg_handler)

# 测试统计数据存储
test_statistics = {
    "total_tests": 0,
    "tests_by_type": {},
    "success_rate": {},
    "test_history": []
}

# 配置模板
templates = Jinja2Templates(directory="templates")

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 配置静态文件
app.mount("/static", StaticFiles(directory="static"), name="static")

# 添加favicon路由
@app.get('/favicon.ico')
async def favicon():
    return FileResponse('static/favicon.ico')

# 创建必要的目录
os.makedirs("static", exist_ok=True)
os.makedirs("static/js", exist_ok=True)
os.makedirs("static/css", exist_ok=True)
os.makedirs("temp/uploads", exist_ok=True)
os.makedirs("temp/results", exist_ok=True)

@app.get("/")
async def root(request: Request):
    """返回主页"""
    return templates.TemplateResponse("index.html", {"request": request})

@app.get("/attack-test")
async def attack_test_page(request: Request):
    """返回攻击测试页面"""
    return templates.TemplateResponse("attack_test.html", {"request": request})

def clean_temp_files():
    """清理超过1小时的临时文件"""
    current_time = datetime.now()
    for dir_path in ['temp/uploads', 'temp/results']:
        for filename in os.listdir(dir_path):
            file_path = os.path.join(dir_path, filename)
            file_modified = datetime.fromtimestamp(os.path.getmtime(file_path))
            if (current_time - file_modified).total_seconds() > 3600:
                os.remove(file_path)

@app.post("/api/encrypt")
async def encrypt_image(
    image: UploadFile = File(...),
    text: str = Form(...),
    use_custom_key: bool = Form(False),
    custom_key: str = Form(None)
):
    """加密文本并隐写到图像中"""
    try:
        clean_temp_files()

        # 保存上传的图像
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        input_path = f"temp/uploads/input_{timestamp}_{image.filename}"
        with open(input_path, "wb") as buffer:
            shutil.copyfileobj(image.file, buffer)

        # 处理密钥
        if use_custom_key and custom_key:
            try:
                # 使用自定义密钥
                if len(custom_key) == 1:
                    # 处理单个字符的特殊情况
                    decoded = custom_key.encode('utf-8')
                else:
                    # 处理多字符的Base64格式
                    missing_padding = len(custom_key) % 4
                    if missing_padding:
                        custom_key += '=' * (4 - missing_padding)

                    # 验证Base64格式
                    if not all(c in 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=' for c in custom_key):
                        raise ValueError("密钥包含无效的Base64字符")

                    # 解码
                    decoded = base64.b64decode(custom_key)

                # 生成最终密钥
                salt = get_random_bytes(16)
                key = PBKDF2(decoded, salt, dkLen=32, count=100000)

                # 记录调试信息
                print(f"成功生成密钥: {base64.b64encode(key).decode()}")

            except Exception as e:
                logging.error(f"密钥处理失败: {str(e)}")
                logging.error(f"输入密钥: {custom_key}")
                raise HTTPException(status_code=400, detail=f"无效的自定义密钥: {str(e)}")
        else:
            # 生成新的32字节密钥
            key = steg_handler.generate_key()

        # 处理输出文件
        output_filename = f"encrypted_{timestamp}_{image.filename}"
        output_path = f"temp/results/{output_filename}"

        # 执行加密和隐写
        steg_handler.embed_text_in_image(input_path, text, key, output_path)

        return JSONResponse({
            "status": "success",
            "message": "加密成功",
            "key": base64.b64encode(key).decode(),
            "download_url": f"/api/download/{output_filename}"
        })

    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.post("/api/decrypt")
async def decrypt_image(
    image: UploadFile = File(...),
    key: str = Form(...)
):
    """从图像中提取并解密文本"""
    try:
        clean_temp_files()

        # 保存上传的图像
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        input_path = f"temp/uploads/decrypt_{timestamp}_{image.filename}"
        with open(input_path, "wb") as buffer:
            shutil.copyfileobj(image.file, buffer)

        # 解码密钥
        try:
            key_bytes = base64.b64decode(key)
            if len(key_bytes) != 32:
                raise ValueError("密钥长度必须为32字节")
        except Exception as e:
            raise HTTPException(status_code=400, detail="密钥错误，请检查后重试！")

        # 提取并解密文本
        decrypted_text = steg_handler.extract_text_from_image(input_path, key_bytes)

        return JSONResponse({
            "status": "success",
            "message": "解密成功",
            "text": decrypted_text
        })

    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.get("/api/download/{filename}")
async def download_file(filename: str):
    """下载处理后的图像"""
    file_path = f"temp/results/{filename}"
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="文件不存在")
    return FileResponse(file_path, filename=filename)

@app.post("/api/attack-test")
async def attack_test(
    image: UploadFile = File(...),
    attack_type: str = Form(...),
    key: str = Form(...),
    params: str = Form("{}")
):
    """对隐写图像进行攻击测试"""
    try:
        clean_temp_files()

        # 确保临时目录存在
        os.makedirs("temp/uploads", exist_ok=True)
        os.makedirs("temp/results", exist_ok=True)

        # 保存上传的图像
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        input_path = f"temp/uploads/attack_input_{timestamp}_{image.filename}"
        with open(input_path, "wb") as buffer:
            shutil.copyfileobj(image.file, buffer)

        # 解析参数
        attack_params = json.loads(params)

        # 解码密钥
        try:
            key_bytes = base64.b64decode(key)
            if len(key_bytes) != 32:
                raise ValueError("密钥长度必须为32字节")
        except Exception as e:
            raise HTTPException(status_code=400, detail="密钥错误，请检查后重试！")

        # 添加密钥到攻击参数
        attack_params["key"] = key_bytes

        # 输出路径
        output_filename = f"attacked_{attack_type}_{timestamp}_{image.filename}"
        output_path = f"temp/results/{output_filename}"

        # 执行攻击
        attack_result = attack_tester.run_attack(attack_type, input_path, output_path, attack_params)

        # 检查输出文件是否存在
        if attack_result.get("success") and attack_result.get("output_path"):
            if not os.path.exists(attack_result["output_path"]):
                logging.error(f"攻击后的文件不存在: {attack_result['output_path']}")
                # 如果输出文件不存在，复制原始文件
                shutil.copy(input_path, output_path)
                attack_result["output_path"] = output_path

        # 评估攻击效果
        evaluation = attack_tester.evaluate_attack(attack_result, key_bytes)

        # 合并结果
        result = {
            "status": "success",
            "attack_type": attack_type,
            "attack_result": attack_result,
            "evaluation": evaluation,
            "download_url": f"/api/download/{output_filename}" if os.path.exists(output_path) else None
        }

        # 更新测试统计数据
        test_id = str(uuid.uuid4())
        test_time = datetime.now().isoformat()

        # 判断测试结果是否成功
        test_success = attack_result.get("success", False)
        extraction_success = evaluation.get("extraction_success", False)

        # 判断测试是否证明了系统的鲁棒性
        # 如果是文本操纵或密钥攻击，提取成功表示系统不够安全
        # 如果是其他攻击，提取失败表示系统具有鲁棒性
        if attack_type in ["text_manipulation", "key_attack"]:
            # 对于文本操纵和密钥攻击，攻击成功但无法提取原始文本表示系统安全
            robustness_proven = test_success and not extraction_success
        else:
            # 对于其他攻击类型，攻击成功但无法提取原始文本表示系统具有鲁棒性
            robustness_proven = test_success and not extraction_success

        # 记录测试数据
        test_record = {
            "id": test_id,
            "timestamp": test_time,
            "attack_type": attack_type,
            "parameters": {k: v for k, v in attack_params.items() if k != "key"},  # 不记录密钥
            "success": test_success,
            "extraction_success": extraction_success,
            "robustness_proven": robustness_proven,
            "message": evaluation.get("message", ""),
            "image_filename": image.filename
        }

        # 更新统计数据
        test_statistics["total_tests"] += 1
        test_statistics["test_history"].append(test_record)

        # 更新按类型统计
        if attack_type not in test_statistics["tests_by_type"]:
            test_statistics["tests_by_type"][attack_type] = {
                "total": 0,
                "success": 0,
                "robustness_proven": 0
            }

        test_statistics["tests_by_type"][attack_type]["total"] += 1
        if test_success:
            test_statistics["tests_by_type"][attack_type]["success"] += 1
        if robustness_proven:
            test_statistics["tests_by_type"][attack_type]["robustness_proven"] += 1

        # 计算成功率
        for attack_type, stats in test_statistics["tests_by_type"].items():
            if stats["total"] > 0:
                test_statistics["success_rate"][attack_type] = {
                    "attack_success_rate": stats["success"] / stats["total"] * 100,
                    "robustness_proven_rate": stats["robustness_proven"] / stats["total"] * 100
                }

        # 添加测试ID到结果中，以便前端可以引用
        result["test_id"] = test_id

        return JSONResponse(result)

    except Exception as e:
        logging.error(f"攻击测试失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@app.get("/api/attack-types")
async def get_attack_types():
    """获取支持的攻击类型"""
    attack_types = [
        {
            "id": "jpeg_compression",
            "name": "JPEG压缩攻击",
            "description": "通过有损压缩破坏LSB中的隐写信息",
            "params": [
                {"name": "quality", "type": "number", "min": 1, "max": 100, "default": 75, "description": "JPEG压缩质量"}
            ]
        },
        {
            "id": "random_crop",
            "name": "随机裁剪攻击",
            "description": "随机裁剪图像的一部分，破坏部分隐写信息",
            "params": [
                {"name": "crop_percentage", "type": "number", "min": 1, "max": 50, "default": 10, "description": "裁剪百分比"}
            ]
        },
        {
            "id": "gaussian_noise",
            "name": "高斯噪声攻击",
            "description": "向图像添加高斯噪声，干扰LSB中的隐写信息",
            "params": [
                {"name": "mean", "type": "number", "default": 0, "description": "噪声均值"},
                {"name": "sigma", "type": "number", "min": 1, "max": 50, "default": 10, "description": "噪声标准差"}
            ]
        },
        {
            "id": "steganalysis",
            "name": "图像隐写分析攻击",
            "description": "模拟隐写分析攻击，通过修改LSB位来破坏隐写信息",
            "params": [
                {"name": "intensity", "type": "number", "min": 1, "max": 5, "default": 3, "description": "攻击强度"}
            ]
        },
        {
            "id": "text_manipulation",
            "name": "文本操纵攻击",
            "description": "尝试修改隐写在图像中的文本",
            "params": [
                {
                    "name": "manipulation_type",
                    "type": "select",
                    "options": [
                        {"value": "random", "label": "随机修改"},
                        {"value": "append", "label": "追加内容"},
                        {"value": "truncate", "label": "截断内容"},
                        {"value": "custom", "label": "自定义文本"}
                    ],
                    "default": "random",
                    "description": "操纵类型"
                },
                {
                    "name": "custom_text",
                    "type": "text",
                    "default": "",
                    "description": "自定义文本内容（仅当选择'自定义文本'时有效）"
                }
            ]
        },
        {
            "id": "key_attack",
            "name": "密钥攻击",
            "description": "尝试修改或破解密钥",
            "params": [
                {
                    "name": "key_attack_type",
                    "type": "select",
                    "options": [
                        {"value": "bit_flip", "label": "位翻转攻击"},
                        {"value": "similar_key", "label": "相似密钥攻击"},
                        {"value": "brute_force", "label": "暴力破解模拟"}
                    ],
                    "default": "bit_flip",
                    "description": "密钥攻击类型"
                }
            ]
        }
    ]

    return JSONResponse(attack_types)

@app.get("/api/test-statistics")
async def get_test_statistics():
    """获取测试统计数据"""
    return JSONResponse({
        "total_tests": test_statistics["total_tests"],
        "tests_by_type": test_statistics["tests_by_type"],
        "success_rate": test_statistics["success_rate"],
        # 只返回最近的20条测试记录，避免数据过大
        "recent_tests": test_statistics["test_history"][-20:] if test_statistics["test_history"] else []
    })

@app.get("/api/test-statistics/clear")
async def clear_test_statistics():
    """清空测试统计数据"""
    global test_statistics
    test_statistics = {
        "total_tests": 0,
        "tests_by_type": {},
        "success_rate": {},
        "test_history": []
    }
    return JSONResponse({"status": "success", "message": "测试统计数据已清空"})

if __name__ == "__main__":
    uvicorn.run(app, host="localhost", port=8000)
