import http.server
import socketserver
import os
import cgi
import json
import urllib.parse
import base64
import io
import shutil
from datetime import datetime
import traceback
import uuid
from steganography import SteganographyHandler
from attack_tests import AttackTester
from Crypto.Protocol.KDF import PBKDF2
from Crypto.Random import get_random_bytes

# 创建steganography处理器
steg_handler = SteganographyHandler()
attack_tester = AttackTester(steg_handler)

# 测试统计数据存储
test_statistics = {
    "total_tests": 0,
    "tests_by_type": {},
    "success_rate": {},
    "test_history": []
}

# 创建必要的目录
os.makedirs("static", exist_ok=True)
os.makedirs("static/js", exist_ok=True)
os.makedirs("static/css", exist_ok=True)
os.makedirs("temp/uploads", exist_ok=True)
os.makedirs("temp/results", exist_ok=True)

def clean_temp_files():
    """清理超过1小时的临时文件"""
    current_time = datetime.now()
    for dir_path in ['temp/uploads', 'temp/results']:
        for filename in os.listdir(dir_path):
            file_path = os.path.join(dir_path, filename)
            file_modified = datetime.fromtimestamp(os.path.getmtime(file_path))
            if (current_time - file_modified).total_seconds() > 3600:
                os.remove(file_path)

class StegServer(http.server.SimpleHTTPRequestHandler):
    # 添加一个简单的模板处理函数，用于替换模板中的url_for调用
    def process_template(self, template_content):
        # 替换url_for('static', path='...')为静态文件的实际路径
        import re
        pattern = r"{{ url_for\('static', path='(.*?)'\) }}"
        return re.sub(pattern, r"/static\1", template_content)

    def do_GET(self):
        # 处理根路径请求 - 显示主页
        if self.path == "/" or self.path == "":
            self.send_response(200)
            self.send_header("Content-type", "text/html")
            self.end_headers()
            # 打开并发送index.html
            try:
                with open("templates/index.html", "r", encoding="utf-8") as file:
                    content = file.read()
                    # 处理模板中的url_for调用
                    processed_content = self.process_template(content)
                    self.wfile.write(processed_content.encode('utf-8'))
            except:
                # 如果原始文件不存在，尝试使用简化版本
                try:
                    with open("templates/index_simple.html", "r", encoding="utf-8") as file:
                        content = file.read()
                        # 处理模板中的url_for调用
                        processed_content = self.process_template(content)
                        self.wfile.write(processed_content.encode('utf-8'))
                except:
                    # 如果两个文件都不存在，提供一个基本的HTML
                    self.wfile.write(b"""
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <title>Image Steganography System</title>
                        <style>
                            body { font-family: Arial; margin: 20px; }
                            .form-group { margin-bottom: 15px; }
                            label { display: block; margin-bottom: 5px; }
                        </style>
                    </head>
                    <body>
                        <h1>Image Steganography System - Simple Version</h1>

                        <h2>Encryption Mode</h2>
                        <form action="/api/encrypt" method="post" enctype="multipart/form-data">
                            <div class="form-group">
                                <label>Select Image:</label>
                                <input type="file" name="image" required>
                            </div>
                            <div class="form-group">
                                <label>Text to Encrypt:</label>
                                <textarea name="text" rows="4" required></textarea>
                            </div>
                            <div class="form-group">
                                <label>
                                    <input type="checkbox" name="use_custom_key"> Use Custom Key
                                </label>
                            </div>
                            <div class="form-group">
                                <label>Custom Key:</label>
                                <input type="text" name="custom_key">
                            </div>
                            <button type="submit">Encrypt File</button>
                        </form>

                        <h2>Decryption Mode</h2>
                        <form action="/api/decrypt" method="post" enctype="multipart/form-data">
                            <div class="form-group">
                                <label>Select Encrypted Image:</label>
                                <input type="file" name="image" required>
                            </div>
                            <div class="form-group">
                                <label>Key:</label>
                                <input type="text" name="key" required>
                            </div>
                            <button type="submit">Decrypt File</button>
                        </form>

                        <p><a href="/attack-test">Go to Attack Test Page</a></p>
                    </body>
                    </html>
                    """)

        # 处理攻击测试页面请求
        elif self.path == "/attack-test":
            self.send_response(200)
            self.send_header("Content-type", "text/html")
            self.end_headers()
            # 打开并发送attack_test.html
            try:
                with open("templates/attack_test.html", "r", encoding="utf-8") as file:
                    content = file.read()
                    # 处理模板中的url_for调用
                    processed_content = self.process_template(content)
                    self.wfile.write(processed_content.encode('utf-8'))
            except:
                # 如果文件不存在，提供一个基本的HTML
                self.wfile.write(b"""
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Steganography Attack Test</title>
                    <style>
                        body { font-family: Arial; margin: 20px; }
                        .form-group { margin-bottom: 15px; }
                        label { display: block; margin-bottom: 5px; }
                    </style>
                </head>
                <body>
                    <h1>Steganography Attack Test - Simple Version</h1>

                    <form action="/api/attack-test" method="post" enctype="multipart/form-data">
                        <div class="form-group">
                            <label>Select Encrypted Image:</label>
                            <input type="file" name="image" required>
                        </div>
                        <div class="form-group">
                            <label>Key:</label>
                            <input type="text" name="key" required>
                        </div>
                        <div class="form-group">
                            <label>Attack Type:</label>
                            <select name="attack_type" required>
                                <option value="jpeg_compression">JPEG Compression</option>
                                <option value="random_crop">Random Crop</option>
                                <option value="gaussian_noise">Gaussian Noise</option>
                                <option value="steganalysis">Steganalysis</option>
                                <option value="text_manipulation">Text Manipulation</option>
                                <option value="key_attack">Key Attack</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Parameters (JSON):</label>
                            <textarea name="params" rows="4">{"quality": 75}</textarea>
                        </div>
                        <button type="submit">Run Attack Test</button>
                    </form>

                    <p><a href="/">Back to Main Page</a></p>
                </body>
                </html>
                """)

        # 处理下载请求
        elif self.path.startswith("/api/download/"):
            filename = self.path[14:]  # 去掉"/api/download/"
            file_path = f"temp/results/{filename}"

            if os.path.exists(file_path):
                self.send_response(200)
                self.send_header("Content-type", "image/png")
                self.send_header("Content-Disposition", f"attachment; filename={filename}")
                self.end_headers()

                with open(file_path, "rb") as file:
                    self.wfile.write(file.read())
            else:
                self.send_response(404)
                self.send_header("Content-type", "text/plain")
                self.end_headers()
                self.wfile.write(b"File not found")

        # 处理获取攻击类型请求
        elif self.path == "/api/attack-types":
            attack_types = [
                {
                    "id": "jpeg_compression",
                    "name": "JPEG压缩攻击",
                    "description": "通过有损压缩破坏LSB中的隐写信息",
                    "params": [
                        {"name": "quality", "type": "number", "min": 1, "max": 100, "default": 75, "description": "JPEG压缩质量"}
                    ]
                },
                {
                    "id": "random_crop",
                    "name": "随机裁剪攻击",
                    "description": "随机裁剪图像的一部分，破坏部分隐写信息",
                    "params": [
                        {"name": "crop_percentage", "type": "number", "min": 1, "max": 50, "default": 10, "description": "裁剪百分比"}
                    ]
                },
                {
                    "id": "gaussian_noise",
                    "name": "高斯噪声攻击",
                    "description": "向图像添加高斯噪声，干扰LSB中的隐写信息",
                    "params": [
                        {"name": "mean", "type": "number", "default": 0, "description": "噪声均值"},
                        {"name": "sigma", "type": "number", "min": 1, "max": 50, "default": 10, "description": "噪声标准差"}
                    ]
                },
                {
                    "id": "steganalysis",
                    "name": "图像隐写分析攻击",
                    "description": "模拟隐写分析攻击，通过修改LSB位来破坏隐写信息",
                    "params": [
                        {"name": "intensity", "type": "number", "min": 1, "max": 5, "default": 3, "description": "攻击强度"}
                    ]
                },
                {
                    "id": "text_manipulation",
                    "name": "文本操纵攻击",
                    "description": "尝试修改隐写在图像中的文本",
                    "params": [
                        {
                            "name": "manipulation_type",
                            "type": "select",
                            "options": [
                                {"value": "random", "label": "随机修改"},
                                {"value": "append", "label": "追加内容"},
                                {"value": "truncate", "label": "截断内容"},
                                {"value": "custom", "label": "自定义文本"}
                            ],
                            "default": "random",
                            "description": "操纵类型"
                        },
                        {
                            "name": "custom_text",
                            "type": "text",
                            "default": "",
                            "description": "自定义文本内容（仅当选择'自定义文本'时有效）"
                        }
                    ]
                },
                {
                    "id": "key_attack",
                    "name": "密钥攻击",
                    "description": "尝试修改或破解密钥",
                    "params": [
                        {
                            "name": "key_attack_type",
                            "type": "select",
                            "options": [
                                {"value": "bit_flip", "label": "位翻转攻击"},
                                {"value": "similar_key", "label": "相似密钥攻击"},
                                {"value": "brute_force", "label": "暴力破解模拟"}
                            ],
                            "default": "bit_flip",
                            "description": "密钥攻击类型"
                        }
                    ]
                }
            ]

            self.send_response(200)
            self.send_header("Content-type", "application/json")
            self.end_headers()
            self.wfile.write(json.dumps(attack_types).encode())

        # 处理获取测试统计数据请求
        elif self.path == "/api/test-statistics":
            self.send_response(200)
            self.send_header("Content-type", "application/json")
            self.end_headers()

            response = {
                "total_tests": test_statistics["total_tests"],
                "tests_by_type": test_statistics["tests_by_type"],
                "success_rate": test_statistics["success_rate"],
                # 只返回最近的20条测试记录，避免数据过大
                "recent_tests": test_statistics["test_history"][-20:] if test_statistics["test_history"] else []
            }

            self.wfile.write(json.dumps(response).encode())

        # 处理清空测试统计数据请求
        elif self.path == "/api/test-statistics/clear":
            # 重置测试统计数据
            test_statistics.clear()
            test_statistics.update({
                "total_tests": 0,
                "tests_by_type": {},
                "success_rate": {},
                "test_history": []
            })

            self.send_response(200)
            self.send_header("Content-type", "application/json")
            self.end_headers()

            response = {
                "status": "success",
                "message": "测试统计数据已清空"
            }

            self.wfile.write(json.dumps(response).encode())

        # 处理静态文件
        elif self.path.startswith("/static/"):
            try:
                super().do_GET()  # 使用内置的文件处理
            except:
                self.send_response(404)
                self.send_header("Content-type", "text/plain")
                self.end_headers()
                self.wfile.write(b"File not found")

        # 处理其他请求
        else:
            try:
                super().do_GET()  # 尝试使用内置的文件处理
            except:
                self.send_response(404)
                self.send_header("Content-type", "text/plain")
                self.end_headers()
                self.wfile.write(b"Not found")

    def do_POST(self):
        try:
            # 处理加密请求
            if self.path == "/api/encrypt":
                # 解析表单数据
                form = cgi.FieldStorage(
                    fp=self.rfile,
                    headers=self.headers,
                    environ={'REQUEST_METHOD': 'POST',
                             'CONTENT_TYPE': self.headers['Content-Type']}
                )

                # 获取表单字段
                if 'image' not in form or 'text' not in form:
                    self.send_error_response(400, "Missing required fields")
                    return

                # 获取图片文件
                file_item = form['image']
                text = form.getvalue('text')
                use_custom_key = form.getvalue('use_custom_key', '').lower() == 'on'
                custom_key = form.getvalue('custom_key', '')

                if not text:
                    self.send_error_response(400, "No encryption text provided")
                    return

                # 保存上传的图像
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                input_path = f"temp/uploads/input_{timestamp}_{os.path.basename(file_item.filename)}"

                with open(input_path, 'wb') as f:
                    f.write(file_item.file.read())

                # 处理密钥
                if use_custom_key and custom_key:
                    try:
                        # 使用自定义密钥
                        if len(custom_key) == 1:
                            # 处理单个字符的特殊情况
                            decoded = custom_key.encode('utf-8')
                        else:
                            # 处理多字符的Base64格式
                            missing_padding = len(custom_key) % 4
                            if missing_padding:
                                custom_key += '=' * (4 - missing_padding)

                            # 验证Base64格式
                            if not all(c in 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=' for c in custom_key):
                                raise ValueError("Invalid Base64 characters in key")

                            # 解码
                            decoded = base64.b64decode(custom_key)

                        # 生成最终密钥
                        salt = get_random_bytes(16)
                        key = PBKDF2(decoded, salt, dkLen=32, count=100000)

                    except Exception as e:
                        self.send_error_response(400, f"Invalid custom key: {str(e)}")
                        return
                else:
                    # 生成新的32字节密钥
                    key = steg_handler.generate_key()

                # 处理输出文件
                output_filename = f"encrypted_{timestamp}_{os.path.basename(file_item.filename)}"
                output_path = f"temp/results/{output_filename}"

                # 执行加密和隐写
                steg_handler.embed_text_in_image(input_path, text, key, output_path)

                # 返回成功响应
                self.send_response(200)
                self.send_header("Content-type", "application/json")
                self.end_headers()

                response = {
                    "status": "success",
                    "message": "Encryption successful",
                    "key": base64.b64encode(key).decode(),
                    "download_url": f"/api/download/{output_filename}"
                }

                self.wfile.write(json.dumps(response).encode())

            # 处理解密请求
            elif self.path == "/api/decrypt":
                # 解析表单数据
                form = cgi.FieldStorage(
                    fp=self.rfile,
                    headers=self.headers,
                    environ={'REQUEST_METHOD': 'POST',
                             'CONTENT_TYPE': self.headers['Content-Type']}
                )

                # 获取表单字段
                if 'image' not in form or 'key' not in form:
                    self.send_error_response(400, "Missing required fields")
                    return

                # 获取图片文件和密钥
                file_item = form['image']
                key = form.getvalue('key', '')

                if not key:
                    self.send_error_response(400, "No key provided")
                    return

                # 保存上传的图像
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                input_path = f"temp/uploads/decrypt_{timestamp}_{os.path.basename(file_item.filename)}"

                with open(input_path, 'wb') as f:
                    f.write(file_item.file.read())

                # 解码密钥
                try:
                    key_bytes = base64.b64decode(key)
                    if len(key_bytes) != 32:
                        raise ValueError("Key must be 32 bytes long")
                except Exception as e:
                    self.send_error_response(400, "Invalid key, please check and try again!")
                    return

                # 提取并解密文本
                decrypted_text = steg_handler.extract_text_from_image(input_path, key_bytes)

                # 返回成功响应
                self.send_response(200)
                self.send_header("Content-type", "application/json")
                self.end_headers()

                response = {
                    "status": "success",
                    "message": "Decryption successful",
                    "text": decrypted_text
                }

                self.wfile.write(json.dumps(response).encode())

            # 处理攻击测试请求
            elif self.path == "/api/attack-test":
                # 解析表单数据
                form = cgi.FieldStorage(
                    fp=self.rfile,
                    headers=self.headers,
                    environ={'REQUEST_METHOD': 'POST',
                             'CONTENT_TYPE': self.headers['Content-Type']}
                )

                # 获取表单字段
                if 'image' not in form or 'key' not in form or 'attack_type' not in form:
                    self.send_error_response(400, "Missing required fields")
                    return

                # 获取图片文件和其他参数
                file_item = form['image']
                key = form.getvalue('key', '')
                attack_type = form.getvalue('attack_type', '')
                params = form.getvalue('params', '{}')

                if not key or not attack_type:
                    self.send_error_response(400, "Missing required parameters")
                    return

                # 保存上传的图像
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                input_path = f"temp/uploads/attack_input_{timestamp}_{os.path.basename(file_item.filename)}"

                with open(input_path, 'wb') as f:
                    f.write(file_item.file.read())

                # 解析参数
                try:
                    attack_params = json.loads(params)
                except:
                    attack_params = {}

                # 解码密钥
                try:
                    key_bytes = base64.b64decode(key)
                    if len(key_bytes) != 32:
                        raise ValueError("Key must be 32 bytes long")
                except Exception as e:
                    self.send_error_response(400, "Invalid key, please check and try again!")
                    return

                # 添加密钥到攻击参数
                attack_params["key"] = key_bytes

                # 输出路径
                output_filename = f"attacked_{attack_type}_{timestamp}_{os.path.basename(file_item.filename)}"
                output_path = f"temp/results/{output_filename}"

                # 执行攻击
                try:
                    attack_result = attack_tester.run_attack(attack_type, input_path, output_path, attack_params)

                    # 检查输出文件是否存在
                    if attack_result.get("success") and attack_result.get("output_path"):
                        if not os.path.exists(attack_result["output_path"]):
                            print(f"攻击后的文件不存在: {attack_result['output_path']}")
                            # 如果输出文件不存在，复制原始文件
                            shutil.copy(input_path, output_path)
                            attack_result["output_path"] = output_path

                    # 评估攻击效果
                    evaluation = attack_tester.evaluate_attack(attack_result, key_bytes)

                    # 合并结果
                    result = {
                        "status": "success",
                        "attack_type": attack_type,
                        "attack_result": attack_result,
                        "evaluation": evaluation,
                        "download_url": f"/api/download/{output_filename}" if os.path.exists(output_path) else None
                    }

                    # 更新测试统计数据
                    test_id = str(uuid.uuid4())
                    test_time = datetime.now().isoformat()

                    # 判断测试结果是否成功
                    test_success = attack_result.get("success", False)
                    extraction_success = evaluation.get("extraction_success", False)

                    # 判断测试是否证明了系统的鲁棒性
                    if attack_type in ["text_manipulation", "key_attack"]:
                        # 对于文本操纵和密钥攻击，攻击成功但无法提取原始文本表示系统安全
                        robustness_proven = test_success and not extraction_success
                    else:
                        # 对于其他攻击类型，攻击成功但无法提取原始文本表示系统具有鲁棒性
                        robustness_proven = test_success and not extraction_success

                    # 记录测试数据
                    test_record = {
                        "id": test_id,
                        "timestamp": test_time,
                        "attack_type": attack_type,
                        "parameters": {k: v for k, v in attack_params.items() if k != "key"},  # 不记录密钥
                        "success": test_success,
                        "extraction_success": extraction_success,
                        "robustness_proven": robustness_proven,
                        "message": evaluation.get("message", ""),
                        "image_filename": file_item.filename
                    }

                    # 更新统计数据
                    test_statistics["total_tests"] += 1
                    test_statistics["test_history"].append(test_record)

                    # 更新按类型统计
                    if attack_type not in test_statistics["tests_by_type"]:
                        test_statistics["tests_by_type"][attack_type] = {
                            "total": 0,
                            "success": 0,
                            "robustness_proven": 0
                        }

                    test_statistics["tests_by_type"][attack_type]["total"] += 1
                    if test_success:
                        test_statistics["tests_by_type"][attack_type]["success"] += 1
                    if robustness_proven:
                        test_statistics["tests_by_type"][attack_type]["robustness_proven"] += 1

                    # 计算成功率
                    for attack_type, stats in test_statistics["tests_by_type"].items():
                        if stats["total"] > 0:
                            test_statistics["success_rate"][attack_type] = {
                                "attack_success_rate": stats["success"] / stats["total"] * 100,
                                "robustness_proven_rate": stats["robustness_proven"] / stats["total"] * 100
                            }

                    # 添加测试ID到结果中，以便前端可以引用
                    result["test_id"] = test_id

                    # 返回成功响应
                    self.send_response(200)
                    self.send_header("Content-type", "application/json")
                    self.end_headers()
                    self.wfile.write(json.dumps(result).encode())

                except Exception as e:
                    traceback.print_exc()
                    self.send_error_response(400, f"Attack test failed: {str(e)}")
                    return

            # 处理其他API请求
            elif self.path in ["/api/attack-types", "/api/test-statistics", "/api/test-statistics/clear"]:
                # 这些API端点已经在GET请求处理中实现，POST请求不需要重复处理
                pass

            else:
                self.send_error_response(404, "API endpoint not found")

        except Exception as e:
            print(f"Error processing request: {str(e)}")
            traceback.print_exc()
            self.send_error_response(500, f"Server error: {str(e)}")

    def send_error_response(self, status_code, error_message):
        """发送错误响应"""
        self.send_response(status_code)
        self.send_header("Content-type", "application/json")
        self.end_headers()

        response = {
            "status": "error",
            "detail": error_message
        }

        self.wfile.write(json.dumps(response).encode())

def run(port=8000):
    # 清理临时文件
    clean_temp_files()

    # 设置服务器
    handler = StegServer
    httpd = socketserver.TCPServer(("", port), handler)

    print(f"Server started at http://localhost:{port}")
    print("Press Ctrl+C to stop the server")

    try:
        # 启动服务器
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("Server stopped")
    finally:
        httpd.server_close()

if __name__ == "__main__":
    run(8000)