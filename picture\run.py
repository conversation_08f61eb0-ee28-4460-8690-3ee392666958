import sys
import os
import importlib.util
import subprocess

def check_module_installed(module_name):
    """检查是否安装了特定模块"""
    return importlib.util.find_spec(module_name) is not None

def run_with_fastapi():
    """使用原始FastAPI版本运行"""
    print("使用FastAPI运行...")
    os.system("python main.py")

def run_with_flask():
    """使用Flask版本运行"""
    print("使用Flask运行...")
    os.system("python main_flask.py")

def run_with_simple_server():
    """使用内置http.server版本运行"""
    print("使用内置HTTP服务器运行...")
    os.system("python main_simple.py")

def install_dependencies():
    """尝试安装依赖"""
    print("正在安装依赖...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("依赖安装成功！")
        return True
    except Exception as e:
        print(f"依赖安装失败: {str(e)}")
        return False

def create_directories():
    """创建必要的目录结构"""
    directories = [
        "static",
        "static/js",
        "static/css",
        "temp",
        "temp/uploads",
        "temp/results"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
    
    print("目录结构创建完成！")

def main():
    # 显示欢迎信息
    print("=" * 50)
    print("图像隐写加密系统启动脚本")
    print("=" * 50)
    
    # 创建必要的目录
    create_directories()
    
    # 检查依赖
    if check_module_installed("fastapi") and check_module_installed("uvicorn"):
        # 如果安装了FastAPI，使用原始版本
        run_with_fastapi()
    elif check_module_installed("flask"):
        # 如果安装了Flask，使用Flask版本
        run_with_flask()
    else:
        # 尝试使用内置http.server
        print("未检测到FastAPI或Flask。")
        choice = input("您想要: [1] 尝试安装依赖 [2] 使用内置HTTP服务器 (输入数字): ")
        
        if choice == "1":
            if install_dependencies():
                # 依赖安装成功，尝试运行FastAPI版本
                run_with_fastapi()
            else:
                # 依赖安装失败，使用内置http.server
                print("将使用内置HTTP服务器...")
                run_with_simple_server()
        else:
            # 直接使用内置http.server
            run_with_simple_server()

if __name__ == "__main__":
    main() 