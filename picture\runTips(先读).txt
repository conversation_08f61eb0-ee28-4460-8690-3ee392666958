1 安装依赖（可选）
pip install -r requirements.txt

2 启动程序（多种方式）

方式1：使用智能启动脚本（推荐）
python run.py
* 此脚本会自动检测您的环境并选择最合适的运行方式

方式2：直接选择对应版本
# 如果已安装FastAPI
python main.py

# 如果已安装Flask
python main_flask.py

# 无需任何额外依赖
python main_simple.py

3 打开 http://localhost:8000 

加密图片 ： 选择图片 -> 输入加密文本、使用自定义秘钥（可选） -> 加密文件 -> 保存秘钥和图片

解密图片 ： 选择图片 -> 输入秘钥 -> 解密文件

注意事项：
- 请妥善保管密钥，一旦丢失将无法恢复隐藏的信息
- 加密图像必须保存为PNG格式，以避免信息丢失
- 隐写容量取决于图像大小，较大的图像可以隐藏更多文本

