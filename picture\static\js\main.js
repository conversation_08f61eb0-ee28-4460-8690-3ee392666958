document.addEventListener('DOMContentLoaded', function() {
    // 创建加载提示
    const loadingModal = document.createElement('div');
    loadingModal.innerHTML = `
        <div id="loadingModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden flex items-center justify-center">
            <div class="bg-white p-6 rounded-lg shadow-xl flex items-center space-x-4">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="text-gray-700" id="loadingText">正在处理中...</p>
            </div>
        </div>
    `;
    document.body.appendChild(loadingModal);

    // 创建结果模态框
    const modal = document.createElement('div');
    modal.innerHTML = `
        <div id="keyModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden flex items-center justify-center">
            <div class="bg-white p-6 rounded-lg shadow-xl max-w-md w-full m-4">
                <h3 class="text-lg font-semibold mb-4">加密成功</h3>
                <div class="mb-4">
                    <p class="text-sm text-gray-600 mb-2">请保存好以下密钥，解密时需要使用：</p>
                    <div class="flex">
                        <input type="text" id="keyDisplay" readonly 
                            class="flex-1 p-2 border rounded-l text-sm font-mono bg-gray-50" />
                        <button id="copyKey" 
                            class="bg-blue-500 text-white px-4 rounded-r hover:bg-blue-600 transition duration-200">
                            复制
                        </button>
                    </div>
                </div>
                <div class="flex justify-end space-x-4">
                    <button id="downloadNow" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 transition duration-200">
                        立即下载
                    </button>
                    <button id="closeModal" class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600 transition duration-200">
                        关闭
                    </button>
                </div>
            </div>
        </div>
    `;
    document.body.appendChild(modal);
    
    // 创建错误模态框
    const errorModal = document.createElement('div');
    errorModal.innerHTML = `
        <div id="errorModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden flex items-center justify-center">
            <div class="bg-white p-6 rounded-lg shadow-xl max-w-md w-full m-4">
                <h3 class="text-lg font-semibold mb-4 text-red-600">错误</h3>
                <div class="mb-4">
                    <p id="errorMessage" class="text-sm text-gray-600"></p>
                </div>
                <div class="flex justify-end">
                    <button id="closeErrorModal" class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600 transition duration-200">
                        确定
                    </button>
                </div>
            </div>
        </div>
    `;
    document.body.appendChild(errorModal);

    // 控制自定义密钥输入框的显示/隐藏
    const useCustomKeyCheckbox = document.getElementById('useCustomKey');
    const customKeyInput = document.getElementById('customKeyInput');
    
    useCustomKeyCheckbox?.addEventListener('change', function() {
        customKeyInput.style.display = this.checked ? 'block' : 'none';
    });

    // 加密表单提交
    document.getElementById('encryptForm')?.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const loadingModal = document.getElementById('loadingModal');
        const loadingText = document.getElementById('loadingText');
        loadingModal.classList.remove('hidden');
        loadingText.textContent = '正在加密图片...';
        
        const formData = new FormData();
        formData.append('image', document.getElementById('encryptImage').files[0]);
        formData.append('text', document.getElementById('encryptText').value);
        formData.append('use_custom_key', useCustomKeyCheckbox.checked);
        
        if (useCustomKeyCheckbox.checked) {
            formData.append('custom_key', document.getElementById('customKey').value);
        }
        
        try {
            const response = await fetch('/api/encrypt', {
                method: 'POST',
                body: formData
            });
            
            const data = await response.json();
            loadingModal.classList.add('hidden');
            
            if (response.ok) {
                const modal = document.getElementById('keyModal');
                const keyDisplay = document.getElementById('keyDisplay');
                const downloadUrl = data.download_url;
                
                keyDisplay.value = data.key;
                modal.classList.remove('hidden');

                document.getElementById('copyKey').addEventListener('click', function() {
                    keyDisplay.select();
                    document.execCommand('copy');
                    this.textContent = '已复制';
                    setTimeout(() => this.textContent = '复制', 2000);
                });

                document.getElementById('downloadNow').addEventListener('click', function() {
                    window.location.href = downloadUrl;
                    modal.classList.add('hidden');
                });

                document.getElementById('closeModal').addEventListener('click', function() {
                    modal.classList.add('hidden');
                });
            } else {
                const errorModal = document.getElementById('errorModal');
                const errorMessage = document.getElementById('errorMessage');
                errorMessage.textContent = '错误：' + data.detail;
                errorModal.classList.remove('hidden');
                
                document.getElementById('closeErrorModal').addEventListener('click', function() {
                    errorModal.classList.add('hidden');
                });
            }
        } catch (error) {
            loadingModal.classList.add('hidden');
            
            if (error.response && error.response.status === 400) {
                const errorModal = document.getElementById('errorModal');
                const errorMessage = document.getElementById('errorMessage');
                errorMessage.textContent = '密钥错误：' + (error.response.data.detail || '无效的密钥格式，请检查后重试');
                errorModal.classList.remove('hidden');
                
                document.getElementById('closeErrorModal').addEventListener('click', function() {
                    errorModal.classList.add('hidden');
                });
            } else {
                const errorModal = document.getElementById('errorModal');
                const errorMessage = document.getElementById('errorMessage');
                errorMessage.textContent = '发生错误：' + error.message;
                errorModal.classList.remove('hidden');
                
                document.getElementById('closeErrorModal').addEventListener('click', function() {
                    errorModal.classList.add('hidden');
                });
            }
        }
    });

    // 解密表单提交
    document.getElementById('decryptForm')?.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const loadingModal = document.getElementById('loadingModal');
        const loadingText = document.getElementById('loadingText');
        loadingModal.classList.remove('hidden');
        loadingText.textContent = '正在解密图片...';
        
        const formData = new FormData();
        formData.append('image', document.getElementById('decryptImage').files[0]);
        formData.append('key', document.getElementById('decryptKey').value);
        
        try {
            const response = await fetch('/api/decrypt', {
                method: 'POST',
                body: formData
            });
            
            const data = await response.json();
            loadingModal.classList.add('hidden');
            
            if (response.ok) {
                document.getElementById('decryptedResult').style.display = 'block';
                document.getElementById('decryptedText').textContent = data.text;
                
                // 平滑滚动到结果区域
                document.getElementById('decryptedResult').scrollIntoView({ 
                    behavior: 'smooth',
                    block: 'start'
                });
            } else {
                const errorModal = document.getElementById('errorModal');
                const errorMessage = document.getElementById('errorMessage');
                errorMessage.textContent = '错误：' + data.detail;
                errorModal.classList.remove('hidden');
                
                document.getElementById('closeErrorModal').addEventListener('click', function() {
                    errorModal.classList.add('hidden');
                });
            }
        } catch (error) {
            loadingModal.classList.add('hidden');
            
            if (error.response && error.response.status === 400) {
                const errorModal = document.getElementById('errorModal');
                const errorMessage = document.getElementById('errorMessage');
                errorMessage.textContent = '密钥错误：' + (error.response.data.detail || '无效的密钥格式，请检查后重试');
                errorModal.classList.remove('hidden');
                
                document.getElementById('closeErrorModal').addEventListener('click', function() {
                    errorModal.classList.add('hidden');
                });
            } else {
                const errorModal = document.getElementById('errorModal');
                const errorMessage = document.getElementById('errorMessage');
                errorMessage.textContent = '发生错误：' + error.message;
                errorModal.classList.remove('hidden');
                
                document.getElementById('closeErrorModal').addEventListener('click', function() {
                    errorModal.classList.add('hidden');
                });
            }
        }
    });
});