import numpy as np
from PIL import Image
from Crypto.Cipher import AES, Cha<PERSON><PERSON><PERSON>
from Crypto.Protocol.KDF import PB<PERSON>DF2
from Crypto.Util.Padding import pad, unpad
from Crypto.Hash import SHA256
from Crypto.Signature import pkcs1_15
from Crypto.Random import get_random_bytes
import hashlib
import zlib
from typing import Tuple, Union
import logging


class SteganographyHandler:
    def __init__(self):
        self.key_size = 32  # AES-256密钥大小
        self.salt_size = 16
        self.magic_header = b'STEG'
        self.iterations = 100000  # PBKDF2迭代次数
        self.derived_key_size = 64  # 派生密钥大小 (32字节用于ChaCha20，32字节用于AES)

    def generate_key(self) -> bytes:
        """生成随机密钥"""
        return get_random_bytes(self.key_size)

    def derive_key(self, password: Union[str, bytes], salt: bytes) -> bytes:
        """使用PBKDF2派生密钥"""
        if isinstance(password, str):
            password = password.encode()
        return PBKDF2(password, salt, dkLen=self.derived_key_size, count=self.iterations)

    def compress_data(self, data: bytes) -> bytes:
        """压缩数据"""
        return zlib.compress(data, level=9)

    def decompress_data(self, compressed_data: bytes) -> bytes:
        """解压数据"""
        return zlib.decompress(compressed_data)

    def encrypt_text(self, text: str, key: bytes) -> Tuple[bytes, bytes]:
        """
        使用AES-256-GCM和ChaCha20进行双重加密
        返回: (encrypted_data, salt)
        """
        try:
            # 生成随机salt
            salt = get_random_bytes(self.salt_size)
            
            # 确保key是32字节
            if len(key) != self.key_size:
                raise ValueError("密钥无效")
            
            # 使用PBKDF2派生密钥
            derived_key = self.derive_key(key, salt)
            
            # 准备数据
            data = self.magic_header + hashlib.blake2b(text.encode(), digest_size=16).digest() + text.encode()
            compressed_data = self.compress_data(data)
            
            # 分割派生密钥
            chacha_key = derived_key[:32]
            aes_key = derived_key[32:64]  # 使用额外的32字节作为AES密钥
            
            # 第一层加密: ChaCha20
            chacha = ChaCha20.new(key=chacha_key)
            chacha_encrypted = chacha.nonce + chacha.encrypt(compressed_data)
            
            # 第二层加密: AES-256-GCM
            aes = AES.new(aes_key, AES.MODE_GCM)
            ciphertext, tag = aes.encrypt_and_digest(chacha_encrypted)
            
            # 组合所有数据
            encrypted_data = aes.nonce + tag + ciphertext
            
            return encrypted_data, salt
            
        except Exception as e:
            logging.error(f"加密失败: {str(e)}")
            raise ValueError(f"加密失败: {str(e)}")

    def decrypt_text(self, encrypted_data: bytes, key: bytes, salt: bytes) -> str:
        """解密文本"""
        try:
            # 确保key是32字节
            if len(key) != self.key_size:
                raise ValueError("密钥无效")
            
            # 验证输入数据
            if len(encrypted_data) < 48:  # 16(nonce) + 16(tag) + 最小密文长度
                raise ValueError("加密数据无效")
            
            if len(salt) != self.salt_size:
                raise ValueError("参数无效")
            
            # 派生密钥
            derived_key = self.derive_key(key, salt)
            
            # 分割派生密钥
            chacha_key = derived_key[:32]
            aes_key = derived_key[32:64]
            
            try:
                # 分离AES组件
                if len(encrypted_data) < 32:
                    raise ValueError("加密数据无效")
                
                aes_nonce = encrypted_data[:16]
                tag = encrypted_data[16:32]
                ciphertext = encrypted_data[32:]
                
                # AES-256-GCM解密
                aes = AES.new(aes_key, AES.MODE_GCM, nonce=aes_nonce)
                try:
                    chacha_encrypted = aes.decrypt_and_verify(ciphertext, tag)
                except ValueError as e:
                    raise ValueError("解密失败")
                
                # ChaCha20解密
                if len(chacha_encrypted) < 8:
                    raise ValueError("数据无效")
                    
                chacha_nonce = chacha_encrypted[:8]
                chacha_data = chacha_encrypted[8:]
                chacha = ChaCha20.new(key=chacha_key, nonce=chacha_nonce)
                decrypted_compressed = chacha.decrypt(chacha_data)
                
                # 解压数据
                try:
                    decrypted_data = self.decompress_data(decrypted_compressed)
                except Exception as e:
                    raise ValueError("解压失败")
                
                # 验证魔数
                if not decrypted_data.startswith(self.magic_header):
                    raise ValueError("数据或密钥无效")
                
                # 验证数据完整性
                checksum = decrypted_data[4:20]
                text = decrypted_data[20:]
                if checksum != hashlib.blake2b(text, digest_size=16).digest():
                    raise ValueError("数据验证失败")
                
                return text.decode()
                
            except Exception as e:
                raise ValueError("解密失败")
            
        except Exception as e:
            logging.error(f"解密失败: {str(e)}", exc_info=True)
            raise ValueError(f"解密失败: {str(e)}")

    def embed_text_in_image(self, image_path: str, text: str, key: bytes, output_path: str):
        """使用改进的LSB算法将加密文本嵌入图像"""
        try:
            # 加密文本
            encrypted_data, salt = self.encrypt_text(text, key)
            
            # 打开并处理图像
            img = Image.open(image_path)
            if img.mode != 'RGB':
                img = img.convert('RGB')
            pixels = np.array(img)
            
            # 计算可用容量
            available_bits = pixels.size * 2  # 每个颜色通道使用2个最低位
            required_bits = (len(encrypted_data) + len(salt)) * 8
            
            if required_bits > available_bits:
                raise ValueError(f"文本太长，图像最多可存储{available_bits//8}字节的加密数据")
            
            # 准备数据
            data = len(encrypted_data).to_bytes(4, 'big') + encrypted_data + salt
            binary_data = ''.join(format(byte, '08b') for byte in data)
            
            # 使用改进的LSB算法嵌入数据
            idx = 0
            for i in range(pixels.shape[0]):
                for j in range(pixels.shape[1]):
                    for k in range(3):  # RGB通道
                        if idx + 2 <= len(binary_data):
                            # 修改最低2位
                            pixels[i, j, k] = (pixels[i, j, k] & 0xFC) | int(binary_data[idx:idx+2], 2)
                            idx += 2
                        elif idx < len(binary_data):
                            # 处理最后一个不完整的字节
                            pixels[i, j, k] = (pixels[i, j, k] & 0xFE) | int(binary_data[idx], 2)
                            idx += 1
                        else:
                            break
            
            # 保存图像
            Image.fromarray(pixels).save(output_path, format='PNG', optimize=False)
            
        except Exception as e:
            logging.error(f"嵌入文本失败: {str(e)}")
            raise ValueError(f"嵌入文本失败: {str(e)}")

    def extract_text_from_image(self, image_path: str, key: bytes) -> str:
        """从图像中提取并解密文本"""
        try:
            # 打开图像
            img = Image.open(image_path)
            if img.mode != 'RGB':
                img = img.convert('RGB')
            pixels = np.array(img)
            
            # 提取二进制数据 - 只读取需要的数据
            binary_data = ''
            total_bytes_needed = 4  # 先只读取长度信息
            bytes_read = 0
            
            # 首先读取长度信息（32位）
            for i in range(pixels.shape[0]):
                if bytes_read >= total_bytes_needed:
                    break
                for j in range(pixels.shape[1]):
                    if bytes_read >= total_bytes_needed:
                        break
                    for k in range(3):
                        if bytes_read >= total_bytes_needed:
                            break
                        binary_data += format(pixels[i, j, k] & 0x03, '02b')
                        bytes_read += 0.25  # 每个像素点可以存储2位
            
            # 获取数据长度
            data_len = int(binary_data[:32], 2)
            if data_len <= 0 or data_len > (pixels.size * 2) // 8:
                raise ValueError("无效的数据长度")
            
            # 计算需要读取的总位数
            total_bits_needed = 32 + (data_len + self.salt_size) * 8
            binary_data = ''
            bits_read = 0
            
            # 读取实际数据
            for i in range(pixels.shape[0]):
                if bits_read >= total_bits_needed:
                    break
                for j in range(pixels.shape[1]):
                    if bits_read >= total_bits_needed:
                        break
                    for k in range(3):
                        if bits_read >= total_bits_needed:
                            break
                        binary_data += format(pixels[i, j, k] & 0x03, '02b')
                        bits_read += 2
            
            # 提取加密数据和salt
            encrypted_data = bytes(int(binary_data[i:i+8], 2) 
                                 for i in range(32, 32 + data_len * 8, 8))
            salt = bytes(int(binary_data[i:i+8], 2) 
                        for i in range(32 + data_len * 8, 32 + data_len * 8 + self.salt_size * 8, 8))
            
            # 验证数据长度
            if len(encrypted_data) != data_len or len(salt) != self.salt_size:
                raise ValueError("数据提取失败")
            
            # 解密数据
            try:
                return self.decrypt_text(encrypted_data, key, salt)
            except Exception as e:
                raise ValueError(f"解密失败: {str(e)}")
            
        except Exception as e:
            logging.error(f"提取文本失败: {str(e)}")
            raise ValueError(f"提取文本失败: {str(e)}")

def generate_signature(message, private_key):
    """生成消息的数字签名"""
    hash_obj = SHA256.new(message.encode())
    signature = pkcs1_15.new(private_key).sign(hash_obj)
    return signature

def verify_signature(message, signature, public_key):
    """验证消息的数字签名"""
    hash_obj = SHA256.new(message.encode())
    try:
        pkcs1_15.new(public_key).verify(hash_obj, signature)
        print("签名验证通过")
    except (ValueError, TypeError):
        print("签名验证失败")