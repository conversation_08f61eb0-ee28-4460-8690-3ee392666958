<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图像隐写攻击测试</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="{{ url_for('static', path='/css/style.css') }}" rel="stylesheet">
    <style>
        /* 基础动画和工具类 */
        .fixed {
            position: fixed;
        }

        .inset-0 {
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
        }

        .hidden {
            display: none;
        }

        .font-mono {
            font-family: monospace;
        }

        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }

        .animate-spin {
            animation: spin 1s linear infinite;
        }

        @keyframes pulse {
            0%, 100% {
                opacity: 1;
                transform: scale(1);
            }
            50% {
                opacity: 0.8;
                transform: scale(1.02);
            }
        }

        .animate-pulse {
            animation: pulse 1s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }

        /* 背景样式 */
        .page-background {
            background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
            overflow-y: auto;
            z-index: 0;
            padding-top: 60px; /* 为固定导航栏留出空间 */
        }

        /* 玻璃卡片效果 */
        .glass-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        /* 渐变文本 */
        .gradient-text {
            background: linear-gradient(90deg, #3b82f6, #1e40af);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            font-size: 2.5rem;
        }

        /* 表单样式 */
        .form-input {
            @apply w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
        }

        .form-label {
            @apply block text-sm font-medium text-gray-700 mb-1;
        }

        .btn-primary {
            @apply px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200;
        }

        .btn-secondary {
            @apply px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors duration-200;
        }

        /* 结果容器 */
        .result-container {
            @apply mt-4 p-4 border border-gray-200 rounded-md bg-white;
        }

        /* 成功和错误状态 */
        .success {
            @apply text-green-600 font-medium;
        }

        .error {
            @apply text-red-600 font-medium;
        }

        /* 图像预览 */
        .image-preview {
            max-width: 100%;
            max-height: 300px;
            @apply mt-2 rounded-md;
        }

        /* 加载遮罩 */
        .loading-overlay {
            @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50;
        }
    </style>
</head>
<body class="page-background">
    <!-- 固定在顶部的导航栏 -->
    <div class="fixed top-0 left-0 right-0 bg-blue-900 bg-opacity-80 backdrop-filter backdrop-blur-md z-50 shadow-md">
        <div class="container mx-auto px-4 py-3 flex justify-between items-center">
            <h2 class="text-white font-bold text-xl">图像隐写攻击测试</h2>
            <div class="flex items-center space-x-4">
                <a href="/" class="text-white hover:text-blue-200 transition-colors duration-300 flex items-center bg-blue-700 hover:bg-blue-800 px-4 py-2 rounded-lg shadow-sm">
                    <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                    </svg>
                    返回主页
                </a>
            </div>
        </div>
    </div>

    <div class="container mx-auto px-4 py-8">

        <h1 class="text-4xl font-bold text-center mb-8 text-white drop-shadow-lg tracking-wide">
            <span class="inline-flex items-center justify-center">
                <svg class="w-10 h-10 mr-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                </svg>
                <span class="inline-block transform hover:scale-105 transition-transform duration-300 border-b-2 border-blue-300 pb-1">
                    图像隐写攻击测试
                </span>
            </span>
        </h1>

    <div class="glass-card rounded-2xl p-8 mb-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <div>
                <h2 class="text-2xl font-semibold mb-2 text-gray-800 flex items-center">
                    <svg class="w-6 h-6 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                    <span class="bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent">
                        攻击测试
                    </span>
                </h2>
                <p class="text-gray-600">选择一个已加密的图像，并选择要执行的攻击类型。系统将执行攻击并评估其效果。</p>
            </div>
            <div class="mt-4 md:mt-0">
                <div class="bg-blue-50 p-3 rounded-lg border border-blue-100 max-w-md">
                    <div class="flex items-start">
                        <svg class="w-5 h-5 text-blue-500 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <p class="text-sm text-blue-700">
                            攻击测试可以评估隐写算法的抗攻击能力，测试结果将显示在下方。
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <form id="attackForm" class="space-y-6">
            <div class="bg-white p-6 rounded-xl shadow-sm border border-blue-100">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <!-- 第一列：图像选择 -->
                    <div class="md:col-span-1 space-y-2">
                        <div class="flex items-center mb-3">
                            <svg class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                            <label for="image" class="block text-sm font-medium text-gray-700">选择加密图像</label>
                        </div>

                        <div class="relative border-2 border-dashed border-gray-300 rounded-lg p-6 flex flex-col items-center justify-center bg-gray-50 hover:bg-gray-100 transition-colors duration-200">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                            </svg>
                            <div class="mt-2 text-center">
                                <p class="text-sm text-gray-600">点击或拖拽文件到此处</p>
                                <p class="text-xs text-gray-500 mt-1">支持PNG, JPG, GIF格式</p>
                            </div>
                            <input type="file" id="image" name="image" accept="image/*" required
                                class="absolute inset-0 w-full h-full opacity-0 cursor-pointer">
                        </div>

                        <div id="imagePreviewContainer" class="hidden mt-3">
                            <p class="text-xs text-gray-500 mb-1">图像预览:</p>
                            <div class="relative rounded-md overflow-hidden border border-gray-200">
                                <img id="imagePreview" class="w-full h-auto max-h-48 object-contain" src="" alt="图像预览">
                                <div class="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs py-1 px-2 text-center">
                                    已选择图像
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 第二列：密钥和攻击类型 -->
                    <div class="md:col-span-1 space-y-5">
                        <!-- 密钥输入区域 -->
                        <div class="bg-gradient-to-r from-blue-50 to-white p-4 rounded-xl shadow-sm border border-blue-100">
                            <div class="flex items-center mb-3">
                                <div class="bg-blue-600 rounded-full p-1.5 mr-3">
                                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
                                    </svg>
                                </div>
                                <label for="key" class="block text-base font-semibold text-gray-800">密钥</label>
                            </div>

                            <div class="relative rounded-lg shadow-sm">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <svg class="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                    </svg>
                                </div>
                                <input type="text" id="key" name="key" placeholder="输入用于加密的密钥" required
                                    class="pl-10 block w-full shadow-sm focus:ring-blue-500 focus:border-blue-500 border-gray-300 rounded-lg py-3 text-base font-medium">
                            </div>

                            <div class="mt-2 flex items-start">
                                <svg class="w-4 h-4 text-blue-500 mt-0.5 mr-1.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                <p class="text-sm text-blue-700">请输入加密图像时使用的密钥</p>
                            </div>
                        </div>

                        <!-- 攻击类型选择区域 -->
                        <div class="bg-gradient-to-r from-blue-50 to-white p-4 rounded-xl shadow-sm border border-blue-100">
                            <div class="flex items-center mb-3">
                                <div class="bg-blue-600 rounded-full p-1.5 mr-3">
                                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                                    </svg>
                                </div>
                                <label for="attackType" class="block text-base font-semibold text-gray-800">攻击类型</label>
                            </div>

                            <div class="relative">
                                <select id="attackType" name="attackType" required
                                    class="block w-full shadow-sm focus:ring-blue-500 focus:border-blue-500 border-gray-300 rounded-lg py-3 text-base font-medium appearance-none">
                                    <option value="">-- 选择攻击类型 --</option>
                                    <!-- 攻击类型将通过JavaScript动态加载 -->
                                </select>
                                <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-3 text-gray-700">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                    </svg>
                                </div>
                            </div>

                            <div class="mt-2 flex items-start">
                                <svg class="w-4 h-4 text-blue-500 mt-0.5 mr-1.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                <p class="text-sm text-blue-700">选择要执行的攻击测试类型</p>
                            </div>
                        </div>
                    </div>

                    <!-- 第三列：攻击参数和提交按钮 -->
                    <div id="attackParams" class="md:col-span-1 p-4 bg-gradient-to-r from-blue-50 to-white rounded-xl border border-blue-100 shadow-sm hidden">
                        <!-- 攻击参数将通过JavaScript动态加载 -->
                    </div>
                </div>

                <div class="mt-6 pt-5 border-t border-gray-200 flex justify-end">
                    <button type="submit" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg shadow-md text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 transform hover:scale-105">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                        执行攻击测试
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- 测试统计面板 -->
    <div id="statisticsContainer" class="glass-card rounded-2xl p-8 mb-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <div>
                <h2 class="text-2xl font-semibold mb-2 text-gray-800 flex items-center">
                    <svg class="w-6 h-6 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                    <span class="bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent">
                        测试统计
                    </span>
                </h2>
                <p class="text-gray-600">统计测试操作、结果和系统鲁棒性评估</p>
            </div>
            <div class="mt-4 md:mt-0 flex space-x-2">
                <button id="refreshStatistics" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200 flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                    刷新统计
                </button>
                <button id="clearStatistics" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors duration-200 flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                    清空统计
                </button>
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <!-- 总体统计卡片 -->
            <div class="bg-white p-5 rounded-xl shadow-sm border border-gray-100">
                <h3 class="text-lg font-medium mb-4 text-gray-700 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
                    </svg>
                    总体统计
                </h3>
                <div class="space-y-3">
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">总测试次数:</span>
                        <span id="totalTests" class="text-xl font-bold text-blue-600">0</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">攻击成功率:</span>
                        <span id="overallSuccessRate" class="text-xl font-bold text-green-600">0%</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">鲁棒性证明率:</span>
                        <span id="overallRobustnessRate" class="text-xl font-bold text-purple-600">0%</span>
                    </div>
                </div>
            </div>

            <!-- 攻击类型分布卡片 -->
            <div class="bg-white p-5 rounded-xl shadow-sm border border-gray-100">
                <h3 class="text-lg font-medium mb-4 text-gray-700 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z" />
                    </svg>
                    攻击类型分布
                </h3>
                <div id="attackTypeDistribution" class="h-40">
                    <!-- 这里将通过JavaScript添加图表 -->
                    <div class="flex items-center justify-center h-full text-gray-400">
                        <p>暂无数据</p>
                    </div>
                </div>
            </div>

            <!-- 鲁棒性评估卡片 -->
            <div class="bg-white p-5 rounded-xl shadow-sm border border-gray-100">
                <h3 class="text-lg font-medium mb-4 text-gray-700 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                    </svg>
                    鲁棒性评估
                </h3>
                <div id="robustnessChart" class="h-40">
                    <!-- 这里将通过JavaScript添加图表 -->
                    <div class="flex items-center justify-center h-full text-gray-400">
                        <p>暂无数据</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 最近测试记录表格 -->
        <div class="bg-white p-5 rounded-xl shadow-sm border border-gray-100">
            <h3 class="text-lg font-medium mb-4 text-gray-700 flex items-center">
                <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
                最近测试记录
            </h3>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">时间</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">攻击类型</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">攻击成功</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">提取成功</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">鲁棒性证明</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">评估消息</th>
                        </tr>
                    </thead>
                    <tbody id="testHistoryTable" class="bg-white divide-y divide-gray-200">
                        <!-- 测试记录将通过JavaScript动态添加 -->
                        <tr>
                            <td colspan="6" class="px-6 py-4 text-center text-gray-400">暂无测试记录</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div id="resultContainer" class="glass-card rounded-2xl p-8 hidden">
        <h2 class="text-2xl font-semibold mb-6 text-gray-800 flex items-center">
            <svg class="w-6 h-6 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
            <span class="bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent">
                攻击测试结果
            </span>
        </h2>

        <div class="grid md:grid-cols-2 gap-6 mb-8">
            <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
                <h3 class="text-lg font-medium mb-3 text-gray-700">原始加密图像</h3>
                <div class="flex justify-center">
                    <img id="originalImage" class="image-preview rounded-md border border-gray-200" src="" alt="原始加密图像">
                </div>
            </div>

            <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
                <h3 class="text-lg font-medium mb-3 text-gray-700">攻击后图像</h3>
                <div class="flex justify-center">
                    <img id="attackedImage" class="image-preview rounded-md border border-gray-200" src="" alt="攻击后图像">
                </div>
                <div class="mt-3 text-center">
                    <a id="downloadLink" href="#" target="_blank" class="text-blue-600 hover:text-blue-800 flex items-center justify-center">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                        </svg>
                        下载攻击后图像
                    </a>
                </div>
            </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-100 mb-6">
            <h3 class="text-lg font-medium mb-4 text-gray-700 flex items-center">
                <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                攻击信息
            </h3>
            <div class="space-y-2 text-gray-600">
                <p><span class="font-medium">攻击类型:</span> <span id="resultAttackType"></span></p>
                <p><span class="font-medium">攻击状态:</span> <span id="resultStatus"></span></p>
                <p><span class="font-medium">攻击消息:</span> <span id="resultMessage"></span></p>

                <div id="keyAttackResult" class="hidden mt-3 p-3 bg-gray-50 rounded-md">
                    <p><span class="font-medium">攻击后密钥:</span> <span id="attackedKey" class="font-mono text-sm"></span></p>
                </div>

                <div id="textManipulationResult" class="hidden mt-3 p-3 bg-gray-50 rounded-md">
                    <p><span class="font-medium">修改后文本:</span> <span id="modifiedText"></span></p>
                </div>
            </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
            <h3 class="text-lg font-medium mb-4 text-gray-700 flex items-center">
                <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                评估结果
            </h3>
            <div class="space-y-3 text-gray-600">
                <p><span class="font-medium">能否提取文本:</span> <span id="extractionSuccess"></span></p>
                <div>
                    <p class="font-medium mb-2">提取的文本:</p>
                    <div id="extractedText" class="font-mono text-sm bg-gray-50 p-4 rounded-md border border-gray-200 max-h-48 overflow-y-auto whitespace-pre-wrap"></div>
                </div>
                <p><span class="font-medium">评估消息:</span> <span id="evaluationMessage"></span></p>
            </div>
        </div>
    </div>

    <div id="loadingOverlay" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white p-6 rounded-lg shadow-xl flex items-center space-x-4">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="text-gray-700">正在处理中...</p>
        </div>
    </div>

    <!-- 引入Chart.js库 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <script>
        // 全局变量，用于存储图表实例
        let attackTypeChart = null;
        let robustnessChart = null;

        // 获取攻击类型列表
        async function loadAttackTypes() {
            try {
                // 显示加载中
                document.getElementById('loadingOverlay').classList.remove('hidden');

                const response = await fetch('/api/attack-types');
                if (!response.ok) {
                    throw new Error('获取攻击类型失败');
                }

                const attackTypes = await response.json();
                const attackTypeSelect = document.getElementById('attackType');

                // 清空现有选项
                attackTypeSelect.innerHTML = '<option value="">-- 选择攻击类型 --</option>';

                // 添加攻击类型选项
                attackTypes.forEach(type => {
                    const option = document.createElement('option');
                    option.value = type.id;
                    option.textContent = type.name;
                    option.dataset.description = type.description;
                    option.dataset.params = JSON.stringify(type.params);
                    attackTypeSelect.appendChild(option);
                });

                // 添加攻击类型变更事件
                attackTypeSelect.addEventListener('change', updateAttackParams);

                // 加载测试统计数据
                await loadTestStatistics();
            } catch (error) {
                console.error('加载攻击类型失败:', error);
                showErrorMessage('加载攻击类型失败: ' + error.message);
            } finally {
                // 隐藏加载中
                document.getElementById('loadingOverlay').classList.add('hidden');
            }
        }

        // 加载测试统计数据
        async function loadTestStatistics() {
            try {
                const response = await fetch('/api/test-statistics');
                if (!response.ok) {
                    throw new Error('获取测试统计数据失败');
                }

                const statistics = await response.json();
                updateStatisticsDisplay(statistics);
            } catch (error) {
                console.error('加载测试统计数据失败:', error);
                // 不显示错误消息，因为这可能是首次加载，没有统计数据是正常的
            }
        }

        // 显示错误消息
        function showErrorMessage(message) {
            // 创建错误提示
            const errorDiv = document.createElement('div');
            errorDiv.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            errorDiv.innerHTML = `
                <div class="bg-white p-6 rounded-lg shadow-xl max-w-md w-full">
                    <h3 class="text-lg font-semibold mb-4 text-red-600 flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                        </svg>
                        错误
                    </h3>
                    <p class="text-gray-700 mb-6">${message}</p>
                    <div class="flex justify-end">
                        <button class="btn-primary" onclick="this.parentNode.parentNode.parentNode.remove()">确定</button>
                    </div>
                </div>
            `;
            document.body.appendChild(errorDiv);
        }

        // 更新攻击参数表单
        function updateAttackParams() {
            const attackTypeSelect = document.getElementById('attackType');
            const attackParamsContainer = document.getElementById('attackParams');

            if (!attackTypeSelect.value) {
                attackParamsContainer.classList.add('hidden');
                attackParamsContainer.innerHTML = '';
                return;
            }

            const selectedOption = attackTypeSelect.options[attackTypeSelect.selectedIndex];
            const params = JSON.parse(selectedOption.dataset.params || '[]');

            if (params.length === 0) {
                attackParamsContainer.classList.add('hidden');
                attackParamsContainer.innerHTML = '';
                return;
            }

            // 创建参数表单
            let paramsHtml = `
                <div class="flex items-center mb-3">
                    <div class="bg-blue-600 rounded-full p-1.5 mr-3">
                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                    </div>
                    <h3 class="text-base font-semibold text-gray-800">攻击参数</h3>
                </div>

                <div class="bg-blue-50 p-3 rounded-lg mb-4 border border-blue-100">
                    <p class="text-sm text-blue-700">${selectedOption.dataset.description}</p>
                </div>
            `;

            params.forEach(param => {
                if (param.type === 'select') {
                    paramsHtml += `
                        <div class="mb-4">
                            <div class="flex items-center mb-2">
                                <div class="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                                <label for="${param.name}" class="block text-sm font-medium text-gray-800">${param.description}</label>
                            </div>
                            <div class="relative">
                                <select id="${param.name}" name="${param.name}"
                                    class="block w-full shadow-sm focus:ring-blue-500 focus:border-blue-500 border-gray-300 rounded-lg py-2.5 text-sm font-medium appearance-none">
                    `;

                    param.options.forEach(option => {
                        const selected = option.value === param.default ? 'selected' : '';
                        paramsHtml += `<option value="${option.value}" ${selected}>${option.label}</option>`;
                    });

                    paramsHtml += `
                                </select>
                                <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-3 text-gray-700">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                    </svg>
                                </div>
                            </div>
                        </div>
                    `;
                } else if (param.type === 'number') {
                    const min = param.min !== undefined ? `min="${param.min}"` : '';
                    const max = param.max !== undefined ? `max="${param.max}"` : '';

                    paramsHtml += `
                        <div class="mb-4">
                            <div class="flex items-center mb-2">
                                <div class="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                                <label for="${param.name}" class="block text-sm font-medium text-gray-800">${param.description}</label>
                            </div>
                            <div class="relative rounded-lg shadow-sm">
                                <input type="number" id="${param.name}" name="${param.name}"
                                       value="${param.default}" ${min} ${max}
                                       class="block w-full shadow-sm focus:ring-blue-500 focus:border-blue-500 border-gray-300 rounded-lg py-2.5 text-sm font-medium">
                                <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                                    <span class="text-gray-500 text-sm font-medium">${param.name === 'quality' ? '%' : ''}</span>
                                </div>
                            </div>
                        </div>
                    `;
                } else {
                    paramsHtml += `
                        <div class="mb-4">
                            <div class="flex items-center mb-2">
                                <div class="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                                <label for="${param.name}" class="block text-sm font-medium text-gray-800">${param.description}</label>
                            </div>
                            <input type="${param.type}" id="${param.name}" name="${param.name}"
                                   value="${param.default || ''}"
                                   class="block w-full shadow-sm focus:ring-blue-500 focus:border-blue-500 border-gray-300 rounded-lg py-2.5 text-sm font-medium">
                        </div>
                    `;
                }
            });

            // 添加提示信息
            paramsHtml += `
                <div class="mt-5 bg-yellow-50 p-3 rounded-lg border border-yellow-200 shadow-sm">
                    <div class="flex items-start">
                        <div class="bg-yellow-400 rounded-full p-1 mr-2 flex-shrink-0">
                            <svg class="w-3.5 h-3.5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                            </svg>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-yellow-800 mb-1">注意事项</h4>
                            <p class="text-sm text-yellow-700">
                                攻击测试会破坏隐写信息，这是测试安全性的必要过程
                            </p>
                        </div>
                    </div>
                </div>
            `;

            attackParamsContainer.innerHTML = paramsHtml;
            attackParamsContainer.classList.remove('hidden');
        }

        // 图像预览
        document.getElementById('image').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (!file) return;

            // 显示文件名
            const fileNameDisplay = document.createElement('div');
            fileNameDisplay.className = 'mt-2 text-xs text-gray-600 flex items-center';
            fileNameDisplay.innerHTML = `
                <svg class="w-4 h-4 mr-1 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                </svg>
                已选择: ${file.name} (${(file.size / 1024).toFixed(1)} KB)
            `;

            // 查找并替换现有的文件名显示
            const existingFileNameDisplay = document.querySelector('.image-file-name');
            if (existingFileNameDisplay) {
                existingFileNameDisplay.remove();
            }

            fileNameDisplay.classList.add('image-file-name');
            document.querySelector('.relative.border-2.border-dashed').appendChild(fileNameDisplay);

            const reader = new FileReader();
            reader.onload = function(e) {
                const preview = document.getElementById('imagePreview');
                preview.src = e.target.result;
                document.getElementById('imagePreviewContainer').classList.remove('hidden');

                // 同时更新原始图像预览
                document.getElementById('originalImage').src = e.target.result;

                // 添加动画效果
                preview.classList.add('animate-pulse');
                setTimeout(() => {
                    preview.classList.remove('animate-pulse');
                }, 1000);
            };
            reader.readAsDataURL(file);
        });

        // 提交攻击测试表单
        document.getElementById('attackForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData();
            const image = document.getElementById('image').files[0];
            const key = document.getElementById('key').value.trim();
            const attackType = document.getElementById('attackType').value;

            if (!image || !key || !attackType) {
                showErrorMessage('请填写所有必填字段');
                return;
            }

            formData.append('image', image);
            formData.append('key', key);
            formData.append('attack_type', attackType);

            // 收集攻击参数
            const attackParams = {};
            const attackParamsContainer = document.getElementById('attackParams');
            if (!attackParamsContainer.classList.contains('hidden')) {
                const inputs = attackParamsContainer.querySelectorAll('input, select');
                inputs.forEach(input => {
                    let value = input.value;
                    if (input.type === 'number') {
                        value = parseFloat(value);
                    }
                    attackParams[input.name] = value;
                });
            }

            formData.append('params', JSON.stringify(attackParams));

            // 显示加载中
            document.getElementById('loadingOverlay').classList.remove('hidden');

            try {
                const response = await fetch('/api/attack-test', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (!response.ok) {
                    throw new Error(result.detail || '攻击测试失败');
                }

                displayResults(result);

                // 测试完成后刷新统计数据
                setTimeout(async () => {
                    await loadTestStatistics();
                }, 500);
            } catch (error) {
                console.error('攻击测试失败:', error);
                showErrorMessage('攻击测试失败: ' + error.message);
            } finally {
                document.getElementById('loadingOverlay').classList.add('hidden');
            }
        });

        // 显示攻击测试结果
        function displayResults(result) {
            // 显示结果容器
            const resultContainer = document.getElementById('resultContainer');
            resultContainer.classList.remove('hidden');

            // 添加结果提示横幅
            const resultBanner = document.createElement('div');
            resultBanner.className = 'fixed top-0 left-0 right-0 bg-green-500 text-white py-2 px-4 text-center z-50 shadow-md';
            resultBanner.innerHTML = `
                <div class="container mx-auto flex items-center justify-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span>攻击测试完成！结果已显示在下方</span>
                    <button class="ml-4 bg-white bg-opacity-20 rounded px-2 py-1 text-sm hover:bg-opacity-30 transition-colors duration-200" onclick="this.parentNode.parentNode.remove()">
                        关闭
                    </button>
                </div>
            `;
            document.body.appendChild(resultBanner);

            // 5秒后自动移除横幅
            setTimeout(() => {
                if (resultBanner.parentNode) {
                    resultBanner.remove();
                }
            }, 5000);

            // 更新攻击信息
            document.getElementById('resultAttackType').textContent = result.attack_type;

            const resultStatus = document.getElementById('resultStatus');
            resultStatus.textContent = result.attack_result.success ? '成功' : '失败';
            resultStatus.className = result.attack_result.success ? 'success' : 'error';

            document.getElementById('resultMessage').textContent = result.attack_result.message;

            // 更新攻击后图像
            if (result.download_url) {
                document.getElementById('attackedImage').src = result.download_url;
                document.getElementById('downloadLink').href = result.download_url;
                document.getElementById('downloadLink').classList.remove('hidden');
            } else {
                document.getElementById('attackedImage').src = '';
                document.getElementById('downloadLink').href = '#';
                document.getElementById('downloadLink').classList.add('hidden');
            }

            // 处理密钥攻击结果
            const keyAttackResult = document.getElementById('keyAttackResult');
            if (result.attack_type === 'key_attack' && result.attack_result.attacked_key) {
                document.getElementById('attackedKey').textContent = result.attack_result.attacked_key;
                keyAttackResult.classList.remove('hidden');
            } else {
                keyAttackResult.classList.add('hidden');
            }

            // 处理文本操纵攻击结果
            const textManipulationResult = document.getElementById('textManipulationResult');
            if (result.attack_type === 'text_manipulation' && result.attack_result.modified_text) {
                document.getElementById('modifiedText').textContent = result.attack_result.modified_text;
                textManipulationResult.classList.remove('hidden');
            } else {
                textManipulationResult.classList.add('hidden');
            }

            // 更新评估结果
            const extractionSuccess = document.getElementById('extractionSuccess');
            extractionSuccess.textContent = result.evaluation.extraction_success ? '成功' : '失败';
            extractionSuccess.className = result.evaluation.extraction_success ? 'success' : 'error';

            document.getElementById('extractedText').textContent = result.evaluation.extracted_text || '无法提取文本';
            document.getElementById('evaluationMessage').textContent = result.evaluation.message;

            // 添加"返回顶部"按钮
            const backToTopButton = document.createElement('button');
            backToTopButton.className = 'fixed bottom-6 right-6 bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 focus:outline-none z-50';
            backToTopButton.innerHTML = `
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                </svg>
            `;
            backToTopButton.addEventListener('click', () => {
                window.scrollTo({ top: 0, behavior: 'smooth' });
            });
            document.body.appendChild(backToTopButton);

            // 滚动到结果区域，但留出一些空间以便看到结果标题
            const yOffset = -100; // 向上偏移100像素
            const y = resultContainer.getBoundingClientRect().top + window.pageYOffset + yOffset;
            window.scrollTo({ top: y, behavior: 'smooth' });

            // 添加结果区域的高亮动画
            resultContainer.classList.add('animate-pulse');
            setTimeout(() => {
                resultContainer.classList.remove('animate-pulse');
            }, 1000);
        }

        // 更新统计数据显示
        function updateStatisticsDisplay(statistics) {
            // 更新总体统计
            document.getElementById('totalTests').textContent = statistics.total_tests;

            // 计算总体成功率和鲁棒性证明率
            let totalSuccess = 0;
            let totalRobustness = 0;
            let totalAttacks = 0;

            for (const attackType in statistics.tests_by_type) {
                const stats = statistics.tests_by_type[attackType];
                totalAttacks += stats.total;
                totalSuccess += stats.success;
                totalRobustness += stats.robustness_proven;
            }

            const overallSuccessRate = totalAttacks > 0 ? (totalSuccess / totalAttacks * 100).toFixed(1) : 0;
            const overallRobustnessRate = totalAttacks > 0 ? (totalRobustness / totalAttacks * 100).toFixed(1) : 0;

            document.getElementById('overallSuccessRate').textContent = `${overallSuccessRate}%`;
            document.getElementById('overallRobustnessRate').textContent = `${overallRobustnessRate}%`;

            // 更新攻击类型分布图表
            updateAttackTypeChart(statistics.tests_by_type);

            // 更新鲁棒性评估图表
            updateRobustnessChart(statistics.success_rate);

            // 更新测试历史记录表格
            updateTestHistoryTable(statistics.recent_tests);
        }

        // 更新攻击类型分布图表
        function updateAttackTypeChart(testsByType) {
            const attackTypeContainer = document.getElementById('attackTypeDistribution');

            // 如果没有数据，显示提示信息
            if (Object.keys(testsByType).length === 0) {
                attackTypeContainer.innerHTML = `
                    <div class="flex items-center justify-center h-full text-gray-400">
                        <p>暂无数据</p>
                    </div>
                `;
                return;
            }

            // 准备图表数据
            const labels = [];
            const data = [];
            const backgroundColors = [
                'rgba(54, 162, 235, 0.7)',
                'rgba(255, 99, 132, 0.7)',
                'rgba(255, 206, 86, 0.7)',
                'rgba(75, 192, 192, 0.7)',
                'rgba(153, 102, 255, 0.7)',
                'rgba(255, 159, 64, 0.7)'
            ];

            let i = 0;
            for (const attackType in testsByType) {
                const attackTypeNames = {
                    'jpeg_compression': 'JPEG压缩',
                    'random_crop': '随机裁剪',
                    'gaussian_noise': '高斯噪声',
                    'steganalysis': '隐写分析',
                    'text_manipulation': '文本操纵',
                    'key_attack': '密钥攻击'
                };

                labels.push(attackTypeNames[attackType] || attackType);
                data.push(testsByType[attackType].total);
                i++;
            }

            // 清空容器
            attackTypeContainer.innerHTML = '<canvas id="attackTypeCanvas"></canvas>';

            // 创建图表
            const ctx = document.getElementById('attackTypeCanvas').getContext('2d');

            // 如果已经存在图表实例，销毁它
            if (attackTypeChart) {
                attackTypeChart.destroy();
            }

            // 创建新的图表
            attackTypeChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: labels,
                    datasets: [{
                        data: data,
                        backgroundColor: backgroundColors.slice(0, data.length),
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                            labels: {
                                boxWidth: 12,
                                font: {
                                    size: 10
                                }
                            }
                        }
                    }
                }
            });
        }

        // 更新鲁棒性评估图表
        function updateRobustnessChart(successRate) {
            const robustnessContainer = document.getElementById('robustnessChart');

            // 如果没有数据，显示提示信息
            if (Object.keys(successRate).length === 0) {
                robustnessContainer.innerHTML = `
                    <div class="flex items-center justify-center h-full text-gray-400">
                        <p>暂无数据</p>
                    </div>
                `;
                return;
            }

            // 准备图表数据
            const labels = [];
            const attackSuccessData = [];
            const robustnessData = [];

            for (const attackType in successRate) {
                const attackTypeNames = {
                    'jpeg_compression': 'JPEG压缩',
                    'random_crop': '随机裁剪',
                    'gaussian_noise': '高斯噪声',
                    'steganalysis': '隐写分析',
                    'text_manipulation': '文本操纵',
                    'key_attack': '密钥攻击'
                };

                labels.push(attackTypeNames[attackType] || attackType);
                attackSuccessData.push(successRate[attackType].attack_success_rate);
                robustnessData.push(successRate[attackType].robustness_proven_rate);
            }

            // 清空容器
            robustnessContainer.innerHTML = '<canvas id="robustnessCanvas"></canvas>';

            // 创建图表
            const ctx = document.getElementById('robustnessCanvas').getContext('2d');

            // 如果已经存在图表实例，销毁它
            if (robustnessChart) {
                robustnessChart.destroy();
            }

            // 创建新的图表
            robustnessChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [
                        {
                            label: '攻击成功率',
                            data: attackSuccessData,
                            backgroundColor: 'rgba(54, 162, 235, 0.7)',
                            borderColor: 'rgba(54, 162, 235, 1)',
                            borderWidth: 1
                        },
                        {
                            label: '鲁棒性证明率',
                            data: robustnessData,
                            backgroundColor: 'rgba(153, 102, 255, 0.7)',
                            borderColor: 'rgba(153, 102, 255, 1)',
                            borderWidth: 1
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top',
                            labels: {
                                boxWidth: 12,
                                font: {
                                    size: 10
                                }
                            }
                        }
                    }
                }
            });
        }

        // 更新测试历史记录表格
        function updateTestHistoryTable(testHistory) {
            const tableBody = document.getElementById('testHistoryTable');

            // 如果没有数据，显示提示信息
            if (!testHistory || testHistory.length === 0) {
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="6" class="px-6 py-4 text-center text-gray-400">暂无测试记录</td>
                    </tr>
                `;
                return;
            }

            // 清空表格
            tableBody.innerHTML = '';

            // 添加测试记录
            testHistory.forEach(test => {
                const attackTypeNames = {
                    'jpeg_compression': 'JPEG压缩攻击',
                    'random_crop': '随机裁剪攻击',
                    'gaussian_noise': '高斯噪声攻击',
                    'steganalysis': '隐写分析攻击',
                    'text_manipulation': '文本操纵攻击',
                    'key_attack': '密钥攻击'
                };

                const row = document.createElement('tr');
                row.className = 'hover:bg-gray-50';

                // 格式化时间
                const testTime = new Date(test.timestamp);
                const formattedTime = `${testTime.getFullYear()}-${String(testTime.getMonth() + 1).padStart(2, '0')}-${String(testTime.getDate()).padStart(2, '0')} ${String(testTime.getHours()).padStart(2, '0')}:${String(testTime.getMinutes()).padStart(2, '0')}:${String(testTime.getSeconds()).padStart(2, '0')}`;

                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${formattedTime}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${attackTypeNames[test.attack_type] || test.attack_type}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${test.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                            ${test.success ? '成功' : '失败'}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${test.extraction_success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                            ${test.extraction_success ? '成功' : '失败'}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${test.robustness_proven ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}">
                            ${test.robustness_proven ? '已证明' : '未证明'}
                        </span>
                    </td>
                    <td class="px-6 py-4 text-sm text-gray-500">${test.message}</td>
                `;

                tableBody.appendChild(row);
            });
        }

        // 清空统计数据
        async function clearTestStatistics() {
            try {
                // 显示加载中
                document.getElementById('loadingOverlay').classList.remove('hidden');

                const response = await fetch('/api/test-statistics/clear');
                if (!response.ok) {
                    throw new Error('清空测试统计数据失败');
                }

                // 重新加载统计数据
                await loadTestStatistics();

                // 显示成功消息
                const successBanner = document.createElement('div');
                successBanner.className = 'fixed top-0 left-0 right-0 bg-green-500 text-white py-2 px-4 text-center z-50 shadow-md';
                successBanner.innerHTML = `
                    <div class="container mx-auto flex items-center justify-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                        <span>测试统计数据已清空</span>
                        <button class="ml-4 bg-white bg-opacity-20 rounded px-2 py-1 text-sm hover:bg-opacity-30 transition-colors duration-200" onclick="this.parentNode.parentNode.remove()">
                            关闭
                        </button>
                    </div>
                `;
                document.body.appendChild(successBanner);

                // 3秒后自动移除横幅
                setTimeout(() => {
                    if (successBanner.parentNode) {
                        successBanner.remove();
                    }
                }, 3000);

            } catch (error) {
                console.error('清空测试统计数据失败:', error);
                showErrorMessage('清空测试统计数据失败: ' + error.message);
            } finally {
                // 隐藏加载中
                document.getElementById('loadingOverlay').classList.add('hidden');
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadAttackTypes();

            // 添加刷新统计按钮事件
            document.getElementById('refreshStatistics').addEventListener('click', loadTestStatistics);

            // 添加清空统计按钮事件
            document.getElementById('clearStatistics').addEventListener('click', clearTestStatistics);

            // 在测试成功后自动刷新统计数据
            document.getElementById('attackForm').addEventListener('submit', async function(e) {
                const originalSubmitHandler = e.onsubmit;
                e.onsubmit = null;

                // 等待原始提交处理完成
                await new Promise(resolve => setTimeout(resolve, 1000));

                // 刷新统计数据
                await loadTestStatistics();

                // 恢复原始提交处理程序
                if (originalSubmitHandler) {
                    e.onsubmit = originalSubmitHandler;
                }
            });
        });
    </script>
</div> <!-- 关闭container -->
</body>
</html>
