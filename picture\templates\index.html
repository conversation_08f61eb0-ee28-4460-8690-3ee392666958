<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图像隐写加密系统</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="{{ url_for('static', path='/css/style.css') }}" rel="stylesheet">
    <style>
        /* 基础动画和工具类 */
        .fixed {
            position: fixed;
        }

        .inset-0 {
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
        }

        .hidden {
            display: none;
        }

        .font-mono {
            font-family: monospace;
        }

        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }

        .animate-spin {
            animation: spin 1s linear infinite;
        }

        /* 背景样式 */
        .page-background {
            background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
            overflow-y: auto;
            z-index: 0;
        }

        .page-background::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="40" fill="none" stroke="white" stroke-width="0.5" stroke-opacity="0.1"/></svg>') repeat;
            opacity: 0.1;
            z-index: -2;
        }

        /* 卡片样式 */
        .glass-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .glass-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        /* 输入框样式 */
        .fancy-input {
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .fancy-input:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
            background: white;
        }

        /* 按钮样式 */
        .fancy-button {
            background: linear-gradient(45deg, #3b82f6, #1d4ed8);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .fancy-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                    transparent,
                    rgba(255, 255, 255, 0.2),
                    transparent);
            transition: 0.5s;
        }

        .fancy-button:hover::before {
            left: 100%;
        }

        /* 标题样式 */
        .gradient-text {
            background: linear-gradient(45deg, #fff, #bfdbfe);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            font-size: 3rem;
            /*margin-top: 20px;*/
        }

        /* 文件选择按钮样式 */
        .file-input-wrapper {
            position: relative;
            display: inline-block;
            width: 100%;
        }

        .file-input-wrapper input[type="file"] {
            position: absolute;
            left: -9999px;
        }

        .file-input-trigger {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0.75rem 1rem;
            background: rgba(255, 255, 255, 0.9);
            border: 2px dashed #3b82f6;
            border-radius: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .file-input-trigger:hover {
            background: rgba(255, 255, 255, 1);
            border-color: #1d4ed8;
        }

        .file-name {
            margin-left: 0.5rem;
            color: #4a5568;
            font-size: 0.875rem;
        }

        /* 背景动画效果 */
        .animated-background {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: -1;
        }

        .animated-circle {
            position: absolute;
            border-radius: 50%;
            background: linear-gradient(45deg, rgba(219, 234, 254, 0.1), rgba(191, 219, 254, 0.05));
            animation: float 20s infinite;
        }

        .circle-1 {
            width: 400px;
            height: 400px;
            top: -100px;
            left: -100px;
            animation-delay: 0s;
        }

        .circle-2 {
            width: 300px;
            height: 300px;
            top: 50%;
            right: -50px;
            animation-delay: -5s;
        }

        .circle-3 {
            width: 250px;
            height: 250px;
            bottom: -50px;
            left: 30%;
            animation-delay: -10s;
        }

        @keyframes float {

            0%,
            100% {
                transform: translate(0, 0) rotate(0deg);
            }

            25% {
                transform: translate(10px, 10px) rotate(5deg);
            }

            50% {
                transform: translate(0, 20px) rotate(0deg);
            }

            75% {
                transform: translate(-10px, 10px) rotate(-5deg);
            }
        }

        /* 背景关键词样式 */
        .keyword-background {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: -1;
            overflow: hidden;
        }

        .floating-keyword {
            position: absolute;
            color: rgba(219, 234, 254, 0.1);
            font-family: 'Courier New', monospace;
            white-space: nowrap;
            pointer-events: none;
            animation: floatKeyword 15s linear infinite;
            font-size: 1.2rem;
            font-weight: bold;
        }

        .binary-text {
            position: absolute;
            color: rgba(219, 234, 254, 0.05);
            font-family: monospace;
            font-size: 0.8rem;
            line-height: 1;
            user-select: none;
        }

        @keyframes floatKeyword {
            0% {
                transform: translateX(100%) translateY(0) rotate(5deg);
                opacity: 0;
            }

            10% {
                opacity: 1;
            }

            90% {
                opacity: 1;
            }

            100% {
                transform: translateX(-100%) translateY(-20px) rotate(-5deg);
                opacity: 0;
            }
        }

        /* 算法说明样式 */
        .algorithm-info {
            max-width: 800px;
            margin: 0 auto 3rem auto;
            text-align: center;
            color: rgba(255, 255, 255, 0.9);
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 1.5rem;
            border-radius: 1rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .algorithm-tags {
            display: flex;
            justify-content: center;
            gap: 1rem;
            flex-wrap: wrap;
            margin-top: 1rem;
        }

        .algorithm-tag {
            background: rgba(255, 255, 255, 0.15);
            padding: 0.5rem 1rem;
            border-radius: 2rem;
            font-size: 0.875rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        /* 右上角动态圆圈样式 */
        .corner-circle {
            position: fixed;
            top: 20px;
            right: 20px;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 2px solid rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .corner-circle::before {
            content: '';
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            border: 2px solid transparent;
            border-top-color: rgba(255, 255, 255, 0.8);
            animation: spinCircle 1s linear infinite;
        }

        .corner-circle::after {
            content: '';
            position: absolute;
            width: 80%;
            height: 80%;
            border-radius: 50%;
            border: 2px solid transparent;
            border-top-color: rgba(255, 255, 255, 0.4);
            animation: spinCircle 0.8s linear infinite reverse;
        }

        @keyframes spinCircle {
            from {
                transform: rotate(0deg);
            }

            to {
                transform: rotate(360deg);
            }
        }

        .corner-circle:hover {
            transform: scale(1.1);
        }

        .corner-circle-inner {
            width: 8px;
            height: 8px;
            background-color: rgba(255, 255, 255, 0.8);
            border-radius: 50%;
        }
    </style>
</head>

<body class="page-background">
    <!-- 固定在顶部的导航栏 -->
    <div class="fixed top-0 left-0 right-0 bg-blue-900 bg-opacity-80 backdrop-filter backdrop-blur-md z-50 shadow-md">
        <div class="container mx-auto px-4 py-3 flex justify-between items-center">
            <h2 class="text-white font-bold text-xl">图像隐写加密系统</h2>
            <a href="/attack-test" class="text-white hover:text-blue-200 transition-colors duration-300 flex items-center bg-blue-700 hover:bg-blue-800 px-4 py-2 rounded-lg shadow-sm">
                <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
                攻击测试
            </a>
        </div>
    </div>

    <!-- 主内容区域，添加上边距以避免被固定导航栏遮挡 -->
    <div class="container mx-auto px-4 py-12 pt-24">
        <h1 class="font-bold text-center mb-8 gradient-text">图像隐写加密系统</h1>

        <div class="algorithm-info">

            <div class="algorithm-tags">
                <span class="algorithm-tag">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                    LSB 最低有效位隐写
                </span>
                <span class="algorithm-tag">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                    </svg>
                    AES-128 加密
                </span>
                <span class="algorithm-tag">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                    </svg>
                    SHA-256 校验
                </span>
                <span class="algorithm-tag">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4" />
                    </svg>
                    数据完整性保护
                </span>
            </div>
        </div>

        <div class="grid md:grid-cols-2 gap-8 max-w-6xl mx-auto">
            <!-- 加密部分 -->
            <div class="glass-card rounded-2xl p-8">
                <h2 class="text-2xl font-semibold mb-8 text-gray-800 flex items-center">
                    <svg class="w-8 h-8 mr-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                    </svg>
                    <span class="bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent">
                        加密文本到图像
                    </span>
                </h2>

                <form id="encryptForm" class="space-y-6">
                    <div class="space-y-3">
                        <label class="block text-sm font-medium text-gray-700">选择图像</label>
                        <div class="file-input-wrapper">
                            <input type="file" id="encryptImage" accept="image/*" required>
                            <label for="encryptImage" class="file-input-trigger">
                                <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                </svg>
                                <span class="ml-2">选择图片</span>
                                <span class="file-name"></span>
                            </label>
                        </div>
                    </div>

                    <div class="space-y-3">
                        <label class="block text-sm font-medium text-gray-700">输入文本</label>
                        <textarea id="encryptText" rows="4"
                            class="fancy-input w-full p-4 rounded-xl resize-none focus:outline-none"
                            required></textarea>
                    </div>

                    <div class="flex items-center space-x-3 bg-blue-50 p-4 rounded-xl">
                        <input type="checkbox" id="useCustomKey"
                            class="w-5 h-5 text-blue-600 rounded focus:ring-blue-500">
                        <label class="text-sm font-medium text-gray-700">使用自定义密钥</label>
                    </div>

                    <div id="customKeyInput" class="hidden space-y-3">
                        <label class="block text-sm font-medium text-gray-700">输入密钥</label>
                        <input type="text" id="customKey" class="fancy-input w-full p-4 rounded-xl focus:outline-none">
                    </div>

                    <button type="submit"
                        class="fancy-button w-full py-4 px-6 rounded-xl text-white font-medium shadow-lg">
                        加密并下载
                    </button>
                </form>
            </div>

            <!-- 解密部分 -->
            <div class="glass-card rounded-2xl p-8">
                <h2 class="text-2xl font-semibold mb-8 text-gray-800 flex items-center">
                    <svg class="w-8 h-8 mr-3 text-blue-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M8 11V7a4 4 0 118 0m-4 8v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2z" />
                    </svg>
                    <span class="bg-gradient-to-r from-blue-700 to-blue-900 bg-clip-text text-transparent">
                        从图像提取文本
                    </span>
                </h2>

                <form id="decryptForm" class="space-y-6">
                    <div class="space-y-3">
                        <label class="block text-sm font-medium text-gray-700">选择图像</label>
                        <div class="file-input-wrapper">
                            <input type="file" id="decryptImage" accept="image/*" required>
                            <label for="decryptImage" class="file-input-trigger">
                                <svg class="w-6 h-6 text-blue-700" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                </svg>
                                <span class="ml-2">选择图片</span>
                                <span class="file-name"></span>
                            </label>
                        </div>
                    </div>

                    <div class="space-y-3">
                        <label class="block text-sm font-medium text-gray-700">输入密钥</label>
                        <input type="text" id="decryptKey" class="fancy-input w-full p-4 rounded-xl focus:outline-none"
                            required>
                    </div>

                    <button type="submit"
                        class="fancy-button w-full py-4 px-6 rounded-xl text-white font-medium shadow-lg">
                        提取文本
                    </button>
                </form>

                <div id="decryptedResult" class="mt-8 hidden">
                    <h3 class="text-lg font-medium mb-4 text-gray-800 flex items-center justify-between">
                        <span>解密结果：</span>
                        <button id="copyDecryptedText" class="text-sm bg-blue-100 hover:bg-blue-200 text-blue-700 px-3 py-1 rounded-md flex items-center transition-colors duration-200">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                            </svg>
                            复制文本
                        </button>
                    </h3>
                    <div class="relative">
                        <pre id="decryptedText"
                            class="bg-white p-6 rounded-xl border border-gray-200 whitespace-pre-wrap text-gray-700 shadow-inner max-h-60 overflow-y-auto"></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="keyword-background">
        <div class="floating-keyword" style="top: 15%; animation-delay: 0s;">ENCRYPTION</div>
        <div class="floating-keyword" style="top: 25%; animation-delay: -3s;">STEGANOGRAPHY</div>
        <div class="floating-keyword" style="top: 35%; animation-delay: -6s;">CRYPTOGRAPHY</div>
        <div class="floating-keyword" style="top: 45%; animation-delay: -9s;">SECURITY</div>
        <div class="floating-keyword" style="top: 55%; animation-delay: -12s;">PRIVACY</div>
        <div class="floating-keyword" style="top: 65%; animation-delay: -15s;">DATA HIDING</div>
        <div class="floating-keyword" style="top: 75%; animation-delay: -18s;">PROTECTION</div>
        <div class="floating-keyword" style="top: 85%; animation-delay: -21s;">CONFIDENTIAL</div>

        <!-- 添加二进制背景文本 -->
        <script>
            document.addEventListener('DOMContentLoaded', function () {
                const binaryBackground = document.createElement('div');
                binaryBackground.style.position = 'absolute';
                binaryBackground.style.top = '0';
                binaryBackground.style.left = '0';
                binaryBackground.style.right = '0';
                binaryBackground.style.bottom = '0';
                binaryBackground.style.zIndex = '-2';
                binaryBackground.style.opacity = '0.05';

                // 生成随机二进制文本
                for (let i = 0; i < 50; i++) {
                    const binaryText = document.createElement('div');
                    binaryText.className = 'binary-text';
                    binaryText.style.left = Math.random() * 100 + '%';
                    binaryText.style.top = Math.random() * 100 + '%';
                    binaryText.style.transform = `rotate(${Math.random() * 360}deg)`;

                    let binary = '';
                    for (let j = 0; j < 8; j++) {
                        binary += Math.random() > 0.5 ? '1' : '0';
                    }
                    binaryText.textContent = binary;

                    binaryBackground.appendChild(binaryText);
                }

                document.querySelector('.keyword-background').appendChild(binaryBackground);
            });
        </script>
    </div>

    <div class="animated-background">
        <div class="animated-circle circle-1"></div>
        <div class="animated-circle circle-2"></div>
        <div class="animated-circle circle-3"></div>
    </div>

    <div class="corner-circle">
        <div class="corner-circle-inner"></div>
    </div>

    <script src="{{ url_for('static', path='/js/main.js') }}"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // 处理文件选择显示
            function handleFileSelect(inputId) {
                const input = document.getElementById(inputId);
                const label = input.nextElementSibling;
                const fileNameSpan = label.querySelector('.file-name');

                input.addEventListener('change', function (e) {
                    if (this.files && this.files[0]) {
                        fileNameSpan.textContent = this.files[0].name;
                    } else {
                        fileNameSpan.textContent = '';
                    }
                });
            }

            handleFileSelect('encryptImage');
            handleFileSelect('decryptImage');

            // 复制解密文本功能
            const copyButton = document.getElementById('copyDecryptedText');
            if (copyButton) {
                copyButton.addEventListener('click', function() {
                    const decryptedText = document.getElementById('decryptedText');
                    if (decryptedText && decryptedText.textContent) {
                        navigator.clipboard.writeText(decryptedText.textContent)
                            .then(() => {
                                // 显示复制成功提示
                                const originalText = copyButton.innerHTML;
                                copyButton.innerHTML = '<svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>复制成功';
                                copyButton.classList.remove('bg-blue-100', 'text-blue-700');
                                copyButton.classList.add('bg-green-100', 'text-green-700');

                                // 2秒后恢复原样
                                setTimeout(() => {
                                    copyButton.innerHTML = originalText;
                                    copyButton.classList.remove('bg-green-100', 'text-green-700');
                                    copyButton.classList.add('bg-blue-100', 'text-blue-700');
                                }, 2000);
                            })
                            .catch(err => {
                                console.error('无法复制文本: ', err);
                                alert('复制失败，请手动选择并复制文本。');
                            });
                    }
                });
            }
        });
    </script>
</body>

</html>