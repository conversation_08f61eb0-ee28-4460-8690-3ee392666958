<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图像隐写加密系统</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            display: none;
        }
        .tabs {
            display: flex;
            border-bottom: 1px solid #ddd;
            margin-bottom: 20px;
        }
        .tab {
            padding: 10px 20px;
            cursor: pointer;
        }
        .tab.active {
            border-bottom: 2px solid #4CAF50;
            font-weight: bold;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        #loadingOverlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        .spinner {
            border: 5px solid #f3f3f3;
            border-top: 5px solid #3498db;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 2s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>图像隐写加密系统</h1>
        
        <div class="tabs">
            <div class="tab active" data-tab="encrypt">加密模式</div>
            <div class="tab" data-tab="decrypt">解密模式</div>
        </div>
        
        <div id="encrypt" class="tab-content active">
            <div class="card">
                <h2>图像加密</h2>
                <form id="encryptForm" enctype="multipart/form-data">
                    <div class="form-group">
                        <label for="encryptImage">选择图片：</label>
                        <input type="file" id="encryptImage" name="image" accept="image/*" required>
                    </div>
                    <div class="form-group">
                        <label for="encryptText">加密文本：</label>
                        <textarea id="encryptText" name="text" rows="4" required></textarea>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="useCustomKey" name="use_custom_key"> 使用自定义密钥
                        </label>
                    </div>
                    <div class="form-group" id="customKeyGroup" style="display: none;">
                        <label for="customKey">自定义密钥：</label>
                        <input type="text" id="customKey" name="custom_key" placeholder="输入Base64格式的密钥">
                    </div>
                    <button type="submit">加密文件</button>
                </form>
                <div id="encryptResult" class="result">
                    <h3>加密结果</h3>
                    <p id="encryptMessage"></p>
                    <div class="form-group">
                        <label for="resultKey">密钥：</label>
                        <input type="text" id="resultKey" readonly>
                        <button id="copyKey">复制密钥</button>
                    </div>
                    <p><a id="downloadLink" href="#" target="_blank">下载加密图片</a></p>
                </div>
            </div>
        </div>
        
        <div id="decrypt" class="tab-content">
            <div class="card">
                <h2>图像解密</h2>
                <form id="decryptForm" enctype="multipart/form-data">
                    <div class="form-group">
                        <label for="decryptImage">选择加密图片：</label>
                        <input type="file" id="decryptImage" name="image" accept="image/*" required>
                    </div>
                    <div class="form-group">
                        <label for="decryptKey">密钥：</label>
                        <input type="text" id="decryptKey" name="key" required>
                    </div>
                    <button type="submit">解密文件</button>
                </form>
                <div id="decryptResult" class="result">
                    <h3>解密结果</h3>
                    <p id="decryptMessage"></p>
                    <div class="form-group">
                        <label for="resultText">解密文本：</label>
                        <textarea id="resultText" rows="4" readonly></textarea>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div id="loadingOverlay">
        <div class="spinner"></div>
    </div>
    
    <script>
        // 切换标签页
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', () => {
                document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));
                
                tab.classList.add('active');
                document.getElementById(tab.dataset.tab).classList.add('active');
            });
        });
        
        // 显示/隐藏自定义密钥输入框
        document.getElementById('useCustomKey').addEventListener('change', function() {
            document.getElementById('customKeyGroup').style.display = this.checked ? 'block' : 'none';
        });
        
        // 加密表单提交
        document.getElementById('encryptForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const loadingOverlay = document.getElementById('loadingOverlay');
            
            try {
                loadingOverlay.style.display = 'flex';
                
                const response = await fetch('/api/encrypt', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    document.getElementById('encryptMessage').textContent = result.message;
                    document.getElementById('resultKey').value = result.key;
                    document.getElementById('downloadLink').href = result.download_url;
                    document.getElementById('encryptResult').style.display = 'block';
                } else {
                    alert(`错误: ${result.detail}`);
                }
            } catch (error) {
                alert(`发生错误: ${error.message}`);
            } finally {
                loadingOverlay.style.display = 'none';
            }
        });
        
        // 解密表单提交
        document.getElementById('decryptForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const loadingOverlay = document.getElementById('loadingOverlay');
            
            try {
                loadingOverlay.style.display = 'flex';
                
                const response = await fetch('/api/decrypt', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    document.getElementById('decryptMessage').textContent = result.message;
                    document.getElementById('resultText').value = result.text;
                    document.getElementById('decryptResult').style.display = 'block';
                } else {
                    alert(`错误: ${result.detail}`);
                }
            } catch (error) {
                alert(`发生错误: ${error.message}`);
            } finally {
                loadingOverlay.style.display = 'none';
            }
        });
        
        // 复制密钥
        document.getElementById('copyKey').addEventListener('click', function() {
            const keyInput = document.getElementById('resultKey');
            keyInput.select();
            document.execCommand('copy');
            alert('密钥已复制到剪贴板');
        });
    </script>
</body>
</html> 