import random
import numpy as np

class Warehouse:
    def __init__(self, layout_config):
        # 初始化仓库三维结构 (x, y, z)
        self.grid = np.zeros((40, 20, 6), dtype=int)
        # 记录每个箱子的当前位置
        self.box_positions = {}
        # 初始化区域划分
        self.initialize_storage(layout_config)
    
    def initialize_storage(self, config):
        """根据配置初始化仓库存储结构"""
        # 生成所有可能的列排位置并按距离排序
        positions = [(x, y) for x in range(40) for y in range(20)]
        positions.sort(key=lambda p: (abs(p[0]-19) + p[1]))  # 工作台在(19,0)
        
        # 计算各区域边界
        total = 40*20
        a_end = int(total * config['quantity'][0])
        b_end = a_end + int(total * config['quantity'][1])
        
        # 分配区域
        box_id = 1
        # A类区域
        for x, y in positions[:a_end]:
            for z in range(6):
                self.grid[x, y, z] = box_id
                self.box_positions[box_id] = (x, y, z)
                box_id += 1
        # B类区域
        for x, y in positions[a_end:b_end]:
            for z in range(6):
                self.grid[x, y, z] = box_id
                self.box_positions[box_id] = (x, y, z)
                box_id += 1
        # C类区域
        for x, y in positions[b_end:]:
            for z in range(6):
                self.grid[x, y, z] = box_id
                self.box_positions[box_id] = (x, y, z)
                box_id += 1

class AGV:
    def __init__(self):
        self.position = (19, 0, 5)  # 初始位置（顶层中间）
        self.carrying = None         # 当前携带的箱子
        self.speed = (3.0, 3.0, 1.6) # x/y/z轴速度（米/秒）
        self.fix_time = 2.5          # 抓取/放下固定时间

    def move_time(self, start, end):
        """计算三维移动时间"""
        dx = abs(start[0]-end[0]) * 0.8  # 列间距0.8米
        dy = abs(start[1]-end[1]) * 0.6  # 排间距0.6米
        dz = abs(start[2]-end[2]) * 0.33 # 层间距0.33米
        return dx/self.speed[0] + dy/self.speed[1] + dz/self.speed[2]

class Simulation:
    def __init__(self, config):
        self.warehouse = Warehouse(config)
        self.agv = AGV()
        self.workstation = (19, 0, 0)  # 工作台位置
        self.time = 0.0
        self.prev_box = None  # (box_id, category, original_pos)
        
        # 生成任务列表
        self.tasks = self.generate_tasks(config)
    
    def generate_tasks(self, config):
        """根据流动速度生成任务列表"""
        total = 1000
        categories = len(config['flow'])
        # 计算每个类别的任务数量
        counts = [int(total * ratio) for ratio in config['flow']]
        counts[-1] = total - sum(counts[:-1])  # 处理四舍五入误差
        
        # 获取各类别箱子ID范围
        id_ranges = []
        current = 1
        for q in config['quantity']:
            size = int(4800 * q)
            id_ranges.append((current, current + size -1))
            current += size
        
        # 随机采样不重复的箱子
        tasks = []
        for i in range(categories):
            start, end = id_ranges[i]
            boxes = list(range(start, end+1))
            tasks.extend(random.sample(boxes, counts[i]))
        random.shuffle(tasks)
        return tasks
    
    def get_category(self, box_id):
        """获取箱子类别"""
        for i, (start, end) in enumerate(self.warehouse.box_ranges):
            if start <= box_id <= end:
                return chr(65+i)  # 'A','B',...
        return None
    
    def handle_obstacles(self, target_pos):
        """处理目标位置上方的障碍箱子（简化版）"""
        x, y, z = target_pos
        time_used = 0
        
        # 从顶层开始处理
        for current_z in range(5, z, -1):
            if self.warehouse.grid[x, y, current_z] != 0:
                # 简化处理：假设每次障碍处理需要额外10秒
                time_used += 10  
        return time_used
    
    def simulate(self):
        """执行仿真"""
        for box_id in self.tasks:
            # 处理前一个箱子的放回
            if self.prev_box:
                prev_id, prev_cat, prev_pos = self.prev_box
                current_cat = self.get_category(box_id)
                
                if prev_cat != current_cat:
                    # 放回原位置需要时间
                    move_t = self.agv.move_time(self.agv.position, prev_pos)
                    self.time += move_t + self.agv.fix_time
                    self.agv.position = prev_pos
                    self.prev_box = None
            
            # 抓取当前箱子
            current_pos = self.warehouse.box_positions[box_id]
            
            # 移动到目标位置
            move_t = self.agv.move_time(self.agv.position, current_pos)
            self.time += move_t
            self.agv.position = current_pos
            
            # 处理障碍（简化处理）
            self.time += self.handle_obstacles(current_pos)
            
            # 抓取操作
            self.time += self.agv.fix_time
            self.agv.carrying = box_id
            
            # 移动到工作台
            move_t = self.agv.move_time(current_pos, self.workstation)
            self.time += move_t
            self.agv.position = self.workstation
            
            # 放下箱子（工作台操作）
            self.time += self.agv.fix_time
            self.agv.carrying = None
            
            # 记录当前箱子信息
            self.prev_box = (box_id, self.get_category(box_id), current_pos)
        
        # 处理最后一个箱子的放回
        if self.prev_box:
            move_t = self.agv.move_time(self.agv.position, self.prev_box[2])
            self.time += move_t + self.agv.fix_time
        return self.time

# 仿真配置模板
configs = [
    {   # ABC三类
        'quantity': [0.15, 0.3, 0.55],
        'flow': [0.5, 0.35, 0.15]
    },
    {   # AB两类
        'quantity': [0.3, 0.7],
        'flow': [0.7, 0.3]
    },
    {   # ABCD四类
        'quantity': [0.1, 0.2, 0.3, 0.4],
        'flow': [0.4, 0.3, 0.2, 0.1]
    },
    {   # ABCDE五类
        'quantity': [0.1, 0.15, 0.2, 0.25, 0.3],
        'flow': [0.3, 0.25, 0.2, 0.15, 0.1]
    }
]

# 执行所有仿真
for i, config in enumerate(configs):
    sim = Simulation(config)
    total_time = sim.simulate()
    print(f"Config {i+1} total time: {total_time/60:.2f} minutes")