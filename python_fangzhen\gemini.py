# -*- coding: utf-8 -*-
import math
import random
import numpy as np
import time # To time the simulation execution itself

# --- Constants ---
COLS = 40 # 长 (x-axis)
ROWS = 20 # 宽 (y-axis)
LAYERS = 6 # 高 (z-axis)
TOTAL_BOXES = COLS * ROWS * LAYERS

BOX_LENGTH = 0.8  # meters (x-dimension)
BOX_WIDTH = 0.6   # meters (y-dimension)
BOX_HEIGHT = 0.33 # meters (z-dimension)

CRANE_SPEED_XY = 3.0  # m/s (同一时间在x和y轴上移动)
CRANE_SPEED_Z = 1.6   # m/s (升降速度)
PICK_PLACE_TIME = 2.5 # seconds (抓取或放下箱子的固定时间)

# Define workstation and initial crane position (using grid indices)
# 仓库底层（长边中间和小车同侧）
# 长边 (COLS=40) 中间 approx x=19 (0-based index)
# "同侧" - Assuming Y=0 (front row)
# 底层 z=0
WORKSTATION_GRID_POS = (COLS // 2 - 1, 0, 0) # (x, y, z) index

# 仓库顶层（长边中间）
# 顶层 z=5 (0-based index is LAYERS-1)
CRANE_START_GRID_POS = (COLS // 2 - 1, 0, LAYERS - 1) # Starts on the top layer in the same column as the workstation

# Calculate a safe travel height (metric Z coordinate) for horizontal movements
# Slightly above the highest possible box center + box half-height
SAFE_TRAVEL_Z = (LAYERS - 0.5) * BOX_HEIGHT + BOX_HEIGHT # Ensure clearance above top layer boxes

# --- Helper Functions ---

def get_real_coords(grid_pos):
    """
    Converts grid indices (x, y, z) to real-world coordinates (X, Y, Z) 
    representing the CENTER of the box volume.
    Assumes grid origin (0,0,0) corresponds to the corner of the warehouse space.
    """
    x, y, z = grid_pos
    # Center of the box volume
    real_x = (x + 0.5) * BOX_LENGTH
    real_y = (y + 0.5) * BOX_WIDTH
    real_z = (z + 0.5) * BOX_HEIGHT
    return real_x, real_y, real_z

def calculate_travel_time(start_pos_xyz, end_pos_xyz):
    """
    Calculates travel time between two real-world coordinate points (X, Y, Z).
    Accounts for simultaneous XY movement and separate Z movement.
    """
    dx = abs(end_pos_xyz[0] - start_pos_xyz[0])
    dy = abs(end_pos_xyz[1] - start_pos_xyz[1])
    dz = abs(end_pos_xyz[2] - start_pos_xyz[2])

    # Time for horizontal (XY) movement is limited by the slower axis
    time_xy = max(dx / CRANE_SPEED_XY, dy / CRANE_SPEED_XY) if CRANE_SPEED_XY > 0 else 0
    
    # Time for vertical (Z) movement
    time_z = dz / CRANE_SPEED_Z if CRANE_SPEED_Z > 0 else 0

    # Total time: Assume XY movement and Z movement can happen concurrently.
    # The total time is the maximum of the time required for XY and Z travel.
    total_time = max(time_xy, time_z)
    
    # --- Alternative Interpretation: Sequential Movement ---
    # If the crane moves in XY first, then Z (or vice versa)
    # total_time = time_xy + time_z 
    # Let's stick with the concurrent interpretation (max) as it's often more realistic for gantry cranes.
    
    return total_time


# --- Simulation Class ---

class WarehouseSimulation:
    def __init__(self, name, quantity_ratios, flow_ratios):
        """Initializes the warehouse simulation environment."""
        self.name = name
        print(f"\n--- Initializing Simulation: {self.name} ---")
        # Validate ratios sum approximately to 1
        if not math.isclose(sum(quantity_ratios.values()), 1.0):
              print(f"Warning: Quantity ratios for {name} do not sum to 1 ({sum(quantity_ratios.values())})")
        if not math.isclose(sum(flow_ratios.values()), 1.0):
             print(f"Warning: Flow ratios for {name} do not sum to 1 ({sum(flow_ratios.values())})")

        self.quantity_ratios = quantity_ratios
        self.flow_ratios = flow_ratios
        self.categories = sorted(list(quantity_ratios.keys())) # Ensure consistent order e.g., A, B, C

        # Warehouse state: 3D grid storing box_id or None
        self.warehouse_grid = [[[None for _ in range(COLS)] for _ in range(ROWS)] for _ in range(LAYERS)]
        
        # Box information lookup
        self.box_locations = {}  # box_id -> current (x, y, z) grid tuple, or None if held/at WS
        self.box_original_locations = {} # box_id -> original (x, y, z) grid tuple
        self.box_categories = {} # box_id -> category ('A', 'B', ...)
        
        # Zone definitions based on proximity and flow rate
        self.zone_definitions = {} # category -> list of column indices (x-values)
        self.category_zones = {} # (x, y, z) -> category (derived from column zones)

        # Box assignment counters
        self.boxes_count_per_category = {}

        # Initialize zones and place boxes
        self._initialize_zones_and_boxes()

        # Calculate workstation and crane start real coordinates
        # Workstation interaction point: center of the grid cell at workstation pos
        self.workstation_xyz = get_real_coords(WORKSTATION_GRID_POS) 
        # Assume crane interacts with the *top* center of the box slot at the workstation
        self.workstation_interaction_xyz = (self.workstation_xyz[0], self.workstation_xyz[1], BOX_HEIGHT / 2) 

        # Crane starts above the top box in its starting column, at safe travel height
        crane_start_xyz_center = get_real_coords(CRANE_START_GRID_POS)
        self.crane_current_xyz = (crane_start_xyz_center[0], crane_start_xyz_center[1], SAFE_TRAVEL_Z)
        
        # Crane state
        self.held_box_id = None # ID of the box currently held by the crane
        self.total_time = 0.0
        self.task_times = [] # Store time for each individual task

        # Temporary storage tracking for obstructions (simplified)
        self.temp_obstruction_spots = {} # obstruction_box_id -> temp_grid_pos (where it was placed)
        # We need a pool of potential temp spots or find dynamically. Let's find dynamically.


    def _initialize_zones_and_boxes(self):
        """
        Assigns zones based on proximity to the workstation column (x=19).
        Assigns categories to zones based on flow ratios (highest flow = closest zone).
        Places boxes into the grid according to quantity ratios and zone assignments.
        """
        print("Initializing zones and placing boxes...")
        num_categories = len(self.categories)
        boxes_per_stack = ROWS * LAYERS # Boxes per full column (x-slice)

        # --- Calculate exact number of boxes per category ---
        total_assigned_boxes = 0
        category_list = self.categories # Use the sorted list
        
        for i, cat in enumerate(category_list):
            if i == num_categories - 1: # Last category gets the remainder
                count = TOTAL_BOXES - total_assigned_boxes
            else:
                # Ensure we don't create fractions of boxes, round might lead to slight over/under total
                # Calculate count, adjust last category to match TOTAL_BOXES exactly
                 raw_count = TOTAL_BOXES * self.quantity_ratios[cat]
                 # Use floor or round, but ensure total is correct
                 # Let's use floor and add remainder to last category
                 count = math.floor(raw_count)
                 # Potential rounding issue: let's adjust dynamically later if needed
            
            # Use rounding and adjust last one strategy
            if i < num_categories -1:
                 count = int(round(TOTAL_BOXES * self.quantity_ratios[cat]))
                 self.boxes_count_per_category[cat] = count
                 total_assigned_boxes += count
            else: # Last category
                 count = TOTAL_BOXES - total_assigned_boxes
                 self.boxes_count_per_category[cat] = count

        print(f"Target box counts per category: {self.boxes_count_per_category}")
        if sum(self.boxes_count_per_category.values()) != TOTAL_BOXES:
             print(f"Error: Total calculated boxes ({sum(self.boxes_count_per_category.values())}) does not match TOTAL_BOXES ({TOTAL_BOXES}). Check ratios.")
             # Simple fix: Adjust the last category count
             diff = TOTAL_BOXES - sum(self.boxes_count_per_category.values())
             self.boxes_count_per_category[category_list[-1]] += diff
             print(f"Adjusted box counts: {self.boxes_count_per_category}")


        # --- Assign zones based on columns, radiating from the center (X=19) ---
        # Categories with highest flow ratios get closest zones (A, B, C...)
        sorted_categories_by_flow = sorted(self.categories, key=lambda c: self.flow_ratios[c], reverse=True)

        assigned_cols = set()
        center_col = WORKSTATION_GRID_POS[0] # Typically 19
        
        # Start assigning columns outwards from the center
        cols_to_assign = list(range(COLS))
        # Sort columns by distance to center_col
        cols_to_assign.sort(key=lambda x: abs(x - center_col))

        col_idx_counter = 0
        for cat in sorted_categories_by_flow:
            # Columns needed for this category based on quantity
            cols_needed = math.ceil(self.boxes_count_per_category[cat] / boxes_per_stack)
            zone_cols = []
            
            # Assign the closest available columns
            assigned_count = 0
            while assigned_count < cols_needed and col_idx_counter < len(cols_to_assign):
                 col = cols_to_assign[col_idx_counter]
                 if col not in assigned_cols:
                      zone_cols.append(col)
                      assigned_cols.add(col)
                      assigned_count += 1
                 col_idx_counter += 1

            if assigned_count < cols_needed:
                 print(f"Warning: Could not assign enough columns ({assigned_count}/{cols_needed}) for category {cat}")

            self.zone_definitions[cat] = sorted(zone_cols)
            print(f"Zone {cat} (Flow: {self.flow_ratios[cat]:.2f}, Qty: {self.boxes_count_per_category[cat]} boxes, Cols needed: {cols_needed}) -> Assigned Columns: {self.zone_definitions[cat]}")

            # Map grid locations in these columns to the category
            for x in zone_cols:
                 for y in range(ROWS):
                      for z in range(LAYERS):
                          self.category_zones[(x,y,z)] = cat

        # --- Assign box IDs and categories, then place them in the grid ---
        box_id_counter = 1
        boxes_placed_per_category = {cat: 0 for cat in self.categories}

        # Create a master list of all box IDs and their assigned categories based on counts
        master_box_list = []
        temp_box_id = 1
        for cat in self.categories: # Iterate A, B, C...
             for _ in range(self.boxes_count_per_category[cat]):
                 if temp_box_id <= TOTAL_BOXES:
                    master_box_list.append({'id': temp_box_id, 'category': cat})
                    temp_box_id += 1
        
        # Shuffle the master list to somewhat randomize placement within zones?
        # Or place sequentially for predictability? Let's place sequentially first.
        # random.shuffle(master_box_list) # Optional randomization

        box_list_idx = 0
        # Iterate through the physical grid zone by zone based on flow (A first)
        for cat in sorted_categories_by_flow: 
             zone_cols = self.zone_definitions[cat]
             placed_in_zone = 0
             # Iterate through columns assigned to this zone
             for x in zone_cols:
                 # Fill the column completely layer by layer, row by row
                 for z in range(LAYERS): # Fill bottom-up or top-down? Let's do bottom-up.
                     for y in range(ROWS):
                         # Find the next available box from the master list assigned to THIS category
                         current_box_info = None
                         temp_list_idx = box_list_idx
                         while temp_list_idx < len(master_box_list):
                             if master_box_list[temp_list_idx]['category'] == cat:
                                  current_box_info = master_box_list.pop(temp_list_idx) # Remove from list once assigned
                                  break
                             temp_list_idx += 1

                         if current_box_info:
                              box_id = current_box_info['id']
                              grid_pos = (x, y, z)
                              
                              if self.warehouse_grid[z][y][x] is None: # Check if slot is free
                                  self.warehouse_grid[z][y][x] = box_id
                                  self.box_locations[box_id] = grid_pos
                                  self.box_original_locations[box_id] = grid_pos # Store original position
                                  self.box_categories[box_id] = cat
                                  boxes_placed_per_category[cat] += 1
                                  placed_in_zone += 1
                              else:
                                   print(f"Error: Slot {grid_pos} already occupied when trying to place Box {box_id}")
                                   # Put the box back in the list to be tried later?
                                   master_box_list.insert(box_list_idx, current_box_info) # Put back

                         else:
                              # This means we ran out of boxes assigned to this category in the master list
                              # Should not happen if counts are correct, but signals end for this cat
                              # print(f"Debug: No more boxes of category {cat} found in master list.")
                              pass
                         
                         # Check if we have placed enough for this category - optimization
                         if boxes_placed_per_category[cat] >= self.boxes_count_per_category[cat]:
                              break # Break y loop
                     if boxes_placed_per_category[cat] >= self.boxes_count_per_category[cat]:
                          break # Break z loop
                 if boxes_placed_per_category[cat] >= self.boxes_count_per_category[cat]:
                      break # Break x loop
             print(f"Category {cat}: Placed {placed_in_zone} boxes.")

        # Final verification
        final_placed_count = sum(boxes_placed_per_category.values())
        print(f"Total boxes placed: {final_placed_count}")
        if final_placed_count != TOTAL_BOXES:
             print(f"Error: Final placed count ({final_placed_count}) mismatch with TOTAL_BOXES ({TOTAL_BOXES}). Master List remaining: {len(master_box_list)}")
             # Add remaining boxes from master list to any available slots? Needs careful handling.


    def _get_box_location(self, box_id):
        """Returns the current grid coordinates (x, y, z) for a given box_id."""
        return self.box_locations.get(box_id)

    def _get_box_category(self, box_id):
        """Returns the category ('A', 'B', ...) for a given box_id."""
        return self.box_categories.get(box_id)

    def _get_zone_category(self, grid_pos):
        """Returns the category assigned to a specific grid location."""
        return self.category_zones.get(grid_pos)

    def _find_empty_slot_in_zone(self, category):
        """
        Finds an available empty slot (returns grid_pos) within the specified category's zone.
        Prefers higher slots (closer to top).
        """
        zone_cols = self.zone_definitions.get(category)
        if not zone_cols:
            print(f"Error: No columns defined for zone {category}!")
            return None

        # Search from top layer down, within the columns for this zone
        for z in range(LAYERS - 1, -1, -1):
            # Search columns somewhat randomly or systematically? Let's do systematically.
            for x in zone_cols:
                # Search rows systematically
                for y in range(ROWS):
                    if self.warehouse_grid[z][y][x] is None:
                        # Found an empty slot
                        return (x, y, z)

        print(f"Error: No empty slot found in zone {category}!")
        return None # Should ideally not happen if simulation logic is correct

    def _move_crane(self, target_xyz, speed_factor=1.0):
        """
        Moves crane from current XYZ to target XYZ, updates time and position.
        Adds travel time to self.total_time.
        Speed factor can be used if needed (e.g., slower movement when carrying).
        """
        if self.crane_current_xyz == target_xyz:
            return 0.0 # No time if already there

        # Create temporary adjusted speeds if factor is used (not used currently)
        # current_speed_xy = CRANE_SPEED_XY * speed_factor
        # current_speed_z = CRANE_SPEED_Z * speed_factor
        
        travel_time = calculate_travel_time(self.crane_current_xyz, target_xyz)
        self.total_time += travel_time
        self.crane_current_xyz = target_xyz
        # print(f"Debug: Moved to {target_xyz}, Time+={travel_time:.2f}, Total={self.total_time:.2f}")
        return travel_time

    def _crane_pick_or_place(self, target_grid_pos, operation_type):
        """
        Simulates the full pick or place cycle at a target grid location.
        Includes: Move horizontal -> Descend -> Action -> Ascend. Updates time.
        Assumes crane is already at SAFE_TRAVEL_Z height before calling.
        Returns the time taken specifically for this pick/place cycle (excluding horizontal travel TO the column).
        """
        cycle_start_time = self.total_time
        target_center_xyz = get_real_coords(target_grid_pos)
        
        # Target point above the box center at safe travel height
        above_target_xyz = (target_center_xyz[0], target_center_xyz[1], SAFE_TRAVEL_Z)

        # 1. Ensure crane is horizontally aligned above the target (should be done before calling)
        #    If not perfectly aligned, add time for the final alignment move.
        if self.crane_current_xyz[0] != above_target_xyz[0] or self.crane_current_xyz[1] != above_target_xyz[1]:
             # print(f"Debug: Minor alignment needed before pick/place at {target_grid_pos}")
             align_time = self._move_crane(above_target_xyz) # Move horizontally only
        
        current_z = self.crane_current_xyz[2]
        target_z = target_center_xyz[2] # Interact with the center of the box

        # 2. Descend from safe height to the box's center Z
        time_descend = abs(target_z - current_z) / CRANE_SPEED_Z if CRANE_SPEED_Z > 0 else 0
        self.total_time += time_descend
        self.crane_current_xyz = (self.crane_current_xyz[0], self.crane_current_xyz[1], target_z)
        # print(f"Debug: Descended to Z={target_z:.2f}, Time+={time_descend:.2f}")

        # 3. Perform Pick or Place action (fixed time)
        self.total_time += PICK_PLACE_TIME
        # print(f"Debug: Performed {operation_type}, Time+={PICK_PLACE_TIME:.2f}")

        # 4. Ascend back to safe travel height
        time_ascend = abs(SAFE_TRAVEL_Z - target_z) / CRANE_SPEED_Z if CRANE_SPEED_Z > 0 else 0
        self.total_time += time_ascend
        self.crane_current_xyz = (self.crane_current_xyz[0], self.crane_current_xyz[1], SAFE_TRAVEL_Z)
        # print(f"Debug: Ascended to Z={SAFE_TRAVEL_Z:.2f}, Time+={time_ascend:.2f}")
        
        cycle_time = self.total_time - cycle_start_time
        return cycle_time


    def _find_nearby_temp_spot_dynamic(self, near_grid_pos):
        """
        Finds an empty spot on the top layer (z=5) near the original position (x, y).
        Searches radially outwards in X/Y on the top layer.
        Returns the grid_pos (x, y, z) of the empty temp spot, or None if none found within a reasonable range.
        """
        nx, ny, nz = near_grid_pos # Base position for search
        search_radius = 5 # How many cells out to check in X/Y

        # Check directly above on top layer first
        top_z = LAYERS - 1
        if 0 <= nx < COLS and 0 <= ny < ROWS:
             if self.warehouse_grid[top_z][ny][nx] is None:
                  # Ensure this spot isn't already used as a temp spot for another obstruction
                  is_used = False
                  for temp_pos in self.temp_obstruction_spots.values():
                      if temp_pos == (nx, ny, top_z):
                          is_used = True
                          break
                  if not is_used:
                      return (nx, ny, top_z)

        # Spiral search outwards on the top layer
        x, y = nx, ny
        dx, dy = 1, 0 # Initial direction
        steps_in_direction = 1
        direction_changes = 0
        
        for _ in range((2 * search_radius + 1)**2): # Max search iterations
            check_x, check_y = x, y
            
            # Check bounds and if empty
            if 0 <= check_x < COLS and 0 <= check_y < ROWS:
                if self.warehouse_grid[top_z][check_y][check_x] is None:
                    is_used = False
                    for temp_pos in self.temp_obstruction_spots.values():
                        if temp_pos == (check_x, check_y, top_z):
                            is_used = True
                            break
                    if not is_used:
                        # print(f"Debug: Found temp spot at {(check_x, check_y, top_z)}")
                        return (check_x, check_y, top_z) # Found suitable empty spot

            # Move to next position in spiral
            x += dx
            y += dy
            
            # Check if direction needs to change
            # Simple way: change direction after 'steps_in_direction' moves
            # This isn't a perfect spiral but explores outwards
            
            # A better spiral:
            # Logic from standard spiral algorithms needed here.
            # Let's use a simpler expanding box search for now:
            for r in range(1, search_radius + 1):
                 for deltax in range(-r, r + 1):
                      for deltay in range(-r, r + 1):
                           if abs(deltax) != r and abs(deltay) != r: continue # Only check perimeter of box

                           check_x = nx + deltax
                           check_y = ny + deltay
                           
                           if 0 <= check_x < COLS and 0 <= check_y < ROWS:
                               if self.warehouse_grid[top_z][check_y][check_x] is None:
                                   is_used = False
                                   for temp_pos in self.temp_obstruction_spots.values():
                                       if temp_pos == (check_x, check_y, top_z):
                                           is_used = True
                                           break
                                   if not is_used:
                                       # print(f"Debug: Found temp spot at {(check_x, check_y, top_z)}")
                                       return (check_x, check_y, top_z)

        print(f"Warning: Could not find nearby empty temp spot on top layer near {near_grid_pos} within radius {search_radius}.")
        # Fallback: Use a predefined "overflow" temp area? For simulation, assume one is found.
        # Or signal error. Let's return None to signal failure.
        return None


    def _handle_obstructions(self, target_grid_pos):
        """
        Manages the process of moving obstructing boxes temporarily and returning them.
        - Identifies boxes above the target.
        - Finds temporary spots dynamically for each obstruction.
        - Simulates the moves (Pick obstruction -> Place temp -> ...).
        - Returns a function `move_obstructions_back` to be called later.
        - Updates total time.
        """
        tx, ty, tz = target_grid_pos
        obstructions_to_move = [] # List of (ob_id, ob_original_pos)

        # Identify obstructions (boxes directly above the target)
        if tz < LAYERS - 1:
            for z_ob in range(tz + 1, LAYERS):
                obstructing_box_id = self.warehouse_grid[z_ob][ty][tx]
                if obstructing_box_id is not None:
                    obstructions_to_move.append((obstructing_box_id, (tx, ty, z_ob)))

        if not obstructions_to_move:
            # Return a dummy function if no obstructions
            return lambda: 0.0 

        # --- Move obstructions out (Top Down) ---
        # print(f"Debug: Handling {len(obstructions_to_move)} obstructions for target at {target_grid_pos}")
        self.temp_obstruction_spots.clear() # Clear previous temp assignments for this operation

        # Sort obstructions from top down (z descending)
        obstructions_to_move.sort(key=lambda item: item[1][2], reverse=True) 

        for ob_id, ob_pos in obstructions_to_move:
            # 1. Find a temporary spot (dynamically)
            temp_spot_grid_pos = self._find_nearby_temp_spot_dynamic(ob_pos)
            if temp_spot_grid_pos is None:
                 print(f"FATAL ERROR: No temporary spot found for obstruction {ob_id} at {ob_pos}. Aborting.")
                 # In a real system, this requires robust error handling or alternative strategies.
                 raise Exception("Failed to find temporary storage for obstruction.") 

            # print(f"Debug: Moving obstruction {ob_id} from {ob_pos} to temp {temp_spot_grid_pos}")

            # 2. Move crane above obstruction
            ob_pos_xyz = get_real_coords(ob_pos)
            above_ob_pos_xyz = (ob_pos_xyz[0], ob_pos_xyz[1], SAFE_TRAVEL_Z)
            self._move_crane(above_ob_pos_xyz)

            # 3. Pick the obstruction
            self._crane_pick_or_place(ob_pos, "Pick Obstruction")
            self.held_box_id = ob_id
            self.warehouse_grid[ob_pos[2]][ob_pos[1]][ob_pos[0]] = None # Mark original spot empty
            self.box_locations[ob_id] = None # Mark as held

            # 4. Move crane above temporary spot
            temp_spot_xyz = get_real_coords(temp_spot_grid_pos)
            above_temp_spot_xyz = (temp_spot_xyz[0], temp_spot_xyz[1], SAFE_TRAVEL_Z)
            self._move_crane(above_temp_spot_xyz)

            # 5. Place obstruction at temporary spot
            self._crane_pick_or_place(temp_spot_grid_pos, "Place Obstruction Temp")
            self.warehouse_grid[temp_spot_grid_pos[2]][temp_spot_grid_pos[1]][temp_spot_grid_pos[0]] = ob_id
            self.box_locations[ob_id] = temp_spot_grid_pos # Update location
            self.temp_obstruction_spots[ob_id] = temp_spot_grid_pos # Record where it went
            self.held_box_id = None # Crane is empty again

        # --- Target box is now accessible ---
        
        # --- Define function to move obstructions back ---
        def move_obstructions_back():
            cleanup_start_time = self.total_time
            # print(f"Debug: Moving obstructions back for original target {target_grid_pos}")
            
            # Move them back in reverse order of removal (bottom first -> original z ascending)
            obstructions_to_return = sorted(list(self.temp_obstruction_spots.items()), key=lambda item: self.box_original_locations[item[0]][2]) # Sort by original Z

            for ob_id, temp_spot_grid_pos in obstructions_to_return:
                 original_pos = self.box_original_locations[ob_id] # Get original pos
                 # print(f"Debug: Returning obstruction {ob_id} from {temp_spot_grid_pos} to {original_pos}")

                 # 1. Move crane above temp spot
                 temp_spot_xyz = get_real_coords(temp_spot_grid_pos)
                 above_temp_spot_xyz = (temp_spot_xyz[0], temp_spot_xyz[1], SAFE_TRAVEL_Z)
                 self._move_crane(above_temp_spot_xyz)

                 # 2. Pick from temporary spot
                 self._crane_pick_or_place(temp_spot_grid_pos, "Pick Obstruction from Temp")
                 self.held_box_id = ob_id
                 self.warehouse_grid[temp_spot_grid_pos[2]][temp_spot_grid_pos[1]][temp_spot_grid_pos[0]] = None # Mark temp spot empty
                 self.box_locations[ob_id] = None # Mark held

                 # 3. Move crane above original spot
                 original_pos_xyz = get_real_coords(original_pos)
                 above_original_pos_xyz = (original_pos_xyz[0], original_pos_xyz[1], SAFE_TRAVEL_Z)
                 self._move_crane(above_original_pos_xyz)

                 # 4. Place back in original spot
                 self._crane_pick_or_place(original_pos, "Place Obstruction Back")
                 self.warehouse_grid[original_pos[2]][original_pos[1]][original_pos[0]] = ob_id
                 self.box_locations[ob_id] = original_pos # Restore location
                 self.held_box_id = None
                 # print(f"Debug: Returned {ob_id} to {original_pos}. Held: {self.held_box_id}")

            self.temp_obstruction_spots.clear() # Clear temp assignments after returning
            cleanup_time = self.total_time - cleanup_start_time
            # print(f"Debug: Obstruction return time: {cleanup_time:.2f}")
            return cleanup_time

        # Return the function that performs the cleanup
        return move_obstructions_back


    def _generate_tasks(self, num_tasks):
        """
        Generates a list of unique box IDs for tasks based on flow probabilities.
        Selects category based on flow ratio, then selects a unique box from that category.
        """
        print(f"Generating {num_tasks} unique tasks...")
        tasks = []
        
        # Create lists of available boxes per category
        boxes_in_category = {cat: [] for cat in self.categories}
        for box_id, cat in self.box_categories.items():
             # Ensure box exists in grid before adding
             if self.box_locations.get(box_id):
                 boxes_in_category[cat].append(box_id)
        
        available_boxes_in_category = {cat: list(ids) for cat, ids in boxes_in_category.items()}
        total_available_boxes = sum(len(ids) for ids in available_boxes_in_category.values())
        
        if num_tasks > total_available_boxes:
            print(f"Warning: Requested {num_tasks} tasks, but only {total_available_boxes} boxes are available. Generating {total_available_boxes} tasks instead.")
            num_tasks = total_available_boxes

        # Prepare for weighted random choice of category
        category_choices = list(self.flow_ratios.keys())
        category_weights = [self.flow_ratios[cat] for cat in category_choices]
        
        if not math.isclose(sum(category_weights), 1.0):
             print("Warning: Normalizing category flow weights for task generation.")
             weight_sum = sum(category_weights)
             if weight_sum > 0:
                  category_weights = [w / weight_sum for w in category_weights]
             else: # Handle zero weights case - uniform probability
                  category_weights = [1.0 / len(category_choices)] * len(category_choices)


        attempt_count = 0
        max_attempts = num_tasks * 5 # Safety break for generation loop

        while len(tasks) < num_tasks and attempt_count < max_attempts:
            attempt_count += 1
            # 1. Choose a category based on flow probabilities
            chosen_category = random.choices(category_choices, weights=category_weights, k=1)[0]

            # 2. Check if boxes are available in this category
            if available_boxes_in_category[chosen_category]:
                # 3. Choose a box uniformly from the available ones in that category
                chosen_box_id = random.choice(available_boxes_in_category[chosen_category])
                
                # 4. Add to tasks and remove from available list
                tasks.append(chosen_box_id)
                available_boxes_in_category[chosen_category].remove(chosen_box_id)
            else:
                # No more boxes available in this category. 
                # Re-sample category? Or adjust weights? Let's just re-sample.
                 # print(f"Debug: No more available boxes in category {chosen_category}. Re-sampling category.")
                 # To avoid infinite loops if weights are skewed and popular categories empty fast,
                 # we could temporarily set the weight of the empty category to 0 and renormalize,
                 # but the attempt limit should handle this reasonably for now.
                 pass

        if len(tasks) < num_tasks:
             print(f"Warning: Could only generate {len(tasks)} tasks after {max_attempts} attempts. There might be issues with availability or weights.")

        print(f"Generated {len(tasks)} tasks.")
        return tasks

    # --- Main Simulation Logic ---
    def run_simulation(self, num_tasks):
        """
        Runs the full warehouse simulation for the specified number of tasks,
        following the detailed logic from the prompt.
        """
        self.tasks = self._generate_tasks(num_tasks)
        actual_num_tasks = len(self.tasks) # Use the number of tasks actually generated
        if actual_num_tasks == 0:
            print("No tasks generated, simulation cannot run.")
            return 0, []

        print(f"\n--- Running Simulation: {self.name} for {actual_num_tasks} tasks ---")
        
        # State variable for the box currently waiting at the workstation
        box_at_workstation = None 

        for i in range(actual_num_tasks):
            task_start_time = self.total_time # Time at the beginning of this task cycle
            current_task_box_id = self.tasks[i]
            current_task_category = self._get_box_category(current_task_box_id)
            current_task_pos = self._get_box_location(current_task_box_id) # Get current location

            if current_task_pos is None:
                 print(f"Error: Box {current_task_box_id} for task {i+1} has no location (maybe already held or issue?). Skipping task.")
                 continue

            print(f"\n=== Task {i+1}/{actual_num_tasks}: Target Box {current_task_box_id} (Cat: {current_task_category}) at {current_task_pos} ===")

            # --- Step 1: Pick Previous Box from Workstation (if applicable) ---
            box_to_return_id = box_at_workstation # Box from task i-1 waiting at WS
            if box_to_return_id:
                print(f"Step 1: Pick Box {box_to_return_id} from Workstation.")
                # Move above workstation
                self._move_crane((self.workstation_interaction_xyz[0], self.workstation_interaction_xyz[1], SAFE_TRAVEL_Z))
                # Perform pick cycle
                self._crane_pick_or_place(WORKSTATION_GRID_POS, "Pick Prev Box from WS")
                self.held_box_id = box_to_return_id
                box_at_workstation = None # WS is now empty
            else:
                # First task, crane is empty, potentially needs to move from start pos
                if i == 0:
                     # Move from initial start pos (above start column) if needed
                     # The first action will be moving towards the first target anyway.
                     print("Step 1: First task, crane starts empty.")
                     pass # Crane is empty, no pickup needed

            # --- Step 2: Determine & Execute Return Strategy for Held Box (box_to_return_id) ---
            if self.held_box_id: # If we are holding the box from the previous task
                prev_box_id = self.held_box_id # Alias for clarity
                prev_box_category = self._get_box_category(prev_box_id)
                
                # Compare with the *current* task's category
                if prev_box_category == current_task_category:
                    # **Same Category:** Move towards current task's location, holding the previous box.
                    # The actual placement happens later.
                    print(f"Step 2: Same Category ({current_task_category}). Moving with Box {prev_box_id} towards target {current_task_pos}.")
                    target_pos_xyz = get_real_coords(current_task_pos)
                    above_target_xyz = (target_pos_xyz[0], target_pos_xyz[1], SAFE_TRAVEL_Z)
                    self._move_crane(above_target_xyz)
                    # Placement deferred. Crane arrives above target column, holding prev_box_id.
                    
                else:
                    # **Different Category:** Find empty slot in prev_box's zone and place it there now.
                    print(f"Step 2: Different Category. Returning Box {prev_box_id} (Cat: {prev_box_category}) to its zone.")
                    return_pos = self._find_empty_slot_in_zone(prev_box_category)
                    if return_pos is None:
                        raise Exception(f"Failed to find return slot for different category box {prev_box_id}")
                    
                    print(f"  - Moving Box {prev_box_id} to empty slot {return_pos}")
                    # Move above return slot
                    return_pos_xyz = get_real_coords(return_pos)
                    above_return_pos_xyz = (return_pos_xyz[0], return_pos_xyz[1], SAFE_TRAVEL_Z)
                    self._move_crane(above_return_pos_xyz)
                    
                    # Perform place cycle
                    self._crane_pick_or_place(return_pos, "Place Prev Box (Diff Cat)")
                    self.warehouse_grid[return_pos[2]][return_pos[1]][return_pos[0]] = prev_box_id
                    self.box_locations[prev_box_id] = return_pos # Update location
                    self.held_box_id = None # Crane is now empty
                    print(f"  - Box {prev_box_id} returned. Crane is empty above {return_pos}.")

            # --- Step 3: Move Crane to Target Box Location (if not already there) ---
            # If crane moved to return a box (Diff Cat), it needs to move to the current target.
            # If Same Cat, crane should already be above the target column (from Step 2).
            target_pos_xyz = get_real_coords(current_task_pos)
            above_target_xyz = (target_pos_xyz[0], target_pos_xyz[1], SAFE_TRAVEL_Z)
            if self.crane_current_xyz != above_target_xyz:
                 print(f"Step 3: Moving empty crane to target column {current_task_pos}.")
                 self._move_crane(above_target_xyz)
            
            # --- Step 4: Handle Obstructions for Current Target ---
            print(f"Step 4: Handling obstructions above {current_task_pos}.")
            move_obstructions_back_func = self._handle_obstructions(current_task_pos)
            # Crane is above target column, obstructions (if any) moved temporarily.

            # --- Step 5: Perform Pick/Swap based on Category ---
            original_target_pos = current_task_pos # Save the exact slot
            
            if self.held_box_id: # Implies Same Category scenario (holding prev_box_id)
                # **Same Category Swap:** "取出第二个任务箱子后把第一个任务箱子放到腾出的位置"
                # Assumes a 'magic swap' capability or uses simplified timing.
                # We need to pick current_task_box_id and place self.held_box_id (prev_box_id).
                print(f"Step 5: Same Category - Swapping Box {self.held_box_id} into {original_target_pos} while picking {current_task_box_id}.")
                
                # Simulate the combined time: Descend + Pick + Place + Ascend
                # Use _crane_pick_or_place twice for timing approximation? Or one cycle + extra fixed time?
                # Let's use time for one pick cycle + one place action time.
                
                swap_time_start = self.total_time
                
                # Descend
                target_center_xyz = get_real_coords(original_target_pos)
                time_descend = abs(target_center_xyz[2] - SAFE_TRAVEL_Z) / CRANE_SPEED_Z if CRANE_SPEED_Z > 0 else 0
                self.total_time += time_descend
                self.crane_current_xyz = (self.crane_current_xyz[0], self.crane_current_xyz[1], target_center_xyz[2])

                # Pick Current + Place Previous Actions
                self.total_time += PICK_PLACE_TIME # For picking current_task_box_id
                self.total_time += PICK_PLACE_TIME # For placing self.held_box_id
                
                # Ascend
                time_ascend = abs(SAFE_TRAVEL_Z - target_center_xyz[2]) / CRANE_SPEED_Z if CRANE_SPEED_Z > 0 else 0
                self.total_time += time_ascend
                self.crane_current_xyz = (self.crane_current_xyz[0], self.crane_current_xyz[1], SAFE_TRAVEL_Z)
                
                # Update State:
                box_placed_in_slot = self.held_box_id # The previous box goes into the slot
                self.warehouse_grid[original_target_pos[2]][original_target_pos[1]][original_target_pos[0]] = box_placed_in_slot
                self.box_locations[box_placed_in_slot] = original_target_pos
                
                self.held_box_id = current_task_box_id # Crane now holds the current task box
                self.box_locations[current_task_box_id] = None # Mark as held
                
                print(f"  - Swap complete. Slot {original_target_pos} has Box {box_placed_in_slot}. Crane holds Box {self.held_box_id}.")
                
            else: # Implies Different Category or First Task (crane is empty)
                # **Normal Pick:**
                print(f"Step 5: Different Category/First Task - Picking Box {current_task_box_id} from {original_target_pos}.")
                self._crane_pick_or_place(original_target_pos, "Pick Target Box")
                self.held_box_id = current_task_box_id
                self.warehouse_grid[original_target_pos[2]][original_target_pos[1]][original_target_pos[0]] = None # Slot is now empty
                self.box_locations[current_task_box_id] = None # Mark as held
                print(f"  - Pick complete. Crane holds Box {self.held_box_id}.")

            # --- Step 6: Return Obstructions ---
            print(f"Step 6: Returning obstructions for {original_target_pos}.")
            move_obstructions_back_func() # Execute the cleanup function
            # Crane is above the (now possibly filled by prev box) target column, holding current_task_box_id.

            # --- Step 7: Move Held Box (current_task_box_id) to Workstation ---
            print(f"Step 7: Moving Box {self.held_box_id} to Workstation.")
            # Move crane above workstation
            above_ws_xyz = (self.workstation_interaction_xyz[0], self.workstation_interaction_xyz[1], SAFE_TRAVEL_Z)
            self._move_crane(above_ws_xyz)

            # --- Step 8: Place Held Box at Workstation ---
            print(f"Step 8: Placing Box {self.held_box_id} at Workstation.")
            self._crane_pick_or_place(WORKSTATION_GRID_POS, "Place Target Box at WS")
            box_at_workstation = self.held_box_id # This box is now waiting at the WS
            self.held_box_id = None # Crane is empty
            print(f"  - Box {box_at_workstation} delivered. Crane empty above WS.")

            # --- Task Cycle End ---
            task_end_time = self.total_time
            task_duration = task_end_time - task_start_time
            self.task_times.append(task_duration)
            print(f"--- Task {i+1} Cycle Time: {task_duration:.2f} seconds ---")

        # --- Simulation End: Handle the very last box left at workstation ---
        if box_at_workstation:
            print("\n=== Post-Simulation: Returning last box from Workstation ===")
            last_box_id = box_at_workstation
            last_box_category = self._get_box_category(last_box_id)
            
            # 1. Pick from Workstation
            print(f"Step Final.1: Picking Box {last_box_id} from Workstation.")
            above_ws_xyz = (self.workstation_interaction_xyz[0], self.workstation_interaction_xyz[1], SAFE_TRAVEL_Z)
            self._move_crane(above_ws_xyz) # Ensure above WS
            self._crane_pick_or_place(WORKSTATION_GRID_POS, "Pick Final Box")
            self.held_box_id = last_box_id
            
            # 2. Find empty slot in its zone
            return_pos = self._find_empty_slot_in_zone(last_box_category)
            if return_pos is None:
                # Fallback: return to original location if possible? This shouldn't happen ideally.
                print(f"Warning: No empty slot found for final return of {last_box_id}. Trying original slot {self.box_original_locations.get(last_box_id)}")
                return_pos = self.box_original_locations.get(last_box_id)
                if return_pos is None or self.warehouse_grid[return_pos[2]][return_pos[1]][return_pos[0]] is not None:
                     raise Exception(f"Cannot find any return slot for final box {last_box_id}")

            # 3. Move to return slot and place
            print(f"Step Final.2: Returning Box {last_box_id} to slot {return_pos}.")
            return_pos_xyz = get_real_coords(return_pos)
            above_return_pos_xyz = (return_pos_xyz[0], return_pos_xyz[1], SAFE_TRAVEL_Z)
            self._move_crane(above_return_pos_xyz)
            self._crane_pick_or_place(return_pos, "Place Final Box")
            self.warehouse_grid[return_pos[2]][return_pos[1]][return_pos[0]] = last_box_id
            self.box_locations[last_box_id] = return_pos
            self.held_box_id = None
            print("Step Final.3: Final box returned.")


        print(f"\n--- Simulation {self.name} Finished ---")
        print(f"Total Time for {actual_num_tasks} tasks: {self.total_time:.2f} seconds")
        if self.task_times:
             avg_time = sum(self.task_times) / len(self.task_times)
             print(f"Average Time per task: {avg_time:.2f} seconds")
        return self.total_time, self.task_times


# --- Simulation Setup and Execution ---

# Define parameters for each scenario
scenarios = [
    {
        "name": "ABC",
        "quantity_ratios": {'A': 0.15, 'B': 0.30, 'C': 0.55},
        "flow_ratios": {'A': 0.50, 'B': 0.35, 'C': 0.15}
    },
    {
        "name": "AB",
        "quantity_ratios": {'A': 0.30, 'B': 0.70},
        "flow_ratios": {'A': 0.70, 'B': 0.30}
    },
    {
        "name": "ABCD",
        "quantity_ratios": {'A': 0.10, 'B': 0.20, 'C': 0.30, 'D': 0.40},
        "flow_ratios": {'A': 0.40, 'B': 0.30, 'C': 0.20, 'D': 0.10}
    },
    {
        "name": "ABCDE",
        "quantity_ratios": {'A': 0.10, 'B': 0.15, 'C': 0.20, 'D': 0.25, 'E': 0.30},
        "flow_ratios": {'A': 0.30, 'B': 0.25, 'C': 0.20, 'D': 0.15, 'E': 0.10}
    }
]

NUM_TASKS_TO_SIMULATE = 1000 # Number of tasks to simulate
results = {}

# Optional: Set a random seed for reproducibility
random.seed(42) 
np.random.seed(42)

start_run_time = time.time()

for params in scenarios:
    sim_start_time = time.time()
    # Create and run the simulation instance
    sim = WarehouseSimulation(params["name"], params["quantity_ratios"], params["flow_ratios"])
    total_time, task_times = sim.run_simulation(NUM_TASKS_TO_SIMULATE)
    
    # Store results
    results[params["name"]] = {
        "total_time": total_time,
        "avg_task_time": total_time / len(task_times) if task_times else 0,
        "task_times": task_times # Store individual times if needed for analysis
    }
    sim_end_time = time.time()
    print(f"Simulation '{params['name']}' execution time: {sim_end_time - sim_start_time:.2f} seconds")


end_run_time = time.time()

# --- Print Summary ---
print("\n\n--- Simulation Results Summary ---")
for name, res in results.items():
    print(f"Scenario: {name}")
    print(f"  Total Time ({len(res['task_times'])} tasks): {res['total_time']:.2f} seconds")
    # Convert total time to hours/minutes for context
    total_seconds = res['total_time']
    hours = int(total_seconds // 3600)
    minutes = int((total_seconds % 3600) // 60)
    seconds = total_seconds % 60
    print(f"  Total Time: {hours}h {minutes}m {seconds:.2f}s")
    print(f"  Avg Task Time: {res['avg_task_time']:.2f} seconds")
    print("-" * 30)
    
print(f"\nTotal script execution time: {end_run_time - start_run_time:.2f} seconds")