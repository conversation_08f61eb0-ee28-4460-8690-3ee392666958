"""
Test script for the warehouse simulation.
This script tests the basic functionality of the warehouse simulation.
"""

import numpy as np
import matplotlib.pyplot as plt

# Constants
COLUMNS = 40  # Length
ROWS = 20     # Width
LAYERS = 6    # Height
TOTAL_BOXES = COLUMNS * ROWS * LAYERS  # 4800 boxes

def main():
    """Test the basic functionality."""
    print("Testing warehouse simulation...")
    
    # Create a simple 2D grid for visualization
    grid = np.zeros((ROWS, COLUMNS))
    
    # Fill the grid with random values (1, 2, 3) representing box types
    for y in range(ROWS):
        for x in range(COLUMNS):
            grid[y, x] = np.random.randint(1, 4)
    
    # Plot the grid
    plt.figure(figsize=(10, 6))
    plt.imshow(grid, cmap='tab10', interpolation='nearest')
    plt.colorbar(ticks=[1, 2, 3], label='Box Type')
    plt.title('Warehouse Layout (Top Layer)')
    plt.xlabel('Column')
    plt.ylabel('Row')
    
    # Mark the workbench position
    workbench_x, workbench_y = COLUMNS // 2, 0
    plt.plot(workbench_x, workbench_y, 'k*', markersize=15, label='Workbench')
    
    # Mark the cart starting position
    cart_x, cart_y = COLUMNS // 2, 0
    plt.plot(cart_x, cart_y, 'ko', markersize=15, label='Cart Start')
    
    plt.legend()
    plt.tight_layout()
    plt.savefig('python_fangzhen/test_layout.png')
    plt.close()
    
    print("Test completed. Check the test_layout.png file.")

if __name__ == "__main__":
    main()
