"""
仓库模拟

本脚本模拟一个多层仓库，其中有一个可移动的小车用于根据生成的订单取回和返回箱子。
模拟计算在不同类别分布下完成1000个任务所需的总时间。

仓库规格:
- 40列（长）× 20排（宽）× 6层（高）= 4800个箱子
- 箱子尺寸：长0.8米、宽0.6米、高0.33米
- 小车速度：x轴和y轴方向为3米/秒，z轴方向升降速度为1.6米/秒
- 抓取和放下箱子的固定时间为2.5秒
"""

import numpy as np
import random
import math
import time
from collections import deque
import os

# 尝试导入matplotlib，如果失败则继续但不进行可视化
try:
    import matplotlib.pyplot as plt
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    print("警告: matplotlib不可用。将跳过可视化部分。")
    MATPLOTLIB_AVAILABLE = False

# 常量
COLUMNS = 40  # 长度
ROWS = 20     # 宽度
LAYERS = 6    # 高度
TOTAL_BOXES = COLUMNS * ROWS * LAYERS  # 4800个箱子

# 箱子尺寸（米）
BOX_LENGTH = 0.8
BOX_WIDTH = 0.6
BOX_HEIGHT = 0.33

# 小车速度（米/秒）
CART_SPEED_XY = 3.0
CART_SPEED_Z = 1.6
GRAB_RELEASE_TIME = 2.5  # 秒

# 订单生成
ORDERS_PER_MINUTE = 4  # 泊松分布均值

# 模拟任务数量
NUM_TASKS = 1000

class Box:
    """表示仓库中的一个箱子。"""

    def __init__(self, box_id, box_type, position):
        """
        初始化一个箱子。

        参数:
            box_id (int): 箱子的唯一标识符
            box_type (str): 箱子的类型 (A, B, C, 等)
            position (tuple): 箱子在仓库中的位置 (x, y, z)
        """
        self.box_id = box_id
        self.box_type = box_type
        self.position = position
        self.original_position = position
        self.in_workbench = False
        self.being_carried = False

class Warehouse:
    """表示仓库结构和操作。"""

    def __init__(self, config):
        """
        初始化仓库。

        参数:
            config (dict): 箱子类型、数量和流动率的配置
        """
        self.columns = COLUMNS
        self.rows = ROWS
        self.layers = LAYERS
        self.box_types = config['box_types']
        self.type_quantities = config['type_quantities']
        self.flow_rates = config['flow_rates']

        # Calculate box counts and ranges for each type
        self.box_counts = {}
        self.box_ranges = {}
        start_id = 1

        for box_type, quantity_pct in self.type_quantities.items():
            count = int(TOTAL_BOXES * quantity_pct)
            self.box_counts[box_type] = count
            self.box_ranges[box_type] = (start_id, start_id + count - 1)
            start_id += count

        # Adjust the last type to ensure total is exactly TOTAL_BOXES
        last_type = list(self.type_quantities.keys())[-1]
        total_allocated = sum(self.box_counts.values())
        if total_allocated != TOTAL_BOXES:
            self.box_counts[last_type] += (TOTAL_BOXES - total_allocated)
            self.box_ranges[last_type] = (
                self.box_ranges[last_type][0],
                self.box_ranges[last_type][0] + self.box_counts[last_type] - 1
            )

        # Create the grid and assign boxes
        self.grid = np.zeros((COLUMNS, ROWS, LAYERS), dtype=object)
        self.boxes = {}
        self.initialize_boxes()

        # Workbench position (bottom layer, middle of length, same side as cart)
        self.workbench_position = (COLUMNS // 2, 0, 0)

        # Cart starting position (top layer, middle of length)
        self.cart_start_position = (COLUMNS // 2, 0, LAYERS - 1)

        # Track empty positions for each box type
        self.empty_positions = {box_type: [] for box_type in self.box_types}

    def initialize_boxes(self):
        """初始化仓库网格中的箱子。"""
        # 计算每种类型的箱子数量
        box_counts = {}
        remaining_boxes = TOTAL_BOXES

        for box_type, quantity_pct in self.type_quantities.items():
            count = int(TOTAL_BOXES * quantity_pct)
            box_counts[box_type] = count
            remaining_boxes -= count

        # 调整最后一种类型以解决舍入误差
        if remaining_boxes != 0:
            last_type = list(self.type_quantities.keys())[-1]
            box_counts[last_type] += remaining_boxes

        # 根据流动率确定区域边界（流动率越高 = 越靠近工作台）
        # 按流动率排序箱子类型（降序）
        sorted_types = sorted(self.box_types, key=lambda t: self.flow_rates[t], reverse=True)

        # 将仓库分成区域
        # 我们将沿着y轴（宽度）划分仓库，因为工作台位于y=0
        total_rows = ROWS
        zone_boundaries = []
        remaining_rows = total_rows

        for i, box_type in enumerate(sorted_types):
            if i == len(sorted_types) - 1:
                # 最后一种类型获取所有剩余行
                rows_for_type = remaining_rows
            else:
                # 根据数量百分比计算行数
                rows_for_type = max(1, int(total_rows * self.type_quantities[box_type]))
                remaining_rows -= rows_for_type

            start_row = sum(b[1] for b in zone_boundaries)
            zone_boundaries.append((box_type, rows_for_type, start_row))

        # 分配箱子到位置
        box_id = 1

        # 对于每个区域
        for box_type, num_rows, start_row in zone_boundaries:
            boxes_to_place = box_counts[box_type]
            boxes_placed = 0

            # 在此区域放置箱子
            for y in range(start_row, start_row + num_rows):
                for x in range(COLUMNS):
                    for z in range(LAYERS):
                        if boxes_placed < boxes_to_place:
                            position = (x, y, z)
                            box = Box(box_id, box_type, position)
                            self.grid[x, y, z] = box
                            self.boxes[box_id] = box
                            box_id += 1
                            boxes_placed += 1
                        else:
                            break
                    if boxes_placed >= boxes_to_place:
                        break
                if boxes_placed >= boxes_to_place:
                    break

            # 更新此类型的箱子范围
            self.box_ranges[box_type] = (box_id - boxes_placed, box_id - 1)

    def get_box_position(self, box_id):
        """获取箱子的当前位置。"""
        if box_id in self.boxes:
            return self.boxes[box_id].position
        return None

    def get_box_at_position(self, position):
        """获取特定位置的箱子。"""
        x, y, z = position
        if 0 <= x < COLUMNS and 0 <= y < ROWS and 0 <= z < LAYERS:
            return self.grid[x, y, z]
        return None

    def remove_box(self, position):
        """从指定位置移除箱子并返回它。"""
        x, y, z = position
        box = self.grid[x, y, z]
        if box:
            self.grid[x, y, z] = 0
            return box
        return None

    def place_box(self, box, position):
        """在指定位置放置箱子。"""
        x, y, z = position
        if 0 <= x < COLUMNS and 0 <= y < ROWS and 0 <= z < LAYERS:
            if self.grid[x, y, z] == 0:  # Position is empty
                self.grid[x, y, z] = box
                box.position = position
                return True
        return False

    def find_empty_position(self, box_type):
        """为特定类型的箱子在适当的区域中找到一个空位置。"""
        # 如果我们有缓存的空位置，使用其中一个
        if self.empty_positions[box_type]:
            return self.empty_positions[box_type].pop()

        # 获取此箱子类型的ID范围
        start_id, end_id = self.box_ranges[box_type]

        # 确定此箱子类型的区域边界
        # 我们将使用一种简单的方法：找到这种类型箱子的最小和最大位置
        min_x, min_y, min_z = COLUMNS, ROWS, LAYERS
        max_x, max_y, max_z = 0, 0, 0

        for box_id in range(start_id, end_id + 1):
            if box_id in self.boxes:
                box = self.boxes[box_id]
                x, y, z = box.position
                min_x = min(min_x, x)
                min_y = min(min_y, y)
                min_z = min(min_z, z)
                max_x = max(max_x, x)
                max_y = max(max_y, y)
                max_z = max(max_z, z)

        # 如果找不到这种类型的箱子，返回None
        if min_x > max_x:
            return None

        # 在区域边界内搜索空位置
        for z in range(min_z, max_z + 1):
            for y in range(min_y, max_y + 1):
                for x in range(min_x, max_x + 1):
                    if 0 <= x < COLUMNS and 0 <= y < ROWS and 0 <= z < LAYERS and self.grid[x, y, z] == 0:
                        return (x, y, z)

        # 如果在确切区域内找不到空位置，寻找区域附近的位置
        search_radius = 1
        while search_radius < max(COLUMNS, ROWS, LAYERS):
            # 扩大搜索区域
            for z in range(max(0, min_z - search_radius), min(LAYERS, max_z + search_radius + 1)):
                for y in range(max(0, min_y - search_radius), min(ROWS, max_y + search_radius + 1)):
                    for x in range(max(0, min_x - search_radius), min(COLUMNS, max_x + search_radius + 1)):
                        # 只检查扩展区域周边的位置
                        if (x == min_x - search_radius or x == max_x + search_radius or
                            y == min_y - search_radius or y == max_y + search_radius or
                            z == min_z - search_radius or z == max_z + search_radius):
                            if self.grid[x, y, z] == 0:
                                return (x, y, z)

            search_radius += 1

        # 如果仍然找不到空位置，在整个仓库中寻找任何空位置
        for z in range(LAYERS):
            for y in range(ROWS):
                for x in range(COLUMNS):
                    if self.grid[x, y, z] == 0:
                        return (x, y, z)

        return None

    def add_empty_position(self, position, box_type):
        """为特定类型的箱子将空位置添加到缓存中。"""
        self.empty_positions[box_type].append(position)

    def get_boxes_above(self, position):
        """获取给定位置上方的所有箱子。"""
        x, y, z = position
        boxes_above = []

        for z_above in range(z + 1, LAYERS):
            box = self.grid[x, y, z_above]
            if box != 0:
                boxes_above.append(box)

        return boxes_above

    def find_nearby_empty_positions(self, position, count):
        """在给定位置附近找到空位置。"""
        x, y, z = position
        empty_positions = []

        # Search in expanding circles around the position
        for radius in range(1, max(COLUMNS, ROWS)):
            for dx in range(-radius, radius + 1):
                for dy in range(-radius, radius + 1):
                    # Only consider positions on the perimeter of the circle
                    if abs(dx) == radius or abs(dy) == radius:
                        nx, ny = x + dx, y + dy
                        if 0 <= nx < COLUMNS and 0 <= ny < ROWS:
                            # Check all layers
                            for nz in range(LAYERS):
                                if self.grid[nx, ny, nz] == 0:
                                    empty_positions.append((nx, ny, nz))
                                    if len(empty_positions) >= count:
                                        return empty_positions

        return empty_positions

class Cart:
    """表示仓库中的可移动小车。"""

    def __init__(self, warehouse):
        """
        初始化小车。

        参数:
            warehouse (Warehouse): 小车操作的仓库
        """
        self.warehouse = warehouse
        self.position = warehouse.cart_start_position
        self.carrying_box = None
        self.total_time = 0  # 总操作时间（秒）

    def calculate_travel_time(self, start_pos, end_pos):
        """计算两个位置之间的移动时间。"""
        x1, y1, z1 = start_pos
        x2, y2, z2 = end_pos

        # 计算每个维度的距离
        x_distance = abs(x2 - x1) * BOX_LENGTH
        y_distance = abs(y2 - y1) * BOX_WIDTH
        z_distance = abs(z2 - z1) * BOX_HEIGHT

        # 计算水平移动（x和y）的时间
        xy_distance = math.sqrt(x_distance**2 + y_distance**2)
        xy_time = xy_distance / CART_SPEED_XY

        # 计算垂直移动（z）的时间
        z_time = z_distance / CART_SPEED_Z

        # 总时间是水平和垂直时间的最大值（它们是并行发生的）
        # 加上抓取/释放的固定时间
        return max(xy_time, z_time)

    def move_to(self, position):
        """将小车移动到指定位置并更新总时间。"""
        travel_time = self.calculate_travel_time(self.position, position)
        self.total_time += travel_time
        self.position = position
        return travel_time

    def grab_box(self, position):
        """从指定位置抓取箱子。"""
        # 移动到指定位置
        self.move_to(position)

        # 抓取箱子
        box = self.warehouse.remove_box(position)
        if box:
            self.carrying_box = box
            box.being_carried = True
            self.total_time += GRAB_RELEASE_TIME  # 抓取时间
            return box

        return None

    def release_box(self, position):
        """在指定位置释放携带的箱子。"""
        if not self.carrying_box:
            return False

        # 移动到指定位置
        self.move_to(position)

        # 释放箱子
        if self.warehouse.place_box(self.carrying_box, position):
            self.carrying_box.being_carried = False
            self.carrying_box = None
            self.total_time += GRAB_RELEASE_TIME  # 释放时间
            return True

        return False

    def handle_obstacles(self, target_position):
        """处理障碍物（目标箱子上方的箱子）。"""
        boxes_above = self.warehouse.get_boxes_above(target_position)
        if not boxes_above:
            return []  # 没有障碍物，返回空列表

        temp_positions = []

        # 为临时存储寻找附近的空位置
        empty_positions = self.warehouse.find_nearby_empty_positions(target_position, len(boxes_above))
        if len(empty_positions) < len(boxes_above):
            # 附近没有足够的空位置
            return -1

        # 将障碍物移动到临时位置
        for i, box in enumerate(boxes_above):
            temp_pos = empty_positions[i]
            original_pos = box.position

            # 抓取障碍箱子
            self.grab_box(original_pos)

            # 在临时位置释放它
            self.release_box(temp_pos)

            # 记住箱子及其临时位置
            temp_positions.append((box, temp_pos, original_pos))

        return temp_positions

    def restore_obstacles(self, temp_positions):
        """将障碍物恢复到原来的位置。"""
        # 按相反的顺序将障碍物移回原来的位置
        for box, temp_pos, original_pos in reversed(temp_positions):
            # 从临时位置抓取箱子
            self.grab_box(temp_pos)

            # 在其原始位置释放它
            self.release_box(original_pos)

class Simulation:
    """管理仓库模拟。"""

    def __init__(self, config):
        """
        初始化模拟。

        参数:
            config (dict): 箱子类型、数量和流动率的配置
        """
        self.config = config
        self.warehouse = Warehouse(config)
        self.cart = Cart(self.warehouse)
        self.orders = []
        self.completed_tasks = 0
        self.total_time = 0
        self.task_times = []

    def generate_orders(self):
        """根据流动率生成订单。"""
        # 计算每种类型箱子的订单数量
        type_orders = {}
        for box_type, flow_rate in self.config['flow_rates'].items():
            type_orders[box_type] = int(NUM_TASKS * flow_rate)

        # 调整以确保总数正好是NUM_TASKS
        total_orders = sum(type_orders.values())
        if total_orders != NUM_TASKS:
            last_type = list(type_orders.keys())[-1]
            type_orders[last_type] += (NUM_TASKS - total_orders)

        # 为每种类型生成箱子ID
        orders = []
        for box_type, num_orders in type_orders.items():
            start_id, end_id = self.warehouse.box_ranges[box_type]
            box_ids = list(range(start_id, end_id + 1))

            # 确保我们不会请求比存在的箱子更多的箱子
            if num_orders > len(box_ids):
                num_orders = len(box_ids)

            # 随机选择这种类型的箱子
            selected_ids = random.sample(box_ids, num_orders)
            orders.extend(selected_ids)

        # 打乱订单
        random.shuffle(orders)
        self.orders = orders

    def execute_task(self, box_id):
        """执行特定箱子的任务。"""
        start_time = self.cart.total_time

        # 获取箱子及其位置
        box = self.warehouse.boxes.get(box_id)
        if not box:
            return 0

        box_position = box.position
        box_type = box.box_type

        # 如果我们从上一个任务中携带箱子
        if self.carrying_previous_box:
            previous_box = self.carrying_previous_box
            previous_box_type = previous_box.box_type

            # 如果上一个箱子与当前箱子是同一类型
            if previous_box_type == box_type:
                # 检查目标箱子上方是否有障碍物
                temp_positions = self.cart.handle_obstacles(box_position)
                if temp_positions == -1:
                    # 无法处理障碍物，在同一区域寻找空位置
                    empty_pos = self.warehouse.find_empty_position(box_type)
                    if not empty_pos:
                        # 找不到空位置，将上一个箱子返回到其原始位置
                        self.cart.release_box(previous_box.original_position)
                        self.carrying_previous_box = None

                        # 为当前箱子开始新任务
                        return self.execute_task(box_id)

                    # 将上一个箱子放在空位置
                    self.cart.release_box(empty_pos)
                    self.carrying_previous_box = None

                    # 为当前箱子开始新任务
                    return self.execute_task(box_id)

                # 抓取目标箱子
                self.cart.grab_box(box_position)

                # 如果有障碍物，恢复它们
                if temp_positions and temp_positions != 0:
                    self.cart.restore_obstacles(temp_positions)

                # 将上一个箱子放在当前箱子的原始位置
                self.cart.release_box(box_position)

                # 带着当前箱子移动到工作台
                self.cart.move_to(self.warehouse.workbench_position)

                # 在工作台释放当前箱子
                self.cart.release_box(self.warehouse.workbench_position)

                # 将当前箱子设置为下一个任务的携带箱子
                self.carrying_previous_box = box
            else:
                # 不同类型的箱子 - 需要先返回上一个箱子
                # 在其区域中为上一个箱子寻找空位置
                empty_pos = self.warehouse.find_empty_position(previous_box_type)
                if not empty_pos:
                    # 找不到空位置，返回到原始位置
                    empty_pos = previous_box.original_position

                # 将上一个箱子放在空位置
                self.cart.release_box(empty_pos)
                self.carrying_previous_box = None

                # 现在处理当前箱子作为新任务
                # 检查目标箱子上方是否有障碍物
                temp_positions = self.cart.handle_obstacles(box_position)
                if temp_positions == -1:
                    # 无法处理障碍物
                    return 0

                # 抓取目标箱子
                self.cart.grab_box(box_position)

                # 如果有障碍物，恢复它们
                if temp_positions and temp_positions != 0:
                    self.cart.restore_obstacles(temp_positions)

                # 移动到工作台
                self.cart.move_to(self.warehouse.workbench_position)

                # 在工作台释放箱子
                self.cart.release_box(self.warehouse.workbench_position)

                # 为下一个任务从工作台抓取箱子
                self.cart.grab_box(self.warehouse.workbench_position)
                self.carrying_previous_box = box
        else:
            # 没有携带上一个箱子 - 标准流程
            # 检查目标箱子上方是否有障碍物
            temp_positions = self.cart.handle_obstacles(box_position)
            if temp_positions == -1:
                # 无法处理障碍物
                return 0

            # 抓取目标箱子
            self.cart.grab_box(box_position)

            # 如果有障碍物，恢复它们
            if temp_positions and temp_positions != 0:
                self.cart.restore_obstacles(temp_positions)

            # 移动到工作台
            self.cart.move_to(self.warehouse.workbench_position)

            # 在工作台释放箱子
            self.cart.release_box(self.warehouse.workbench_position)

            # 为下一个任务从工作台抓取箱子
            self.cart.grab_box(self.warehouse.workbench_position)
            self.carrying_previous_box = box

        # 计算任务时间
        task_time = self.cart.total_time - start_time
        self.task_times.append(task_time)

        return task_time

    def run_simulation(self):
        """运行指定任务数量的模拟。"""
        print(f"运行模拟，箱子类型数量: {len(self.config['box_types'])}")

        # 生成订单
        self.generate_orders()

        # 初始化变量
        self.cart.total_time = 0
        self.carrying_previous_box = None
        self.task_times = []

        # 执行任务
        for i, box_id in enumerate(self.orders):
            task_time = self.execute_task(box_id)
            self.completed_tasks += 1

            # 打印进度
            if (i + 1) % 100 == 0:
                print(f"已完成 {i + 1} 个任务。当前时间: {self.cart.total_time:.2f} 秒")

        # 返回起始位置
        self.cart.move_to(self.warehouse.cart_start_position)

        # 计算统计数据
        total_time = self.cart.total_time
        avg_time = total_time / self.completed_tasks if self.completed_tasks > 0 else 0

        print(f"模拟完成。")
        print(f"总时间: {total_time:.2f} 秒 ({total_time/60:.2f} 分钟)")
        print(f"完成任务数: {self.completed_tasks}")
        print(f"每个任务平均时间: {avg_time:.2f} 秒")

        return {
            'total_time': total_time,
            'completed_tasks': self.completed_tasks,
            'avg_time': avg_time,
            'task_times': self.task_times
        }



def plot_warehouse_layout(warehouse, filename):
    """绘制显示不同箱子类型的仓库布局。"""
    if not MATPLOTLIB_AVAILABLE:
        print(f"Skipping warehouse layout visualization (matplotlib not available): {filename}")
        return

    try:
        # Create a figure with subplots for each layer
        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
        axes = axes.flatten()

        # Define colors for each box type
        colors = {
            'A': 'red',
            'B': 'green',
            'C': 'blue',
            'D': 'orange',
            'E': 'purple'
        }

        # Plot each layer
        for z in range(LAYERS):
            ax = axes[z]

            # Create a 2D array for this layer
            layer_data = np.zeros((ROWS, COLUMNS))

            # Fill the array with box type indices
            for y in range(ROWS):
                for x in range(COLUMNS):
                    box = warehouse.grid[x, y, z]
                    if box != 0:
                        # Assign a value based on box type
                        type_idx = warehouse.box_types.index(box.box_type) + 1
                        layer_data[y, x] = type_idx

            # Create a colormap with distinct colors for each box type
            cmap = plt.cm.get_cmap('tab10', len(warehouse.box_types) + 1)

            # Plot the layer
            im = ax.imshow(layer_data, cmap=cmap, interpolation='nearest')
            ax.set_title(f'Layer {z+1}')
            ax.set_xlabel('Column')
            ax.set_ylabel('Row')

            # Mark the workbench position if it's on this layer
            wx, wy, wz = warehouse.workbench_position
            if wz == z:
                ax.plot(wx, wy, 'k*', markersize=15, label='Workbench')

            # Mark the cart starting position if it's on this layer
            cx, cy, cz = warehouse.cart_start_position
            if cz == z:
                ax.plot(cx, cy, 'ko', markersize=15, label='Cart Start')

        # Add a colorbar
        cbar_ax = fig.add_axes([0.92, 0.15, 0.02, 0.7])
        cbar = fig.colorbar(im, cax=cbar_ax)
        cbar.set_ticks(np.arange(0.5, len(warehouse.box_types) + 1.5))
        cbar.set_ticklabels(['Empty'] + warehouse.box_types)

        plt.tight_layout(rect=[0, 0, 0.9, 1])
        plt.savefig(filename)
        plt.close()
        print(f"Warehouse layout visualization saved to: {filename}")
    except Exception as e:
        print(f"Error creating warehouse layout visualization: {e}")

def plot_results(results_dict):
    """绘制所有模拟的结果。"""
    if not MATPLOTLIB_AVAILABLE:
        print("Skipping results visualization (matplotlib not available)")
        return

    try:
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

        # Plot total times
        simulations = list(results_dict.keys())
        total_times = [results_dict[sim]['total_time'] / 60 for sim in simulations]  # Convert to minutes

        ax1.bar(simulations, total_times)
        ax1.set_title('Total Time for 1000 Tasks')
        ax1.set_xlabel('Simulation')
        ax1.set_ylabel('Time (minutes)')

        # Plot average task times
        avg_times = [results_dict[sim]['avg_time'] for sim in simulations]

        ax2.bar(simulations, avg_times)
        ax2.set_title('Average Time per Task')
        ax2.set_xlabel('Simulation')
        ax2.set_ylabel('Time (seconds)')

        plt.tight_layout()
        plt.savefig('python_fangzhen/simulation_results.png')
        plt.close()
        print("Results visualization saved to: python_fangzhen/simulation_results.png")
    except Exception as e:
        print(f"Error creating results visualization: {e}")

def main():
    """运行所有模拟的主函数。"""
    start_time = time.time()

    # 如果输出目录不存在，创建它
    if not os.path.exists('python_fangzhen'):
        os.makedirs('python_fangzhen')

    # 运行所有模拟
    results = {}

    try:
        # ABC 模拟
        print("运行 ABC 模拟...")
        config_abc = {
            'box_types': ['A', 'B', 'C'],
            'type_quantities': {'A': 0.15, 'B': 0.30, 'C': 0.55},
            'flow_rates': {'A': 0.50, 'B': 0.35, 'C': 0.15}
        }
        simulation_abc = Simulation(config_abc)

        # 在模拟前可视化仓库布局
        plot_warehouse_layout(simulation_abc.warehouse, 'python_fangzhen/warehouse_layout_ABC.png')

        # 运行模拟
        results['ABC'] = simulation_abc.run_simulation()

        # AB 模拟
        print("\n运行 AB 模拟...")
        config_ab = {
            'box_types': ['A', 'B'],
            'type_quantities': {'A': 0.30, 'B': 0.70},
            'flow_rates': {'A': 0.70, 'B': 0.30}
        }
        simulation_ab = Simulation(config_ab)

        # 可视化仓库布局
        plot_warehouse_layout(simulation_ab.warehouse, 'python_fangzhen/warehouse_layout_AB.png')

        # 运行模拟
        results['AB'] = simulation_ab.run_simulation()

        # ABCD simulation
        print("\nRunning ABCD simulation...")
        config_abcd = {
            'box_types': ['A', 'B', 'C', 'D'],
            'type_quantities': {'A': 0.10, 'B': 0.20, 'C': 0.30, 'D': 0.40},
            'flow_rates': {'A': 0.40, 'B': 0.30, 'C': 0.20, 'D': 0.10}
        }
        simulation_abcd = Simulation(config_abcd)

        # Visualize the warehouse layout
        plot_warehouse_layout(simulation_abcd.warehouse, 'python_fangzhen/warehouse_layout_ABCD.png')

        # Run the simulation
        results['ABCD'] = simulation_abcd.run_simulation()

        # ABCDE simulation
        print("\nRunning ABCDE simulation...")
        config_abcde = {
            'box_types': ['A', 'B', 'C', 'D', 'E'],
            'type_quantities': {'A': 0.10, 'B': 0.15, 'C': 0.20, 'D': 0.25, 'E': 0.30},
            'flow_rates': {'A': 0.30, 'B': 0.25, 'C': 0.20, 'D': 0.15, 'E': 0.10}
        }
        simulation_abcde = Simulation(config_abcde)

        # Visualize the warehouse layout
        plot_warehouse_layout(simulation_abcde.warehouse, 'python_fangzhen/warehouse_layout_ABCDE.png')

        # Run the simulation
        results['ABCDE'] = simulation_abcde.run_simulation()

        # Plot results
        plot_results(results)

        # Print summary
        print("\nSummary of Results:")
        print("-" * 50)
        for sim_name, sim_results in results.items():
            print(f"{sim_name} Simulation:")
            print(f"  Total time: {sim_results['total_time']:.2f} seconds ({sim_results['total_time']/60:.2f} minutes)")
            print(f"  Average time per task: {sim_results['avg_time']:.2f} seconds")
            print("-" * 50)

    except Exception as e:
        print(f"Error during simulation: {e}")

    end_time = time.time()
    print(f"Total execution time: {end_time - start_time:.2f} seconds")
    print(f"Results saved in the python_fangzhen directory.")

if __name__ == "__main__":
    main()
