#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
厦门市未来天气预测 - 一键运行脚本
"""

import os
import sys
import time

def run_script(script_name):
    """
    运行指定的Python脚本
    """
    print(f"\n{'='*50}")
    print(f"正在运行 {script_name}...")
    print(f"{'='*50}")
    
    start_time = time.time()
    exit_code = os.system(f"python {script_name}")
    end_time = time.time()
    
    if exit_code == 0:
        print(f"\n{script_name} 运行成功！")
    else:
        print(f"\n{script_name} 运行失败，退出代码: {exit_code}")
        sys.exit(exit_code)
    
    print(f"运行时间: {end_time - start_time:.2f} 秒")
    print(f"{'='*50}")

def main():
    """
    主函数
    """
    print("厦门市未来天气预测 - 一键运行脚本")
    print("="*50)
    
    # 检查数据文件是否存在
    if not os.path.exists("weather.xls"):
        print("错误: 未找到数据文件 'weather.xls'")
        print("请确保数据文件位于当前目录中")
        sys.exit(1)
    
    # 运行预测脚本
    run_script("weather_prediction.py")
    
    # 运行可视化脚本
    run_script("weather_visualization.py")
    
    print("\n所有脚本运行完成！")
    print("请查看生成的预测结果和可视化图表")

if __name__ == "__main__":
    main()
