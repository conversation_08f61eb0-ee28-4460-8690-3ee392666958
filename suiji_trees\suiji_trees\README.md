# 厦门市未来天气预测 - 随机森林模型

本项目使用随机森林算法对厦门市的历史天气数据进行分析，并预测未来几天的关键气象指标。

## 数据集说明

数据集（`weather.xls` 或 `weather.csv`）应包含历史气象观测记录，其中可能包括以下特征（部分特征可能在预处理中被移除）：

- **日期/当地时间**: 观测时间
- **T**: 温度 (°C)
- **Po**: 测站气压 (毫米汞柱)
- **P**: 海平面气压 (毫米汞柱)
- **Pa**: 3小时气压趋势 (毫米汞柱)
- **U**: 相对湿度 (%)
- **DD**: 风向 (文本描述)
- **Ff**: 平均风速 (米/秒)
- **ff10/ff3**: 最大阵风风速 (米/秒)
- **N**: 总云量
- **WW/W1/W2**: 当前/过去天气状况 (文本描述)
- **Tn**: 最低气温 (°C)
- **Tx**: 最高气温 (°C)
- **Cl/Cm/Ch**: 低/中/高云状 (文本描述)
- **Nh**: 低云或中云量
- **H**: 最低云底高度 (米)
- **VV**: 水平能见度 (千米)
- **Td**: 露点温度 (°C)
- **RRR**: 降水量 (毫米或文本如'无降水')
- **tR**: 降水持续时间 (小时)
- **E/E'**: 土壤状况 (文本描述)
- **Tg**: 土壤表面最低温度 (°C)
- **sss**: 雪深 (厘米)

*(注意：脚本会根据数据质量自动处理或移除部分特征)*

## 文件结构

- `weather.xls`/`weather.csv`: 原始天气数据文件。
- `weather_prediction.py`: 数据加载、预处理、模型训练（随机森林）、评估和保存的主脚本。
- `weather_predict.py`: 使用已保存的模型进行未来天气预测的脚本。
- `weather_model.pkl`: 训练好的随机森林主模型。
- `selected_features.pkl`: 训练时选定的特征名称列表。
- `model_info.pkl`: 包含目标变量、问题类型等信息的字典。
- `future_weather_predictions.csv`: 运行 `weather_predict.py` 后生成的未来天气预测结果。
- `*.png`: 模型训练和评估过程中生成的图表（如特征重要性、预测对比图等）。
- `prediction_errors.csv`: 模型在测试集上的详细误差分析。

## 数据处理与模型训练流程 (`weather_prediction.py`)

1.  **数据加载**:
    *   优先尝试加载 `weather.xls`，若失败或不存在则尝试加载 `weather.csv`。
    *   处理常见的编码问题 (utf-8-sig, gbk, latin1)。
    *   将 '当地时间' 列重命名为 '日期'。

2.  **数据清洗 (`clean_data`)**:
    *   根据预设列表 (`text_columns_to_remove`) 删除指定的文本特征列（如 'DD', 'WW' 等）。
    *   尝试将 '日期' 列转换为 datetime 对象。
    *   处理 'RRR' 列：将 '无降水' 转换为 0，其他值转为数值，存储到新列 '降水量'，并删除原 'RRR' 列。
    *   删除缺失值比例 > 50% 的列。
    *   使用**中位数**填充数值型特征的剩余缺失值。
    *   使用**众数**填充分类（对象类型）特征的剩余缺失值。
    *   删除仍包含缺失值的行。

3.  **数据预处理与特征工程 (`preprocess_data`)**:
    *   **日期特征提取**: 从 '日期' 列提取年、月、日、星期、小时、季节。
    *   **单位转换**: 将气压相关的列 ('Po', 'P', 'Pa') 从毫米汞柱转换为帕斯卡，并创建新列 (如 'Po\_Pa')。
    *   **时间序列特征**:
        *   为关键气象指标 ('T', 'U', 'Po', 'Tn', 'Tx', '降水量') 创建**滞后特征** (前1, 2, 3, 7天)。
        *   为这些指标创建**移动平均特征** (3天和7天窗口)。
        *   为这些指标创建**趋势特征** (当前值与前1天值的差)。
        *   由于滞后和移动平均会产生NaN，删除这些行。
    *   **分类特征编码**: 使用 `LabelEncoder` 对剩余的对象类型特征进行编码。
    *   **常量特征删除**: 删除值唯一的列。
    *   **(已移除)** 高度相关特征删除逻辑。

4.  **特征选择 (`select_features`)**:
    *   使用 `RandomForestRegressor` 计算所有特征的重要性。
    *   基于重要性阈值 (0.001) 和最小特征数 (10) 选择特征子集。
    *   生成并保存特征重要性图 (`feature_importance.png`)。

5.  **数据分割**:
    *   采用**时间序列分割**: 将排序后的数据按 80%/20% 比例分割为训练集和测试集，确保测试集数据在时间上晚于训练集。

6.  **模型训练与评估 (`main` 函数内)**:
    *   **模型**: `RandomForestRegressor`。
    *   **超参数调优**: 使用 `GridSearchCV` (5折交叉验证) 寻找最佳参数组合 (`n_estimators`, `max_depth`, `min_samples_split`, `min_samples_leaf`)，优化目标为最小化负均方误差 (`neg_mean_squared_error`)。
    *   **评估**: 在测试集上评估最佳模型，计算指标：
        *   均方误差 (MSE)
        *   R² 分数
        *   平均绝对误差 (MAE)
        *   中位绝对误差 (MedAE)
        *   平均绝对百分比误差 (MAPE)
    *   生成并保存预测值 vs 实际值对比图 (`prediction_vs_actual.png`)。
    *   进行误差分析，找出误差最大的预测点，并保存到 `prediction_errors.csv`。

7.  **模型保存**:
    *   使用 `joblib` 保存训练好的最佳 `RandomForestRegressor` 模型 (`weather_model.pkl`)、选定的特征列表 (`selected_features.pkl`) 和模型信息 (`model_info.pkl`)。

8.  **未来天气预测 (`predict_future_weather`)**:
    *   实际预测使用 `weather_predict.py` 脚本。**
    *   为主要天气变量 ('T', 'U', 'Po', 'Tn', 'Tx', '降水量') 分别训练独立的 `RandomForestRegressor` 模型（使用完整数据集和选定特征）。
    *   基于数据集最后日期的**最近7天**数据均值创建未来日期的基础特征。
    *   使用对应的独立模型预测未来每一天的各项指标。
    *   保存预测结果演示 (`future_weather_predictions.csv` - 会被 `weather_predict.py` 覆盖)。

## 未来天气预测 (`weather_predict.py`)

此脚本用于加载已保存的模型并进行实际的未来天气预测：

1.  **加载**: 加载 `weather_model.pkl`, `selected_features.pkl`, `model_info.pkl`。
2.  **输入**: 获取用户希望预测的天数和起始日期。
3.  **特征生成**: 为未来日期**合成**输入特征。由于没有真实的历史数据用于预测未来，脚本使用基于当前日期、周期函数（模拟季节/日变化）和随机性的**启发式方法**来生成特征值。
4.  **预测**:
    *   使用加载的**主模型** (`weather_model.pkl`) 和合成的特征预测主要目标变量。
    *   使用内置的**启发式规则**估算其他天气变量（如最高/最低温基于主温度的偏移，湿度与温度的关系等）。**注意：此步骤不使用 `weather_prediction.py` 中训练的那些专门模型。**
5.  **输出**:
    *   打印预测结果表格。
    *   保存预测结果到 `future_weather_predictions.csv`。
    *   生成并保存温度预测图 (`temperature_forecast.png`) 和降水量预测图 (`precipitation_forecast.png`)。

## 使用方法

1.  **安装依赖**:
    ```bash
    pip install pandas numpy matplotlib scikit-learn joblib openpyxl # openpyxl 用于读取 .xls
    ```
2.  **准备数据**: 确保 `weather.xls` 或 `weather.csv` 文件在项目目录下。
3.  **训练模型**:
    ```bash
    python weather_prediction.py
    ```
    这将进行数据处理、训练、评估并保存模型及相关文件。
4.  **进行预测**:
    ```bash
    python weather_predict.py
    ```
    脚本会提示输入预测天数和起始日期，然后加载模型进行预测并保存结果。
5.  **(可选)** 运行 `run_all.py` 一次性执行训练和预测。
6.  **(可选)** 运行 `weather_visualization.py` 进行数据探索。

## 注意事项

- `weather_prediction.py` 默认使用 'T' (温度) 作为主要目标变量进行训练和评估。
- 预测脚本 (`weather_predict.py`) 生成的特征是基于假设的，预测精度依赖于这些假设和主模型的泛化能力。
- 数据清洗步骤会移除一些预设的文本列和缺失过多的列，请检查输出日志确认。
