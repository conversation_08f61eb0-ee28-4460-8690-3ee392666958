#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
厦门市历史天气数据分析与模型训练 - 随机森林
该脚本负责加载、清洗、预处理天气数据，训练随机森林回归模型，
评估模型性能，并保存模型及相关信息以供预测脚本使用。
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os
import joblib # 用于模型保存与加载
from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier
from sklearn.model_selection import train_test_split, GridSearchCV, TimeSeriesSplit # 添加TimeSeriesSplit
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import mean_squared_error, r2_score, accuracy_score, classification_report, confusion_matrix, mean_absolute_error, median_absolute_error
try:
    from sklearn.metrics import mean_absolute_percentage_error
    mape_available = True
except ImportError:
    # 兼容旧版本 sklearn
    mape_available = False
from sklearn.feature_selection import SelectFromModel
import warnings
warnings.filterwarnings('ignore')

# 设置中文显示
try:
    import platform
    system = platform.system()

    if system == 'Windows':
        # Windows系统优先使用微软雅黑
        plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'SimSun', 'NSimSun', 'FangSong', 'Arial Unicode MS']
    elif system == 'Darwin':  # macOS
        plt.rcParams['font.sans-serif'] = ['PingFang SC', 'STHeiti', 'Heiti TC', 'Arial Unicode MS']
    else:  # Linux等其他系统
        plt.rcParams['font.sans-serif'] = ['WenQuanYi Micro Hei', 'WenQuanYi Zen Hei', 'DejaVu Sans', 'Arial Unicode MS']

    # 备用字体列表
    fallback_fonts = ['DejaVu Sans', 'Arial Unicode MS', 'sans-serif']
    plt.rcParams['font.sans-serif'].extend(fallback_fonts)

    # 解决负号显示问题
    plt.rcParams['axes.unicode_minus'] = False

    # 测试中文显示
    import matplotlib.font_manager as fm
    fonts = [f.name for f in fm.fontManager.ttflist]
    print(f"系统检测到的字体数量: {len(fonts)}")

    # 检查是否有中文字体
    chinese_fonts = [f for f in fonts if any(name in f for name in ['SimHei', 'Microsoft YaHei', '微软雅黑', '宋体', 'SimSun', 'WenQuanYi'])]
    if chinese_fonts:
        print(f"检测到的中文字体: {chinese_fonts[:5]}...")
    else:
        print("警告: 未检测到中文字体，将尝试使用系统默认字体")

except Exception as e:
    print(f"警告: 设置中文字体时出错: {e}")
    print("图表中的中文可能无法正确显示")

def load_data(file_path):
    """
    加载天气数据文件（Excel或CSV）。
    自动处理常见编码问题，并将 '当地时间' 列重命名为 '日期'。
    """
    print("正在加载数据...")
    try:
        if file_path.endswith('.xls') or file_path.endswith('.xlsx'):
            try:
                df = pd.read_excel(file_path)
            except Exception as excel_error:
                # 如果Excel读取失败，尝试读取同名的CSV文件
                print(f"Excel读取失败: {excel_error}")
                print("尝试读取CSV文件...")
                csv_path = file_path.replace('.xls', '.csv').replace('.xlsx', '.csv')
                if os.path.exists(csv_path):
                    df = pd.read_csv(csv_path, encoding='utf-8-sig')
                else:
                    raise FileNotFoundError(f"无法找到 Excel 文件 {file_path} 或对应的 CSV 文件 {csv_path}") # 明确错误
        elif file_path.endswith('.csv'):
            try:
                df = pd.read_csv(file_path, encoding='utf-8-sig')
            except UnicodeDecodeError:
                try:
                    df = pd.read_csv(file_path, encoding='gbk')
                except UnicodeDecodeError:
                    df = pd.read_csv(file_path, encoding='latin1') # 最后尝试latin1
        else:
            raise ValueError(f"不支持的文件格式: {file_path}")

        # 统一日期列名
        if '当地时间' in df.columns and '日期' not in df.columns:
            df = df.rename(columns={'当地时间': '日期'})
        # 处理"当地时间 厦门市(气象站)"列
        if '当地时间 厦门市(气象站)' in df.columns and '日期' not in df.columns:
            df = df.rename(columns={'当地时间 厦门市(气象站)': '日期'})

        print(f"数据加载成功，共有 {df.shape[0]} 行，{df.shape[1]} 列")
        return df
    except Exception as e:
        print(f"数据加载失败: {e}")
        return None

def explore_data(df):
    """
    对加载的数据进行初步探索性分析。
    打印基本信息、统计描述、前几行数据和缺失值统计。
    返回包含缺失值信息的DataFrame。
    """
    print("\n数据基本信息:")
    print(df.info())

    print("\n数据统计描述:")
    # 限制小数位数显示
    print(df.describe().round(2))

    print("\n前5行数据:")
    print(df.head())

    print("\n缺失值统计:")
    missing_values = df.isnull().sum()
    missing_percent = (missing_values / len(df)) * 100
    missing_df = pd.DataFrame({
        '缺失值数量': missing_values,
        '缺失比例(%)': missing_percent.round(2) # 保留两位小数
    })
    # 只显示存在缺失值的列
    missing_df = missing_df[missing_df['缺失值数量'] > 0].sort_values('缺失比例(%)', ascending=False)
    if not missing_df.empty:
        print(missing_df)
    else:
        print("数据中无缺失值。")

    return missing_df

def clean_data(df, missing_df):
    """
    对DataFrame进行数据清洗。
    包括删除指定列、转换日期、处理降水量、填充或删除缺失值。
    """
    print("\n开始数据清洗...")
    df_cleaned = df.copy()

    # 1. 删除预定义的文本特征列 (这些列通常难以直接用于模型或包含冗余信息)
    text_columns_to_remove = ['DD', 'Ff', 'WW', 'W1', 'W2', 'H', 'Cl', 'Cm', 'Ch', 'E', 'E\'']
    columns_to_remove = [col for col in text_columns_to_remove if col in df_cleaned.columns]
    if columns_to_remove:
        print(f"删除预设的文本特征列: {columns_to_remove}")
        df_cleaned = df_cleaned.drop(columns=columns_to_remove)

    # 2. 处理日期列
    if '日期' in df_cleaned.columns:
        try:
            # 尝试多种日期格式
            date_formats = [
                '%d.%m.%Y %H:%M',  # 31.12.2024 23:00
                '%Y-%m-%d %H:%M',   # 2024-12-31 23:00
                '%Y/%m/%d %H:%M',   # 2024/12/31 23:00
                '%d/%m/%Y %H:%M',   # 31/12/2024 23:00
                '%m/%d/%Y %H:%M'    # 12/31/2024 23:00
            ]

            # 先尝试特定格式
            for date_format in date_formats:
                try:
                    df_cleaned['日期'] = pd.to_datetime(df_cleaned['日期'], format=date_format, errors='coerce')
                    invalid_dates_count = df_cleaned['日期'].isnull().sum()
                    if invalid_dates_count == 0:
                        print(f"日期列已成功按 '{date_format}' 格式解析")
                        break
                    elif invalid_dates_count < len(df_cleaned) * 0.5:  # 如果超过一半的日期可以解析，则认为找到了正确的格式
                        print(f"日期列部分按 '{date_format}' 格式解析成功，有 {invalid_dates_count} 个无效值")
                        break
                except:
                    continue

            # 如果特定格式都失败，尝试自动推断
            invalid_dates_count = df_cleaned['日期'].isnull().sum()
            if invalid_dates_count > 0:
                print(f"警告: 使用特定格式后仍有 {invalid_dates_count} 个无效日期值，尝试自动解析...")
                df_cleaned['日期'] = pd.to_datetime(df_cleaned['日期'], errors='coerce')
                new_invalid_count = df_cleaned['日期'].isnull().sum()
                if new_invalid_count > 0:
                    print(f"警告: 自动解析后仍有 {new_invalid_count} 个无效日期值，这些行可能在后续步骤被删除。")
                else:
                    print("日期列已通过自动推断格式成功解析")

        except Exception as e:
            print(f"日期转换出错: {e}. 尝试使用更通用的方法...")
            try:
                # 最后尝试，将日期列转换为字符串后再解析
                df_cleaned['日期'] = df_cleaned['日期'].astype(str)
                df_cleaned['日期'] = pd.to_datetime(df_cleaned['日期'], errors='coerce')
                invalid_dates_count = df_cleaned['日期'].isnull().sum()
                if invalid_dates_count > 0:
                    print(f"警告: 最终尝试后仍有 {invalid_dates_count} 个无效日期值")
                else:
                    print("日期列已通过最终尝试成功解析")
            except Exception as e2:
                print(f"日期转换最终尝试失败: {e2}. 保留原始格式或尝试在后续步骤处理。")

    # 3. 处理RRR列（降水量）
    if 'RRR' in df_cleaned.columns:
        # 创建新列'降水量'，便于明确处理
        df_cleaned['降水量'] = df_cleaned['RRR'].copy()
        if df_cleaned['降水量'].dtype == 'object':
            # 将 '无降水' 或类似文本替换为 '0'
            df_cleaned['降水量'] = df_cleaned['降水量'].replace(['无降水', 'trace', 'Trace'], '0', regex=False) # 添加常见表示
            # 尝试转换为数值，无法转换的设为NaN
            df_cleaned['降水量'] = pd.to_numeric(df_cleaned['降水量'], errors='coerce')
            # 用0填充转换失败产生的NaN（假设无法转换的值代表无降水或极微量）
            nan_count_after_conversion = df_cleaned['降水量'].isnull().sum()
            if nan_count_after_conversion > 0:
                 print(f"警告: 'RRR' 列中有 {nan_count_after_conversion} 个值无法转换为数值，已填充为0。")
                 df_cleaned['降水量'].fillna(0, inplace=True)
            print("已将'RRR'列内容处理并存储到'降水量'列。")
        # 删除原始RRR列
        df_cleaned.drop(columns=['RRR'], inplace=True, errors='ignore')

    # 4. 处理缺失值
    # 4.1 删除缺失比例过高的特征
    if not missing_df.empty:
        high_missing_cols = missing_df[missing_df['缺失比例(%)'] > 50].index.tolist()
        # 确保这些列还存在于df_cleaned中
        high_missing_cols = [col for col in high_missing_cols if col in df_cleaned.columns]
        if high_missing_cols:
            print(f"删除缺失比例高于50%的特征: {high_missing_cols}")
            df_cleaned = df_cleaned.drop(columns=high_missing_cols)

    # 4.2 填充数值型特征的缺失值 (使用中位数，对异常值不敏感)
    numeric_cols = df_cleaned.select_dtypes(include=np.number).columns
    for col in numeric_cols:
        if df_cleaned[col].isnull().sum() > 0:
            median_value = df_cleaned[col].median()
            df_cleaned[col].fillna(median_value, inplace=True)
            print(f"数值特征 '{col}' 的缺失值已用中位数 {median_value:.2f} 填充")

    # 4.3 填充分类特征的缺失值 (使用众数)
    categorical_cols = df_cleaned.select_dtypes(include=['object']).columns
    for col in categorical_cols:
        # 确保不是日期列（虽然日期通常已转为datetime，以防万一）
        if col != '日期' and df_cleaned[col].isnull().sum() > 0:
            # 检查列是否只包含缺失值
            if df_cleaned[col].count() == 0: # count() 计算非NA单元格数量
                print(f"特征 '{col}' 全为缺失值，将删除该列")
                df_cleaned = df_cleaned.drop(columns=[col])
            else:
                mode_value = df_cleaned[col].mode()[0]
                df_cleaned[col].fillna(mode_value, inplace=True)
                print(f"分类特征 '{col}' 的缺失值已用众数 '{mode_value}' 填充")

    # 5. 删除仍包含任何缺失值的行
    initial_rows = df_cleaned.shape[0]
    df_cleaned = df_cleaned.dropna()
    final_rows = df_cleaned.shape[0]
    if initial_rows > final_rows:
         print(f"删除了 {initial_rows - final_rows} 行包含无法处理的缺失值的数据。")

    remaining_missing = df_cleaned.isnull().sum().sum()
    if remaining_missing == 0:
        print(f"数据清洗完成，剩余数据: {df_cleaned.shape[0]} 行, {df_cleaned.shape[1]} 列。无缺失值。")
    else:
         # 这通常不应该发生，除非有特殊数据类型
         print(f"警告: 清洗后仍有 {remaining_missing} 个缺失值。请检查数据。")
         print(df_cleaned.isnull().sum())

    return df_cleaned

def preprocess_data(df):
    """
    进行数据预处理和特征工程。
    包括日期特征提取、单位转换、时间序列特征生成、分类编码和常量特征删除。
    """
    print("\n开始数据预处理与特征工程...")
    df_processed = df.copy()

    # 1. 日期特征提取
    if '日期' in df_processed.columns:
        if pd.api.types.is_datetime64_any_dtype(df_processed['日期']):
            df_processed['年'] = df_processed['日期'].dt.year
            df_processed['月'] = df_processed['日期'].dt.month
            df_processed['日'] = df_processed['日期'].dt.day
            df_processed['星期'] = df_processed['日期'].dt.dayofweek # 0=周一, 6=周日
            df_processed['小时'] = df_processed['日期'].dt.hour
            # 定义季节: 1-春(3-5月), 2-夏(6-8月), 3-秋(9-11月), 4-冬(12-2月)
            df_processed['季节'] = df_processed['月'].apply(lambda x: 1 if 3 <= x <= 5 else
                                                          2 if 6 <= x <= 8 else
                                                          3 if 9 <= x <= 11 else 4)
            print("已从日期中提取年、月、日、星期、小时和季节特征")
        else:
            print("警告: '日期'列不是datetime类型，无法提取日期特征。")

    # 2. 气压单位转换 (毫米汞柱 -> 帕斯卡)
    pressure_columns = ['Po', 'P', 'Pa']
    mmHg_to_Pa = 133.322
    for col in pressure_columns:
        if col in df_processed.columns:
            # 创建新列以保留原始值（如果需要）并进行转换
            new_col_name = f'{col}_Pa'
            df_processed[new_col_name] = df_processed[col] * mmHg_to_Pa
            print(f"已将 {col} 从毫米汞柱转换为帕斯卡单位，存储为 {new_col_name}")

    # 3. 时间序列特征工程 (滞后、移动平均、趋势)
    if '日期' in df_processed.columns and pd.api.types.is_datetime64_any_dtype(df_processed['日期']):
        # 必须先按日期排序才能正确计算滞后和移动窗口
        print("生成时间序列特征 (滞后、移动平均、趋势)...")
        df_processed = df_processed.sort_values('日期')

        lag_features = ['T', 'U', 'Po', 'Tn', 'Tx', '降水量'] # 主要气象指标
        lag_days = [1, 2, 3, 7] # 定义滞后期数

        for feature in lag_features:
            if feature in df_processed.columns:
                # 创建滞后特征
                for lag in lag_days:
                    lag_col = f'{feature}_lag{lag}'
                    df_processed[lag_col] = df_processed[feature].shift(lag)

                # 创建移动平均特征 (窗口大小包含当前点)
                df_processed[f'{feature}_ma3'] = df_processed[feature].rolling(window=3, min_periods=1).mean() # 至少1个点
                df_processed[f'{feature}_ma7'] = df_processed[feature].rolling(window=7, min_periods=1).mean() # 至少1个点

                # 创建趋势特征 (与前一天比较)
                if 1 in lag_days: # 确保lag1存在
                   df_processed[f'{feature}_trend'] = df_processed[feature] - df_processed[f'{feature}_lag1']
            else:
                 print(f"跳过时间序列特征生成：列 '{feature}' 不存在。")


        # 处理因shift和rolling产生的NaN值（通常在数据开头）
        initial_rows = df_processed.shape[0]
        df_processed = df_processed.dropna()
        final_rows = df_processed.shape[0]
        if initial_rows > final_rows:
            print(f"因生成时间序列特征，删除了 {initial_rows - final_rows} 行开头的NaN数据。")
        print(f"添加时间序列特征后数据集大小: {df_processed.shape}")
    else:
        print("警告: '日期'列不可用或非datetime类型，跳过时间序列特征生成。")


    # 4. 处理剩余的分类特征 (使用Label Encoding)
    categorical_cols = df_processed.select_dtypes(include=['object']).columns
    label_encoders = {} # 存储编码器以便未来可能的逆转换

    for col in categorical_cols:
        if col != '日期': # 排除已处理的日期列
            # 再次检查缺失值（理论上clean_data已处理，但以防万一）
            if df_processed[col].isnull().any():
                 print(f"警告: 分类特征 '{col}' 仍有缺失值，将使用众数填充。")
                 mode_value = df_processed[col].mode()[0]
                 df_processed[col].fillna(mode_value, inplace=True)

            # 先将所有值转换为字符串，确保类型统一
            df_processed[col] = df_processed[col].astype(str)

            # 应用LabelEncoder
            le = LabelEncoder()
            df_processed[col] = le.fit_transform(df_processed[col])
            label_encoders[col] = le
            print(f"分类特征 '{col}' 已使用 LabelEncoder 编码")

    # 5. 检查并删除常量特征 (对模型无用的特征)
    constant_features = [col for col in df_processed.columns if df_processed[col].nunique() == 1]
    if constant_features:
        print(f"删除常量特征: {constant_features}")
        df_processed = df_processed.drop(columns=constant_features)

    # （已移除）6. 检查并删除高度相关的特征
    # if len(df_processed.columns) > 2:
    #     try:
    #         corr_matrix = df_processed.select_dtypes(include=['number']).corr().abs()
    #         upper = corr_matrix.where(np.triu(np.ones(corr_matrix.shape), k=1).astype(bool))
    #         to_drop = [column for column in upper.columns if any(upper[column] > 0.95)]
    #         if to_drop:
    #             print(f"删除高度相关的特征 (阈值>0.95): {to_drop}")
    #             df_processed = df_processed.drop(columns=to_drop)
    #     except Exception as e:
    #         print(f"计算相关性时出错: {e}")

    print(f"数据预处理完成，最终数据集大小: {df_processed.shape}")
    return df_processed, label_encoders

def select_features(X, y, threshold=0.001, min_features=10):
    """
    使用随机森林模型进行特征选择。
    基于特征重要性进行筛选，并确保至少选择 min_features 个特征。
    绘制并保存特征重要性图。
    """
    print("\n开始特征选择...")
    if X.empty:
        print("错误：特征集 X 为空，无法进行特征选择。")
        return None, [] # 返回None表示失败

    # 确定是回归还是分类问题以选择合适的模型
    # 注意：此处仅用于特征选择模型，主训练模型在main函数中定义
    is_regression = len(np.unique(y)) > 10 # 简单启发式判断
    if is_regression:
        # 使用较少估计器以加快特征选择速度
        selector_model = RandomForestRegressor(n_estimators=100, random_state=42, n_jobs=-1)
    else:
        selector_model = RandomForestClassifier(n_estimators=100, random_state=42, n_jobs=-1)

    print(f"使用 {'RandomForestRegressor' if is_regression else 'RandomForestClassifier'} 进行特征重要性计算...")
    selector_model.fit(X, y)

    # 获取并展示特征重要性
    importances = selector_model.feature_importances_
    feature_importance = pd.DataFrame({
        '特征': X.columns,
        '重要性': importances
    }).sort_values('重要性', ascending=False)

    print("\n特征重要性排名 (Top 20):")
    print(feature_importance.head(20))

    # 绘制特征重要性图 (Top 15)
    plt.figure(figsize=(10, 8))
    top_features = feature_importance.head(15)
    plt.barh(top_features['特征'], top_features['重要性'])

    # 使用更明确的字体设置，确保中文显示
    font_properties = {'family': plt.rcParams['font.sans-serif'][0], 'size': 12}
    title_font = {'family': plt.rcParams['font.sans-serif'][0], 'size': 14, 'weight': 'bold'}

    plt.xlabel('重要性', fontdict=font_properties) # 添加x轴标签
    plt.ylabel('特征', fontdict=font_properties) # 添加y轴标签
    plt.title('特征重要性排名 (Top 15)', fontdict=title_font)
    plt.gca().invert_yaxis() # 将最重要的放顶部

    # 设置刻度标签字体
    plt.tick_params(labelsize=10)
    for label in plt.gca().get_xticklabels() + plt.gca().get_yticklabels():
        label.set_fontproperties(plt.rcParams['font.sans-serif'][0])

    plt.tight_layout()
    try:
        plt.savefig('feature_importance.png', dpi=300, bbox_inches='tight')
        print("特征重要性图已保存至 'feature_importance.png'")
    except Exception as e:
        print(f"保存特征重要性图时出错: {e}")
    plt.close() # 关闭图形，释放内存

    # 基于阈值选择特征
    selector = SelectFromModel(selector_model, threshold=threshold, prefit=True)
    # 获取被选中的特征的布尔掩码
    support_mask = selector.get_support()
    selected_features = X.columns[support_mask]

    # 如果选择的特征数量少于最小要求，则直接选择最重要的 min_features 个
    if len(selected_features) < min_features:
         # 确保 X 的列数不少于 min_features
         actual_min_features = min(min_features, len(X.columns))
         if len(selected_features) < actual_min_features:
            print(f"基于阈值选择的特征数量 ({len(selected_features)}) 少于最小要求 ({actual_min_features})，将选择Top {actual_min_features} 重要特征。")
            selected_features = feature_importance['特征'].head(actual_min_features).tolist() # 转换为列表

    print(f"\n最终选择的特征数量: {len(selected_features)}")
    print(f"选择的特征: {list(selected_features)}")

    # 返回选定特征的DataFrame和特征名称列表
    # 注意：旧代码返回了 X_selected (numpy array)，现在改为返回 X[selected_features] (DataFrame)
    return X[selected_features], list(selected_features) # 返回DataFrame和列表


# train_random_forest 函数已合并到 main 函数中进行评估


def main():
    """
    主函数：执行数据加载、清洗、预处理、训练、评估和保存模型的完整流程。
    """
    print("=" * 50)
    print("厦门市天气预测模型训练 - 随机森林")
    print("=" * 50)

    # 1. 加载数据
    file_path = 'weather.xls' # 或者 'weather.csv'
    df = load_data(file_path)
    if df is None:
        print("程序终止: 无法加载数据")
        return

    # 2. 数据探索
    missing_df = explore_data(df)

    # 3. 数据清洗
    df_cleaned = clean_data(df, missing_df)
    if df_cleaned.empty:
        print("程序终止: 数据清洗后无可用数据。")
        return

    # 4. 数据预处理与特征工程
    df_processed, label_encoders = preprocess_data(df_cleaned)
    if df_processed.empty:
        print("程序终止: 数据预处理后无可用数据。")
        return

    # 5. 确定目标变量和特征集
    # 默认使用温度 'T' 作为目标变量
    target_col = 'T'
    if target_col not in df_processed.columns:
        # 如果 'T' 不存在，尝试寻找其他数值列
        numeric_cols = df_processed.select_dtypes(include=np.number).columns
        # 排除可能的ID或日期衍生列
        potential_targets = [col for col in numeric_cols if col not in ['年', '月', '日', '星期', '小时', '季节']]
        if potential_targets:
            target_col = potential_targets[0] # 选择第一个找到的数值列
            print(f"警告: 默认目标变量 'T' 不存在，已选择 '{target_col}' 作为目标变量。")
        else:
            print("错误: 无法找到合适的目标变量。")
            return
    print(f"\n选择 '{target_col}' 作为主要目标变量进行训练和评估。")

    # 准备特征集 X 和目标变量 y
    # 排除目标变量、原始日期列以及原始气压列（使用转换后的 *_Pa 列）
    exclude_cols = [target_col]
    if '日期' in df_processed.columns and pd.api.types.is_datetime64_any_dtype(df_processed['日期']):
         exclude_cols.append('日期')
    pressure_original_cols = ['Po', 'P', 'Pa']
    exclude_cols.extend([col for col in pressure_original_cols if col in df_processed.columns])

    # 确保只删除存在的列
    cols_to_drop = [col for col in exclude_cols if col in df_processed.columns]
    X = df_processed.drop(columns=cols_to_drop)
    y = df_processed[target_col]

    # 检查X, y 是否为空
    if X.empty or y.empty:
        print("错误：特征集 X 或目标变量 y 为空，无法继续。")
        return


    # 6. 特征选择
    X_selected_df, selected_features = select_features(X, y, threshold=0.001, min_features=15) # 增加min_features
    if X_selected_df is None or not selected_features:
        print("程序终止：特征选择失败。")
        return
    # 使用选定特征更新 X
    X = X_selected_df


    # 7. 数据分割 (时间序列分割)
    if '日期' in df_processed.columns and pd.api.types.is_datetime64_any_dtype(df_processed['日期']):
        print("\n使用时间序列分割方法 (80% 训练, 20% 测试)...")
        # 使用 df_processed 中对应行的索引来分割 X 和 y
        df_sorted_indices = df_processed.sort_values('日期').index
        split_idx = int(len(df_sorted_indices) * 0.8)

        train_indices = df_sorted_indices[:split_idx]
        test_indices = df_sorted_indices[split_idx:]

        # 使用 .loc 确保按索引正确选择数据
        X_train = X.loc[train_indices]
        y_train = y.loc[train_indices]
        X_test = X.loc[test_indices]
        y_test = y.loc[test_indices]
    else:
        print("\n警告: 无法进行时间序列分割，将使用随机分割...")
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42
        )

    print(f"训练集大小: {X_train.shape[0]}，测试集大小: {X_test.shape[0]}")
    if X_train.empty or X_test.empty:
        print("错误：训练集或测试集为空，无法继续。请检查数据量和分割比例。")
        return


    # --- 8. 模型训练与评估 ---
    print("\n开始训练随机森林模型 (使用GridSearchCV进行超参数调优)...")

    # 定义模型和参数网格
    rf_model = RandomForestRegressor(random_state=42, n_jobs=-1) # 使用所有可用CPU核心
    # 调整参数网格，可以根据需要增减
    rf_param_grid = {
        'n_estimators': [100, 200], # 减少数量以加快速度，可根据性能需求增加
        'max_depth': [10, 20, None], # 添加None表示不限制深度
        'min_samples_split': [5, 10], # 增加最小样本分割数，防止过拟合
        'min_samples_leaf': [3, 5]      # 增加最小叶子节点样本数
        # 'max_features': ['sqrt', 'log2', None] # 可以尝试不同的特征选择策略
    }

    # 使用网格搜索找到最佳参数 (5折交叉验证)
    # scoring 使用 neg_mean_squared_error, 因为GridSearchCV默认最大化分数
    grid_search = GridSearchCV(
        rf_model, rf_param_grid, cv=5,
        scoring='neg_mean_squared_error',
        n_jobs=-1 # 并行运行交叉验证
    )
    grid_search.fit(X_train, y_train)

    best_model = grid_search.best_estimator_
    print(f"\nGridSearchCV 完成。")
    print(f"最佳参数: {grid_search.best_params_}")
    print(f"最佳交叉验证 MSE: {-grid_search.best_score_:.4f}") # 显示正的MSE

    # --- 评估最佳模型 ---
    print("\n在测试集上评估最佳模型...")
    y_pred = best_model.predict(X_test)

    # 计算评估指标
    mse = mean_squared_error(y_test, y_pred)
    r2 = r2_score(y_test, y_pred)
    mae = mean_absolute_error(y_test, y_pred)
    medae = median_absolute_error(y_test, y_pred)

    print(f"  测试集 均方误差 (MSE): {mse:.4f}")
    print(f"  测试集 R² 分数: {r2:.4f}")
    print(f"  测试集 平均绝对误差 (MAE): {mae:.4f}")
    print(f"  测试集 中位绝对误差 (MedAE): {medae:.4f}")

    # 计算并打印 MAPE
    mape_value = np.nan # 初始化
    if mape_available:
        try:
            mape_value = mean_absolute_percentage_error(y_test, y_pred)
            print(f"  测试集 平均绝对百分比误差 (MAPE): {mape_value:.2f}%")
        except Exception as e:
             print(f"  计算MAPE时出错: {e}")
    else:
        # 手动计算 MAPE，处理 y_test 中的零值
        non_zero_mask = y_test != 0
        if np.any(non_zero_mask):
            mape_value = np.mean(np.abs((y_test[non_zero_mask] - y_pred[non_zero_mask]) / y_test[non_zero_mask])) * 100
            print(f"  测试集 平均绝对百分比误差 (MAPE): {mape_value:.2f}%")
        else:
            print("  警告: 无法计算MAPE，因为测试集目标变量全为零或仅含零。")


    # 绘制预测值与实际值的对比图
    plt.figure(figsize=(10, 6))
    plt.scatter(y_test, y_pred, alpha=0.6, label='预测值 vs 实际值') # 增加透明度
    plt.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--', label='理想情况 (y=x)')

    # 使用更明确的字体设置，确保中文显示
    font_properties = {'family': plt.rcParams['font.sans-serif'][0], 'size': 12}
    title_font = {'family': plt.rcParams['font.sans-serif'][0], 'size': 14, 'weight': 'bold'}
    legend_font = {'family': plt.rcParams['font.sans-serif'][0], 'size': 10}

    plt.xlabel(f'实际 {target_col}', fontdict=font_properties)
    plt.ylabel(f'预测 {target_col}', fontdict=font_properties)
    plt.title(f'预测值 vs 实际值 (RandomForest, 测试集)\nMSE={mse:.2f}, R²={r2:.2f}, MAE={mae:.2f}', fontdict=title_font)

    # 设置图例字体
    legend = plt.legend(prop=legend_font)

    # 设置刻度标签字体
    plt.tick_params(labelsize=10)
    for label in plt.gca().get_xticklabels() + plt.gca().get_yticklabels():
        label.set_fontproperties(plt.rcParams['font.sans-serif'][0])

    plt.grid(True)
    plt.tight_layout()
    try:
        plt.savefig('prediction_vs_actual.png', dpi=300, bbox_inches='tight')
        print("预测值与实际值对比图已保存至 'prediction_vs_actual.png'")
    except Exception as e:
        print(f"保存预测值与实际值对比图时出错: {e}")
    plt.close()

    # 误差分析：找出误差最大的 N 个点
    errors = np.abs(y_test - y_pred)
    error_analysis = pd.DataFrame({
        '实际值': y_test,
        '预测值': y_pred.round(2), # 保留两位小数
        '绝对误差': errors.round(2)
    })
    # 可以加入原始特征值帮助分析，但需要确保索引对齐
    # error_analysis = error_analysis.join(X_test) # 合并特征值

    worst_predictions = error_analysis.sort_values('绝对误差', ascending=False).head(10)
    print("\n误差最大的10个预测点:")
    print(worst_predictions)
    try:
        error_analysis.to_csv('prediction_errors.csv', index=True) # 保存索引（可能是日期）
        print("误差分析已保存至 'prediction_errors.csv'")
    except Exception as e:
        print(f"保存误差分析时出错: {e}")

    # --- 模型训练评估结束 ---


    # 9. 保存模型和相关信息
    model = best_model
    final_features = selected_features # 使用特征选择后的列表

    print("\n保存模型及相关信息...")
    try:
        # 保存主模型
        joblib.dump(model, 'weather_model.pkl')
        # 保存选择的特征列表
        joblib.dump(final_features, 'selected_features.pkl')
        # 保存模型信息
        model_info = {
            'target_col': target_col,
            'is_classification': False, # 当前固定为回归
            'feature_names': final_features,
            'training_date': pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S'), # 记录训练时间
            'test_mse': mse, # 保存关键评估指标
            'test_r2': r2,
            'test_mae': mae
        }
        joblib.dump(model_info, 'model_info.pkl')
        print("模型和相关信息已成功保存至：")
        print("  - weather_model.pkl")
        print("  - selected_features.pkl")
        print("  - model_info.pkl")
        print("\n现在可以使用 weather_predict.py 脚本进行预测。")
    except Exception as e:
        print(f"保存模型时出错: {e}")


    print("\n程序执行完成!")

if __name__ == "__main__":
    main()
