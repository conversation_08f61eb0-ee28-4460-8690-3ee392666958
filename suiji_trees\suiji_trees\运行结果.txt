PS D:\Code-money\suiji_trees\suiji_trees> python .\weather_prediction.py
系统检测到的字体数量: 401
检测到的中文字体: ['Microsoft YaHei', 'SimSun', 'SimSun-ExtB', 'Microsoft YaHei', 'Microsoft YaHei']...
==================================================
厦门市天气预测模型训练 - 随机森林
==================================================
正在加载数据...
数据加载成功，共有 29162 行，29 列

数据基本信息:
<class 'pandas.core.frame.DataFrame'>
RangeIndex: 29162 entries, 0 to 29161
Data columns (total 29 columns):
 #   Column  Non-Null Count  Dtype  
---  ------  --------------  -----  
 0   日期      29162 non-null  object 
 1   T       29156 non-null  float64
 2   Po      29157 non-null  float64
 3   P       29151 non-null  float64
 4   Pa      26888 non-null  float64
 5   U       29156 non-null  float64
 6   DD      29161 non-null  object 
 7   Ff      29161 non-null  float64
 8   ff10    0 non-null      float64
 9   ff3     14007 non-null  float64
 10  N       14957 non-null  object 
 11  WW      29162 non-null  object 
 12  W1      12535 non-null  object 
 13  W2      12535 non-null  object 
 14  Tn      16372 non-null  float64
 15  Tx      14419 non-null  float64
 16  Cl      4 non-null      object 
 17  Nh      7591 non-null   object 
 18  H       20322 non-null  object 
 19  Cm      1 non-null      object 
 20  Ch      0 non-null      float64
 21  VV      28680 non-null  object 
 22  Td      29162 non-null  float64
 23  RRR     16354 non-null  object 
 24  tR      16354 non-null  float64
 25  E       23 non-null     object 
 26  Tg      0 non-null      float64
 27  E'      0 non-null      float64
 28  sss     0 non-null      float64
dtypes: float64(16), object(13)
memory usage: 6.5+ MB
None

数据统计描述:
              T        Po         P        Pa  ...        tR   Tg   E'  sss
count  29156.00  29157.00  29151.00  26888.00  ...  16354.00  0.0  0.0  0.0
mean      21.90    748.36    760.64      0.00  ...     10.69  NaN  NaN  NaN
std        6.26      5.02      5.31      0.99  ...      2.49  NaN  NaN  NaN
min        0.40    723.60    735.20    -10.90  ...      6.00  NaN  NaN  NaN
25%       16.80    744.50    756.50     -0.60  ...     12.00  NaN  NaN  NaN
50%       22.60    748.40    760.60      0.00  ...     12.00  NaN  NaN  NaN
75%       27.00    752.20    764.70      0.70  ...     12.00  NaN  NaN  NaN
max       38.40    764.70    778.00      9.50  ...     24.00  NaN  NaN  NaN

[8 rows x 16 columns]

前5行数据:
                 日期     T     Po      P   Pa     U  ...  RRR    tR    E  Tg  E' sss
0  31.12.2024 23:00  13.0  753.4  766.0  0.5  48.0  ...  无降水  12.0  NaN NaN NaN NaN
1  31.12.2024 20:00  14.1  752.9  765.7  1.4  51.0  ...  无降水  12.0  NaN NaN NaN NaN
2  31.12.2024 17:00  15.8  751.5  764.2 -0.5  62.0  ...  无降水  12.0  NaN NaN NaN NaN
3  31.12.2024 14:00  18.8  752.0  764.6 -2.1  45.0  ...  无降水  12.0  NaN NaN NaN NaN
4  31.12.2024 11:00  17.4  754.1  766.8  0.1  51.0  ...  无降水  12.0  NaN NaN NaN NaN

[5 rows x 29 columns]

缺失值统计:
      缺失值数量  缺失比例(%)
sss   29162   100.00
E'    29162   100.00
Tg    29162   100.00
Ch    29162   100.00
Cm    29161   100.00
ff10  29162   100.00
Cl    29158    99.99
E     29139    99.92
Nh    21571    73.97
W1    16627    57.02
W2    16627    57.02
ff3   15155    51.97
Tx    14743    50.56
N     14205    48.71
RRR   12808    43.92
tR    12808    43.92
Tn    12790    43.86
H      8840    30.31
Pa     2274     7.80
VV      482     1.65
P        11     0.04
Po        5     0.02
U         6     0.02
T         6     0.02
Ff        1     0.00
DD        1     0.00

开始数据清洗...
删除预设的文本特征列: ['DD', 'Ff', 'WW', 'W1', 'W2', 'H', 'Cl', 'Cm', 'Ch', 'E', "E'"]
日期列已成功按 '%d.%m.%Y %H:%M' 格式解析
警告: 'RRR' 列中有 13332 个值无法转换为数值，已填充为0。
已将'RRR'列内容处理并存储到'降水量'列。
删除缺失比例高于50%的特征: ['sss', 'Tg', 'ff10', 'Nh', 'ff3', 'Tx']
数值特征 'T' 的缺失值已用中位数 22.60 填充
数值特征 'Po' 的缺失值已用中位数 748.40 填充
数值特征 'P' 的缺失值已用中位数 760.60 填充
数值特征 'Pa' 的缺失值已用中位数 0.00 填充
数值特征 'U' 的缺失值已用中位数 77.00 填充
数值特征 'Tn' 的缺失值已用中位数 20.20 填充
数值特征 'tR' 的缺失值已用中位数 12.00 填充
分类特征 'N' 的缺失值已用众数 '由于雾和/或其他气象现象，天空不可见。' 填充
分类特征 'VV' 的缺失值已用众数 '30' 填充
数据清洗完成，剩余数据: 29162 行, 12 列。无缺失值。

开始数据预处理与特征工程...
已从日期中提取年、月、日、星期、小时和季节特征
已将 Po 从毫米汞柱转换为帕斯卡单位，存储为 Po_Pa
已将 P 从毫米汞柱转换为帕斯卡单位，存储为 P_Pa
已将 Pa 从毫米汞柱转换为帕斯卡单位，存储为 Pa_Pa
生成时间序列特征 (滞后、移动平均、趋势)...
跳过时间序列特征生成：列 'Tx' 不存在。
因生成时间序列特征，删除了 7 行开头的NaN数据。
添加时间序列特征后数据集大小: (29155, 56)
分类特征 'N' 已使用 LabelEncoder 编码
分类特征 'VV' 已使用 LabelEncoder 编码
数据预处理完成，最终数据集大小: (29155, 56)

选择 'T' 作为主要目标变量进行训练和评估。

开始特征选择...
使用 RandomForestRegressor 进行特征重要性计算...

特征重要性排名 (Top 20):
          特征       重要性
20     T_ma3  0.925667
22   T_trend  0.038633
0          U  0.016208
16    T_lag1  0.007029
4         Td  0.005226
19    T_lag7  0.004282
17    T_lag2  0.001242
11        小时  0.000450
36  Po_trend  0.000195
15     Pa_Pa  0.000118
18    T_lag3  0.000076
29   U_trend  0.000065
21     T_ma7  0.000059
24    U_lag2  0.000055
2         Tn  0.000053
25    U_lag3  0.000042
9          日  0.000036
26    U_lag7  0.000034
3         VV  0.000032
33   Po_lag7  0.000030
特征重要性图已保存至 'feature_importance.png'
基于阈值选择的特征数量 (7) 少于最小要求 (15)，将选择Top 15 重要特征。

最终选择的特征数量: 15
选择的特征: ['T_ma3', 'T_trend', 'U', 'T_lag1', 'Td', 'T_lag7', 'T_lag2', '小时', 'Po_trend', 'Pa_Pa', 'T_lag3', 'U_trend', 'T_ma7', 'U_lag2', 'Tn']

使用时间序列分割方法 (80% 训练, 20% 测试)...
训练集大小: 23324，测试集大小: 5831

开始训练随机森林模型 (使用GridSearchCV进行超参数调优)...

GridSearchCV 完成。
最佳参数: {'max_depth': 20, 'min_samples_leaf': 3, 'min_samples_split': 5, 'n_estimators': 200}
最佳交叉验证 MSE: 0.0408

在测试集上评估最佳模型...
  测试集 均方误差 (MSE): 0.0299
  测试集 R² 分数: 0.9992
  测试集 平均绝对误差 (MAE): 0.0746
  测试集 中位绝对误差 (MedAE): 0.0302
  测试集 平均绝对百分比误差 (MAPE): 0.00%
预测值与实际值对比图已保存至 'prediction_vs_actual.png'

误差最大的10个预测点:
       实际值    预测值  绝对误差
5703  22.6  18.77  3.83
5702  22.6  20.01  2.59
5701  14.3  16.70  2.40
5489  13.3  15.36  2.06
5700  14.7  16.48  1.78
5650   9.5  11.25  1.75
2352  24.0  22.27  1.73
5362  13.4  15.12  1.72
5361  15.4  17.10  1.70
4079  34.0  32.47  1.53
误差分析已保存至 'prediction_errors.csv'

保存模型及相关信息...
模型和相关信息已成功保存至：
  - weather_model.pkl
  - selected_features.pkl
  - model_info.pkl

现在可以使用 weather_predict.py 脚本进行预测。

程序执行完成!
PS D:\Code-money\suiji_trees\suiji_trees> 