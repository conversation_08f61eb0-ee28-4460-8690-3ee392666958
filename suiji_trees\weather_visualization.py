#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
厦门市天气数据可视化
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os

# 设置中文显示
try:
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'sans-serif']  # 尝试多种字体
    plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号
except:
    print("警告: 无法设置中文字体，图表中的中文可能无法正确显示")

def load_data(file_path):
    """
    加载数据
    """
    try:
        # 尝试读取文件
        if file_path.endswith('.xls') or file_path.endswith('.xlsx'):
            try:
                # 尝试读取Excel文件
                df = pd.read_excel(file_path)
            except Exception as excel_error:
                print(f"Excel读取失败: {excel_error}")
                # 尝试读取CSV文件（如果已经由预测脚本生成）
                csv_path = file_path.replace('.xls', '.csv').replace('.xlsx', '.csv')
                if os.path.exists(csv_path):
                    print(f"尝试读取CSV文件: {csv_path}")
                    df = pd.read_csv(csv_path, encoding='utf-8-sig')
                else:
                    raise excel_error
        elif file_path.endswith('.csv'):
            # 读取CSV文件
            df = pd.read_csv(file_path, encoding='utf-8-sig')
        else:
            raise ValueError(f"不支持的文件格式: {file_path}")

        print(f"数据加载成功，共有 {df.shape[0]} 行，{df.shape[1]} 列")
        return df
    except Exception as e:
        print(f"数据加载失败: {e}")
        return None

def visualize_temperature_trends(df):
    """
    可视化温度趋势
    """
    if 'T' not in df.columns or '日期' not in df.columns:
        print("数据中缺少温度或日期列，无法绘制温度趋势图")
        return

    # 确保日期列是日期类型
    df['日期'] = pd.to_datetime(df['日期'])

    # 按日期排序
    df = df.sort_values('日期')

    # 绘制温度趋势图
    plt.figure(figsize=(12, 6))
    plt.plot(df['日期'], df['T'], marker='o', linestyle='-', markersize=2)
    plt.title('厦门市温度趋势')
    plt.xlabel('日期')
    plt.ylabel('温度 (°C)')
    plt.grid(True)
    plt.tight_layout()
    plt.savefig('temperature_trends.png')
    print("温度趋势图已保存至 'temperature_trends.png'")

def visualize_weather_distribution(df):
    """
    可视化天气分布
    """
    if 'WW' not in df.columns:
        print("数据中缺少天气情况列，无法绘制天气分布图")
        return

    # 检查WW列的类型
    if df['WW'].dtype == 'object':
        # 如果是文本类型，创建一个映射
        weather_map = {
            '晴': '晴',
            '多云': '多云',
            '阴': '阴',
            '雾': '雾/薄雾',
            '薄雾': '雾/薄雾',
            '雨': '雨',
            '小雨': '小雨',
            '中雨': '中雨',
            '大雨': '大雨',
            '雷雨': '雷雨',
            '雪': '雪',
            '雨夹雪': '雨夹雪'
        }

        # 尝试映射天气情况
        def map_weather(weather_desc):
            if pd.isna(weather_desc):
                return '未知'

            for key, value in weather_map.items():
                if key in str(weather_desc):
                    return value

            return '其他'

        # 创建一个新列用于分类
        df['天气类别'] = df['WW'].apply(map_weather)

        # 计算天气情况的分布
        weather_counts = df['天气类别'].value_counts().sort_values(ascending=False)
    else:
        # 如果是数值类型，直接计算分布
        weather_counts = df['WW'].value_counts().sort_values(ascending=False)

        # 如果数值类型，尝试添加标签
        if df['WW'].dtype in ['int64', 'float64']:
            weather_labels = {
                0: '晴',
                1: '多云',
                2: '阴',
                3: '雾/薄雾',
                4: '小雨',
                5: '中雨',
                6: '大雨',
                7: '雷雨',
                8: '雪',
                9: '雨夹雪',
                10: '其他'
            }

            # 创建新的索引标签
            new_labels = [weather_labels.get(idx, str(idx)) for idx in weather_counts.index]
            weather_counts.index = new_labels

    # 绘制天气分布图
    plt.figure(figsize=(12, 6))
    plt.bar(weather_counts.index, weather_counts.values)
    plt.title('厦门市天气情况分布')
    plt.xlabel('天气情况')
    plt.ylabel('频次')
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig('weather_distribution.png')
    print("天气分布图已保存至 'weather_distribution.png'")

def visualize_correlation_matrix(df):
    """
    可视化相关性矩阵
    """
    # 选择数值型列
    numeric_df = df.select_dtypes(include=['int64', 'float64'])

    # 计算相关性矩阵
    corr_matrix = numeric_df.corr()

    # 绘制相关性热图
    plt.figure(figsize=(14, 12))
    mask = np.triu(np.ones_like(corr_matrix, dtype=bool))

    # 使用matplotlib代替seaborn绘制热图
    masked_corr = np.ma.array(corr_matrix, mask=mask)
    cmap = plt.cm.coolwarm
    cmap.set_bad('white')
    plt.imshow(masked_corr, interpolation='nearest', cmap=cmap, vmin=-1, vmax=1)
    plt.colorbar()

    # 添加标题
    plt.title('特征相关性矩阵')
    plt.tight_layout()
    plt.savefig('correlation_matrix.png')
    print("相关性矩阵图已保存至 'correlation_matrix.png'")

    # 找出与目标变量相关性最高的特征
    if 'T' in numeric_df.columns:
        temp_corr = corr_matrix['T'].sort_values(ascending=False)
        print("\n与温度 (T) 相关性最高的特征:")
        print(temp_corr)

    if 'WW' in numeric_df.columns:
        weather_corr = corr_matrix['WW'].sort_values(ascending=False)
        print("\n与天气情况 (WW) 相关性最高的特征:")
        print(weather_corr)

def visualize_seasonal_patterns(df):
    """
    可视化季节性模式
    """
    if 'T' not in df.columns or '日期' not in df.columns:
        print("数据中缺少温度或日期列，无法绘制季节性模式图")
        return

    # 确保日期列是日期类型
    df['日期'] = pd.to_datetime(df['日期'])

    # 提取月份
    df['月份'] = df['日期'].dt.month

    # 按月份分组计算平均温度
    monthly_temp = df.groupby('月份')['T'].mean()

    # 绘制月度平均温度图
    plt.figure(figsize=(10, 6))
    plt.bar(monthly_temp.index, monthly_temp.values)
    plt.title('厦门市月度平均温度')
    plt.xlabel('月份')
    plt.ylabel('平均温度 (°C)')
    plt.grid(True, axis='y')
    plt.tight_layout()
    plt.savefig('monthly_temperature.png')
    print("月度平均温度图已保存至 'monthly_temperature.png'")

def visualize_future_predictions(predictions_path):
    """
    可视化未来天气预测结果
    """
    try:
        predictions = pd.read_csv(predictions_path, encoding='utf-8-sig')
        print(f"预测结果加载成功，共有 {predictions.shape[0]} 行，{predictions.shape[1]} 列")

        # 确保日期列是日期类型
        predictions['日期'] = pd.to_datetime(predictions['日期'])

        # 找出预测的目标变量列
        target_cols = [col for col in predictions.columns if col not in ['日期', '年', '月', '日', '星期', '季节']
                       and not col.endswith('_概率')]

        if not target_cols:
            print("预测结果中没有找到目标变量列")
            return

        target_col = target_cols[0]

        # 绘制预测结果图
        plt.figure(figsize=(10, 6))
        plt.plot(predictions['日期'], predictions[target_col], marker='o', linestyle='-')
        plt.title(f'厦门市未来天气预测 - {target_col}')
        plt.xlabel('日期')
        plt.ylabel(target_col)
        plt.grid(True)
        plt.tight_layout()
        plt.savefig('future_predictions.png')
        print("未来预测结果图已保存至 'future_predictions.png'")

    except Exception as e:
        print(f"预测结果可视化失败: {e}")

def main():
    """
    主函数
    """
    print("=" * 50)
    print("厦门市天气数据可视化")
    print("=" * 50)

    # 加载数据
    file_path = 'weather.xls'
    df = load_data(file_path)

    if df is None:
        print("程序终止: 无法加载数据")
        return

    # 创建可视化目录
    os.makedirs('suiji_trees', exist_ok=True)

    # 可视化温度趋势
    visualize_temperature_trends(df)

    # 可视化天气分布
    visualize_weather_distribution(df)

    # 可视化相关性矩阵
    visualize_correlation_matrix(df)

    # 可视化季节性模式
    visualize_seasonal_patterns(df)

    # 可视化未来预测结果（如果存在）
    predictions_path = 'future_weather_predictions.csv'
    if os.path.exists(predictions_path):
        visualize_future_predictions(predictions_path)
    else:
        print(f"预测结果文件 '{predictions_path}' 不存在，请先运行预测模型")

    print("\n可视化程序执行完成!")

if __name__ == "__main__":
    main()
