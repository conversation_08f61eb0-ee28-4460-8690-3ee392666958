import tkinter as tk
from tkinter import filedialog, messagebox
import customtkinter as ctk
import pandas as pd
import numpy as np
import random
import threading
import os
import concurrent.futures
import time
import string
import multiprocessing
from pathlib import Path

# --- 手机号前缀定义 ---
MOBILE_PREFIXES = ["139", "138", "137", "136", "135", "134", "150", "151", "152", "157", "158", "159", "182", "183", "184", "187", "188", "198"]
UNICOM_PREFIXES = ["130", "131", "132", "155", "156", "185", "186", "145", "176"]
TELECOM_PREFIXES = ["133", "153", "180", "181", "189", "199", "177"]
ALL_VALID_PREFIXES = MOBILE_PREFIXES + UNICOM_PREFIXES + TELECOM_PREFIXES
PREFIX_MAP = {}
for prefix in ALL_VALID_PREFIXES:
    PREFIX_MAP.setdefault(prefix[:2], []).append(prefix)

# 移除预生成的随机数字池，使用原始方法生成随机数字

def generate_realistic_number(masked_number_str):
    """保持原有的手机号生成函数逻辑"""
    if not isinstance(masked_number_str, str):
        return None
    
    # 去除空格并验证格式
    masked_number_str = masked_number_str.strip()
    if not (len(masked_number_str) == 11 and masked_number_str[2:9] == "*******"):
        return None
        
    # 提取首尾数字
    first_two_digits = masked_number_str[:2]
    last_two_digits = masked_number_str[-2:]
    
    # 根据前两位选择前缀
    possible_full_prefixes = PREFIX_MAP.get(first_two_digits)
    if not possible_full_prefixes:
        chosen_prefix = random.choice(ALL_VALID_PREFIXES)
    else:
        chosen_prefix = random.choice(possible_full_prefixes)
    
    # 保持原始随机生成方式
    middle_four_digits = "".join(random.choices("0123456789", k=4))
    user_number_part1 = "".join(random.choices("0123456789", k=2))
    
    # 组装手机号
    new_phone_number = f"{chosen_prefix}{middle_four_digits}{user_number_part1}{last_two_digits}"
    return new_phone_number

def process_chunk(chunk_data):
    """处理一批数据的函数"""
    source_column = chunk_data['source_col']
    results = []
    
    for idx, value in enumerate(source_column):
        new_phone = None
        if isinstance(value, str):
            new_phone = generate_realistic_number(value)
        results.append(new_phone)
    
    return results


class App(ctk.CTk):
    def __init__(self):
        super().__init__()

        self.title("Excel 号码替换工具 (高速版)")
        self.geometry("700x700")
        ctk.set_appearance_mode("System")
        ctk.set_default_color_theme("blue")

        self.grid_columnconfigure(0, weight=1)
        self.grid_rowconfigure(7, weight=1)

        self.is_processing = False
        self.cancel_processing = False

        # --- 文件选择 ---
        self.file_frame = ctk.CTkFrame(self)
        self.file_frame.grid(row=0, column=0, padx=20, pady=(20,10), sticky="ew")
        self.file_frame.grid_columnconfigure(1, weight=1)
        self.select_button = ctk.CTkButton(self.file_frame, text="选择 Excel 文件 (.xlsx)", command=self.select_file)
        self.select_button.grid(row=0, column=0, padx=10, pady=10)
        self.file_label = ctk.CTkLabel(self.file_frame, text="未选择文件", wraplength=480)
        self.file_label.grid(row=0, column=1, padx=10, pady=10, sticky="w")
        self.filepath = None

        # --- 新增保存选项 ---
        self.save_frame = ctk.CTkFrame(self)
        self.save_frame.grid(row=1, column=0, padx=20, pady=5, sticky="ew")
        
        self.save_mode_var = tk.StringVar(value="original")
        self.save_original_radio = ctk.CTkRadioButton(
            self.save_frame, text="覆盖原文件", 
            variable=self.save_mode_var, value="original")
        self.save_original_radio.grid(row=0, column=0, padx=10, pady=5, sticky="w")
        
        self.save_new_radio = ctk.CTkRadioButton(
            self.save_frame, text="保存到新文件", 
            variable=self.save_mode_var, value="new")
        self.save_new_radio.grid(row=0, column=1, padx=10, pady=5, sticky="w")

        # --- 列选择 ---
        self.column_select_frame = ctk.CTkFrame(self)
        self.column_select_frame.grid(row=2, column=0, padx=20, pady=5, sticky="ew")
        self.column_select_frame.grid_columnconfigure((1, 3), weight=1)

        self.source_col_label = ctk.CTkLabel(self.column_select_frame, text="源数据列 (含*号号码):")
        self.source_col_label.grid(row=0, column=0, padx=(10,5), pady=5, sticky="w")
        
        excel_columns = list(string.ascii_uppercase) # A-Z
        self.source_col_var = ctk.StringVar(value="C") # Default C
        self.source_col_menu = ctk.CTkOptionMenu(self.column_select_frame, variable=self.source_col_var, values=excel_columns)
        self.source_col_menu.grid(row=0, column=1, padx=5, pady=5, sticky="ew")

        self.target_col_label = ctk.CTkLabel(self.column_select_frame, text="目标数据列 (新号码):")
        self.target_col_label.grid(row=0, column=2, padx=(20,5), pady=5, sticky="w")
        
        self.target_col_var = ctk.StringVar(value="E") # Default E
        self.target_col_menu = ctk.CTkOptionMenu(self.column_select_frame, variable=self.target_col_var, values=excel_columns)
        self.target_col_menu.grid(row=0, column=3, padx=5, pady=5, sticky="ew")

        # --- 处理选项 ---
        self.options_frame = ctk.CTkFrame(self)
        self.options_frame.grid(row=3, column=0, padx=20, pady=5, sticky="ew")
        self.options_frame.grid_columnconfigure(0, weight=1)
        self.options_frame.grid_columnconfigure(1, weight=1)
        
        # 进程数调节
        cpu_cores = multiprocessing.cpu_count()
        self.default_procs = min(max(cpu_cores - 1, 1), 8)  # 默认使用CPU核心数-1，至少1个
        self.max_procs = cpu_cores * 2
        
        self.proc_label = ctk.CTkLabel(self.options_frame, text=f"进程数: {self.default_procs}")
        self.proc_label.grid(row=0, column=0, padx=(10,5), pady=5, sticky="w")
        
        self.proc_slider = ctk.CTkSlider(
            self.options_frame, from_=1, to=self.max_procs,
            number_of_steps=self.max_procs - 1 if self.max_procs > 1 else 1,
            command=self.update_proc_label
        )
        self.proc_slider.set(self.default_procs)
        self.proc_slider.grid(row=0, column=1, padx=5, pady=5, sticky="ew")
        
        # 批处理大小调节
        self.chunk_size_label = ctk.CTkLabel(self.options_frame, text="批处理大小: 10000")
        self.chunk_size_label.grid(row=1, column=0, padx=(10,5), pady=5, sticky="w")
        
        self.chunk_size_slider = ctk.CTkSlider(
            self.options_frame, from_=1000, to=100000,
            number_of_steps=99,
            command=self.update_chunk_size_label
        )
        self.chunk_size_slider.set(10000)
        self.chunk_size_slider.grid(row=1, column=1, padx=5, pady=5, sticky="ew")

        # --- 处理控制 ---
        self.control_frame = ctk.CTkFrame(self)
        self.control_frame.grid(row=4, column=0, padx=20, pady=10, sticky="ew")
        self.control_frame.grid_columnconfigure(0, weight=1)
        self.control_frame.grid_columnconfigure(1, weight=1)
        
        self.process_button = ctk.CTkButton(
            self.control_frame, text="开始处理", 
            command=self.start_processing_thread, 
            state="disabled"
        )
        self.process_button.grid(row=0, column=0, padx=10, pady=10, sticky="ew")
        
        self.cancel_button = ctk.CTkButton(
            self.control_frame, text="取消", 
            command=self.cancel_processing_request,
            state="disabled"
        )
        self.cancel_button.grid(row=0, column=1, padx=10, pady=10, sticky="ew")

        # --- 进度与状态 ---
        self.progress_frame = ctk.CTkFrame(self)
        self.progress_frame.grid(row=5, column=0, padx=20, pady=10, sticky="ew")
        self.progress_frame.grid_columnconfigure(0, weight=1)
        
        self.status_label = ctk.CTkLabel(self.progress_frame, text="状态: 空闲")
        self.status_label.grid(row=0, column=0, padx=10, pady=(5,0), sticky="w")
        
        self.progressbar = ctk.CTkProgressBar(self.progress_frame, orientation="horizontal")
        self.progressbar.set(0)
        self.progressbar.grid(row=1, column=0, padx=10, pady=(0,10), sticky="ew")
        
        # --- 日志区域 ---
        self.log_frame = ctk.CTkFrame(self)
        self.log_frame.grid(row=6, column=0, padx=20, pady=(0,20), sticky="nsew")
        self.log_frame.grid_columnconfigure(0, weight=1)
        self.log_frame.grid_rowconfigure(1, weight=1)
        
        self.log_label = ctk.CTkLabel(self.log_frame, text="处理日志:")
        self.log_label.grid(row=0, column=0, padx=10, pady=(5,0), sticky="w")
        
        self.log_textbox = ctk.CTkTextbox(self.log_frame, wrap="word", state="disabled", height=150)
        self.log_textbox.grid(row=1, column=0, padx=10, pady=10, sticky="nsew")
        self.log_textbox.tag_config("error", foreground="red")
        self.log_textbox.tag_config("success", foreground="green")
        self.log_textbox.tag_config("info", foreground=self._apply_appearance_mode(ctk.ThemeManager.theme["CTkLabel"]["text_color"]))

    def update_proc_label(self, value):
        self.proc_label.configure(text=f"进程数: {int(value)}")

    def update_chunk_size_label(self, value):
        self.chunk_size_label.configure(text=f"批处理大小: {int(value)}")

    def log_message_safe(self, message, level="info"):
        def _log():
            self.log_textbox.configure(state="normal")
            self.log_textbox.insert(tk.END, f"{time.strftime('%H:%M:%S')} - {message}\n", level)
            self.log_textbox.configure(state="disabled")
            self.log_textbox.see(tk.END)
        self.after(0, _log)

    def update_progress_safe(self, value, status_text=""):
        def _update():
            self.progressbar.set(value)
            if status_text:
                self.status_label.configure(text=f"状态: {status_text}")
        self.after(0, _update)

    def select_file(self):
        if self.is_processing: return
        self.filepath = filedialog.askopenfilename(
            title="选择 Excel 文件",
            filetypes=(("Excel files", "*.xlsx"), ("All files", "*.*"))
        )
        if self.filepath:
            self.file_label.configure(text=os.path.basename(self.filepath))
            self.process_button.configure(state="normal")
            self.log_message_safe(f"已选择文件: {self.filepath}", "info")
        else:
            self.file_label.configure(text="未选择文件")
            self.process_button.configure(state="disabled")
            self.log_message_safe("取消选择文件。", "info")

    def set_ui_processing_state(self, processing: bool):
        self.is_processing = processing
        normal_state = "disabled" if processing else "normal"
        cancel_state = "normal" if processing else "disabled"
        
        self.select_button.configure(state=normal_state)
        self.process_button.configure(state=normal_state)
        self.cancel_button.configure(state=cancel_state)
        self.proc_slider.configure(state=normal_state)
        self.chunk_size_slider.configure(state=normal_state)
        self.source_col_menu.configure(state=normal_state)
        self.target_col_menu.configure(state=normal_state)
        self.save_original_radio.configure(state=normal_state)
        self.save_new_radio.configure(state=normal_state)

    def cancel_processing_request(self):
        if self.is_processing:
            self.log_message_safe("正在取消处理...", "info")
            self.cancel_processing = True

    def start_processing_thread(self):
        if not self.filepath:
            messagebox.showerror("错误", "请先选择一个 Excel 文件。")
            return
        if self.is_processing:
            self.log_message_safe("已经在处理中，请等待当前任务完成。", "info")
            return

        source_col_letter = self.source_col_var.get()
        target_col_letter = self.target_col_var.get()

        if source_col_letter == target_col_letter:
            messagebox.showwarning("警告", "源数据列和目标数据列不能相同，否则会覆盖原始数据！")
            return

        self.cancel_processing = False
        self.set_ui_processing_state(True)
        self.update_progress_safe(0, "正在准备...")
        self.log_message_safe("开始处理...", "info")

        num_processes = int(self.proc_slider.get())
        chunk_size = int(self.chunk_size_slider.get())
        save_mode = self.save_mode_var.get()
        
        self.log_message_safe(
            f"使用 {num_processes} 个进程，批处理大小: {chunk_size}。"
            f"源列: {source_col_letter}, 目标列: {target_col_letter}, "
            f"保存模式: {'覆盖原文件' if save_mode == 'original' else '保存到新文件'}", 
            "info"
        )

        thread = threading.Thread(
            target=self.run_excel_processing_logic, 
            args=(num_processes, chunk_size, source_col_letter, target_col_letter, save_mode)
        )
        thread.daemon = True
        thread.start()

    def run_excel_processing_logic(self, num_processes, chunk_size, source_column_letter, target_column_letter, save_mode):
        overall_start_time = time.time()
        try:
            self.log_message_safe("步骤 1/5: 加载 Excel 文件...", "info")
            load_start_time = time.time()
            
            # 先读取文件以获取数据，稍后再使用openpyxl处理完整格式
            try:
                # 先尝试用pandas读取，更快
                df = pd.read_excel(self.filepath, engine='openpyxl')
                load_time = time.time() - load_start_time
                self.log_message_safe(f"Excel 文件加载完成，耗时: {load_time:.2f} 秒。文件大小: {len(df)} 行。", "info")
                
                # 读取现有数据列
                source_col_idx = ord(source_column_letter) - ord('A')
                if source_col_idx < len(df.columns):
                    source_col = df.iloc[:, source_col_idx]
                else:
                    # 如果指定列不存在
                    self.log_message_safe(f"警告: 指定的源列 '{source_column_letter}' 超出数据范围，将使用空列", "info")
                    source_col = pd.Series([None] * len(df))
                    
            except Exception as e:
                self.log_message_safe(f"尝试使用pandas加载文件失败: {str(e)}，切换到openpyxl读取模式...", "info")
                import openpyxl
                wb = openpyxl.load_workbook(self.filepath, read_only=True)
                sheet = wb.active
                
                # 获取指定列的所有值
                source_col_idx = ord(source_column_letter) - ord('A') + 1  # openpyxl列索引从1开始
                source_values = []
                
                # 获取最大行数
                max_row = sheet.max_row
                for row_idx in range(1, max_row + 1):
                    cell = sheet.cell(row=row_idx, column=source_col_idx)
                    source_values.append(cell.value)
                
                load_time = time.time() - load_start_time
                self.log_message_safe(f"Excel 文件加载完成，耗时: {load_time:.2f} 秒。文件大小: {len(source_values)} 行。", "info")
                
                # 创建Series对象以便与pandas版本保持一致的处理逻辑
                source_col = pd.Series(source_values)
            
            self.log_message_safe(f"步骤 2/5: 将数据分成{num_processes}个批次处理...", "info")
            total_rows = len(df)
            
            if total_rows == 0:
                self.log_message_safe(f"文件中没有数据行可处理。", "info")
                self.processing_finished_safe(success=True, message="没有数据行。")
                return
                
            # 将数据分成更小的块
            chunks = []
            for i in range(0, total_rows, chunk_size):
                end_idx = min(i + chunk_size, total_rows)
                chunks.append({'source_col': source_col.iloc[i:end_idx]})
            
            self.log_message_safe(f"数据已分成 {len(chunks)} 个批次，每批最多 {chunk_size} 行。", "info")
            
            self.log_message_safe(f"步骤 3/5: 使用 {num_processes} 个进程并行生成号码...", "info")
            gen_start_time = time.time()
            
            results = []
            chunks_completed = 0
            
            # 使用ProcessPoolExecutor进行真正的并行处理
            with concurrent.futures.ProcessPoolExecutor(max_workers=num_processes) as executor:
                futures = {executor.submit(process_chunk, chunk): i for i, chunk in enumerate(chunks)}
                
                for future in concurrent.futures.as_completed(futures):
                    if self.cancel_processing:
                        for f in futures:
                            f.cancel()
                        self.log_message_safe("处理已取消。", "info")
                        self.processing_finished_safe(success=False, message="处理已取消。")
                        return
                        
                    chunk_idx = futures[future]
                    try:
                        chunk_results = future.result()
                        start_idx = chunk_idx * chunk_size
                        for i, result in enumerate(chunk_results):
                            idx = start_idx + i
                            if idx < total_rows:  # 防止索引越界
                                results.append((idx, result))
                    except Exception as e:
                        self.log_message_safe(f"处理批次 {chunk_idx+1} 时发生错误: {e}", "error")
                    
                    chunks_completed += 1
                    progress = chunks_completed / len(chunks)
                    self.update_progress_safe(progress, f"生成号码... {chunks_completed}/{len(chunks)}")
            
            gen_time = time.time() - gen_start_time
            self.log_message_safe(f"号码生成完成，耗时: {gen_time:.2f} 秒。", "info")
            
            # 对结果排序，确保按正确的行顺序写入
            results.sort(key=lambda x: x[0])
            
            self.log_message_safe(f"步骤 4/5: 使用openpyxl保持原格式，将替换后的号码写入目标列...", "info")
            self.update_progress_safe(0.9, "正在写入数据...")
            
            # 计算替换的号码数量
            replaced_count = sum(1 for _, num in results if num is not None)
            
            # 使用openpyxl直接打开原始文件，保留所有格式
            import openpyxl
            self.log_message_safe("打开原始Excel文件以保留格式...", "info")
            workbook = openpyxl.load_workbook(self.filepath)
            sheet = workbook.active
            
            # 获取目标列索引
            target_col_idx = ord(target_column_letter) - ord('A') + 1  # openpyxl列索引从1开始
            
            # 将生成的号码直接写入特定单元格，保持其他格式不变
            for idx, new_phone in results:
                if new_phone:
                    # 由于pandas和openpyxl索引可能不同，根据行号计算openpyxl行
                    # pandas的idx从0开始，openpyxl从1开始，但可能还需要考虑表头
                    openpyxl_row = idx + 1
                    sheet.cell(row=openpyxl_row, column=target_col_idx).value = new_phone
            
            self.log_message_safe("步骤 5/5: 保存 Excel 文件...", "info")
            self.update_progress_safe(0.95, "正在保存文件...")
            save_start_time = time.time()
            
            if save_mode == 'original':
                output_path = self.filepath
            else:
                # 创建新文件名，添加后缀
                base_path = Path(self.filepath)
                output_path = base_path.parent / f"{base_path.stem}_已处理{base_path.suffix}"
            
            # 使用openpyxl保存，保留所有格式
            workbook.save(output_path)
            
            save_time = time.time() - save_start_time
            self.log_message_safe(f"文件保存完成，耗时: {save_time:.2f} 秒。", "success")
            
            overall_time = time.time() - overall_start_time
            summary_message = (
                f"处理完成！共处理 {total_rows} 条记录，"
                f"成功替换 {replaced_count} 个号码。总耗时: {overall_time:.2f} 秒。"
            )
            self.log_message_safe(summary_message, "success")
            self.log_message_safe(f"结果已保存到: {output_path}", "success")
            
            # 计算处理速度
            speed = total_rows / overall_time if overall_time > 0 else 0
            self.log_message_safe(f"处理速度: {speed:.2f} 行/秒", "success")
            
            self.processing_finished_safe(success=True, message=f"处理完毕！替换了 {replaced_count} 个号码。")
            
        except FileNotFoundError:
            err_msg = f"错误: 文件未找到 - {self.filepath}"
            self.log_message_safe(err_msg, "error")
            self.processing_finished_safe(success=False, message="错误: 文件未找到。")
        except PermissionError:
            err_msg = f"错误: 没有权限写入文件 - {self.filepath}。请关闭 Excel 后重试。"
            self.log_message_safe(err_msg, "error")
            self.processing_finished_safe(success=False, message="错误: 文件权限不足或被占用。")
        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            self.log_message_safe(f"处理过程中发生未知错误: {str(e)}\n详细信息:\n{error_details}", "error")
            self.processing_finished_safe(success=False, message=f"发生严重错误: {str(e)}")
        finally:
            self.after(0, self.set_ui_processing_state, False)

    def processing_finished_safe(self, success, message):
        def _finish():
            self.status_label.configure(text=f"状态: {message}")
            if success:
                self.progressbar.set(1)
                messagebox.showinfo("完成", message)
            else:
                messagebox.showerror("错误", message)
        self.after(0, _finish)

if __name__ == "__main__":
    # 确保使用足够大的递归限制，避免深度递归问题
    import sys
    sys.setrecursionlimit(10000)
    
    # 设置进程启动方法 (在Windows下不需要，但在某些平台需要)
    if hasattr(multiprocessing, 'set_start_method'):
        try:
            multiprocessing.set_start_method('spawn')
        except RuntimeError:
            pass  # 如果已经设置，则忽略
    
    app = App()
    app.mainloop()