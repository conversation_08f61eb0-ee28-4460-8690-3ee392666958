import tkinter as tk
from tkinter import filedialog, messagebox
import customtkinter as ctk
import openpyxl
from openpyxl.utils import column_index_from_string
import random
import threading # 主处理逻辑仍然在线程中，但该线程会启动进程池
import os
import concurrent.futures
import time
import string
import multiprocessing # 导入 multiprocessing

# --- 手机号前缀定义 (保持不变) ---
MOBILE_PREFIXES = ["139", "138", "137", "136", "135", "134", "150", "151", "152", "157", "158", "159", "182", "183", "184", "187", "188", "198"]
UNICOM_PREFIXES = ["130", "131", "132", "155", "156", "185", "186", "145", "176"]
TELECOM_PREFIXES = ["133", "153", "180", "181", "189", "199", "177"]
ALL_VALID_PREFIXES = MOBILE_PREFIXES + UNICOM_PREFIXES + TELECOM_PREFIXES
PREFIX_MAP = {}
for prefix in ALL_VALID_PREFIXES:
    PREFIX_MAP.setdefault(prefix[:2], []).append(prefix)

# --- 核心处理函数 (必须是顶层函数，以便被子进程pickle) ---
def generate_realistic_number_for_process(masked_number_str): # 重命名以区分
    if not isinstance(masked_number_str, str):
        return None
    masked_number_str = masked_number_str.strip()
    if not (len(masked_number_str) == 11 and masked_number_str[2:9] == "*******"):
        return None
    first_two_digits = masked_number_str[:2]
    last_two_digits = masked_number_str[-2:]
    
    # PREFIX_MAP 需要能被子进程访问。
    # 对于多进程，全局变量会被复制到每个子进程的地址空间。
    # 或者，可以将PREFIX_MAP作为参数传递，但这会增加IPC开销。
    # 当前实现，依赖子进程复制全局变量。

    possible_full_prefixes = PREFIX_MAP.get(first_two_digits)
    if not possible_full_prefixes:
        chosen_prefix = random.choice(ALL_VALID_PREFIXES) # ALL_VALID_PREFIXES 也需可访问
    else:
        chosen_prefix = random.choice(possible_full_prefixes)
        
    middle_four_digits = "".join(random.choices("0123456789", k=4))
    user_number_part1 = "".join(random.choices("0123456789", k=2))
    new_phone_number = f"{chosen_prefix}{middle_four_digits}{user_number_part1}{last_two_digits}"
    return new_phone_number

def process_row_task_for_process(row_data_tuple): # 重命名
    row_idx, masked_phone_value = row_data_tuple
    new_phone = None
    if masked_phone_value:
        new_phone = generate_realistic_number_for_process(str(masked_phone_value))
    return row_idx, new_phone, masked_phone_value


class App(ctk.CTk):
    def __init__(self):
        super().__init__()

        self.title("Excel 号码替换工具 (多进程加速)")
        self.geometry("700x650")
        ctk.set_appearance_mode("System")
        ctk.set_default_color_theme("blue")

        self.grid_columnconfigure(0, weight=1)
        self.grid_rowconfigure(5, weight=1) # 日志区域行索引调整

        self.is_processing = False

        # --- 文件选择 ---
        self.file_frame = ctk.CTkFrame(self)
        self.file_frame.grid(row=0, column=0, padx=20, pady=(20,10), sticky="ew")
        self.file_frame.grid_columnconfigure(1, weight=1)
        self.select_button = ctk.CTkButton(self.file_frame, text="选择 Excel 文件 (.xlsx)", command=self.select_file)
        self.select_button.grid(row=0, column=0, padx=10, pady=10)
        self.file_label = ctk.CTkLabel(self.file_frame, text="未选择文件", wraplength=480)
        self.file_label.grid(row=0, column=1, padx=10, pady=10, sticky="w")
        self.filepath = None

        # --- 列选择 ---
        self.column_select_frame = ctk.CTkFrame(self)
        self.column_select_frame.grid(row=1, column=0, padx=20, pady=5, sticky="ew")
        self.column_select_frame.grid_columnconfigure((1, 3), weight=1)
        self.source_col_label = ctk.CTkLabel(self.column_select_frame, text="源数据列:")
        self.source_col_label.grid(row=0, column=0, padx=(10,5), pady=5, sticky="w")
        excel_columns = list(string.ascii_uppercase)
        self.source_col_var = ctk.StringVar(value="C")
        self.source_col_menu = ctk.CTkOptionMenu(self.column_select_frame, variable=self.source_col_var, values=excel_columns)
        self.source_col_menu.grid(row=0, column=1, padx=5, pady=5, sticky="ew")
        self.target_col_label = ctk.CTkLabel(self.column_select_frame, text="目标数据列:")
        self.target_col_label.grid(row=0, column=2, padx=(20,5), pady=5, sticky="w")
        self.target_col_var = ctk.StringVar(value="E")
        self.target_col_menu = ctk.CTkOptionMenu(self.column_select_frame, variable=self.target_col_var, values=excel_columns)
        self.target_col_menu.grid(row=0, column=3, padx=5, pady=5, sticky="ew")

        # --- 进程数调节 ---
        self.process_num_frame = ctk.CTkFrame(self) # Renamed from thread_frame
        self.process_num_frame.grid(row=2, column=0, padx=20, pady=5, sticky="ew")
        cpu_cores = os.cpu_count() or 2 # Default to 2 if cpu_count fails
        self.default_processes = min(cpu_cores, 8) # Sensible default max 8, or CPU cores
        self.max_processes = max(cpu_cores, 2) # Allow at least 2, up to CPU cores
        self.process_num_label = ctk.CTkLabel(self.process_num_frame, text=f"并行进程数: {self.default_processes}")
        self.process_num_label.pack(side="left", padx=(10,5), pady=5)
        self.process_num_slider = ctk.CTkSlider(
            self.process_num_frame, from_=1, to=self.max_processes,
            number_of_steps=self.max_processes - 1 if self.max_processes > 1 else 1,
            command=self.update_process_num_label
        )
        self.process_num_slider.set(self.default_processes)
        self.process_num_slider.pack(side="left", padx=5, pady=5, fill="x", expand=True)

        # --- 处理控制 ---
        self.process_button = ctk.CTkButton(self, text="开始处理", command=self.start_processing_thread, state="disabled")
        self.process_button.grid(row=3, column=0, padx=20, pady=10, sticky="ew")

        # --- 进度与状态 ---
        self.progress_frame = ctk.CTkFrame(self)
        self.progress_frame.grid(row=4, column=0, padx=20, pady=10, sticky="ew")
        self.progress_frame.grid_columnconfigure(0, weight=1)
        self.status_label = ctk.CTkLabel(self.progress_frame, text="状态: 空闲")
        self.status_label.grid(row=0, column=0, padx=10, pady=(5,0), sticky="w")
        self.progressbar = ctk.CTkProgressBar(self.progress_frame, orientation="horizontal")
        self.progressbar.set(0)
        self.progressbar.grid(row=1, column=0, padx=10, pady=(0,10), sticky="ew")
        
        # --- 日志区域 ---
        self.log_textbox = ctk.CTkTextbox(self, wrap="word", state="disabled", height=180) # Increased height
        self.log_textbox.grid(row=5, column=0, padx=20, pady=(0,20), sticky="nsew")
        self.log_textbox.tag_config("error", foreground="red")
        self.log_textbox.tag_config("success", foreground="green")
        self.log_textbox.tag_config("info", foreground=self._apply_appearance_mode(ctk.ThemeManager.theme["CTkLabel"]["text_color"]))

    def update_process_num_label(self, value):
        self.process_num_label.configure(text=f"并行进程数: {int(value)}")

    # GUI update methods (log_message_safe, update_progress_safe, select_file, set_ui_processing_state) remain largely the same
    def log_message_safe(self, message, level="info"):
        def _log():
            self.log_textbox.configure(state="normal")
            self.log_textbox.insert(tk.END, f"{time.strftime('%H:%M:%S')} - {message}\n", level)
            self.log_textbox.configure(state="disabled")
            self.log_textbox.see(tk.END)
        self.after(0, _log)

    def update_progress_safe(self, value, status_text=""):
        def _update():
            self.progressbar.set(value)
            if status_text:
                self.status_label.configure(text=f"状态: {status_text}")
        self.after(0, _update)

    def select_file(self):
        if self.is_processing: return
        self.filepath = filedialog.askopenfilename(
            title="选择 Excel 文件", filetypes=(("Excel files", "*.xlsx"), ("All files", "*.*"))
        )
        if self.filepath:
            self.file_label.configure(text=os.path.basename(self.filepath))
            self.process_button.configure(state="normal")
            self.log_message_safe(f"已选择文件: {self.filepath}", "info")
        else:
            self.file_label.configure(text="未选择文件")
            self.process_button.configure(state="disabled")
            self.log_message_safe("取消选择文件。", "info")

    def set_ui_processing_state(self, processing: bool):
        self.is_processing = processing
        state = "disabled" if processing else "normal"
        self.select_button.configure(state=state)
        self.process_button.configure(state=state)
        self.process_num_slider.configure(state=state) # Updated name
        self.source_col_menu.configure(state=state)
        self.target_col_menu.configure(state=state)

    def start_processing_thread(self):
        if not self.filepath:
            messagebox.showerror("错误", "请先选择一个 Excel 文件。")
            return
        if self.is_processing:
            self.log_message_safe("已经在处理中，请等待当前任务完成。", "info")
            return

        source_col_letter = self.source_col_var.get()
        target_col_letter = self.target_col_var.get()
        if source_col_letter == target_col_letter:
            messagebox.showwarning("警告", "源数据列和目标数据列不能相同！")
            return

        self.set_ui_processing_state(True)
        self.update_progress_safe(0, "正在准备...")
        self.log_message_safe("开始处理...", "info")

        num_processes = int(self.process_num_slider.get()) # Updated name
        self.log_message_safe(f"使用 {num_processes} 个并行进程。源列: {source_col_letter}, 目标列: {target_col_letter}", "info")

        # The main processing logic will run in a separate thread
        # to keep the GUI responsive. This thread will manage the ProcessPoolExecutor.
        thread = threading.Thread(target=self.run_excel_processing_logic, 
                                  args=(num_processes, source_col_letter, target_col_letter))
        thread.daemon = True
        thread.start()

    def run_excel_processing_logic(self, num_processes, source_column_letter, target_column_letter):
        overall_start_time = time.time()
        try:
            source_col_idx = column_index_from_string(source_column_letter)
            target_col_idx = column_index_from_string(target_column_letter)

            # --- 1. Load Workbook ---
            self.log_message_safe("阶段 1/6: 加载 Excel 文件...", "info")
            load_start_time = time.time()
            workbook = openpyxl.load_workbook(self.filepath)
            sheet = workbook.active
            load_time = time.time() - load_start_time
            self.log_message_safe(f"Excel 文件加载完成，耗时: {load_time:.2f} 秒。", "info")

            # --- 2. Read Data from Source Column ---
            self.log_message_safe(f"阶段 2/6: 从列 '{source_column_letter}' 读取数据...", "info")
            read_start_time = time.time()
            tasks_to_process = []
            start_row = 1
            
            current_r_idx = start_row
            for row_cells_tuple in sheet.iter_rows(min_row=start_row, 
                                                   max_row=sheet.max_row,
                                                   min_col=source_col_idx, 
                                                   max_col=source_col_idx,
                                                   values_only=True):
                if row_cells_tuple and row_cells_tuple[0] is not None:
                    tasks_to_process.append((current_r_idx, row_cells_tuple[0]))
                current_r_idx += 1
            
            read_time = time.time() - read_start_time
            total_tasks = len(tasks_to_process)
            self.log_message_safe(f"数据读取完成，耗时: {read_time:.2f} 秒。共 {total_tasks} 条数据待处理。", "info")

            if total_tasks == 0:
                self.processing_finished_safe(success=True, message="源列中没有数据行可处理。")
                return

            # --- 3. Parallel Number Generation using ProcessPoolExecutor ---
            self.log_message_safe(f"阶段 3/6: 使用 {num_processes} 个进程并行生成号码...", "info")
            processed_results = []
            tasks_completed_count = 0
            gen_start_time = time.time()
            
            # --- GUI Update Throttling ---
            last_gui_update_time = time.time()
            GUI_UPDATE_INTERVAL_COUNT = max(100, total_tasks // 100) # Update 100 times or every 100 items
            GUI_UPDATE_INTERVAL_SECONDS = 0.2 # Or every 0.2 seconds

            # chunksize can help with IPC overhead for large number of small tasks
            # For 100k tasks, a chunksize like 100 or 1000 might be beneficial.
            # Let's calculate a dynamic chunksize
            chunksize = max(1, min(500, total_tasks // (num_processes * 4) if num_processes > 0 else total_tasks // 4))
            self.log_message_safe(f"使用 chunksize: {chunksize} 进行任务分配。", "info")

            with concurrent.futures.ProcessPoolExecutor(max_workers=num_processes) as executor:
                # map is often more efficient for applying a function to a sequence
                # but as_completed gives more control for progress updates.
                # Let's use submit with as_completed for better progress feedback.
                future_to_taskdata = {
                    executor.submit(process_row_task_for_process, task_data): task_data 
                    for task_data in tasks_to_process
                }
                
                for future in concurrent.futures.as_completed(future_to_taskdata):
                    original_task_data = future_to_taskdata[future]
                    try:
                        result_tuple = future.result()
                        processed_results.append(result_tuple)
                    except Exception as e:
                        self.log_message_safe(f"处理行 {original_task_data[0]} (源列 '{source_column_letter}', 值: {original_task_data[1]}) 时发生错误: {e}", "error")
                        processed_results.append((original_task_data[0], None, original_task_data[1]))
                    finally:
                        tasks_completed_count += 1
                        current_time = time.time()
                        if tasks_completed_count % GUI_UPDATE_INTERVAL_COUNT == 0 or \
                           (current_time - last_gui_update_time) > GUI_UPDATE_INTERVAL_SECONDS or \
                           tasks_completed_count == total_tasks:
                            progress = tasks_completed_count / total_tasks
                            self.update_progress_safe(progress, f"生成号码... {tasks_completed_count}/{total_tasks}")
                            last_gui_update_time = current_time
            
            gen_time = time.time() - gen_start_time
            self.log_message_safe(f"号码生成完成，耗时: {gen_time:.2f} 秒。", "info")

            # --- 4. Write Results to Sheet (in main thread/process) ---
            self.log_message_safe(f"阶段 4/6: 将替换后的号码写入列 '{target_column_letter}'...", "info")
            write_start_time = time.time()
            replaced_count = 0
            for row_idx, new_phone, original_masked_value in processed_results:
                if new_phone:
                    sheet.cell(row=row_idx, column=target_col_idx).value = new_phone
                    replaced_count += 1
                elif original_masked_value:
                    # Log only if it was supposed to be processed but failed format
                    # generate_realistic_number_for_process to check format
                    is_format_issue = generate_realistic_number_for_process(str(original_masked_value)) is None
                    if is_format_issue and isinstance(original_masked_value, str) and original_masked_value.count('*') == 7:
                         self.log_message_safe(f"第 {row_idx} 行源列 '{source_column_letter}' 内容 '{original_masked_value}' 格式不符或无法生成，已跳过。", "info")
            write_time = time.time() - write_start_time
            self.log_message_safe(f"数据写入Sheet完成，耗时: {write_time:.2f} 秒。", "info")
            
            # --- 5. Save Workbook ---
            self.log_message_safe("阶段 5/6: 保存 Excel 文件 (此步骤可能耗时较长)...", "info")
            self.update_progress_safe(0.95, "正在保存文件...")
            save_start_time = time.time()
            workbook.save(self.filepath)
            save_time = time.time() - save_start_time
            self.log_message_safe(f"文件保存完成，耗时: {save_time:.2f} 秒。", "success")

            # --- 6. Final Summary ---
            overall_time = time.time() - overall_start_time
            summary_message = (f"处理完成！共处理 {total_tasks} 条记录，"
                               f"成功替换 {replaced_count} 个号码。总耗时: {overall_time:.2f} 秒。")
            self.log_message_safe(summary_message, "success")
            self.log_message_safe(f"结果已保存到原文件: {self.filepath}", "success")
            self.processing_finished_safe(success=True, message=f"处理完毕！替换了 {replaced_count} 个号码。")

        except FileNotFoundError:
            err_msg = f"错误: 文件未找到 - {self.filepath}"
            self.log_message_safe(err_msg, "error")
            self.processing_finished_safe(success=False, message="错误: 文件未找到。")
        except PermissionError:
            err_msg = f"错误: 没有权限写入文件 - {self.filepath}。请关闭 Excel 后重试。"
            self.log_message_safe(err_msg, "error")
            self.processing_finished_safe(success=False, message="错误: 文件权限不足或被占用。")
        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            self.log_message_safe(f"处理过程中发生未知错误: {str(e)}\n详细信息:\n{error_details}", "error")
            self.processing_finished_safe(success=False, message=f"发生严重错误: {str(e)}")
        finally:
            self.after(0, self.set_ui_processing_state, False)

    def processing_finished_safe(self, success, message):
        def _finish():
            self.status_label.configure(text=f"状态: {message}")
            if success:
                self.progressbar.set(1)
                messagebox.showinfo("完成", message)
            else:
                messagebox.showerror("错误", message)
        self.after(0, _finish)

if __name__ == "__main__":
    # !!! IMPORTANT FOR PYINSTALLER AND MULTIPROCESSING (especially on Windows) !!!
    # This line must be under `if __name__ == '__main__':`
    multiprocessing.freeze_support() 
    
    app = App()
    app.mainloop()