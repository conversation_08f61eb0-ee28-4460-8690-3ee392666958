#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
习题 1：统计同花顺
-------------------------------------------------------------
1. 一副 52 张牌（去大小王），按 4 人（E S W N）发牌，每人 13 张
2. 四种花色：♥ ♠ ♣ ♦      （按课件表格行序）
3. 同一花色内部按面值升序显示：2 3 … 10 J Q K A
4. 统计
   •  同花顺“总个数”      —— 所有玩家、所有发牌轮中出现的同花顺总数
   •  出现同花顺的“发牌次数”—— 只要该轮至少有 1 个同花顺就 +1
   •  概率 ＝ 出现同花顺的发牌次数 ÷ 发牌总次数
5. 一张牌不得出现在两个同花顺里
   （例如同花 7-K 连成 7 张，只计 7·8·9·10·J 一个同花顺）
"""

import random
from collections import defaultdict

# 基础映射 --------------------------------------------------------------------
SUITS = ['♥', '♠', '♣', '♦']          # 行序 = 课件 S[0]~S[3]
RANKS = ['2', '3', '4', '5', '6', '7',
         '8', '9', '10', 'J', 'Q', 'K', 'A']         # 列序 V[0]~V[12]

def card_repr(idx: int) -> str:
    """将 0-51 的索引转成 '♥7' 这种字符串"""
    suit = SUITS[idx // 13]
    rank = RANKS[idx % 13]
    return f'{suit}{rank}'

# -------------------------- 同花顺检测（不可重叠） -----------------------------
def count_flush_straights(hand: list[int]) -> int:
    """
    hand: 13 张牌（int 索引）
    返回该手牌中同花顺的个数（5 连、同花、不重叠）
    """
    # 按花色拆分
    suit_to_ranks = defaultdict(list)
    for idx in hand:
        suit_to_ranks[idx // 13].append(idx % 13)   # 只要面值

    total = 0
    for ranks in suit_to_ranks.values():
        if len(ranks) < 5:
            continue
        ranks = sorted(ranks)
        i = 0
        while i < len(ranks):
            # 找以 ranks[i] 开头的连续段
            j = i
            while j + 1 < len(ranks) and ranks[j+1] == ranks[j] + 1:
                j += 1
            run_len = j - i + 1
            if run_len >= 5:        # 有同花顺
                total += 1
                i = j + 1          # 跳过整段（不允许重叠）
            else:
                i += 1
    return total

# -------------------------- 发牌与统计主循环 ----------------------------------
def deal_once(deck: list[int]) -> tuple[list[list[int]], int]:
    """
    洗一次牌，返回 4 手牌 和 本轮同花顺个数
    """
    random.shuffle(deck)
    hands = [sorted(deck[i*13:(i+1)*13]) for i in range(4)]
    this_flush_cnt = sum(count_flush_straights(h) for h in hands)
    return hands, this_flush_cnt

def format_hand(hand: list[int]) -> str:
    """
    输出示例和课件保持一致：
    ♥ 2 5 9 10 Q   ♠ 3 8 J   ♣ …   ♦ …
    """
    parts = []
    for s, suit_char in enumerate(SUITS):
        suited = [card_repr(idx) for idx in hand if idx // 13 == s]
        if suited:
            # 去掉花色字符后的面值，保持升序
            values = [c[1:] if c.startswith(('♠','♥','♣','♦')) else c
                      for c in suited]
            parts.append(f"{suit_char} " + " ".join(values))
    return "  ".join(parts)

def main():
    # ------------------ 输入校验 ------------------
    while True:
        raw = input("请输入发牌总次数：").strip()
        if raw.isdigit() and int(raw) > 0:
            total_deals = int(raw)
            break
        print("⚠ 请输入正整数！")

    deck = list(range(52))
    flush_total = 0           # 所有同花顺个数
    flush_deal_times = 0      # 出现同花顺的发牌次数

    seat_names = ['E', 'S', 'W', 'N']   # 东 南 西 北（按截图）
    for deal in range(1, total_deals + 1):
        hands, per_deal_cnt = deal_once(deck)
        if per_deal_cnt:
            flush_deal_times += 1
            flush_total      += per_deal_cnt
        # ------------------ 单轮输出 ------------------
        print(f"\n第{deal}次发牌>>>")
        for seat, hand in zip(seat_names, hands):
            print(f"{seat:<2}: {format_hand(hand)}")
        if per_deal_cnt:
            print(f"出现{per_deal_cnt}个同花顺！！")
        else:
            print("未出现同花顺…")

    # ------------------ 统计汇总 ------------------
    prob = flush_deal_times / total_deals
    print("\n" + "—"*38)
    print(f"出现同花顺的总个数      : {flush_total}")
    print(f"出现同花顺的发牌次数    : {flush_deal_times}")
    print(f"发牌总次数              : {total_deals}")
    print(f"出现同花顺的发牌概率    : {prob:.6f}")

# -------------------------- 井然有序地启动！ ----------------------------------
if __name__ == "__main__":
    main()
