#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
四连环游戏 (Connect Four)
程序扮演一位玩家(劳拉)，用户作为其对手。
"""

import random
import time

class ConnectFour:
    def __init__(self):
        # 初始化8列6行的棋盘，0表示空，1表示劳拉(O)，2表示用户(X)
        self.board = [[0 for _ in range(8)] for _ in range(6)]
        self.rows = 6
        self.cols = 8
        self.laura = 1  # 劳拉用O表示，内部用1表示
        self.user = 2   # 用户用X表示，内部用2表示
        self.current_player = self.laura  # 劳拉先手
        self.game_over = False
        self.winner = None
        
    def print_board(self):
        """打印当前棋盘状态"""
        print(" 1 2 3 4 5 6 7 8")
        for row in range(self.rows):
            line = "|"
            for col in range(self.cols):
                if self.board[row][col] == 0:
                    line += " |"
                elif self.board[row][col] == 1:
                    line += "O|"
                else:
                    line += "X|"
            print(line)
        print("-----------------")
        
    def is_valid_move(self, col):
        """检查在指定列放置棋子是否有效"""
        # 检查列号是否在有效范围内
        if col < 0 or col >= self.cols:
            return False
        # 检查该列是否已满
        return self.board[0][col] == 0
    
    def make_move(self, col, player):
        """在指定列放置棋子"""
        # 从底部向上找到第一个空位
        for row in range(self.rows-1, -1, -1):
            if self.board[row][col] == 0:
                self.board[row][col] = player
                return row
        return -1  # 该列已满，不应该发生
    
    def check_win(self, row, col, player):
        """检查是否有四子连成一线"""
        # 检查水平方向
        count = 0
        for c in range(max(0, col-3), min(col+4, self.cols)):
            if self.board[row][c] == player:
                count += 1
                if count == 4:
                    return True
            else:
                count = 0
                
        # 检查垂直方向
        count = 0
        for r in range(max(0, row-3), min(row+4, self.rows)):
            if self.board[r][col] == player:
                count += 1
                if count == 4:
                    return True
            else:
                count = 0
                
        # 检查正对角线方向 (左上到右下)
        count = 0
        for i in range(-3, 4):
            r, c = row + i, col + i
            if 0 <= r < self.rows and 0 <= c < self.cols:
                if self.board[r][c] == player:
                    count += 1
                    if count == 4:
                        return True
                else:
                    count = 0
                    
        # 检查反对角线方向 (右上到左下)
        count = 0
        for i in range(-3, 4):
            r, c = row + i, col - i
            if 0 <= r < self.rows and 0 <= c < self.cols:
                if self.board[r][c] == player:
                    count += 1
                    if count == 4:
                        return True
                else:
                    count = 0
                    
        return False
    
    def is_board_full(self):
        """检查棋盘是否已满"""
        for col in range(self.cols):
            if self.board[0][col] == 0:
                return False
        return True
    
    def evaluate_move(self, col, player):
        """评估在指定列放置棋子的价值"""
        # 模拟在该列放置棋子
        temp_board = [row[:] for row in self.board]
        row = -1
        for r in range(self.rows-1, -1, -1):
            if temp_board[r][col] == 0:
                temp_board[r][col] = player
                row = r
                break
        if row == -1:  # 该列已满
            return -1000
            
        score = 0
        opponent = 3 - player  # 对手的棋子类型
        
        # 检查是否能形成四连
        # 水平方向
        for c in range(max(0, col-3), min(col+1, self.cols-3)):
            window = [temp_board[row][c+i] for i in range(4)]
            score += self._evaluate_window(window, player, opponent)
            
        # 垂直方向
        for r in range(max(0, row-3), min(row+1, self.rows-3)):
            window = [temp_board[r+i][col] for i in range(4)]
            score += self._evaluate_window(window, player, opponent)
            
        # 正对角线
        for i in range(-3, 1):
            if 0 <= row+i < self.rows-3 and 0 <= col+i < self.cols-3:
                window = [temp_board[row+i+j][col+i+j] for j in range(4)]
                score += self._evaluate_window(window, player, opponent)
                
        # 反对角线
        for i in range(-3, 1):
            if 0 <= row+i < self.rows-3 and 0 <= col-i < self.cols and col-i-3 >= 0:
                window = [temp_board[row+i+j][col-i-j] for j in range(4)]
                score += self._evaluate_window(window, player, opponent)
                
        # 优先选择中间的列
        center_preference = [3, 2, 4, 1, 5, 0, 6, 7]
        score += (7 - center_preference.index(col)) * 2
                
        return score
    
    def _evaluate_window(self, window, player, opponent):
        """评估一个窗口的价值"""
        score = 0
        player_count = window.count(player)
        empty_count = window.count(0)
        opponent_count = window.count(opponent)
        
        # 如果能形成四连，给予最高分
        if player_count == 4:
            score += 100
        # 如果能形成三连，给予高分
        elif player_count == 3 and empty_count == 1:
            score += 10
        # 如果能形成二连，给予低分
        elif player_count == 2 and empty_count == 2:
            score += 3
            
        # 阻止对手形成四连
        if opponent_count == 3 and empty_count == 1:
            score += 80
            
        return score
    
    def laura_move(self):
        """劳拉(AI)选择最佳落子位置"""
        valid_moves = [col for col in range(self.cols) if self.is_valid_move(col)]
        
        # 检查是否有立即获胜的走法
        for col in valid_moves:
            row = self.make_move(col, self.laura)
            if self.check_win(row, col, self.laura):
                return col
            # 撤销移动
            self.board[row][col] = 0
            
        # 检查是否需要阻止对手获胜
        for col in valid_moves:
            row = self.make_move(col, self.user)
            if self.check_win(row, col, self.user):
                # 撤销移动
                self.board[row][col] = 0
                return col
            # 撤销移动
            self.board[row][col] = 0
            
        # 评估每个可能的移动
        scores = []
        for col in valid_moves:
            score = self.evaluate_move(col, self.laura)
            scores.append((col, score))
            
        # 选择得分最高的移动
        if scores:
            best_moves = sorted(scores, key=lambda x: x[1], reverse=True)
            best_score = best_moves[0][1]
            best_cols = [col for col, score in best_moves if score == best_score]
            return random.choice(best_cols)
        
        # 如果没有有效移动，随机选择一个
        return random.choice(valid_moves) if valid_moves else -1
    
    def play(self):
        """开始游戏"""
        print("Hi，欢迎来到四连环游戏！我是劳拉，我用O棋子，你用X棋子。")
        print("游戏规则：双方轮流在棋盘的列号放进自己的棋子，")
        print("          目标是在横向、纵向或斜向连成四个同号的棋子。一方连成四子即获胜!")
        print()
        print("开始了，这是棋盘的初始状态：")
        self.print_board()
        
        while not self.game_over:
            if self.current_player == self.laura:
                print(">>>轮到劳拉,我把O棋子放在第", end="")
                col = self.laura_move()
                print(f"{col+1}列...")
                row = self.make_move(col, self.laura)
                self.print_board()
                
                if self.check_win(row, col, self.laura):
                    print("******* 耶！劳拉赢了！^_^")
                    self.game_over = True
                    self.winner = self.laura
                elif self.is_board_full():
                    print("******* 平分秋色！@_@")
                    self.game_over = True
                else:
                    self.current_player = self.user
            else:
                print(">>>轮到你了,请放X棋子,请选择列号(1-8): ", end="")
                try:
                    user_col = int(input()) - 1  # 用户输入1-8，转换为0-7
                    if not (0 <= user_col < self.cols):
                        print("列号应该在1到8之间，请重新选择...")
                        continue
                    if not self.is_valid_move(user_col):
                        print("这一列已经满了，请选择其他列...")
                        continue
                    
                    row = self.make_move(user_col, self.user)
                    self.print_board()
                    
                    if self.check_win(row, user_col, self.user):
                        print("******* 恭喜你赢了！*_*")
                        self.game_over = True
                        self.winner = self.user
                    elif self.is_board_full():
                        print("******* 平分秋色！@_@")
                        self.game_over = True
                    else:
                        self.current_player = self.laura
                except ValueError:
                    print("请输入1到8之间的数字...")

if __name__ == "__main__":
    game = ConnectFour()
    game.play()
