(Big-Model) D:\Code-money\矩阵分解>python main_2.py 

==================== 使用改进方法测试 MNIST 数据集 ====================
MNIST 数据矩阵 (X_true) 维度: (784, 2000) (像素 x 图像)
使用 2000 张图像, 观测比例=0.3
智能掩码观测比例: 0.3000 (目标: 0.3000)
智能掩码观测比例: 0.3000 (目标: 0.3000)
开始超参数网格搜索: 3×3×3×2 = 54 组参数

测试参数: rank=30, lr=0.0005, lambda=0.01, adam=True
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=500, 秩=30
参数: 学习率=0.0005, 迭代次数=150, lambda=0.01
优化选项: 动量=False, Adam=True, 自适应学习率=True
优化完成，总耗时: 1.76秒
结果: RMSE(测试)=0.16022, RMSE(所有)=0.17755, 耗时: 2.73秒
找到新的最佳参数! RMSE: 0.16022

测试参数: rank=30, lr=0.0005, lambda=0.01, adam=False
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=500, 秩=30
参数: 学习率=0.0005, 迭代次数=150, lambda=0.01
优化选项: 动量=True, Adam=False, 自适应学习率=True
优化完成，总耗时: 1.47秒
结果: RMSE(测试)=0.16632, RMSE(所有)=0.16783, 耗时: 1.53秒

测试参数: rank=30, lr=0.0005, lambda=0.02, adam=True
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=500, 秩=30
参数: 学习率=0.0005, 迭代次数=150, lambda=0.02
优化选项: 动量=False, Adam=True, 自适应学习率=True
优化完成，总耗时: 1.47秒
结果: RMSE(测试)=0.16016, RMSE(所有)=0.17751, 耗时: 1.53秒
找到新的最佳参数! RMSE: 0.16016

测试参数: rank=30, lr=0.0005, lambda=0.02, adam=False
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=500, 秩=30
参数: 学习率=0.0005, 迭代次数=150, lambda=0.02
优化选项: 动量=True, Adam=False, 自适应学习率=True
优化完成，总耗时: 1.51秒
结果: RMSE(测试)=0.16594, RMSE(所有)=0.16757, 耗时: 1.57秒

测试参数: rank=30, lr=0.0005, lambda=0.05, adam=True
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=500, 秩=30
参数: 学习率=0.0005, 迭代次数=150, lambda=0.05
优化选项: 动量=False, Adam=True, 自适应学习率=True
优化完成，总耗时: 1.59秒
结果: RMSE(测试)=0.15999, RMSE(所有)=0.17740, 耗时: 1.65秒
找到新的最佳参数! RMSE: 0.15999

测试参数: rank=30, lr=0.0005, lambda=0.05, adam=False
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=500, 秩=30
参数: 学习率=0.0005, 迭代次数=150, lambda=0.05
优化选项: 动量=True, Adam=False, 自适应学习率=True
优化完成，总耗时: 1.41秒
结果: RMSE(测试)=0.16484, RMSE(所有)=0.16682, 耗时: 1.46秒

测试参数: rank=30, lr=0.001, lambda=0.01, adam=True
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=500, 秩=30
参数: 学习率=0.001, 迭代次数=150, lambda=0.01
优化选项: 动量=False, Adam=True, 自适应学习率=True
优化完成，总耗时: 1.54秒
结果: RMSE(测试)=0.15420, RMSE(所有)=0.16659, 耗时: 1.60秒
找到新的最佳参数! RMSE: 0.15420

测试参数: rank=30, lr=0.001, lambda=0.01, adam=False
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=500, 秩=30
参数: 学习率=0.001, 迭代次数=150, lambda=0.01
优化选项: 动量=True, Adam=False, 自适应学习率=True
优化完成，总耗时: 1.43秒
结果: RMSE(测试)=0.18246, RMSE(所有)=0.17734, 耗时: 1.48秒

测试参数: rank=30, lr=0.001, lambda=0.02, adam=True
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=500, 秩=30
参数: 学习率=0.001, 迭代次数=150, lambda=0.02
优化选项: 动量=False, Adam=True, 自适应学习率=True
优化完成，总耗时: 1.60秒
结果: RMSE(测试)=0.15407, RMSE(所有)=0.16650, 耗时: 1.65秒
找到新的最佳参数! RMSE: 0.15407

测试参数: rank=30, lr=0.001, lambda=0.02, adam=False
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=500, 秩=30
参数: 学习率=0.001, 迭代次数=150, lambda=0.02
优化选项: 动量=True, Adam=False, 自适应学习率=True
优化完成，总耗时: 1.75秒
结果: RMSE(测试)=0.18191, RMSE(所有)=0.17695, 耗时: 1.81秒

测试参数: rank=30, lr=0.001, lambda=0.05, adam=True
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=500, 秩=30
参数: 学习率=0.001, 迭代次数=150, lambda=0.05
优化选项: 动量=False, Adam=True, 自适应学习率=True
优化完成，总耗时: 1.51秒
结果: RMSE(测试)=0.15367, RMSE(所有)=0.16624, 耗时: 1.56秒
找到新的最佳参数! RMSE: 0.15367

测试参数: rank=30, lr=0.001, lambda=0.05, adam=False
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=500, 秩=30
参数: 学习率=0.001, 迭代次数=150, lambda=0.05
优化选项: 动量=True, Adam=False, 自适应学习率=True
优化完成，总耗时: 1.43秒
结果: RMSE(测试)=0.18027, RMSE(所有)=0.17579, 耗时: 1.49秒

测试参数: rank=30, lr=0.002, lambda=0.01, adam=True
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=500, 秩=30
参数: 学习率=0.002, 迭代次数=150, lambda=0.01
优化选项: 动量=False, Adam=True, 自适应学习率=True
优化完成，总耗时: 1.47秒
结果: RMSE(测试)=0.16607, RMSE(所有)=0.16816, 耗时: 1.52秒

测试参数: rank=30, lr=0.002, lambda=0.01, adam=False
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=500, 秩=30
参数: 学习率=0.002, 迭代次数=150, lambda=0.01
优化选项: 动量=True, Adam=False, 自适应学习率=True
优化完成，总耗时: 1.43秒
结果: RMSE(测试)=0.19413, RMSE(所有)=0.18519, 耗时: 1.49秒

测试参数: rank=30, lr=0.002, lambda=0.02, adam=True
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=500, 秩=30
参数: 学习率=0.002, 迭代次数=150, lambda=0.02
优化选项: 动量=False, Adam=True, 自适应学习率=True
优化完成，总耗时: 1.47秒
结果: RMSE(测试)=0.16574, RMSE(所有)=0.16794, 耗时: 1.53秒

测试参数: rank=30, lr=0.002, lambda=0.02, adam=False
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=500, 秩=30
参数: 学习率=0.002, 迭代次数=150, lambda=0.02
优化选项: 动量=True, Adam=False, 自适应学习率=True
优化完成，总耗时: 1.43秒
结果: RMSE(测试)=0.19341, RMSE(所有)=0.18466, 耗时: 1.50秒

测试参数: rank=30, lr=0.002, lambda=0.05, adam=True
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=500, 秩=30
参数: 学习率=0.002, 迭代次数=150, lambda=0.05
优化选项: 动量=False, Adam=True, 自适应学习率=True
优化完成，总耗时: 1.50秒
结果: RMSE(测试)=0.16477, RMSE(所有)=0.16727, 耗时: 1.56秒

测试参数: rank=30, lr=0.002, lambda=0.05, adam=False
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=500, 秩=30
参数: 学习率=0.002, 迭代次数=150, lambda=0.05
优化选项: 动量=True, Adam=False, 自适应学习率=True
优化完成，总耗时: 1.44秒
结果: RMSE(测试)=0.19127, RMSE(所有)=0.18310, 耗时: 1.49秒

测试参数: rank=40, lr=0.0005, lambda=0.01, adam=True
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=500, 秩=40
参数: 学习率=0.0005, 迭代次数=150, lambda=0.01
优化选项: 动量=False, Adam=True, 自适应学习率=True
优化完成，总耗时: 1.58秒
结果: RMSE(测试)=0.16745, RMSE(所有)=0.17624, 耗时: 1.67秒

测试参数: rank=40, lr=0.0005, lambda=0.01, adam=False
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=500, 秩=40
参数: 学习率=0.0005, 迭代次数=150, lambda=0.01
优化选项: 动量=True, Adam=False, 自适应学习率=True
优化完成，总耗时: 1.56秒
结果: RMSE(测试)=0.17068, RMSE(所有)=0.16439, 耗时: 1.62秒

测试参数: rank=40, lr=0.0005, lambda=0.02, adam=True
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=500, 秩=40
参数: 学习率=0.0005, 迭代次数=150, lambda=0.02
优化选项: 动量=False, Adam=True, 自适应学习率=True
优化完成，总耗时: 1.62秒
结果: RMSE(测试)=0.16736, RMSE(所有)=0.17617, 耗时: 1.69秒

测试参数: rank=40, lr=0.0005, lambda=0.02, adam=False
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=500, 秩=40
参数: 学习率=0.0005, 迭代次数=150, lambda=0.02
优化选项: 动量=True, Adam=False, 自适应学习率=True
优化完成，总耗时: 1.53秒
结果: RMSE(测试)=0.17022, RMSE(所有)=0.16407, 耗时: 1.60秒

测试参数: rank=40, lr=0.0005, lambda=0.05, adam=True
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=500, 秩=40
参数: 学习率=0.0005, 迭代次数=150, lambda=0.05
优化选项: 动量=False, Adam=True, 自适应学习率=True
优化完成，总耗时: 1.64秒
结果: RMSE(测试)=0.16709, RMSE(所有)=0.17599, 耗时: 1.72秒

测试参数: rank=40, lr=0.0005, lambda=0.05, adam=False
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=500, 秩=40
参数: 学习率=0.0005, 迭代次数=150, lambda=0.05
优化选项: 动量=True, Adam=False, 自适应学习率=True
优化完成，总耗时: 1.61秒
结果: RMSE(测试)=0.16888, RMSE(所有)=0.16311, 耗时: 1.68秒

测试参数: rank=40, lr=0.001, lambda=0.01, adam=True
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=500, 秩=40
参数: 学习率=0.001, 迭代次数=150, lambda=0.01
优化选项: 动量=False, Adam=True, 自适应学习率=True
优化完成，总耗时: 1.75秒
结果: RMSE(测试)=0.16263, RMSE(所有)=0.16565, 耗时: 1.82秒

测试参数: rank=40, lr=0.001, lambda=0.01, adam=False
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=500, 秩=40
参数: 学习率=0.001, 迭代次数=150, lambda=0.01
优化选项: 动量=True, Adam=False, 自适应学习率=True
优化完成，总耗时: 1.61秒
结果: RMSE(测试)=0.19036, RMSE(所有)=0.17623, 耗时: 1.68秒

测试参数: rank=40, lr=0.001, lambda=0.02, adam=True
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=500, 秩=40
参数: 学习率=0.001, 迭代次数=150, lambda=0.02
优化选项: 动量=False, Adam=True, 自适应学习率=True
优化完成，总耗时: 1.61秒
结果: RMSE(测试)=0.16242, RMSE(所有)=0.16550, 耗时: 1.70秒

测试参数: rank=40, lr=0.001, lambda=0.02, adam=False
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=500, 秩=40
参数: 学习率=0.001, 迭代次数=150, lambda=0.02
优化选项: 动量=True, Adam=False, 自适应学习率=True
优化完成，总耗时: 1.54秒
结果: RMSE(测试)=0.18963, RMSE(所有)=0.17567, 耗时: 1.60秒

测试参数: rank=40, lr=0.001, lambda=0.05, adam=True
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=500, 秩=40
参数: 学习率=0.001, 迭代次数=150, lambda=0.05
优化选项: 动量=False, Adam=True, 自适应学习率=True
优化完成，总耗时: 1.60秒
结果: RMSE(测试)=0.16179, RMSE(所有)=0.16506, 耗时: 1.68秒

测试参数: rank=40, lr=0.001, lambda=0.05, adam=False
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=500, 秩=40
参数: 学习率=0.001, 迭代次数=150, lambda=0.05
优化选项: 动量=True, Adam=False, 自适应学习率=True
优化完成，总耗时: 1.54秒
结果: RMSE(测试)=0.18745, RMSE(所有)=0.17405, 耗时: 1.61秒

测试参数: rank=40, lr=0.002, lambda=0.01, adam=True
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=500, 秩=40
参数: 学习率=0.002, 迭代次数=150, lambda=0.01
优化选项: 动量=False, Adam=True, 自适应学习率=True
优化完成，总耗时: 1.60秒
结果: RMSE(测试)=0.17867, RMSE(所有)=0.16982, 耗时: 1.67秒

测试参数: rank=40, lr=0.002, lambda=0.01, adam=False
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=500, 秩=40
参数: 学习率=0.002, 迭代次数=150, lambda=0.01
优化选项: 动量=True, Adam=False, 自适应学习率=True
优化完成，总耗时: 1.57秒
结果: RMSE(测试)=0.20844, RMSE(所有)=0.18858, 耗时: 1.64秒

测试参数: rank=40, lr=0.002, lambda=0.02, adam=True
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=500, 秩=40
参数: 学习率=0.002, 迭代次数=150, lambda=0.02
优化选项: 动量=False, Adam=True, 自适应学习率=True
优化完成，总耗时: 1.58秒
结果: RMSE(测试)=0.17816, RMSE(所有)=0.16944, 耗时: 1.65秒

测试参数: rank=40, lr=0.002, lambda=0.02, adam=False
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=500, 秩=40
参数: 学习率=0.002, 迭代次数=150, lambda=0.02
优化选项: 动量=True, Adam=False, 自适应学习率=True
优化完成，总耗时: 1.57秒
结果: RMSE(测试)=0.20735, RMSE(所有)=0.18773, 耗时: 1.64秒

测试参数: rank=40, lr=0.002, lambda=0.05, adam=True
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=500, 秩=40
参数: 学习率=0.002, 迭代次数=150, lambda=0.05
优化选项: 动量=False, Adam=True, 自适应学习率=True
优化完成，总耗时: 1.60秒
结果: RMSE(测试)=0.17664, RMSE(所有)=0.16833, 耗时: 1.67秒

测试参数: rank=40, lr=0.002, lambda=0.05, adam=False
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=500, 秩=40
参数: 学习率=0.002, 迭代次数=150, lambda=0.05
优化选项: 动量=True, Adam=False, 自适应学习率=True
优化完成，总耗时: 1.56秒
结果: RMSE(测试)=0.20413, RMSE(所有)=0.18526, 耗时: 1.67秒

测试参数: rank=50, lr=0.0005, lambda=0.01, adam=True
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=500, 秩=50
参数: 学习率=0.0005, 迭代次数=150, lambda=0.01
优化选项: 动量=False, Adam=True, 自适应学习率=True
优化完成，总耗时: 1.70秒
结果: RMSE(测试)=0.17645, RMSE(所有)=0.17653, 耗时: 1.78秒

测试参数: rank=50, lr=0.0005, lambda=0.01, adam=False
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=500, 秩=50
参数: 学习率=0.0005, 迭代次数=150, lambda=0.01
优化选项: 动量=True, Adam=False, 自适应学习率=True
优化完成，总耗时: 1.65秒
结果: RMSE(测试)=0.17458, RMSE(所有)=0.16242, 耗时: 1.72秒

测试参数: rank=50, lr=0.0005, lambda=0.02, adam=True
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=500, 秩=50
参数: 学习率=0.0005, 迭代次数=150, lambda=0.02
优化选项: 动量=False, Adam=True, 自适应学习率=True
优化完成，总耗时: 1.83秒
结果: RMSE(测试)=0.17633, RMSE(所有)=0.17644, 耗时: 1.90秒

测试参数: rank=50, lr=0.0005, lambda=0.02, adam=False
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=500, 秩=50
参数: 学习率=0.0005, 迭代次数=150, lambda=0.02
优化选项: 动量=True, Adam=False, 自适应学习率=True
优化完成，总耗时: 1.73秒
结果: RMSE(测试)=0.17406, RMSE(所有)=0.16204, 耗时: 1.80秒

测试参数: rank=50, lr=0.0005, lambda=0.05, adam=True
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=500, 秩=50
参数: 学习率=0.0005, 迭代次数=150, lambda=0.05
优化选项: 动量=False, Adam=True, 自适应学习率=True
优化完成，总耗时: 1.71秒
结果: RMSE(测试)=0.17596, RMSE(所有)=0.17617, 耗时: 1.82秒

测试参数: rank=50, lr=0.0005, lambda=0.05, adam=False
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=500, 秩=50
参数: 学习率=0.0005, 迭代次数=150, lambda=0.05
优化选项: 动量=True, Adam=False, 自适应学习率=True
优化完成，总耗时: 1.78秒
结果: RMSE(测试)=0.17254, RMSE(所有)=0.16094, 耗时: 1.85秒

测试参数: rank=50, lr=0.001, lambda=0.01, adam=True
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=500, 秩=50
参数: 学习率=0.001, 迭代次数=150, lambda=0.01
优化选项: 动量=False, Adam=True, 自适应学习率=True
优化完成，总耗时: 1.76秒
结果: RMSE(测试)=0.17431, RMSE(所有)=0.16742, 耗时: 1.83秒

测试参数: rank=50, lr=0.001, lambda=0.01, adam=False
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=500, 秩=50
参数: 学习率=0.001, 迭代次数=150, lambda=0.01
优化选项: 动量=True, Adam=False, 自适应学习率=True
优化完成，总耗时: 1.86秒
结果: RMSE(测试)=0.19614, RMSE(所有)=0.17590, 耗时: 1.93秒

测试参数: rank=50, lr=0.001, lambda=0.02, adam=True
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=500, 秩=50
参数: 学习率=0.001, 迭代次数=150, lambda=0.02
优化选项: 动量=False, Adam=True, 自适应学习率=True
优化完成，总耗时: 1.67秒
结果: RMSE(测试)=0.17401, RMSE(所有)=0.16720, 耗时: 1.74秒

测试参数: rank=50, lr=0.001, lambda=0.02, adam=False
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=500, 秩=50
参数: 学习率=0.001, 迭代次数=150, lambda=0.02
优化选项: 动量=True, Adam=False, 自适应学习率=True
优化完成，总耗时: 1.64秒
结果: RMSE(测试)=0.19526, RMSE(所有)=0.17522, 耗时: 1.74秒

测试参数: rank=50, lr=0.001, lambda=0.05, adam=True
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=500, 秩=50
参数: 学习率=0.001, 迭代次数=150, lambda=0.05
优化选项: 动量=False, Adam=True, 自适应学习率=True
优化完成，总耗时: 1.78秒
结果: RMSE(测试)=0.17313, RMSE(所有)=0.16655, 耗时: 1.84秒

测试参数: rank=50, lr=0.001, lambda=0.05, adam=False
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=500, 秩=50
参数: 学习率=0.001, 迭代次数=150, lambda=0.05
优化选项: 动量=True, Adam=False, 自适应学习率=True
优化完成，总耗时: 1.65秒
结果: RMSE(测试)=0.19265, RMSE(所有)=0.17323, 耗时: 1.73秒

测试参数: rank=50, lr=0.002, lambda=0.01, adam=True
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=500, 秩=50
参数: 学习率=0.002, 迭代次数=150, lambda=0.01
优化选项: 动量=False, Adam=True, 自适应学习率=True
优化完成，总耗时: 1.68秒
结果: RMSE(测试)=0.19266, RMSE(所有)=0.17480, 耗时: 1.75秒

测试参数: rank=50, lr=0.002, lambda=0.01, adam=False
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=500, 秩=50
参数: 学习率=0.002, 迭代次数=150, lambda=0.01
优化选项: 动量=True, Adam=False, 自适应学习率=True
优化完成，总耗时: 1.68秒
结果: RMSE(测试)=0.22055, RMSE(所有)=0.19356, 耗时: 1.75秒

测试参数: rank=50, lr=0.002, lambda=0.02, adam=True
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=500, 秩=50
参数: 学习率=0.002, 迭代次数=150, lambda=0.02
优化选项: 动量=False, Adam=True, 自适应学习率=True
优化完成，总耗时: 1.72秒
结果: RMSE(测试)=0.19192, RMSE(所有)=0.17422, 耗时: 1.79秒

测试参数: rank=50, lr=0.002, lambda=0.02, adam=False
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=500, 秩=50
参数: 学习率=0.002, 迭代次数=150, lambda=0.02
优化选项: 动量=True, Adam=False, 自适应学习率=True
优化完成，总耗时: 1.64秒
结果: RMSE(测试)=0.21907, RMSE(所有)=0.19240, 耗时: 1.70秒

测试参数: rank=50, lr=0.002, lambda=0.05, adam=True
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=500, 秩=50
参数: 学习率=0.002, 迭代次数=150, lambda=0.05
优化选项: 动量=False, Adam=True, 自适应学习率=True
优化完成，总耗时: 1.66秒
结果: RMSE(测试)=0.18973, RMSE(所有)=0.17255, 耗时: 1.73秒

测试参数: rank=50, lr=0.002, lambda=0.05, adam=False
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=500, 秩=50
参数: 学习率=0.002, 迭代次数=150, lambda=0.05
优化选项: 动量=True, Adam=False, 自适应学习率=True
优化完成，总耗时: 1.64秒
结果: RMSE(测试)=0.21478, RMSE(所有)=0.18902, 耗时: 1.71秒

所有参数组合的结果 (按测试RMSE排序):
1. rank=30, lr=0.001, lambda=0.05, adam=True: RMSE(测试)=0.15367
2. rank=30, lr=0.001, lambda=0.02, adam=True: RMSE(测试)=0.15407
3. rank=30, lr=0.001, lambda=0.01, adam=True: RMSE(测试)=0.15420
4. rank=30, lr=0.0005, lambda=0.05, adam=True: RMSE(测试)=0.15999
5. rank=30, lr=0.0005, lambda=0.02, adam=True: RMSE(测试)=0.16016

最佳参数: rank=30, lr=0.001, lambda=0.05, adam=True
最佳测试RMSE: 0.15367

使用最佳参数训练最终模型...
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=2000, 秩=30
参数: 学习率=0.001, 迭代次数=400, lambda=0.05
优化选项: 动量=False, Adam=True, 自适应学习率=True
开始改进版 Burer-Monteiro 恢复: m=784, n=2000, 秩=30
参数: 学习率=0.001, 迭代次数=400, lambda=0.05
优化选项: 动量=False, Adam=True, 自适应学习率=True
参数: 学习率=0.001, 迭代次数=400, lambda=0.05
优化选项: 动量=False, Adam=True, 自适应学习率=True
优化选项: 动量=False, Adam=True, 自适应学习率=True
迭代 20/400, RMSE (观测值): 0.251213, 损失: 14939.03, 耗时: 0.77秒
迭代 40/400, RMSE (观测值): 0.236898, 损失: 13305.05, 耗时: 1.47秒
迭代 20/400, RMSE (观测值): 0.251213, 损失: 14939.03, 耗时: 0.77秒
迭代 40/400, RMSE (观测值): 0.236898, 损失: 13305.05, 耗时: 1.47秒
第 41 轮调整学习率为: 0.000950
迭代 40/400, RMSE (观测值): 0.236898, 损失: 13305.05, 耗时: 1.47秒
第 41 轮调整学习率为: 0.000950
迭代 60/400, RMSE (观测值): 0.226742, 损失: 12204.41, 耗时: 2.17秒
第 41 轮调整学习率为: 0.000950
迭代 60/400, RMSE (观测值): 0.226742, 损失: 12204.41, 耗时: 2.17秒
迭代 80/400, RMSE (观测值): 0.218986, 损失: 11396.83, 耗时: 2.89秒
迭代 60/400, RMSE (观测值): 0.226742, 损失: 12204.41, 耗时: 2.17秒
迭代 80/400, RMSE (观测值): 0.218986, 损失: 11396.83, 耗时: 2.89秒
第 81 轮调整学习率为: 0.000902
迭代 100/400, RMSE (观测值): 0.213130, 损失: 10806.19, 耗时: 3.65秒
迭代 120/400, RMSE (观测值): 0.208360, 损失: 10337.25, 耗时: 4.39秒
第 121 轮调整学习率为: 0.000857
迭代 140/400, RMSE (观测值): 0.204573, 损失: 9972.79, 耗时: 5.12秒
迭代 160/400, RMSE (观测值): 0.201371, 损失: 9670.19, 耗时: 5.84秒
第 161 轮调整学习率为: 0.000815
迭代 160/400, RMSE (观测值): 0.201371, 损失: 9670.19, 耗时: 5.84秒
第 161 轮调整学习率为: 0.000815
第 161 轮调整学习率为: 0.000815
迭代 180/400, RMSE (观测值): 0.198756, 损失: 9426.78, 耗时: 6.55秒
迭代 200/400, RMSE (观测值): 0.196495, 损失: 9218.99, 耗时: 7.26秒
第 201 轮调整学习率为: 0.000774
迭代 220/400, RMSE (观测值): 0.194609, 损失: 9047.76, 耗时: 8.04秒
迭代 240/400, RMSE (观测值): 0.192948, 损失: 8898.35, 耗时: 8.78秒
第 241 轮调整学习率为: 0.000735
迭代 260/400, RMSE (观测值): 0.191539, 损失: 8772.72, 耗时: 9.49秒
迭代 280/400, RMSE (观测值): 0.190278, 损失: 8661.12, 耗时: 10.23秒
第 281 轮调整学习率为: 0.000698
迭代 300/400, RMSE (观测值): 0.189194, 损失: 8565.83, 耗时: 10.93秒
迭代 320/400, RMSE (观测值): 0.188213, 损失: 8480.12, 耗时: 11.63秒
第 321 轮调整学习率为: 0.000663
迭代 340/400, RMSE (观测值): 0.187363, 损失: 8406.23, 耗时: 12.36秒
迭代 360/400, RMSE (观测值): 0.186589, 损失: 8339.32, 耗时: 13.17秒
第 361 轮调整学习率为: 0.000630
迭代 380/400, RMSE (观测值): 0.185916, 损失: 8281.34, 耗时: 13.97秒
迭代 400/400, RMSE (观测值): 0.185301, 损失: 8228.63, 耗时: 14.80秒
优化完成，总耗时: 14.81秒

训练集成模型...
开始训练 3 个模型进行集成...
模型 1/3: rank=28, lr=0.00154, lambda=0.04299, adam=False
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=2000, 秩=28
参数: 学习率=0.001535590070526721, 迭代次数=200, lambda=0.04298906801250972
优化选项: 动量=True, Adam=False, 自适应学习率=True
优化完成，总耗时: 7.83秒
模型 1 训练完成，耗时: 7.98秒
模型 2/3: rank=22, lr=0.00101, lambda=0.01494, adam=False
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=2000, 秩=22
参数: 学习率=0.0010129611260672523, 迭代次数=200, lambda=0.014935117229632925
优化选项: 动量=True, Adam=False, 自适应学习率=True
优化完成，总耗时: 7.19秒
模型 2 训练完成，耗时: 7.30秒
模型 3/3: rank=25, lr=0.00198, lambda=0.01183, adam=False
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=2000, 秩=25
优化完成，总耗时: 7.83秒
模型 1 训练完成，耗时: 7.98秒
模型 2/3: rank=22, lr=0.00101, lambda=0.01494, adam=False
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=2000, 秩=22
参数: 学习率=0.0010129611260672523, 迭代次数=200, lambda=0.014935117229632925
优化选项: 动量=True, Adam=False, 自适应学习率=True
优化完成，总耗时: 7.19秒
模型 2 训练完成，耗时: 7.30秒
模型 3/3: rank=25, lr=0.00198, lambda=0.01183, adam=False
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=2000, 秩=25
参数: 学习率=0.0019770124860418233, 迭代次数=200, lambda=0.01182536615482507
优化选项: 动量=True, Adam=False, 自适应学习率=True
模型 1 训练完成，耗时: 7.98秒
模型 2/3: rank=22, lr=0.00101, lambda=0.01494, adam=False
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=2000, 秩=22
参数: 学习率=0.0010129611260672523, 迭代次数=200, lambda=0.014935117229632925
优化选项: 动量=True, Adam=False, 自适应学习率=True
优化完成，总耗时: 7.19秒
模型 2 训练完成，耗时: 7.30秒
模型 3/3: rank=25, lr=0.00198, lambda=0.01183, adam=False
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=2000, 秩=25
参数: 学习率=0.0019770124860418233, 迭代次数=200, lambda=0.01182536615482507
优化选项: 动量=True, Adam=False, 自适应学习率=True
模型 2/3: rank=22, lr=0.00101, lambda=0.01494, adam=False
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=2000, 秩=22
参数: 学习率=0.0010129611260672523, 迭代次数=200, lambda=0.014935117229632925
优化选项: 动量=True, Adam=False, 自适应学习率=True
优化完成，总耗时: 7.19秒
模型 2 训练完成，耗时: 7.30秒
模型 3/3: rank=25, lr=0.00198, lambda=0.01183, adam=False
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=2000, 秩=25
参数: 学习率=0.0019770124860418233, 迭代次数=200, lambda=0.01182536615482507
优化选项: 动量=True, Adam=False, 自适应学习率=True
开始改进版 Burer-Monteiro 恢复: m=784, n=2000, 秩=22
参数: 学习率=0.0010129611260672523, 迭代次数=200, lambda=0.014935117229632925
优化选项: 动量=True, Adam=False, 自适应学习率=True
优化完成，总耗时: 7.19秒
模型 2 训练完成，耗时: 7.30秒
模型 3/3: rank=25, lr=0.00198, lambda=0.01183, adam=False
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=2000, 秩=25
参数: 学习率=0.0019770124860418233, 迭代次数=200, lambda=0.01182536615482507
优化选项: 动量=True, Adam=False, 自适应学习率=True
优化选项: 动量=True, Adam=False, 自适应学习率=True
优化完成，总耗时: 7.19秒
模型 2 训练完成，耗时: 7.30秒
模型 3/3: rank=25, lr=0.00198, lambda=0.01183, adam=False
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=2000, 秩=25
参数: 学习率=0.0019770124860418233, 迭代次数=200, lambda=0.01182536615482507
优化选项: 动量=True, Adam=False, 自适应学习率=True
优化完成，总耗时: 7.19秒
模型 2 训练完成，耗时: 7.30秒
模型 3/3: rank=25, lr=0.00198, lambda=0.01183, adam=False
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=2000, 秩=25
参数: 学习率=0.0019770124860418233, 迭代次数=200, lambda=0.01182536615482507
优化选项: 动量=True, Adam=False, 自适应学习率=True
模型 3/3: rank=25, lr=0.00198, lambda=0.01183, adam=False
使用SVD初始化成功
开始改进版 Burer-Monteiro 恢复: m=784, n=2000, 秩=25
参数: 学习率=0.0019770124860418233, 迭代次数=200, lambda=0.01182536615482507
优化选项: 动量=True, Adam=False, 自适应学习率=True
开始改进版 Burer-Monteiro 恢复: m=784, n=2000, 秩=25
参数: 学习率=0.0019770124860418233, 迭代次数=200, lambda=0.01182536615482507
优化选项: 动量=True, Adam=False, 自适应学习率=True
优化完成，总耗时: 7.35秒
模型 3 训练完成，耗时: 7.46秒

参数: 学习率=0.0019770124860418233, 迭代次数=200, lambda=0.01182536615482507
优化选项: 动量=True, Adam=False, 自适应学习率=True
优化完成，总耗时: 7.35秒
模型 3 训练完成，耗时: 7.46秒

--- MNIST 改进模型结果 ---
优化完成，总耗时: 7.35秒
模型 3 训练完成，耗时: 7.46秒

--- MNIST 改进模型结果 ---

--- MNIST 改进模型结果 ---
参数: 秩=30, 学习率=0.001, Lambda正则=0.05, 使用Adam=True
--- MNIST 改进模型结果 ---
参数: 秩=30, 学习率=0.001, Lambda正则=0.05, 使用Adam=True
参数: 秩=30, 学习率=0.001, Lambda正则=0.05, 使用Adam=True
单模型最终 RMSE (观测条目): 0.18527
单模型最终 RMSE (所有条目): 0.16338
单模型最终 RMSE (观测条目): 0.18527
单模型最终 RMSE (所有条目): 0.16338
单模型最终 RMSE (未观测条目): 0.15304
单模型最终 RMSE (未观测条目): 0.15304

集成模型 RMSE (观测条目): 0.18233 ↑
集成模型 RMSE (所有条目): 0.13096 ↑
集成模型 RMSE (未观测条目): 0.10126 ↑

(Big-Model) D:\Code-money\矩阵分解>