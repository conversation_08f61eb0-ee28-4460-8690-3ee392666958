import numpy as np

import tensorflow as tf # 用于 MNIST 数据集

import matplotlib.pyplot as plt # 用于绘图


# --- Matplotlib 字体设置 (解决中文显示问题) ---
plt.rcParams['font.sans-serif'] = ['SimHei']  # 指定默认字体为黑体
# plt.rcParams['font.sans-serif'] = ['Microsoft YaHei'] # 或者使用微软雅黑
plt.rcParams['axes.unicode_minus'] = False  # 解决保存图像时负号'-'显示为方块的问题

# --- Burer-Monteiro 核心算法 ---
def burer_monteiro_recovery(X_observed, observed_mask, rank,
                            learning_rate=0.001, epochs=100,
                            lambda_reg=0.01, verbose=True, tol=1e-5, early_stopping_patience=10):
    """
    使用 Burer-Monteiro 分解执行低秩矩阵恢复。
    （参数说明同原代码）
    """
    m, n = X_observed.shape

    # 初始化 U 和 V
    non_zero_observed_values = X_observed[observed_mask == 1]
    if len(non_zero_observed_values) > 0:
        mean_abs_val = np.mean(np.abs(non_zero_observed_values))
    else:
        mean_abs_val = 1.0

    scale_factor_heuristic = np.sqrt(max(mean_abs_val, 0.01) / rank)

    U = np.random.normal(scale=scale_factor_heuristic, size=(m, rank))
    V = np.random.normal(scale=scale_factor_heuristic, size=(n, rank))

    num_observed_entries = np.sum(observed_mask)
    if num_observed_entries == 0:
        print("警告: 没有观测到的条目！返回初始的 U, V。")
        return U, V, [], []

    history_rmse_observed = []
    history_loss = []
    best_rmse = float('inf')
    epochs_no_improve = 0

    print(f"开始 Burer-Monteiro 恢复: m={m}, n={n}, 秩={rank}, 学习率={learning_rate}, 迭代次数={epochs}, lambda={lambda_reg}")
    print(f"初始化 U, V 使用的 scale_factor_heuristic: {scale_factor_heuristic:.4f} (基于 mean_abs_val={mean_abs_val:.4f})")


    for epoch in range(epochs):
        X_reconstructed = U @ V.T
        Residual_masked = observed_mask * (X_observed - X_reconstructed)

        mse_observed = np.sum(Residual_masked**2) / num_observed_entries
        if np.isinf(mse_observed) or np.isnan(mse_observed):
            print(f"迭代 {epoch+1}: MSE 溢出或为 NaN。可能学习率过高或正则化不足。提前停止。")
            return U, V, history_rmse_observed, history_loss
        rmse_observed = np.sqrt(mse_observed)
        history_rmse_observed.append(rmse_observed)

        reconstruction_error_term = 0.5 * np.sum(Residual_masked**2)
        regularization_term = 0.5 * lambda_reg * (np.sum(U**2) + np.sum(V**2))
        current_loss = reconstruction_error_term + regularization_term
        if np.isinf(current_loss) or np.isnan(current_loss):
            print(f"迭代 {epoch+1}: 损失值溢出或为 NaN。提前停止。")
            return U, V, history_rmse_observed, history_loss
        history_loss.append(current_loss)

        grad_U = -Residual_masked @ V + lambda_reg * U
        grad_V = -Residual_masked.T @ U + lambda_reg * V

        if np.any(np.isinf(grad_U)) or np.any(np.isnan(grad_U)) or \
           np.any(np.isinf(grad_V)) or np.any(np.isnan(grad_V)):
            print(f"迭代 {epoch+1}: 梯度溢出或为 NaN。提前停止。")
            return U, V, history_rmse_observed, history_loss

        U -= learning_rate * grad_U
        V -= learning_rate * grad_V

        if verbose and (epoch + 1) % max(1, (epochs // 20)) == 0:
            print(f"迭代 {epoch+1}/{epochs}, RMSE (观测值): {rmse_observed:.6f}, 损失: {current_loss:.2f}")

        if rmse_observed < best_rmse - tol:
            best_rmse = rmse_observed
            epochs_no_improve = 0
        else:
            epochs_no_improve += 1

        if epochs_no_improve >= early_stopping_patience:
            print(f"在迭代 {epoch+1} 时早停，因为 RMSE 在 {early_stopping_patience} 次迭代中没有改善。")
            break

    return U, V, history_rmse_observed, history_loss

# --- 辅助函数 ---
def calculate_rmse(X_true, X_reconstructed, mask_for_evaluation=None):
    """
    （同原代码）
    """
    if mask_for_evaluation is None:
        error = X_true - X_reconstructed
        return np.sqrt(np.mean(error**2))
    else:
        num_eval_entries = np.sum(mask_for_evaluation)
        if num_eval_entries == 0:
            return float('nan')
        error_masked = mask_for_evaluation * (X_true - X_reconstructed)
        return np.sqrt(np.sum(error_masked**2) / num_eval_entries)

# --- 测试案例: MNIST 数据集 ---
def test_mnist_data(num_images_to_use=2000, observation_ratio=0.3, recovery_rank=50,
                    lr=0.0005, epochs=500, reg_lambda=0.01):
    print("\n" + "="*20 + " 测试 MNIST 数据集 " + "="*20)

    (x_train, _), (_, _) = tf.keras.datasets.mnist.load_data()
    X_true_mnist = x_train[:num_images_to_use].reshape(num_images_to_use, -1).T / 255.0
    n_pixels, n_imgs = X_true_mnist.shape
    print(f"MNIST 数据矩阵 (X_true) 维度: {X_true_mnist.shape} (像素 x 图像)")
    print(f"使用 {num_images_to_use} 张图像, 观测比例={observation_ratio}, 恢复秩={recovery_rank}")

    observed_mask_mnist = np.random.choice([0, 1], size=X_true_mnist.shape, p=[1-observation_ratio, observation_ratio])
    X_observed_mnist = X_true_mnist * observed_mask_mnist

    num_total_entries = X_true_mnist.size
    num_observed_entries = np.sum(observed_mask_mnist)
    print(f"总条目数: {num_total_entries}, 观测条目数: {num_observed_entries} ({num_observed_entries/num_total_entries*100:.1f}%)")

    U_rec_mnist, V_rec_mnist, history_rmse_obs, history_loss = burer_monteiro_recovery(
        X_observed_mnist, observed_mask_mnist, recovery_rank,
        learning_rate=lr, epochs=epochs, lambda_reg=reg_lambda
    )

    X_reconstructed_mnist = U_rec_mnist @ V_rec_mnist.T

    rmse_on_observed_final = history_rmse_obs[-1] if history_rmse_obs else float('nan')
    rmse_on_all_entries = calculate_rmse(X_true_mnist, X_reconstructed_mnist)
    unseen_mask_mnist = 1 - observed_mask_mnist
    rmse_on_unseen_entries = calculate_rmse(X_true_mnist, X_reconstructed_mnist, unseen_mask_mnist)

    print(f"\n--- MNIST 结果 ---")
    print(f"参数: 秩={recovery_rank}, 学习率={lr}, Lambda正则={reg_lambda}")
    print(f"最终 RMSE (观测条目，训练集): {rmse_on_observed_final:.6f}")
    print(f"最终 RMSE (所有条目): {rmse_on_all_entries:.6f}")
    print(f"最终 RMSE (未观测条目): {rmse_on_unseen_entries:.6f}")

    if history_rmse_obs and history_loss: # 确保历史记录不为空
        fig, ax1 = plt.subplots(figsize=(10, 5))
        color = 'tab:red'
        ax1.set_xlabel('迭代次数 (Epoch)')
        ax1.set_ylabel('RMSE (观测值)', color=color)
        ax1.plot(history_rmse_obs, color=color, label='RMSE (观测值)')
        ax1.tick_params(axis='y', labelcolor=color)
        ax1.grid(True, axis='y', linestyle=':', alpha=0.7)

        ax2 = ax1.twinx()
        color = 'tab:blue'
        ax2.set_ylabel('总损失', color=color)
        ax2.plot(history_loss, color=color, linestyle='--', label='总损失')
        ax2.tick_params(axis='y', labelcolor=color)

        # MODIFICATION: Set title and legend BEFORE fig.tight_layout()
        plt.title(f"MNIST: 训练历史 (秩 {recovery_rank}, 学习率 {lr}, Lambda {reg_lambda})")
        
        lines, labels = ax1.get_legend_handles_labels()
        lines2, labels2 = ax2.get_legend_handles_labels()
        ax2.legend(lines + lines2, labels + labels2, loc='upper right')
        
        fig.tight_layout() # Call tight_layout after title and legend are set
        plt.show()

    num_to_show = 5
    if n_imgs > 0 :
        fig, axes = plt.subplots(3, num_to_show, figsize=(num_to_show * 2, 6))
        plt.suptitle(f"MNIST 图像重构 (秩 {recovery_rank})", fontsize=14)
        for i in range(num_to_show):
            if n_imgs <= i: break
            idx = np.random.randint(0, n_imgs)

            axes[0, i].imshow(X_true_mnist[:, idx].reshape(28, 28), cmap='gray', vmin=0, vmax=1)
            axes[0, i].set_title(f"原始图像 {idx}")
            axes[0, i].axis('off')

            axes[1, i].imshow(X_observed_mnist[:, idx].reshape(28, 28), cmap='gray', vmin=0, vmax=1)
            axes[1, i].set_title(f"观测图像 ({observation_ratio*100:.0f}%)")
            axes[1, i].axis('off')

            axes[2, i].imshow(X_reconstructed_mnist[:, idx].reshape(28, 28), cmap='gray', vmin=0, vmax=1)
            axes[2, i].set_title(f"重构图像")
            axes[2, i].axis('off')
        plt.tight_layout(rect=[0, 0, 1, 0.96]) # rect is for suptitle space
        plt.show()
    return {
        "rank": recovery_rank,
        "lr": lr,
        "lambda": reg_lambda,
        "rmse_observed": rmse_on_observed_final,
        "rmse_all": rmse_on_all_entries,
        "rmse_unseen": rmse_on_unseen_entries
    }



# --- 主执行部分 ---
if __name__ == '__main__':
    # --- MNIST 测试执行 ---
    mnist_results = test_mnist_data(
        num_images_to_use=2000,
        observation_ratio=0.4,
        recovery_rank=35,
        lr=0.001,
        epochs=400,
        reg_lambda=0.02
    )


    print("\n" + "="*30 + " 所有测试执行完毕 " + "="*30)
    if mnist_results:
        print("\nMNIST 数据集最终测试结果:")
        print(f"  参数: 秩={mnist_results['rank']}, 学习率={mnist_results['lr']}, Lambda正则={mnist_results['lambda']}")
        print(f"  RMSE (观测像素，训练集): {mnist_results['rmse_observed']:.4f}")
        print(f"  RMSE (所有像素): {mnist_results['rmse_all']:.4f}")
        print(f"  RMSE (未观测像素): {mnist_results['rmse_unseen']:.4f}")

