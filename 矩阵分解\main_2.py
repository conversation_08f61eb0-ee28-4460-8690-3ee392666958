import numpy as np
import tensorflow as tf
import matplotlib.pyplot as plt
from time import time

# --- Matplotlib 字体设置 ---
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# --- 改进版的 Burer-Monteiro 算法 ---
def burer_monteiro_advanced(X_observed, observed_mask, rank,
                           learning_rate=0.001, epochs=100,
                           lambda_reg=0.01, verbose=True, tol=1e-5, 
                           early_stopping_patience=10, 
                           use_momentum=True, momentum_coef=0.9,
                           adaptive_lr=True, lr_decay_rate=0.95, 
                           lr_decay_steps=40,
                           use_adam=False, beta1=0.9, beta2=0.999,
                           use_svd_init=True):
    """
    改进版的 Burer-Monteiro 分解算法，加入多种优化技术
    """
    m, n = X_observed.shape
    
    # 初始化 U 和 V
    non_zero_observed_values = X_observed[observed_mask == 1]
    if len(non_zero_observed_values) > 0:
        mean_abs_val = np.mean(np.abs(non_zero_observed_values))
    else:
        mean_abs_val = 1.0

    scale_factor_heuristic = np.sqrt(max(mean_abs_val, 0.01) / rank)
    
    # SVD初始化方法
    if use_svd_init:
        try:
            # 填充缺失值为0，然后进行截断SVD
            X_filled = np.zeros((m, n))
            X_filled[observed_mask == 1] = X_observed[observed_mask == 1]
            
            # 由于MNIST是大型矩阵，使用随机SVD更高效
            from sklearn.utils.extmath import randomized_svd
            u, s, vh = randomized_svd(X_filled, n_components=rank, random_state=42)
            
            # 使用SVD结果初始化
            U = u * np.sqrt(s.reshape(1, -1))
            V = vh.T * np.sqrt(s.reshape(1, -1))
            print("使用SVD初始化成功")
        except Exception as e:
            print(f"SVD初始化失败: {e}，使用随机初始化")
            U = np.random.normal(scale=scale_factor_heuristic, size=(m, rank))
            V = np.random.normal(scale=scale_factor_heuristic, size=(n, rank))
    else:
        U = np.random.normal(scale=scale_factor_heuristic, size=(m, rank))
        V = np.random.normal(scale=scale_factor_heuristic, size=(n, rank))
    
    # 优化器变量初始化
    if use_momentum:
        velocity_U = np.zeros_like(U)
        velocity_V = np.zeros_like(V)
    
    if use_adam:
        m_U, v_U = np.zeros_like(U), np.zeros_like(U)
        m_V, v_V = np.zeros_like(V), np.zeros_like(V)
        epsilon = 1e-8
    
    num_observed_entries = np.sum(observed_mask)
    history_rmse_observed = []
    history_loss = []
    best_rmse = float('inf')
    best_epoch = 0
    epochs_no_improve = 0
    current_lr = learning_rate
    best_U, best_V = None, None
    
    print(f"开始改进版 Burer-Monteiro 恢复: m={m}, n={n}, 秩={rank}")
    print(f"参数: 学习率={learning_rate}, 迭代次数={epochs}, lambda={lambda_reg}")
    print(f"优化选项: 动量={use_momentum}, Adam={use_adam}, 自适应学习率={adaptive_lr}")
    
    start_time = time()
    
    for epoch in range(epochs):
        # 自适应学习率
        if adaptive_lr and epoch > 0 and epoch % lr_decay_steps == 0:
            current_lr *= lr_decay_rate
            if verbose:
                print(f"第 {epoch+1} 轮调整学习率为: {current_lr:.6f}")
        
        X_reconstructed = U @ V.T
        Residual_masked = observed_mask * (X_observed - X_reconstructed)
        
        # 计算损失和RMSE
        mse_observed = np.sum(Residual_masked**2) / num_observed_entries
        rmse_observed = np.sqrt(mse_observed)
        history_rmse_observed.append(rmse_observed)
        
        # 结合多种正则化策略
        # 1. Tikhonov正则化 (传统L2)
        l2_reg_term = 0.5 * lambda_reg * (np.sum(U**2) + np.sum(V**2))
        
        # 2. 秩正则化的近似 (通过列范数的乘积)
        nuclear_norm_proxy = 0.2 * lambda_reg * np.sum(np.sqrt(np.sum(U**2, axis=0) * np.sum(V**2, axis=0)))
        
        # 3. 稀疏性正则化 (鼓励U和V中的一些元素为零)
        sparsity_reg = 0.05 * lambda_reg * (np.sum(np.abs(U)) + np.sum(np.abs(V)))
        
        reconstruction_error_term = 0.5 * np.sum(Residual_masked**2)
        current_loss = reconstruction_error_term + l2_reg_term + nuclear_norm_proxy + sparsity_reg
        history_loss.append(current_loss)
        
        # 检查损失是否有效
        if np.isinf(current_loss) or np.isnan(current_loss):
            print(f"迭代 {epoch+1}: 损失值溢出或为 NaN。提前停止。")
            if best_U is not None and best_V is not None:
                return best_U, best_V, history_rmse_observed[:best_epoch+1], history_loss[:best_epoch+1]
            return U, V, history_rmse_observed, history_loss
        
        # 计算梯度
        grad_U = -Residual_masked @ V + lambda_reg * U + 0.05 * lambda_reg * np.sign(U)
        grad_V = -Residual_masked.T @ U + lambda_reg * V + 0.05 * lambda_reg * np.sign(V)
        
        # 使用Adam优化器
        if use_adam:
            # U的更新
            m_U = beta1 * m_U + (1 - beta1) * grad_U
            v_U = beta2 * v_U + (1 - beta2) * (grad_U**2)
            m_U_hat = m_U / (1 - beta1**(epoch+1))
            v_U_hat = v_U / (1 - beta2**(epoch+1))
            U -= current_lr * m_U_hat / (np.sqrt(v_U_hat) + epsilon)
            
            # V的更新
            m_V = beta1 * m_V + (1 - beta1) * grad_V
            v_V = beta2 * v_V + (1 - beta2) * (grad_V**2)
            m_V_hat = m_V / (1 - beta1**(epoch+1))
            v_V_hat = v_V / (1 - beta2**(epoch+1))
            V -= current_lr * m_V_hat / (np.sqrt(v_V_hat) + epsilon)
            
        # 使用动量优化器
        elif use_momentum:
            velocity_U = momentum_coef * velocity_U - current_lr * grad_U
            velocity_V = momentum_coef * velocity_V - current_lr * grad_V
            U += velocity_U
            V += velocity_V
        # 普通梯度下降
        else:
            U -= current_lr * grad_U
            V -= current_lr * grad_V
        
        # 输出训练进度
        if verbose and (epoch + 1) % max(1, (epochs // 20)) == 0:
            elapsed = time() - start_time
            print(f"迭代 {epoch+1}/{epochs}, RMSE (观测值): {rmse_observed:.6f}, 损失: {current_loss:.2f}, 耗时: {elapsed:.2f}秒")
        
        # 早停策略
        if rmse_observed < best_rmse - tol:
            best_rmse = rmse_observed
            best_epoch = epoch
            epochs_no_improve = 0
            # 保存当前最佳模型
            best_U, best_V = U.copy(), V.copy()
        else:
            epochs_no_improve += 1
            
        if epochs_no_improve >= early_stopping_patience:
            print(f"在迭代 {epoch+1} 时早停，因为 RMSE 在 {early_stopping_patience} 次迭代中没有改善。")
            if best_U is not None and best_V is not None:
                U, V = best_U, best_V
            break
    
    elapsed = time() - start_time
    print(f"优化完成，总耗时: {elapsed:.2f}秒")
    
    # 返回最佳模型
    if best_U is not None and best_V is not None and best_rmse < rmse_observed:
        print(f"返回第 {best_epoch+1} 轮的最佳模型，RMSE: {best_rmse:.6f}")
        return best_U, best_V, history_rmse_observed, history_loss
    
    return U, V, history_rmse_observed, history_loss

# --- 智能观测掩码生成 ---
def create_intelligent_mask(X_true, observation_ratio=0.3):
    """
    创建更智能的观测掩码，保留图像重要区域的更多像素
    """
    m, n = X_true.shape
    total_pixels = m * n
    target_observations = int(total_pixels * observation_ratio)
    
    # 创建基本掩码 - 先随机观测部分像素
    base_ratio = observation_ratio * 0.5  # 一半的目标观测比例用于随机采样
    base_mask = np.random.choice([0, 1], size=X_true.shape, p=[1-base_ratio, base_ratio])
    
    # 计算剩余需要观测的像素数
    remaining_observations = target_observations - np.sum(base_mask)
    
    if remaining_observations <= 0:
        return base_mask
    
    # 计算梯度幅度
    gradient_mask = np.zeros_like(X_true)
    
    for i in range(n):  # 对每个图像列（即每个样本）
        img = X_true[:, i].reshape(28, 28)
        
        # 计算水平和垂直梯度
        grad_x = np.abs(np.gradient(img, axis=0))
        grad_y = np.abs(np.gradient(img, axis=1))
        
        # 梯度幅度
        grad_mag = np.sqrt(grad_x**2 + grad_y**2)
        
        # 图像边缘通常有更大的梯度值
        gradient_mask[:, i] = grad_mag.flatten()
    
    # 归一化梯度幅度
    gradient_mask = (gradient_mask - np.min(gradient_mask)) / (np.max(gradient_mask) - np.min(gradient_mask) + 1e-10)
    
    # 在未观测的像素中，选择梯度最大的部分作为额外观测点
    unobserved_indices = np.where(base_mask == 0)
    unobserved_gradients = gradient_mask[unobserved_indices]
    
    # 按梯度值排序
    sorted_indices = np.argsort(unobserved_gradients)[::-1]  # 降序排列
    
    # 选择剩余需要观测的像素
    n_to_select = min(int(remaining_observations), len(sorted_indices))
    selected_indices = sorted_indices[:n_to_select]
    
    # 创建智能掩码
    intelligent_mask = base_mask.copy()
    intelligent_mask[unobserved_indices[0][selected_indices], unobserved_indices[1][selected_indices]] = 1
    
    actual_ratio = np.mean(intelligent_mask)
    print(f"智能掩码观测比例: {actual_ratio:.4f} (目标: {observation_ratio:.4f})")
    
    return intelligent_mask

# --- 超参数网格搜索 ---
def hyperparameter_grid_search(X_true, observation_ratio=0.3, test_ratio=0.1, use_intelligent_mask=True):
    """
    执行超参数网格搜索以找到最佳参数组合
    """
    m, n = X_true.shape
    
    # 创建观测和测试掩码
    if use_intelligent_mask:
        observed_mask = create_intelligent_mask(X_true, observation_ratio)
    else:
        observed_mask = np.random.choice([0, 1], size=X_true.shape, p=[1-observation_ratio, observation_ratio])
    
    # 创建测试掩码 (从未观测区域中随机选择)
    unobserved_mask = 1 - observed_mask
    test_pixels_needed = int(m * n * test_ratio)
    unobserved_indices = np.where(unobserved_mask == 1)
    
    if len(unobserved_indices[0]) > test_pixels_needed:
        # 随机选择测试像素
        test_indices = np.random.choice(len(unobserved_indices[0]), test_pixels_needed, replace=False)
        test_mask = np.zeros_like(X_true)
        test_mask[unobserved_indices[0][test_indices], unobserved_indices[1][test_indices]] = 1
    else:
        # 如果未观测像素不够，使用所有未观测像素
        test_mask = unobserved_mask
    
    X_observed = X_true * observed_mask
    
    # 定义搜索网格
    ranks = [30, 40, 50]
    learning_rates = [0.0005, 0.001, 0.002]
    lambdas = [0.01, 0.02, 0.05]
    use_adam_options = [True, False]
    
    best_rmse = float('inf')
    best_params = {}
    results = []
    
    print(f"开始超参数网格搜索: {len(ranks)}×{len(learning_rates)}×{len(lambdas)}×{len(use_adam_options)} = {len(ranks)*len(learning_rates)*len(lambdas)*len(use_adam_options)} 组参数")
    
    for rank in ranks:
        for lr in learning_rates:
            for lambda_reg in lambdas:
                for use_adam in use_adam_options:
                    param_desc = f"rank={rank}, lr={lr}, lambda={lambda_reg}, adam={use_adam}"
                    print(f"\n测试参数: {param_desc}")
                    
                    # 使用改进的算法
                    start_time = time()
                    U, V, _, _ = burer_monteiro_advanced(
                        X_observed, observed_mask, rank,
                        learning_rate=lr, epochs=150, lambda_reg=lambda_reg,
                        verbose=False, use_momentum=not use_adam, use_adam=use_adam,
                        adaptive_lr=True
                    )
                    elapsed = time() - start_time
                    
                    X_reconstructed = U @ V.T
                    
                    # 评估性能
                    rmse_test = calculate_rmse(X_true, X_reconstructed, test_mask)
                    rmse_all = calculate_rmse(X_true, X_reconstructed)
                    rmse_observed = calculate_rmse(X_true, X_reconstructed, observed_mask)
                    
                    result = {
                        "rank": rank,
                        "lr": lr,
                        "lambda": lambda_reg,
                        "use_adam": use_adam,
                        "rmse_test": rmse_test,
                        "rmse_all": rmse_all,
                        "rmse_observed": rmse_observed,
                        "time": elapsed
                    }
                    results.append(result)
                    
                    print(f"结果: RMSE(测试)={rmse_test:.5f}, RMSE(所有)={rmse_all:.5f}, 耗时: {elapsed:.2f}秒")
                    
                    if rmse_test < best_rmse:
                        best_rmse = rmse_test
                        best_params = result
                        print(f"找到新的最佳参数! RMSE: {best_rmse:.5f}")
    
    # 输出所有结果，按测试RMSE排序
    print("\n所有参数组合的结果 (按测试RMSE排序):")
    sorted_results = sorted(results, key=lambda x: x["rmse_test"])
    for i, result in enumerate(sorted_results[:5]):
        print(f"{i+1}. rank={result['rank']}, lr={result['lr']}, lambda={result['lambda']}, adam={result['use_adam']}: RMSE(测试)={result['rmse_test']:.5f}")
    
    print(f"\n最佳参数: rank={best_params['rank']}, lr={best_params['lr']}, lambda={best_params['lambda']}, adam={best_params['use_adam']}")
    print(f"最佳测试RMSE: {best_params['rmse_test']:.5f}")
    
    return best_params, sorted_results, observed_mask, test_mask

# --- 数据增强预处理 ---
def preprocess_enhance(X, gamma=0.7, denoise=True, normalize=True):
    """
    对数据进行增强预处理
    """
    # 确保X在[0,1]范围内
    if normalize:
        X_min, X_max = np.min(X), np.max(X)
        X = (X - X_min) / (X_max - X_min + 1e-8)
    
    # 应用Gamma校正进行对比度增强
    X_enhanced = X ** gamma
    
    # 简单去噪 - 阈值化
    if denoise:
        # 低于阈值的像素置为0
        threshold = 0.1
        X_enhanced[X_enhanced < threshold] = 0
    
    return X_enhanced

# --- 集成多个模型 ---
def ensemble_models(X_observed, observed_mask, n_models=3, base_rank=40, rank_var=10):
    """
    训练多个模型并集成结果
    """
    m, n = X_observed.shape
    ensemble_predictions = np.zeros((m, n))
    
    print(f"开始训练 {n_models} 个模型进行集成...")
    
    for i in range(n_models):
        # 随机选择超参数
        rank = max(10, base_rank + np.random.randint(-rank_var, rank_var))
        lr = np.random.uniform(0.0005, 0.002)
        lambda_reg = np.random.uniform(0.01, 0.05)
        use_adam = np.random.choice([True, False])
        
        param_desc = f"rank={rank}, lr={lr:.5f}, lambda={lambda_reg:.5f}, adam={use_adam}"
        print(f"模型 {i+1}/{n_models}: {param_desc}")
        
        # 训练模型
        start_time = time()
        U, V, _, _ = burer_monteiro_advanced(
            X_observed, observed_mask, rank,
            learning_rate=lr, epochs=200, lambda_reg=lambda_reg,
            verbose=False, use_momentum=not use_adam, use_adam=use_adam,
            early_stopping_patience=15
        )
        elapsed = time() - start_time
        
        # 添加到集成预测
        X_reconstructed = U @ V.T
        
        # 限制范围在[0,1]
        X_reconstructed = np.clip(X_reconstructed, 0, 1)
        
        ensemble_predictions += X_reconstructed
        
        print(f"模型 {i+1} 训练完成，耗时: {elapsed:.2f}秒")
    
    # 平均集成结果
    ensemble_predictions /= n_models
    
    return ensemble_predictions

# --- 测试MNIST数据 ---
def test_mnist_with_improvements(num_images=2000, observation_ratio=0.3, show_plots=True):
    """
    使用改进的方法在MNIST数据集上进行测试
    """
    print("\n" + "="*20 + " 使用改进方法测试 MNIST 数据集 " + "="*20)
    
    # 加载MNIST数据
    (x_train, _), (_, _) = tf.keras.datasets.mnist.load_data()
    X_true_raw = x_train[:num_images].reshape(num_images, -1).T / 255.0
    
    # 应用预处理增强
    X_true = preprocess_enhance(X_true_raw, gamma=0.7, denoise=True)
    
    n_pixels, n_imgs = X_true.shape
    print(f"MNIST 数据矩阵 (X_true) 维度: {X_true.shape} (像素 x 图像)")
    print(f"使用 {num_images} 张图像, 观测比例={observation_ratio}")
    
    # 创建智能观测掩码
    observed_mask = create_intelligent_mask(X_true, observation_ratio)
    X_observed = X_true * observed_mask
    
    # 超参数搜索(小规模)
    params, _, _, _ = hyperparameter_grid_search(
        X_true[:, :500],  # 使用较小的样本集进行超参数搜索
        observation_ratio=observation_ratio, 
        test_ratio=0.1,
        use_intelligent_mask=True
    )
    
    # 使用最佳参数训练模型
    print("\n使用最佳参数训练最终模型...")
    U_rec, V_rec, history_rmse_obs, history_loss = burer_monteiro_advanced(
        X_observed, observed_mask, params['rank'],
        learning_rate=params['lr'], epochs=400, lambda_reg=params['lambda'],
        use_adam=params['use_adam'], use_momentum=not params['use_adam'],
        adaptive_lr=True, use_svd_init=True
    )
    
    X_reconstructed = U_rec @ V_rec.T
    
    # 训练集成模型
    print("\n训练集成模型...")
    X_ensemble = ensemble_models(X_observed, observed_mask, n_models=3, base_rank=params['rank'])
    
    # 评估性能
    rmse_on_observed = calculate_rmse(X_true, X_reconstructed, observed_mask)
    rmse_on_all = calculate_rmse(X_true, X_reconstructed)
    unobserved_mask = 1 - observed_mask
    rmse_on_unobserved = calculate_rmse(X_true, X_reconstructed, unobserved_mask)
    
    # 评估集成模型性能
    rmse_ensemble_observed = calculate_rmse(X_true, X_ensemble, observed_mask)
    rmse_ensemble_all = calculate_rmse(X_true, X_ensemble)
    rmse_ensemble_unobserved = calculate_rmse(X_true, X_ensemble, unobserved_mask)
    
    print(f"\n--- MNIST 改进模型结果 ---")
    print(f"参数: 秩={params['rank']}, 学习率={params['lr']}, Lambda正则={params['lambda']}, 使用Adam={params['use_adam']}")
    print(f"单模型最终 RMSE (观测条目): {rmse_on_observed:.5f}")
    print(f"单模型最终 RMSE (所有条目): {rmse_on_all:.5f}")
    print(f"单模型最终 RMSE (未观测条目): {rmse_on_unobserved:.5f}")
    print(f"\n集成模型 RMSE (观测条目): {rmse_ensemble_observed:.5f} {'↑' if rmse_ensemble_observed < rmse_on_observed else '↓'}")
    print(f"集成模型 RMSE (所有条目): {rmse_ensemble_all:.5f} {'↑' if rmse_ensemble_all < rmse_on_all else '↓'}")
    print(f"集成模型 RMSE (未观测条目): {rmse_ensemble_unobserved:.5f} {'↑' if rmse_ensemble_unobserved < rmse_on_unobserved else '↓'}")
    
    # 绘制训练历史
    if show_plots and history_rmse_obs and history_loss:
        fig, ax1 = plt.subplots(figsize=(10, 5))
        color = 'tab:red'
        ax1.set_xlabel('迭代次数 (Epoch)')
        ax1.set_ylabel('RMSE (观测值)', color=color)
        ax1.plot(history_rmse_obs, color=color, label='RMSE (观测值)')
        ax1.tick_params(axis='y', labelcolor=color)
        ax1.grid(True, axis='y', linestyle=':', alpha=0.7)

        ax2 = ax1.twinx()
        color = 'tab:blue'
        ax2.set_ylabel('总损失', color=color)
        ax2.plot(history_loss, color=color, linestyle='--', label='总损失')
        ax2.tick_params(axis='y', labelcolor=color)

        plt.title(f"MNIST改进: 训练历史 (秩 {params['rank']}, 学习率 {params['lr']}, Lambda {params['lambda']})")
        
        lines, labels = ax1.get_legend_handles_labels()
        lines2, labels2 = ax2.get_legend_handles_labels()
        ax2.legend(lines + lines2, labels + labels2, loc='upper right')
        
        fig.tight_layout()
        plt.show()
    
    # 可视化重构结果
    if show_plots:
        num_to_show = 5
        if n_imgs > 0:
            fig, axes = plt.subplots(4, num_to_show, figsize=(num_to_show * 2.5, 10))
            plt.suptitle(f"MNIST 图像重构 (改进模型，秩 {params['rank']})", fontsize=14)
            
            for i in range(num_to_show):
                if n_imgs <= i: break
                idx = np.random.randint(0, n_imgs)

                axes[0, i].imshow(X_true[:, idx].reshape(28, 28), cmap='gray', vmin=0, vmax=1)
                axes[0, i].set_title(f"原始图像 {idx}")
                axes[0, i].axis('off')

                axes[1, i].imshow(X_observed[:, idx].reshape(28, 28), cmap='gray', vmin=0, vmax=1)
                axes[1, i].set_title(f"观测图像 ({observation_ratio*100:.0f}%)")
                axes[1, i].axis('off')

                axes[2, i].imshow(X_reconstructed[:, idx].reshape(28, 28), cmap='gray', vmin=0, vmax=1)
                axes[2, i].set_title(f"单模型重构")
                axes[2, i].axis('off')
                
                axes[3, i].imshow(X_ensemble[:, idx].reshape(28, 28), cmap='gray', vmin=0, vmax=1)
                axes[3, i].set_title(f"集成模型重构")
                axes[3, i].axis('off')
                
            plt.tight_layout(rect=[0, 0, 1, 0.96])
            plt.show()
    
    # 返回结果
    return {
        "params": params,
        "rmse_single": {
            "observed": rmse_on_observed,
            "all": rmse_on_all,
            "unobserved": rmse_on_unobserved
        },
        "rmse_ensemble": {
            "observed": rmse_ensemble_observed,
            "all": rmse_ensemble_all,
            "unobserved": rmse_ensemble_unobserved
        }
    }

# --- 辅助函数 ---
def calculate_rmse(X_true, X_reconstructed, mask_for_evaluation=None):
    """
    计算RMSE
    """
    if mask_for_evaluation is None:
        error = X_true - X_reconstructed
        return np.sqrt(np.mean(error**2))
    else:
        num_eval_entries = np.sum(mask_for_evaluation)
        if num_eval_entries == 0:
            return float('nan')
        error_masked = mask_for_evaluation * (X_true - X_reconstructed)
        return np.sqrt(np.sum(error_masked**2) / num_eval_entries)

# --- 主执行部分 ---
if __name__ == '__main__':
    # 测试改进方法
    test_mnist_with_improvements(
        num_images=2000,
        observation_ratio=0.3,
        show_plots=True
    )