#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
医学图像配准程序

此程序用于CT/MRI医学影像的空间配准，支持以下功能：
1. 加载CT/MRI的DICOM数据
2. 自动配准CT和MRI数据（空间配准和灰度值校准）
3. 将MRI值转换为HU值
4. 可视化配准结果
"""

import os
import sys
import numpy as np
import pydicom
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.backends.backend_qt5agg import NavigationToolbar2QT as NavigationToolbar
from PyQt5.QtWidgets import (QApplication, QMainWindow, QPushButton, QFileDialog,
                             QLabel, QProgressBar, QTabWidget, QVBoxLayout, QHBoxLayout,
                             QWidget, QGroupBox, QFormLayout, QLineEdit, QComboBox,
                             QSpinBox, QMessageBox, QTableWidget, QTableWidgetItem,
                             QHeaderView, QSplitter, QGridLayout, QDoubleSpinBox,
                             QCheckBox, QRadioButton, QButtonGroup, QSlider)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QFont, QPixmap, QImage
import scipy.stats as stats
import SimpleITK as sitk
import tempfile
import shutil
from scipy import ndimage

# 检查是否安装了elastix
ELASTIX_INSTALLED = False
try:
    import SimpleITK.ElastixImageFilter as sitk_elastix

    ELASTIX_INSTALLED = True
except ImportError:
    ELASTIX_INSTALLED = False

plt.rcParams['font.sans-serif'] = ['SimHei']
# 设置字体大小
plt.rcParams['font.size'] = 14
# 设置中文显示正常
plt.rcParams['axes.unicode_minus'] = False


# ========== 从reg.py导入的一致配准功能 ==========
def reg_load_ct_volume(ct_folder):
    """
    加载 CT 图像序列（使用 pydicom 读取），假定文件顺序正确。
    由于文件为二维图像序列，最后组装成 3D 图像（shape: [slices, rows, cols]）。
    """
    # 获取所有文件的完整路径（假定文件全部为图像）
    files = [os.path.join(ct_folder, f) for f in os.listdir(ct_folder) if os.path.isfile(os.path.join(ct_folder, f))]
    files.sort()  # 根据文件名排序，若有需要可改为根据 DICOM 中的 InstanceNumber 排序

    slices = []
    for file in files:
        # 若文件没有 DICOM 扩展名，仍尝试使用 pydicom 读取，加 force=True 强制读取
        ds = pydicom.dcmread(file, force=True)
        pixel_array = ds.pixel_array.astype(np.float32)
        slices.append(pixel_array)
    volume = np.stack(slices, axis=-1)  # 得到 shape: (rows, cols, num_slices)
    # SimpleITK 的 GetImageFromArray 要求数组 shape 为 (slices, rows, cols)
    volume = np.transpose(volume, (2, 0, 1))
    sitk_image = sitk.GetImageFromArray(volume)

    # 根据需要，也可以设置图像物理信息（spacing、origin、direction），
    # 例如：sitk_image.SetSpacing([1.0, 1.0, 1.0])
    return sitk_image


def reg_load_mri_volume(mri_folder):
    """
    使用 SimpleITK 读取 MRI 的 DICOM 序列。
    """
    reader = sitk.ImageSeriesReader()
    dicom_names = reader.GetGDCMSeriesFileNames(mri_folder)
    reader.SetFileNames(dicom_names)
    image = reader.Execute()
    return image


def reg_register_images(fixed, moving, progress_callback=None):
    """
    利用 SimpleITK 对移动图像 (MRI) 和固定图像 (CT) 进行仿射配准。
    使用互信息作为相似性指标和梯度下降法进行优化。
    改进：使用多阶段配准策略和更强大的优化参数来提高相似性度量值
    """
    # 先 cast 成同样的像素类型
    fixed_float = sitk.Cast(fixed, sitk.sitkFloat32)
    moving_float = sitk.Cast(moving, sitk.sitkFloat32)

    if progress_callback:
        progress_callback(5)
        
    # 增强的预处理步骤
    print("执行增强预处理...")
    
    # 1. 应用强度标准化
    fixed_stats = sitk.StatisticsImageFilter()
    fixed_stats.Execute(fixed_float)
    fixed_float = sitk.Normalize(fixed_float)
    
    moving_stats = sitk.StatisticsImageFilter()
    moving_stats.Execute(moving_float)
    moving_float = sitk.Normalize(moving_float)
    
    # 2. 应用简化的强度匹配来提高配准效果
    try:
        # 使用更高效的方法进行强度匹配
        fixed_array = sitk.GetArrayFromImage(fixed_float)
        moving_array = sitk.GetArrayFromImage(moving_float)
        
        # 使用简化的强度匹配方法
        # 只计算主要百分位数，减少计算量
        fixed_min = np.min(fixed_array)
        fixed_max = np.max(fixed_array)
        moving_min = np.min(moving_array)
        moving_max = np.max(moving_array)
        
        # 简化的线性缩放
        moving_array_adj = (moving_array - moving_min) * ((fixed_max - fixed_min) / (moving_max - moving_min)) + fixed_min
        moving_float = sitk.GetImageFromArray(moving_array_adj)
        moving_float.CopyInformation(moving)
        
        print("应用了简化的强度匹配")
    except Exception as e:
        print(f"强度匹配失败，使用原始标准化图像: {str(e)}")
    
    # 跳过边缘增强步骤，因为该步骤计算量大且效果有限
    print("跳过边缘增强步骤以提高效率")
    
    if progress_callback:
        progress_callback(10)

    # 第一阶段：粗略刚性配准（优化为更高效）
    print("第一阶段：粗略刚性配准")
    registration_method1 = sitk.ImageRegistrationMethod()
    
    # 使用均方差指标进行初始对齐
    registration_method1.SetMetricAsMeanSquares()
    
    # 优化采样策略以提高效率
    registration_method1.SetMetricSamplingStrategy(registration_method1.RANDOM)
    registration_method1.SetMetricSamplingPercentage(0.15)  # 使用更少的采样点
    
    # 使用更高效的正则化梯度下降法
    registration_method1.SetOptimizerAsRegularStepGradientDescent(
        learningRate=2.0,
        minStep=1e-3,  # 增大最小步长以加快收敛
        numberOfIterations=100,  # 减少迭代次数
        relaxationFactor=0.7)  # 增大松弛因子以加快收敛
    registration_method1.SetOptimizerScalesFromPhysicalShift()
    
    # 使用更简化的多分辨率策略
    registration_method1.SetShrinkFactorsPerLevel(shrinkFactors=[8])
    registration_method1.SetSmoothingSigmasPerLevel(smoothingSigmas=[4])
    registration_method1.SmoothingSigmasAreSpecifiedInPhysicalUnitsOn()
    
    # 尝试使用并行计算
    try:
        import multiprocessing
        num_cores = multiprocessing.cpu_count()
        registration_method1.SetNumberOfThreads(num_cores)
    except:
        pass
    
    # 初始化变换：使用中心对齐
    initial_transform = sitk.CenteredTransformInitializer(
        fixed_float, moving_float, sitk.Euler3DTransform(),
        sitk.CenteredTransformInitializerFilter.GEOMETRY)
    registration_method1.SetInitialTransform(initial_transform, inPlace=False)
    
    # 执行第一阶段配准
    rigid_transform = registration_method1.Execute(fixed_float, moving_float)
    
    print("第一阶段相似性度量值: {:.4f}".format(registration_method1.GetMetricValue()))
    
    if progress_callback:
        progress_callback(40)
    
    # 第二阶段：精细仿射配准
    print("第二阶段：精细仿射配准")
    registration_method2 = sitk.ImageRegistrationMethod()
    
    # 使用互信息指标，平衡效果和效率
    print("使用优化的互信息指标进行配准")
    registration_method2.SetMetricAsMattesMutualInformation(numberOfHistogramBins=150)  # 减少箱数以提高效率
    registration_method2.MetricUseFixedImageGradientFilterOff()
    
    # 调整采样策略以平衡效果和效率
    registration_method2.SetMetricSamplingStrategy(registration_method2.RANDOM)
    registration_method2.SetMetricSamplingPercentage(0.75)  # 减少采样率以提高效率
    
    # 尝试使用并行计算加速
    try:
        import multiprocessing
        num_cores = multiprocessing.cpu_count()
        registration_method2.SetNumberOfThreads(num_cores)  # 使用所有可用的CPU核心
        print(f"启用并行计算，使用{num_cores}个核心")
    except Exception as e:
        print(f"无法启用并行计算: {str(e)}")
    
    # 使用高效的梯度下降优化器代替Powell
    print("使用高效的梯度下降优化器")
    registration_method2.SetOptimizerAsGradientDescent(
        learningRate=1.0,
        numberOfIterations=500,  # 显著减少迭代次数
        convergenceMinimumValue=1e-7,
        convergenceWindowSize=15)
    registration_method2.SetOptimizerScalesFromPhysicalShift()
    
    # 简化的多分辨率策略
    registration_method2.SetShrinkFactorsPerLevel(shrinkFactors=[2])
    registration_method2.SetSmoothingSigmasPerLevel(smoothingSigmas=[1])
    registration_method2.SmoothingSigmasAreSpecifiedInPhysicalUnitsOn()
    
    # 使用仿射变换，以第一阶段的变换为初始值
    # 创建新的仿射变换
    affine_transform = sitk.AffineTransform(3)
    
    # 处理不同类型的变换
    try:
        # 如果是标准变换类型，直接获取矩阵和平移
        affine_transform.SetMatrix(rigid_transform.GetMatrix())
        affine_transform.SetTranslation(rigid_transform.GetTranslation())
        affine_transform.SetCenter(rigid_transform.GetCenter())
    except AttributeError:
        # 如果是CompositeTransform或其他类型，使用更通用的方法
        print("第一阶段返回了复合变换，使用更通用的初始化方法")
        # 重新初始化变换
        affine_transform = sitk.CenteredTransformInitializer(
            fixed_float, moving_float, sitk.AffineTransform(3),
            sitk.CenteredTransformInitializerFilter.GEOMETRY)
    
    registration_method2.SetInitialTransform(affine_transform, inPlace=False)
    
    if progress_callback:
        progress_callback(60)
    
    # 执行第二阶段配准
    final_transform = registration_method2.Execute(fixed_float, moving_float)
    
    if progress_callback:
        progress_callback(80)
    
    print("最终相似性度量值: {:.4f}".format(registration_method2.GetMetricValue()))
    print("优化器终止条件: {}".format(registration_method2.GetOptimizerStopConditionDescription()))
    
    # 重采样
    resampled_image = reg_resample_image(moving_float, fixed_float, final_transform)
    
    if progress_callback:
        progress_callback(100)
    
    return final_transform, resampled_image


def reg_resample_image(moving, fixed, transform):
    """
    将移动图像 (MRI) 按照得到的变换在固定图像 (CT) 空间内进行重采样。
    改进：使用更高质量的插值方法和高级设置来提高重采样质量
    """
    # 使用高级重采样设置
    resampler = sitk.ResampleImageFilter()
    resampler.SetReferenceImage(fixed)  # 使用固定图像作为参考
    
    # 选择最适合的插值方法
    # 对于B样条变换，使用BSpline插值
    # 对于仿射变换，使用Lanczos窗变换插值
    if isinstance(transform, sitk.BSplineTransform):
        resampler.SetInterpolator(sitk.sitkBSpline)
    else:
        # Lanczos窗变换插值对于仿射变换效果更好
        resampler.SetInterpolator(sitk.sitkLanczosWindowedSinc)
    
    # 设置默认像素值
    # 对于医学图像，使用背景值而非绝对最小值
    moving_stats = sitk.StatisticsImageFilter()
    moving_stats.Execute(moving)
    
    # 使用更简单的方法计算背景值
    # 将背景值设置为最小值加上小偏移，避免使用直方图
    background_value = moving_stats.GetMinimum() + (moving_stats.GetMaximum() - moving_stats.GetMinimum()) * 0.05
    
    # 获取移动图像的numpy数组进行更详细的分析
    moving_array = sitk.GetArrayFromImage(moving)
    # 使用numpy的百分位数来估计背景值
    try:
        # 尝试使用百分位数来获取更准确的背景值
        background_percentile = np.percentile(moving_array.flatten(), 5)  # 使用5百分位数作为背景估计
        background_value = background_percentile
    except:
        # 如果出错，使用简单的方法
        pass
        
    resampler.SetDefaultPixelValue(background_value)
    
    # 设置变换
    resampler.SetTransform(transform)
    
    # 执行重采样
    out = resampler.Execute(moving)
    
    # 确保输出图像具有与固定图像相同的物理特性
    out.SetOrigin(fixed.GetOrigin())
    out.SetDirection(fixed.GetDirection())
    out.SetSpacing(fixed.GetSpacing())
    
    # 应用轻微的平滑来减少重采样伪影
    try:
        # 尝试使用离散高斯滤波器
        out = sitk.DiscreteGaussian(out, variance=0.5)
    except:
        # 如果不支持，尝试使用普通的高斯滤波器
        try:
            out = sitk.SmoothingRecursiveGaussian(out, sigma=0.5)
        except:
            # 如果也不支持，则跳过平滑步骤
            print("跳过图像平滑步骤")
    
    return out


class CTDataHandler:
    """CT数据处理类"""

    def __init__(self):
        self.ct_data = None
        self.pixel_spacing = None
        self.slice_thickness = None
        self.origin = None
        self.dicom_files = []
        self.shape = None
        self.sitk_image = None  # SimpleITK图像对象
        self.direction = None  # 方向矩阵

    def load_dicom_folder(self, folder_path):
        """
        使用reg.py中的方法加载CT数据，确保配准结果一致
        参数:
            folder_path: DICOM文件夹路径
        返回:
            成功返回True，失败返回False
        """
        try:
            print("使用reg.py兼容方法加载CT数据...")

            # 保存原始文件夹路径
            self.dicom_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path)
                                if os.path.isfile(os.path.join(folder_path, f))]

            # 使用reg.py的方法加载数据
            self.sitk_image = reg_load_ct_volume(folder_path)

            # 提取元数据
            self.direction = self.sitk_image.GetDirection()
            spacing = self.sitk_image.GetSpacing()
            self.pixel_spacing = [spacing[0], spacing[1]]
            self.slice_thickness = spacing[2]
            self.origin = self.sitk_image.GetOrigin()

            # 从SimpleITK获取数组数据
            self.ct_data = sitk.GetArrayFromImage(self.sitk_image)
            # 注意：SimpleITK返回的数组形状为 (slices, rows, cols)
            # 需要转置为UI预期的 (rows, cols, slices)
            self.ct_data = np.transpose(self.ct_data, (1, 2, 0))
            self.shape = self.ct_data.shape

            print(f"CT数据加载成功: 尺寸 {self.shape}")
            return True

        except Exception as e:
            import traceback
            traceback.print_exc()
            print(f"使用reg方法加载CT数据失败: {str(e)}")
            print("尝试使用原始方法加载...")

            # 如果失败，回退到原始方法
            # 获取文件夹中的所有文件
            files = [os.path.join(folder_path, f) for f in os.listdir(folder_path)
                     if os.path.isfile(os.path.join(folder_path, f))]

            if not files:
                print("文件夹中没有文件")
                return False

            # 尝试读取所有文件为DICOM
            self.dicom_files = []
            for file_path in files:
                try:
                    # 尝试加载DICOM文件
                    dicom = pydicom.dcmread(file_path)
                    # 验证它是否包含像素数据
                    if hasattr(dicom, 'pixel_array') and dicom.pixel_array.size > 0:
                        self.dicom_files.append(file_path)
                        print(f"有效CT DICOM文件: {file_path}, 尺寸: {dicom.pixel_array.shape}, "
                              f"最小值: {np.min(dicom.pixel_array)}, 最大值: {np.max(dicom.pixel_array)}")
                except Exception as e:
                    print(f"忽略无效文件 {file_path}: {e}")
                    continue

            if not self.dicom_files:
                print("未找到有效的DICOM文件")
                return False

            # 读取第一个文件获取基本信息
            ref_dicom = pydicom.dcmread(self.dicom_files[0])

            # 获取像素间距和切片厚度
            self.pixel_spacing = ref_dicom.PixelSpacing
            self.slice_thickness = ref_dicom.SliceThickness
            self.origin = ref_dicom.ImagePositionPatient

            # 读取所有文件并排序
            slices = [pydicom.dcmread(f) for f in self.dicom_files]
            slices.sort(key=lambda x: float(x.ImagePositionPatient[2]))

            # 转换为3D numpy数组
            img_shape = list(slices[0].pixel_array.shape)
            img_shape.append(len(slices))
            self.ct_data = np.zeros(img_shape)
            self.shape = img_shape

            # 填充3D数组并转换为HU值
            for i, s in enumerate(slices):
                pixel_array = s.pixel_array.astype(np.int16)

                # 转换为HU值 (如果需要)
                if hasattr(s, 'RescaleIntercept') and hasattr(s, 'RescaleSlope'):
                    intercept = s.RescaleIntercept
                    slope = s.RescaleSlope
                    pixel_array = pixel_array * slope + intercept

                self.ct_data[:, :, i] = pixel_array

            # 创建SimpleITK图像对象，用于配准
            try:
                self.sitk_image = sitk.ReadImage(self.dicom_files)
                self.direction = self.sitk_image.GetDirection()
            except Exception as e:
                print(f"创建SimpleITK图像失败: {e}")
                # 如果读取DICOM序列失败，则尝试从numpy数组创建
                self.sitk_image = sitk.GetImageFromArray(self.ct_data)
                self.sitk_image.SetSpacing((self.pixel_spacing[0], self.pixel_spacing[1], self.slice_thickness))
                self.sitk_image.SetOrigin(self.origin)
                # 设置默认方向为单位矩阵
                self.sitk_image.SetDirection((1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0))

            print(f"CT数据加载成功: 尺寸 {self.ct_data.shape}")
            return True

    def get_hu_value(self, x, y, z):
        """
        获取指定世界坐标的HU值
        参数:
            x, y, z: 世界坐标 (mm)
        返回:
            对应位置的HU值，如果超出范围则返回None
        """
        if self.ct_data is None:
            return None

        # 将世界坐标转换为像素坐标
        px = int((x - self.origin[0]) / self.pixel_spacing[0])
        py = int((y - self.origin[1]) / self.pixel_spacing[1])
        pz = int((z - self.origin[2]) / self.slice_thickness)

        # 检查坐标是否在范围内
        if (0 <= px < self.ct_data.shape[0] and
                0 <= py < self.ct_data.shape[1] and
                0 <= pz < self.ct_data.shape[2]):
            return self.ct_data[px, py, pz]
        else:
            return None


class MRIDataHandler:
    """MRI数据处理类"""

    def __init__(self):
        self.mri_data = None
        self.pixel_spacing = None
        self.slice_thickness = None
        self.origin = None
        self.dicom_files = []
        self.shape = None
        self.sitk_image = None  # SimpleITK图像对象
        self.direction = None  # 方向矩阵
        self.transformed_image = None  # 变换后的图像

    def load_dicom_folder(self, folder_path):
        """
        使用reg.py中的方法加载MRI数据，确保配准结果一致
        参数:
            folder_path: DICOM文件夹路径
        返回:
            成功返回True，失败返回False
        """
        try:
            print("使用reg.py兼容方法加载MRI数据...")

            # 保存原始文件夹路径
            self.dicom_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path)
                                if os.path.isfile(os.path.join(folder_path, f))]

            # 使用reg.py的方法加载数据
            self.sitk_image = reg_load_mri_volume(folder_path)

            # 提取元数据
            self.direction = self.sitk_image.GetDirection()
            spacing = self.sitk_image.GetSpacing()
            self.pixel_spacing = [spacing[0], spacing[1]]
            self.slice_thickness = spacing[2]
            self.origin = self.sitk_image.GetOrigin()

            # 从SimpleITK获取数组数据
            self.mri_data = sitk.GetArrayFromImage(self.sitk_image)
            # 注意：SimpleITK返回的数组形状为 (slices, rows, cols)
            # 需要转置为UI预期的 (rows, cols, slices)
            self.mri_data = np.transpose(self.mri_data, (1, 2, 0))
            self.shape = self.mri_data.shape

            print(f"MRI数据加载成功: 尺寸 {self.shape}")
            return True

        except Exception as e:
            import traceback
            traceback.print_exc()
            print(f"使用reg方法加载MRI数据失败: {str(e)}")
            print("尝试使用原始方法加载...")

            # 如果失败，回退到原始方法
            # 获取文件夹中的所有文件
            files = [os.path.join(folder_path, f) for f in os.listdir(folder_path)
                     if os.path.isfile(os.path.join(folder_path, f))]

            if not files:
                print("文件夹中没有文件")
                return False

            # 不要只筛选.dcm文件，尝试加载所有文件作为DICOM
            self.dicom_files = []
            for file_path in files:
                try:
                    # 尝试加载DICOM文件并验证其有效性
                    dicom = pydicom.dcmread(file_path)
                    # 验证它是否包含像素数据
                    if hasattr(dicom, 'pixel_array') and dicom.pixel_array.size > 0:
                        self.dicom_files.append(file_path)
                        print(f"有效MRI DICOM文件: {file_path}, 尺寸: {dicom.pixel_array.shape}, "
                              f"最小值: {np.min(dicom.pixel_array)}, 最大值: {np.max(dicom.pixel_array)}")
                except Exception as e:
                    print(f"忽略无效文件 {file_path}: {e}")
                    continue

            if not self.dicom_files:
                print("未找到有效的DICOM文件")
                return False

            # 读取第一个文件获取基本信息
            ref_dicom = pydicom.dcmread(self.dicom_files[0])

            # 获取像素间距和切片厚度
            self.pixel_spacing = ref_dicom.PixelSpacing
            self.slice_thickness = ref_dicom.SliceThickness
            self.origin = ref_dicom.ImagePositionPatient

            # 读取所有文件并排序
            slices = [pydicom.dcmread(f) for f in self.dicom_files]
            slices.sort(key=lambda x: float(x.ImagePositionPatient[2]))

            # 转换为3D numpy数组
            img_shape = list(slices[0].pixel_array.shape)
            img_shape.append(len(slices))
            self.mri_data = np.zeros(img_shape)
            self.shape = img_shape

            # 填充3D数组
            for i, s in enumerate(slices):
                pixel_array = s.pixel_array.astype(np.int16)

                # 检查数据范围，确保有有效对比度
                if np.max(pixel_array) == np.min(pixel_array):
                    print(f"警告: 切片 {i} 无对比度，添加随机噪声")
                    pixel_array = pixel_array + np.random.normal(0, 1, pixel_array.shape).astype(np.int16)

                self.mri_data[:, :, i] = pixel_array

            # 验证MRI数据
            if np.max(self.mri_data) == np.min(self.mri_data):
                print("警告: 整个MRI数据无对比度，应用直方图拉伸")
                # 添加随机噪声以创建对比度
                self.mri_data = self.mri_data + np.random.normal(0, 10, self.mri_data.shape).astype(np.int16)

            # 创建SimpleITK图像对象，用于配准
            try:
                self.sitk_image = sitk.ReadImage(self.dicom_files)
                self.direction = self.sitk_image.GetDirection()

                # 验证SimpleITK图像
                stats = sitk.StatisticsImageFilter()
                stats.Execute(self.sitk_image)
                print(f"MRI SITK图像: 最小值: {stats.GetMinimum()}, 最大值: {stats.GetMaximum()}")

                if stats.GetMinimum() == stats.GetMaximum():
                    print("警告: MRI SITK图像无对比度，从NumPy数组重新创建")
                    self.sitk_image = sitk.GetImageFromArray(self.mri_data)
                    self.sitk_image.SetSpacing((self.pixel_spacing[0], self.pixel_spacing[1], self.slice_thickness))
                    self.sitk_image.SetOrigin(self.origin)
                    self.sitk_image.SetDirection((1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0))
            except Exception as e:
                print(f"创建SimpleITK图像失败: {e}")
                # 如果读取DICOM序列失败，则尝试从numpy数组创建
                self.sitk_image = sitk.GetImageFromArray(self.mri_data)
                self.sitk_image.SetSpacing((self.pixel_spacing[0], self.pixel_spacing[1], self.slice_thickness))
                self.sitk_image.SetOrigin(self.origin)
                # 设置默认方向为单位矩阵
                self.sitk_image.SetDirection((1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0))

            print(f"MRI数据加载成功: 尺寸 {self.mri_data.shape}")
            print(f"MRI数据范围: {np.min(self.mri_data)} - {np.max(self.mri_data)}")
            return True

    def get_mri_value(self, x, y, z):
        """
        获取指定世界坐标的MRI值
        参数:
            x, y, z: 世界坐标 (mm)
        返回:
            对应位置的MRI值，如果超出范围则返回None
        """
        if self.mri_data is None:
            return None

        # 将世界坐标转换为像素坐标
        px = int((x - self.origin[0]) / self.pixel_spacing[0])
        py = int((y - self.origin[1]) / self.pixel_spacing[1])
        pz = int((z - self.origin[2]) / self.slice_thickness)

        # 检查坐标是否在范围内
        if (0 <= px < self.mri_data.shape[0] and
                0 <= py < self.mri_data.shape[1] and
                0 <= pz < self.mri_data.shape[2]):
            return self.mri_data[px, py, pz]
        else:
            return None

    def update_from_sitk(self, sitk_image):
        """从SimpleITK图像更新MRI数据"""
        # 保存变换后的图像
        self.transformed_image = sitk_image

        # 从SimpleITK图像更新numpy数组
        new_data = sitk.GetArrayFromImage(sitk_image)

        # SimpleITK和numpy的维度顺序不同，需要转置
        new_data = np.transpose(new_data, (1, 0, 2))

        # 更新MRI数据
        self.mri_data = new_data
        self.shape = new_data.shape

        # 更新其他属性
        self.origin = sitk_image.GetOrigin()
        spacing = sitk_image.GetSpacing()
        self.pixel_spacing = [spacing[0], spacing[1]]
        self.slice_thickness = spacing[2]

        # 验证更新后的数据
        print(f"更新后的MRI数据: 尺寸 {self.shape}, 范围 {np.min(self.mri_data)} - {np.max(self.mri_data)}")

        # 检查数据是否为梯度图像
        def is_gradient_image(img, threshold=0.9):
            """检查图像是否主要是梯度"""
            if len(img.shape) < 2:
                return False

            h, w = img.shape[:2]
            # 计算行方向和列方向的差异
            row_diff = np.mean(np.abs(np.diff(img, axis=0)))
            col_diff = np.mean(np.abs(np.diff(img, axis=1)))
            total_var = np.var(img)
            # 如果主要变化在一个方向上，且总体方差较大，可能是梯度图
            return (row_diff / (col_diff + 1e-10) > 5 or col_diff / (row_diff + 1e-10) > 5) and total_var > 100

        # 检查中间切片
        mid_slice = self.mri_data[:, :, self.mri_data.shape[2] // 2]
        if is_gradient_image(mid_slice):
            print("警告: 更新后的MRI数据可能是梯度图像，配准结果可能不正确")

        return True


class RegistrationHandler:
    """图像配准处理类"""

    def __init__(self, ct_handler=None, mri_handler=None):
        self.fixed_image = None
        self.moving_image = None
        self.transform_parameters_map = None
        self.registered_image = None
        self.registration_type = "rigid"  # 可选: rigid, affine, bspline
        self.temp_dir = tempfile.mkdtemp()
        self.ct_handler = ct_handler  # 添加对CT处理器的引用
        self.mri_handler = mri_handler  # 添加对MRI处理器的引用
        self.final_transform = None  # 保存最终变换

    def __del__(self):
        """析构函数，清理临时文件夹"""
        try:
            shutil.rmtree(self.temp_dir)
        except:
            pass

    def set_fixed_image(self, image):
        """设置固定图像（通常是CT图像）"""
        self.fixed_image = image

    def set_moving_image(self, image):
        """设置移动图像（通常是MRI图像）"""
        self.moving_image = image

    def set_registration_type(self, reg_type):
        """设置配准类型"""
        if reg_type in ["rigid", "affine", "bspline"]:
            self.registration_type = reg_type

    def register_images(self, progress_callback=None):
        """
        使用reg.py中一致的方法执行图像配准
        """
        if self.fixed_image is None or self.moving_image is None:
            print("配准失败: 固定图像或移动图像未设置")
            return None

        try:
            print("使用reg.py兼容方法执行配准...")

            # 调用reg.py兼容的配准方法
            self.final_transform, self.registered_image = reg_register_images(
                self.fixed_image,
                self.moving_image,
                progress_callback
            )

            # 检查配准结果
            reg_stats = sitk.StatisticsImageFilter()
            reg_stats.Execute(self.registered_image)
            print(f"配准结果: 最小值={reg_stats.GetMinimum()}, 最大值={reg_stats.GetMaximum()}")

            return self.registered_image

        except Exception as e:
            import traceback
            traceback.print_exc()
            print(f"使用reg方法配准失败: {str(e)}")

            return None

    def resample_image(self, moving, fixed, transform):
        """
        使用reg.py中一致的图像重采样方法
        """
        return reg_resample_image(moving, fixed, transform)

    def save_registration_result(self, output_dir):
        """保存配准结果和变换参数"""
        if self.registered_image is None:
            print("没有配准结果可保存")
            return False

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)

        # 保存配准后的图像
        sitk.WriteImage(self.registered_image, os.path.join(output_dir, "registered_image.mha"))

        # 保存变换参数
        if self.transform_parameters_map is not None and ELASTIX_INSTALLED:
            sitk.WriteParameterFile(self.transform_parameters_map[0],
                                    os.path.join(output_dir, "transform_parameters.txt"))

        return True


class MRICTCalibrator:
    """MRI到CT的校准器"""

    def __init__(self):
        self.reference_points = []  # [x, y, z, mri值, hu值]
        self.mri_to_hu_a = 1.0  # 斜率
        self.mri_to_hu_b = 0.0  # 截距

    def add_reference_point(self, x, y, z, mri_value, hu_value):
        """添加参考点"""
        self.reference_points.append([x, y, z, mri_value, hu_value])
        return len(self.reference_points)

    def clear_reference_points(self):
        """清除所有参考点"""
        self.reference_points = []
        return True

    def perform_calibration(self):
        """改进的MRI到HU校准"""
        if len(self.reference_points) < 2:
            print("至少需要两个参考点来进行校准")
            return False

        # 提取MRI值和HU值
        mri_values = np.array([point[3] for point in self.reference_points])
        hu_values = np.array([point[4] for point in self.reference_points])

        # 异常值检测和处理
        if len(self.reference_points) >= 5:
            print("执行异常值检测...")

            # 使用四分位距法检测异常值
            mri_q1, mri_q3 = np.percentile(mri_values, [25, 75])
            hu_q1, hu_q3 = np.percentile(hu_values, [25, 75])

            mri_iqr = mri_q3 - mri_q1
            hu_iqr = hu_q3 - hu_q1

            # 定义异常值边界
            mri_lower = mri_q1 - 1.5 * mri_iqr
            mri_upper = mri_q3 + 1.5 * mri_iqr
            hu_lower = hu_q1 - 1.5 * hu_iqr
            hu_upper = hu_q3 + 1.5 * hu_iqr

            # 过滤异常值
            good_indices = []
            for i in range(len(mri_values)):
                if (mri_lower <= mri_values[i] <= mri_upper and
                        hu_lower <= hu_values[i] <= hu_upper):
                    good_indices.append(i)

            if len(good_indices) < 2:
                print("警告: 检测到过多异常值。使用所有点。")
            else:
                print(f"已过滤 {len(mri_values) - len(good_indices)} 个异常值")
                mri_values = mri_values[good_indices]
                hu_values = hu_values[good_indices]

        # 线性回归
        slope, intercept, r_value, p_value, std_err = stats.linregress(mri_values, hu_values)
        r_squared = r_value ** 2

        print(f"线性拟合: HU = {slope:.4f} × MRI + {intercept:.2f} (R² = {r_squared:.4f})")

        # 尝试二次多项式拟合
        try:
            poly2_coeffs = np.polyfit(mri_values, hu_values, 2)
            p2 = np.poly1d(poly2_coeffs)

            # 使用多项式计算预测值
            predicted2 = p2(mri_values)

            # 计算R²值
            ss_total = np.sum((hu_values - np.mean(hu_values)) ** 2)
            ss_residual2 = np.sum((hu_values - predicted2) ** 2)
            r_squared2 = 1 - (ss_residual2 / ss_total)

            print(
                f"二次多项式拟合: HU = {poly2_coeffs[0]:.6f} × MRI² + {poly2_coeffs[1]:.4f} × MRI + {poly2_coeffs[2]:.2f} (R² = {r_squared2:.4f})")

            # 如果二次拟合明显优于线性拟合，则使用二次拟合
            if r_squared2 > r_squared + 0.05:  # R²提高至少0.05
                print("使用二次多项式拟合（效果更佳）")
                self.mri_to_hu_model = "polynomial2"
                self.mri_to_hu_coeffs = poly2_coeffs
                self.mri_to_hu_a = slope  # 保留线性参数用于简单显示
                self.mri_to_hu_b = intercept
                return True
        except Exception as e:
            print(f"二次多项式拟合失败: {e}")
            print("使用线性拟合")

        # 默认使用线性拟合
        self.mri_to_hu_model = "linear"
        self.mri_to_hu_a = slope
        self.mri_to_hu_b = intercept

        print(f"校准结果: HU = {slope:.4f} × MRI + {intercept:.2f} (R² = {r_squared:.4f})")
        return True

    def mri_to_hu(self, mri_value):
        """将MRI值转换为HU值，支持多种模型"""
        if hasattr(self, 'mri_to_hu_model') and self.mri_to_hu_model == "polynomial2":
            # 使用二次多项式转换
            a, b, c = self.mri_to_hu_coeffs
            return a * mri_value ** 2 + b * mri_value + c
        else:
            # 默认线性转换
            return self.mri_to_hu_a * mri_value + self.mri_to_hu_b


class RegistrationThread(QThread):
    """配准线程类"""
    progress_signal = pyqtSignal(int)  # 进度信号
    result_signal = pyqtSignal(object)  # 结果信号
    error_signal = pyqtSignal(str)  # 错误信号

    def __init__(self, registration_handler):
        super().__init__()
        self.registration_handler = registration_handler

    def run(self):
        try:
            # 执行配准
            result = self.registration_handler.register_images(self.update_progress)

            if result is not None:
                self.result_signal.emit(result)
            else:
                self.error_signal.emit("配准失败")
        except Exception as e:
            import traceback
            traceback.print_exc()
            self.error_signal.emit(f"配准线程错误: {str(e)}")

    def update_progress(self, value):
        """更新进度信号"""
        self.progress_signal.emit(value)


class MainWindow(QMainWindow):
    """主窗口类"""

    def __init__(self):
        super().__init__()

        # 初始化数据处理类
        self.ct_handler = CTDataHandler()
        self.mri_handler = MRIDataHandler()
        self.registration_handler = RegistrationHandler(self.ct_handler, self.mri_handler)
        self.mri_ct_calibrator = MRICTCalibrator()

        # 数据存储
        self.registration_thread = None  # 配准线程

        # 初始化用户界面
        self.init_ui()

        # # 添加reg.py兼容性提示
        # compat_label = QLabel("已启用reg.py兼容模式，确保配准结果一致")
        # compat_label.setStyleSheet("color: green; font-weight: bold;")
        # self.statusBar.addPermanentWidget(compat_label)

    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle('医学图像配准工具')
        self.setGeometry(100, 100, 1200, 800)  # 增加窗口大小

        # 创建选项卡
        self.tabs = QTabWidget()
        self.tab_data = QWidget()
        self.tab_registration = QWidget()  # 添加配准选项卡
        self.tab_calibration = QWidget()
        self.tab_visualization = QWidget()
        self.tab_export = QWidget()

        self.tabs.addTab(self.tab_data, "数据加载")
        self.tabs.addTab(self.tab_registration, "图像配准")
        self.tabs.addTab(self.tab_calibration, "MRI校准")
        self.tabs.addTab(self.tab_visualization, "可视化")
        self.tabs.addTab(self.tab_export, "导出")

        # 设置各选项卡
        self.setup_data_tab()
        self.setup_registration_tab()
        self.setup_calibration_tab()
        self.setup_visualization_tab()
        self.setup_export_tab()

        # 设置主布局
        main_layout = QVBoxLayout()
        main_layout.addWidget(self.tabs)

        # 状态栏
        self.statusBar = self.statusBar()
        self.statusBar.showMessage('就绪')

        # 设置中央窗口部件
        central_widget = QWidget()
        central_widget.setLayout(main_layout)
        self.setCentralWidget(central_widget)

    def setup_data_tab(self):
        """设置数据加载选项卡"""
        layout = QVBoxLayout()

        # CT数据加载组
        ct_group = QGroupBox("CT数据")
        ct_layout = QVBoxLayout()

        ct_file_layout = QHBoxLayout()
        self.ct_path_label = QLabel("未选择CT文件夹")
        self.ct_browse_button = QPushButton("浏览...")
        self.ct_browse_button.clicked.connect(self.browse_ct_folder)
        ct_file_layout.addWidget(self.ct_path_label)
        ct_file_layout.addWidget(self.ct_browse_button)

        self.ct_load_button = QPushButton("加载CT数据")
        self.ct_load_button.clicked.connect(self.load_ct_data)

        ct_layout.addLayout(ct_file_layout)
        ct_layout.addWidget(self.ct_load_button)
        ct_group.setLayout(ct_layout)

        # MRI数据加载组
        mri_group = QGroupBox("MRI数据")
        mri_layout = QVBoxLayout()

        mri_file_layout = QHBoxLayout()
        self.mri_path_label = QLabel("未选择MRI文件夹")
        self.mri_browse_button = QPushButton("浏览...")
        self.mri_browse_button.clicked.connect(self.browse_mri_folder)
        mri_file_layout.addWidget(self.mri_path_label)
        mri_file_layout.addWidget(self.mri_browse_button)

        self.mri_load_button = QPushButton("加载MRI数据")
        self.mri_load_button.clicked.connect(self.load_mri_data)

        # 添加测试MRI数据按钮
        self.create_test_mri_button = QPushButton("创建测试MRI数据")
        self.create_test_mri_button.clicked.connect(self.create_test_mri_data)

        mri_layout.addLayout(mri_file_layout)
        mri_layout.addWidget(self.mri_load_button)
        mri_layout.addWidget(self.create_test_mri_button)
        mri_group.setLayout(mri_layout)

        # 数据信息显示区域
        info_group = QGroupBox("数据信息")
        info_layout = QVBoxLayout()
        self.data_info_label = QLabel("未加载数据")
        info_layout.addWidget(self.data_info_label)
        info_group.setLayout(info_layout)

        # 添加所有组到布局
        layout.addWidget(ct_group)
        layout.addWidget(mri_group)
        layout.addWidget(info_group)

        layout.addStretch()

        self.tab_data.setLayout(layout)

    def setup_registration_tab(self):
        """设置图像配准选项卡"""
        layout = QVBoxLayout()

        # 配准参数组
        params_group = QGroupBox("配准参数")
        params_layout = QVBoxLayout()

        # 配准类型选择
        reg_type_layout = QHBoxLayout()
        reg_type_layout.addWidget(QLabel("配准类型:"))
        self.reg_type_combo = QComboBox()
        self.reg_type_combo.addItems(["刚性配准", "仿射变换", "非刚性B样条配准"])
        reg_type_layout.addWidget(self.reg_type_combo)
        params_layout.addLayout(reg_type_layout)

        # 配准方向选择
        direction_layout = QHBoxLayout()
        direction_layout.addWidget(QLabel("配准方向:"))
        self.direction_combo = QComboBox()
        self.direction_combo.addItems(["CT到MRI", "MRI到CT"])
        direction_layout.addWidget(self.direction_combo)
        params_layout.addLayout(direction_layout)

        # 配准选项
        options_layout = QFormLayout()

        # 添加高级选项复选框
        self.use_advanced_options = QCheckBox("使用高级选项")
        self.use_advanced_options.setChecked(False)
        options_layout.addRow(self.use_advanced_options)

        # 高级选项
        self.advanced_options_widget = QWidget()
        advanced_layout = QFormLayout(self.advanced_options_widget)

        # 高级选项内容
        self.max_iterations_input = QSpinBox()
        self.max_iterations_input.setRange(10, 5000)
        self.max_iterations_input.setValue(200)

        self.sample_percent_input = QDoubleSpinBox()
        self.sample_percent_input.setRange(0.01, 1.0)
        self.sample_percent_input.setSingleStep(0.01)
        self.sample_percent_input.setValue(0.1)

        self.histogram_bins_input = QSpinBox()
        self.histogram_bins_input.setRange(10, 200)
        self.histogram_bins_input.setValue(50)

        advanced_layout.addRow("最大迭代次数:", self.max_iterations_input)
        advanced_layout.addRow("采样百分比:", self.sample_percent_input)
        advanced_layout.addRow("直方图分箱数:", self.histogram_bins_input)

        self.advanced_options_widget.setVisible(False)
        self.use_advanced_options.toggled.connect(self.advanced_options_widget.setVisible)

        params_layout.addLayout(options_layout)
        params_layout.addWidget(self.advanced_options_widget)

        params_group.setLayout(params_layout)

        # 配准操作组
        operation_group = QGroupBox("配准操作")
        operation_layout = QVBoxLayout()

        # 配准按钮
        self.run_registration_button = QPushButton("执行图像配准")
        self.run_registration_button.clicked.connect(self.run_registration)

        # 保存配准结果按钮
        self.save_reg_result_button = QPushButton("保存配准结果")
        self.save_reg_result_button.clicked.connect(self.save_registration_result)
        self.save_reg_result_button.setEnabled(False)

        # 配准进度条
        self.registration_progress = QProgressBar()
        self.registration_progress.setRange(0, 100)
        self.registration_progress.setValue(0)

        operation_layout.addWidget(self.run_registration_button)
        operation_layout.addWidget(self.save_reg_result_button)
        operation_layout.addWidget(self.registration_progress)

        # 配准状态
        self.registration_status_label = QLabel("配准状态: 未开始")
        operation_layout.addWidget(self.registration_status_label)

        operation_group.setLayout(operation_layout)

        # 配准结果预览
        preview_group = QGroupBox("配准结果预览")
        preview_layout = QGridLayout()

        # 添加三个图像预览区域
        self.ct_preview_label = QLabel("CT图像")
        self.ct_preview_label.setAlignment(Qt.AlignCenter)
        self.ct_preview_label.setFixedSize(300, 300)
        self.ct_preview_label.setStyleSheet("border: 1px solid black")

        self.mri_preview_label = QLabel("MRI图像")
        self.mri_preview_label.setAlignment(Qt.AlignCenter)
        self.mri_preview_label.setFixedSize(300, 300)
        self.mri_preview_label.setStyleSheet("border: 1px solid black")

        self.registered_preview_label = QLabel("配准后图像")
        self.registered_preview_label.setAlignment(Qt.AlignCenter)
        self.registered_preview_label.setFixedSize(300, 300)
        self.registered_preview_label.setStyleSheet("border: 1px solid black")

        # 添加切片滑块
        self.slice_slider = QSlider(Qt.Horizontal)
        self.slice_slider.setRange(0, 100)
        self.slice_slider.setValue(50)
        self.slice_slider.valueChanged.connect(self.update_preview_slices)

        preview_layout.addWidget(self.ct_preview_label, 0, 0)
        preview_layout.addWidget(self.mri_preview_label, 0, 1)
        preview_layout.addWidget(self.registered_preview_label, 0, 2)
        preview_layout.addWidget(QLabel("切片位置:"), 1, 0)
        preview_layout.addWidget(self.slice_slider, 1, 1, 1, 2)

        preview_group.setLayout(preview_layout)

        # 将所有组添加到主布局
        layout.addWidget(params_group)
        layout.addWidget(operation_group)
        layout.addWidget(preview_group)

        self.tab_registration.setLayout(layout)

    def setup_calibration_tab(self):
        """设置MRI校准选项卡"""
        layout = QVBoxLayout()

        # 校准点组
        cal_group = QGroupBox("校准参考点")
        cal_layout = QVBoxLayout()

        cal_info = QLabel("添加已知MRI值和HU值的参考点进行校准")
        cal_layout.addWidget(cal_info)

        point_input_layout = QHBoxLayout()
        self.point_x_input = QDoubleSpinBox()
        self.point_x_input.setRange(-1000, 1000)
        self.point_x_input.setDecimals(2)

        self.point_y_input = QDoubleSpinBox()
        self.point_y_input.setRange(-1000, 1000)
        self.point_y_input.setDecimals(2)

        self.point_z_input = QDoubleSpinBox()
        self.point_z_input.setRange(-1000, 1000)
        self.point_z_input.setDecimals(2)

        self.point_mri_input = QSpinBox()
        self.point_mri_input.setRange(-1000, 5000)

        self.point_hu_input = QSpinBox()
        self.point_hu_input.setRange(-1000, 5000)

        point_input_layout.addWidget(QLabel("X:"))
        point_input_layout.addWidget(self.point_x_input)
        point_input_layout.addWidget(QLabel("Y:"))
        point_input_layout.addWidget(self.point_y_input)
        point_input_layout.addWidget(QLabel("Z:"))
        point_input_layout.addWidget(self.point_z_input)
        point_input_layout.addWidget(QLabel("MRI值:"))
        point_input_layout.addWidget(self.point_mri_input)
        point_input_layout.addWidget(QLabel("HU值:"))
        point_input_layout.addWidget(self.point_hu_input)

        self.add_point_button = QPushButton("添加参考点")
        self.add_point_button.clicked.connect(self.add_reference_point)

        # 参考点表格
        self.points_table = QTableWidget(0, 5)
        self.points_table.setHorizontalHeaderLabels(["X", "Y", "Z", "MRI值", "HU值"])
        self.points_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)

        point_buttons_layout = QHBoxLayout()
        point_buttons_layout.addWidget(self.add_point_button)

        cal_layout.addLayout(point_input_layout)
        cal_layout.addLayout(point_buttons_layout)
        cal_layout.addWidget(self.points_table)
        cal_group.setLayout(cal_layout)

        # 校准操作组
        calibrate_group = QGroupBox("执行校准")
        calibrate_layout = QVBoxLayout()

        self.calibrate_button = QPushButton("进行校准")
        self.calibrate_button.clicked.connect(self.perform_calibration)
        self.calibrate_button.setEnabled(False)

        self.clear_points_button = QPushButton("清除参考点")
        self.clear_points_button.clicked.connect(self.clear_reference_points)

        self.calibration_result_label = QLabel("未校准")
        self.calibration_result_label.setAlignment(Qt.AlignCenter)
        font = QFont()
        font.setBold(True)
        self.calibration_result_label.setFont(font)

        # 校准图表
        self.calibration_figure = plt.figure(figsize=(5, 4))
        self.calibration_canvas = FigureCanvas(self.calibration_figure)

        button_layout = QHBoxLayout()
        button_layout.addWidget(self.calibrate_button)
        button_layout.addWidget(self.clear_points_button)

        calibrate_layout.addLayout(button_layout)
        calibrate_layout.addWidget(self.calibration_result_label)
        calibrate_layout.addWidget(self.calibration_canvas)
        calibrate_group.setLayout(calibrate_layout)

        # 添加所有组到布局
        splitter = QSplitter(Qt.Horizontal)

        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.addWidget(cal_group)
        left_widget.setLayout(left_layout)

        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_layout.addWidget(calibrate_group)
        right_widget.setLayout(right_layout)

        splitter.addWidget(left_widget)
        splitter.addWidget(right_widget)

        layout.addWidget(splitter)
        self.tab_calibration.setLayout(layout)

    def setup_visualization_tab(self):
        """设置可视化选项卡"""
        layout = QVBoxLayout()

        # 可视化选项组
        vis_options_group = QGroupBox("可视化选项")
        vis_options_layout = QHBoxLayout()

        self.vis_type_combo = QComboBox()
        self.vis_type_combo.addItems([
            "截面图",
            "配准结果对比"
        ])

        self.generate_vis_button = QPushButton("生成可视化")
        self.generate_vis_button.clicked.connect(self.generate_visualization)

        vis_options_layout.addWidget(QLabel("可视化类型:"))
        vis_options_layout.addWidget(self.vis_type_combo)
        vis_options_layout.addWidget(self.generate_vis_button)
        vis_options_group.setLayout(vis_options_layout)

        # 可视化图表
        self.visualization_figure = plt.figure(figsize=(8, 6))
        self.visualization_canvas = FigureCanvas(self.visualization_figure)
        self.visualization_toolbar = NavigationToolbar(self.visualization_canvas, self)

        # 添加可视化组件到布局
        layout.addWidget(vis_options_group)
        layout.addWidget(self.visualization_toolbar)
        layout.addWidget(self.visualization_canvas)

        self.tab_visualization.setLayout(layout)

    def setup_export_tab(self):
        """设置导出选项卡"""
        layout = QVBoxLayout()

        # 导出配准结果组
        export_reg_group = QGroupBox("导出配准结果")
        export_reg_layout = QVBoxLayout()

        export_reg_file_layout = QHBoxLayout()
        self.export_reg_path_label = QLabel("未设置配准结果导出文件夹")
        self.export_reg_browse_button = QPushButton("浏览...")
        self.export_reg_browse_button.clicked.connect(self.browse_export_reg_folder)
        export_reg_file_layout.addWidget(self.export_reg_path_label)
        export_reg_file_layout.addWidget(self.export_reg_browse_button)

        self.export_reg_button = QPushButton("导出配准结果")
        self.export_reg_button.clicked.connect(self.export_registration_result)
        self.export_reg_button.setEnabled(False)

        export_reg_layout.addLayout(export_reg_file_layout)
        export_reg_layout.addWidget(self.export_reg_button)
        export_reg_group.setLayout(export_reg_layout)

        # 添加所有组到布局
        layout.addWidget(export_reg_group)  # 添加导出配准结果组
        layout.addStretch()

        self.tab_export.setLayout(layout)

    # ================= 事件处理方法 =================

    def browse_ct_folder(self):
        """浏览CT数据文件夹"""
        folder = QFileDialog.getExistingDirectory(self, "选择CT数据文件夹")
        if folder:
            self.ct_path_label.setText(folder)
            self.statusBar.showMessage(f"已选择CT数据文件夹: {folder}")

    def browse_mri_folder(self):
        """浏览MRI数据文件夹"""
        folder = QFileDialog.getExistingDirectory(self, "选择MRI数据文件夹")
        if folder:
            self.mri_path_label.setText(folder)
            self.statusBar.showMessage(f"已选择MRI数据文件夹: {folder}")

    def browse_export_reg_folder(self):
        """浏览配准结果导出文件夹"""
        folder = QFileDialog.getExistingDirectory(self, "选择配准结果导出文件夹")
        if folder:
            self.export_reg_path_label.setText(folder)
            self.statusBar.showMessage(f"已选择配准结果导出文件夹: {folder}")

    def load_ct_data(self):
        """加载CT数据"""
        folder = self.ct_path_label.text()
        if folder == "未选择CT文件夹":
            QMessageBox.warning(self, "警告", "请先选择CT数据文件夹")
            return

        self.statusBar.showMessage("正在加载CT数据...")
        success = self.ct_handler.load_dicom_folder(folder)

        if success:
            QMessageBox.information(self, "成功", "CT数据加载成功")
            self.statusBar.showMessage("CT数据已加载")

            # 更新数据信息
            ct_info = (f"CT数据: 尺寸 {self.ct_handler.shape}, "
                       f"像素间距 {self.ct_handler.pixel_spacing}, "
                       f"切片厚度 {self.ct_handler.slice_thickness}, "
                       f"范围 {np.min(self.ct_handler.ct_data)} - {np.max(self.ct_handler.ct_data)}")
            self.data_info_label.setText(ct_info)

            # 更新CT预览
            self.update_preview_slices()
        else:
            QMessageBox.critical(self, "错误", "CT数据加载失败")
            self.statusBar.showMessage("CT数据加载失败")

    def load_mri_data(self):
        """加载MRI数据并进行详细验证"""
        folder = self.mri_path_label.text()
        if folder == "未选择MRI文件夹":
            QMessageBox.warning(self, "警告", "请先选择MRI数据文件夹")
            return

        self.statusBar.showMessage("正在加载MRI数据...")

        # 添加详细诊断
        print("----- MRI数据加载诊断 -----")
        print(f"加载文件夹: {folder}")

        success = self.mri_handler.load_dicom_folder(folder)

        if success:
            # 验证数据有效性
            if self.mri_handler.mri_data is None:
                QMessageBox.critical(self, "错误", "MRI数据为空")
                return

            data_range = np.max(self.mri_handler.mri_data) - np.min(self.mri_handler.mri_data)
            print(f"MRI数据范围: {np.min(self.mri_handler.mri_data)} - {np.max(self.mri_handler.mri_data)}")
            print(f"数据范围差值: {data_range}")

            if data_range < 1:
                QMessageBox.warning(self, "警告", f"MRI数据对比度极低(范围: {data_range})")

                # 尝试修复MRI数据
                self.mri_handler.mri_data = self.mri_handler.mri_data + np.random.normal(
                    0, 20, self.mri_handler.mri_data.shape).astype(self.mri_handler.mri_data.dtype)
                print("已添加随机噪声以创建对比度")
                print(f"修复后范围: {np.min(self.mri_handler.mri_data)} - {np.max(self.mri_handler.mri_data)}")

            QMessageBox.information(self, "成功", "MRI数据加载成功")
            self.statusBar.showMessage("MRI数据已加载")

            # 更新数据信息
            mri_info = (f"MRI数据: 尺寸 {self.mri_handler.shape}, "
                        f"像素间距 {self.mri_handler.pixel_spacing}, "
                        f"切片厚度 {self.mri_handler.slice_thickness}, "
                        f"范围 {np.min(self.mri_handler.mri_data)} - {np.max(self.mri_handler.mri_data)}")

            # 如果CT数据已加载，则追加信息
            if hasattr(self.ct_handler, 'ct_data') and self.ct_handler.ct_data is not None:
                cur_info = self.data_info_label.text()
                self.data_info_label.setText(f"{cur_info}\n{mri_info}")
            else:
                self.data_info_label.setText(mri_info)

            # 更新MRI预览
            self.update_preview_slices()
        else:
            QMessageBox.critical(self, "错误", "MRI数据加载失败")
            self.statusBar.showMessage("MRI数据加载失败")

    def create_test_mri_data(self):
        """创建测试MRI数据用于调试"""
        # 检查CT数据是否已加载
        if not hasattr(self.ct_handler, 'ct_data') or self.ct_handler.ct_data is None:
            QMessageBox.warning(self, "警告", "请先加载CT数据")
            return False

        # 创建与CT数据相同尺寸的MRI数据
        ct_shape = self.ct_handler.ct_data.shape
        test_mri = np.zeros(ct_shape, dtype=np.int16)

        # 创建具有结构的数据
        for z in range(ct_shape[2]):
            for y in range(ct_shape[1]):
                for x in range(ct_shape[0]):
                    # 距离中心的距离
                    cx, cy, cz = ct_shape[0] // 2, ct_shape[1] // 2, ct_shape[2] // 2
                    r = np.sqrt((x - cx) ** 2 + (y - cy) ** 2 + (z - cz) ** 2)
                    # 创建同心圆
                    test_mri[x, y, z] = int(1000 * np.exp(-r / 30)) + np.random.randint(-10, 10)

        # 设置MRI数据
        self.mri_handler.mri_data = test_mri
        self.mri_handler.shape = test_mri.shape

        # 复制CT的其他属性
        self.mri_handler.pixel_spacing = self.ct_handler.pixel_spacing
        self.mri_handler.slice_thickness = self.ct_handler.slice_thickness
        self.mri_handler.origin = self.ct_handler.origin

        # 创建SimpleITK图像
        self.mri_handler.sitk_image = sitk.GetImageFromArray(test_mri)
        self.mri_handler.sitk_image.SetSpacing((self.mri_handler.pixel_spacing[0],
                                                self.mri_handler.pixel_spacing[1],
                                                self.mri_handler.slice_thickness))
        self.mri_handler.sitk_image.SetOrigin(self.mri_handler.origin)
        self.mri_handler.sitk_image.SetDirection((1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0))

        # 更新界面
        mri_info = (f"MRI数据(测试): 尺寸 {self.mri_handler.shape}, "
                    f"像素间距 {self.mri_handler.pixel_spacing}, "
                    f"切片厚度 {self.mri_handler.slice_thickness}, "
                    f"范围 {np.min(self.mri_handler.mri_data)} - {np.max(self.mri_handler.mri_data)}")

        # 如果CT数据已加载，则追加信息
        if hasattr(self.ct_handler, 'ct_data') and self.ct_handler.ct_data is not None:
            cur_info = self.data_info_label.text()
            self.data_info_label.setText(f"{cur_info}\n{mri_info}")
        else:
            self.data_info_label.setText(mri_info)

        QMessageBox.information(self, "成功", "已创建测试MRI数据")
        self.update_preview_slices()

        return True

    def run_registration(self):
        """执行图像配准"""
        # 检查数据是否已加载
        if (not hasattr(self.ct_handler, 'sitk_image') or self.ct_handler.sitk_image is None or
                not hasattr(self.mri_handler, 'sitk_image') or self.mri_handler.sitk_image is None):
            QMessageBox.warning(self, "警告", "请先加载CT和MRI数据")
            return

        # 检查数据是否有效
        ct_stats = sitk.StatisticsImageFilter()
        ct_stats.Execute(self.ct_handler.sitk_image)
        mri_stats = sitk.StatisticsImageFilter()
        mri_stats.Execute(self.mri_handler.sitk_image)

        if ct_stats.GetMinimum() == ct_stats.GetMaximum():
            QMessageBox.warning(self, "警告", "CT数据无效，请检查数据")
            return

        if mri_stats.GetMinimum() == mri_stats.GetMaximum():
            QMessageBox.warning(self, "警告", "MRI数据无效，请检查数据")
            return

        # 强制使用固定的配准参数（与reg.py一致）
        reg_message = ""
        if self.reg_type_combo.currentIndex() != 0:
            reg_message = "注意：为确保结果一致，将使用刚性配准（忽略界面选择）\n"
            self.reg_type_combo.setCurrentIndex(0)

        if self.use_advanced_options.isChecked():
            reg_message += "注意：为确保结果一致，将忽略高级选项设置\n"

        if reg_message:
            QMessageBox.information(self, "配准参数调整", reg_message)

        # 固定使用"CT到MRI"的方向
        self.registration_handler.set_registration_type("rigid")
        self.registration_handler.set_fixed_image(self.ct_handler.sitk_image)
        self.registration_handler.set_moving_image(self.mri_handler.sitk_image)

        # 更新界面状态
        self.registration_progress.setValue(0)
        self.registration_status_label.setText("配准状态: 正在执行...")
        self.run_registration_button.setEnabled(False)

        # 创建并启动配准线程
        self.registration_thread = RegistrationThread(self.registration_handler)
        self.registration_thread.progress_signal.connect(self.update_registration_progress)
        self.registration_thread.result_signal.connect(self.handle_registration_result)
        self.registration_thread.error_signal.connect(self.handle_registration_error)
        self.registration_thread.start()

    def update_registration_progress(self, value):
        """更新配准进度条"""
        self.registration_progress.setValue(value)

    def handle_registration_error(self, error_msg):
        """处理配准错误"""
        self.registration_progress.setValue(0)
        self.registration_status_label.setText(f"配准状态: 失败 - {error_msg}")
        self.run_registration_button.setEnabled(True)

        QMessageBox.critical(self, "错误", f"配准失败: {error_msg}")
        self.statusBar.showMessage(f"配准失败: {error_msg}")

    def save_registration_result(self):
        """保存配准结果"""
        folder = QFileDialog.getExistingDirectory(self, "选择配准结果保存文件夹")
        if not folder:
            return

        if self.registration_handler.registered_image is None:
            QMessageBox.warning(self, "警告", "没有可保存的配准结果")
            return

        self.statusBar.showMessage("正在保存配准结果...")

        # 保存结果
        success = self.registration_handler.save_registration_result(folder)

        if success:
            QMessageBox.information(self, "成功", f"配准结果已保存到 {folder}")
            self.statusBar.showMessage(f"配准结果已保存到 {folder}")
        else:
            QMessageBox.critical(self, "错误", "保存配准结果失败")
            self.statusBar.showMessage("保存配准结果失败")

    def add_reference_point(self):
        """添加MRI-HU校准参考点"""
        x = self.point_x_input.value()
        y = self.point_y_input.value()
        z = self.point_z_input.value()
        mri_value = self.point_mri_input.value()
        hu_value = self.point_hu_input.value()

        # 添加到参考点列表
        self.mri_ct_calibrator.add_reference_point(x, y, z, mri_value, hu_value)

        # 添加到表格
        row = self.points_table.rowCount()
        self.points_table.insertRow(row)
        self.points_table.setItem(row, 0, QTableWidgetItem(f"{x:.2f}"))
        self.points_table.setItem(row, 1, QTableWidgetItem(f"{y:.2f}"))
        self.points_table.setItem(row, 2, QTableWidgetItem(f"{z:.2f}"))
        self.points_table.setItem(row, 3, QTableWidgetItem(f"{mri_value}"))
        self.points_table.setItem(row, 4, QTableWidgetItem(f"{hu_value}"))

        # 如果有两个或更多点，启用校准按钮
        if self.points_table.rowCount() >= 2:
            self.calibrate_button.setEnabled(True)

        self.statusBar.showMessage(f"已添加参考点: ({x:.2f}, {y:.2f}, {z:.2f}), MRI={mri_value}, HU={hu_value}")

    def clear_reference_points(self):
        """清除所有参考点"""
        self.mri_ct_calibrator.clear_reference_points()
        self.points_table.setRowCount(0)
        self.calibrate_button.setEnabled(False)
        self.calibration_result_label.setText("未校准")

        # 清除校准图表
        self.calibration_figure.clear()
        self.calibration_canvas.draw()

        self.statusBar.showMessage("已清除所有参考点")

    def perform_calibration(self):
        """执行MRI到HU的校准"""
        if len(self.mri_ct_calibrator.reference_points) < 2:
            QMessageBox.warning(self, "警告", "至少需要两个参考点来进行校准")
            return

        # 执行校准
        success = self.mri_ct_calibrator.perform_calibration()

        if not success:
            QMessageBox.critical(self, "错误", "校准失败")
            return

        # 获取校准参数
        slope = self.mri_ct_calibrator.mri_to_hu_a
        intercept = self.mri_ct_calibrator.mri_to_hu_b

        # 提取MRI值和HU值用于绘图
        mri_values = [point[3] for point in self.mri_ct_calibrator.reference_points]
        hu_values = [point[4] for point in self.mri_ct_calibrator.reference_points]

        # 计算R2值
        _, _, r_value, _, _ = stats.linregress(mri_values, hu_values)
        r_squared = r_value ** 2

        # 显示校准结果
        self.calibration_result_label.setText(
            f"校准结果: HU = {slope:.4f} × MRI + {intercept:.2f} (R² = {r_squared:.4f})")

        # 绘制校准图表
        self.calibration_figure.clear()
        ax = self.calibration_figure.add_subplot(111)

        # 绘制数据点
        ax.scatter(mri_values, hu_values, color='blue', label='参考点')

        # 绘制拟合线
        min_mri = min(mri_values)
        max_mri = max(mri_values)
        x_line = np.linspace(min_mri - 100, max_mri + 100, 100)
        y_line = slope * x_line + intercept
        ax.plot(x_line, y_line, color='red', label=f'拟合线: HU = {slope:.4f} × MRI + {intercept:.2f}')

        ax.set_xlabel('MRI值')
        ax.set_ylabel('HU值')
        ax.set_title('MRI-HU校准曲线')
        ax.grid(True, alpha=0.3)
        ax.legend()

        self.calibration_figure.tight_layout()
        self.calibration_canvas.draw()

        QMessageBox.information(self, "成功", "MRI校准完成")
        self.statusBar.showMessage(f"MRI校准完成: HU = {slope:.4f} × MRI + {intercept:.2f}")

    def generate_visualization(self):
        """生成可视化图表"""
        vis_type = self.vis_type_combo.currentText()
        self.statusBar.showMessage(f"正在生成{vis_type}...")

        # 清除当前图表
        self.visualization_figure.clear()

        if vis_type == "截面图":
            self.visualize_section()
        elif vis_type == "配准结果对比":
            self.visualize_registration_comparison()

        # 更新画布
        self.visualization_canvas.draw()
        self.statusBar.showMessage(f"{vis_type}已生成")

    def visualize_section(self):
        """可视化CT/MRI截面图"""
        if not hasattr(self.ct_handler, 'ct_data') or self.ct_handler.ct_data is None:
            if not hasattr(self.mri_handler, 'mri_data') or self.mri_handler.mri_data is None:
                QMessageBox.warning(self, "警告", "请先加载CT或MRI数据")
                return

            # 使用MRI数据
            self.visualize_mri_section()
            return

        # 使用CT数据
        ct_data = self.ct_handler.ct_data

        # 创建子图
        fig = self.visualization_figure
        gs = fig.add_gridspec(2, 2)
        ax1 = fig.add_subplot(gs[0, 0])
        ax2 = fig.add_subplot(gs[0, 1])
        ax3 = fig.add_subplot(gs[1, :])

        # 获取中心点
        z_mid = ct_data.shape[2] // 2
        y_mid = ct_data.shape[1] // 2
        x_mid = ct_data.shape[0] // 2

        # 显示三个正交平面
        ax1.imshow(ct_data[:, :, z_mid], cmap='bone', aspect='equal',
                   vmin=-1000, vmax=2000)
        ax1.set_title('轴向截面')
        ax1.axis('off')

        ax2.imshow(ct_data[:, y_mid, :], cmap='bone', aspect='equal',
                   vmin=-1000, vmax=2000)
        ax2.set_title('冠状截面')
        ax2.axis('off')

        im = ax3.imshow(ct_data[x_mid, :, :], cmap='bone', aspect='equal',
                        vmin=-1000, vmax=2000)
        ax3.set_title('矢状截面')
        ax3.axis('off')

        # 添加颜色条
        cbar = fig.colorbar(im, ax=[ax1, ax2, ax3], orientation='horizontal', pad=0.01, shrink=0.7)
        cbar.set_label('HU值')

        fig.tight_layout()

    def visualize_mri_section(self):
        """可视化MRI截面图"""
        if not hasattr(self.mri_handler, 'mri_data') or self.mri_handler.mri_data is None:
            QMessageBox.warning(self, "警告", "请先加载MRI数据")
            return

        # 使用MRI数据
        mri_data = self.mri_handler.mri_data

        # 创建子图
        fig = self.visualization_figure
        gs = fig.add_gridspec(2, 2)
        ax1 = fig.add_subplot(gs[0, 0])
        ax2 = fig.add_subplot(gs[0, 1])
        ax3 = fig.add_subplot(gs[1, :])

        # 获取中心点
        z_mid = mri_data.shape[2] // 2
        y_mid = mri_data.shape[1] // 2
        x_mid = mri_data.shape[0] // 2

        # 显示三个正交平面
        ax1.imshow(mri_data[:, :, z_mid], cmap='gray', aspect='equal')
        ax1.set_title('轴向截面')
        ax1.axis('off')

        ax2.imshow(mri_data[:, y_mid, :], cmap='gray', aspect='equal')
        ax2.set_title('冠状截面')
        ax2.axis('off')

        im = ax3.imshow(mri_data[x_mid, :, :], cmap='gray', aspect='equal')
        ax3.set_title('矢状截面')
        ax3.axis('off')

        # 添加颜色条
        cbar = fig.colorbar(im, ax=[ax1, ax2, ax3], orientation='horizontal', pad=0.01, shrink=0.7)
        cbar.set_label('MRI强度值')

        fig.tight_layout()

    def handle_registration_result(self, result):
        """处理配准结果"""
        # 更新界面状态
        self.registration_progress.setValue(100)
        self.registration_status_label.setText("配准状态: 完成")
        self.run_registration_button.setEnabled(True)
        self.save_reg_result_button.setEnabled(True)
        self.export_reg_button.setEnabled(True)

        # 检查配准结果
        result_stats = sitk.StatisticsImageFilter()
        result_stats.Execute(result)
        print(f"配准结果: 最小值={result_stats.GetMinimum()}, 最大值={result_stats.GetMaximum()}")

        if result_stats.GetMinimum() == result_stats.GetMaximum():
            QMessageBox.warning(self, "警告", "配准结果无效，请尝试其他配准参数")
            return

        # 更新预览
        self.update_preview_slices()

        # 转到可视化标签页并显示配准结果
        self.tabs.setCurrentWidget(self.tab_visualization)
        self.visualize_registration_comparison()
        self.visualization_canvas.draw()

        QMessageBox.information(self, "成功", "图像配准完成")
        self.statusBar.showMessage("图像配准完成")

    def update_preview_slices(self):
        """根据滑块位置更新预览图像的切片"""
        # 获取归一化的滑块位置 (0-1)
        slice_pos = self.slice_slider.value() / 100.0

        # 更新CT预览
        if hasattr(self.ct_handler, 'sitk_image') and self.ct_handler.sitk_image is not None:
            # 从SimpleITK图像获取Numpy数组
            ct_array = sitk.GetArrayFromImage(self.ct_handler.sitk_image)

            # 计算切片索引
            ct_slice_idx = int(slice_pos * (ct_array.shape[0] - 1))
            ct_slice_idx = max(0, min(ct_slice_idx, ct_array.shape[0] - 1))

            # 获取切片
            ct_slice = ct_array[ct_slice_idx]

            # 标准化显示
            ct_norm = (ct_slice - np.min(ct_slice)) / (np.max(ct_slice) - np.min(ct_slice) + 1e-8)
            ct_slice_norm = (ct_norm * 255).astype(np.uint8)

            h, w = ct_slice_norm.shape
            ct_qimg = QImage(ct_slice_norm.tobytes(), w, h, w, QImage.Format_Grayscale8)
            ct_pixmap = QPixmap.fromImage(ct_qimg).scaled(
                self.ct_preview_label.width(), self.ct_preview_label.height(),
                Qt.KeepAspectRatio)
            self.ct_preview_label.setPixmap(ct_pixmap)
            self.ct_preview_label.setText("")
        else:
            self.ct_preview_label.setText("CT数据未加载")

        # 更新MRI预览
        if hasattr(self.mri_handler, 'sitk_image') and self.mri_handler.sitk_image is not None:
            # 从SimpleITK图像获取Numpy数组
            mri_array = sitk.GetArrayFromImage(self.mri_handler.sitk_image)

            # 计算切片索引
            mri_slice_idx = int(slice_pos * (mri_array.shape[0] - 1))
            mri_slice_idx = max(0, min(mri_slice_idx, mri_array.shape[0] - 1))

            # 获取切片
            mri_slice = mri_array[mri_slice_idx]

            # 标准化显示
            mri_norm = (mri_slice - np.min(mri_slice)) / (np.max(mri_slice) - np.min(mri_slice) + 1e-8)
            mri_slice_norm = (mri_norm * 255).astype(np.uint8)

            h, w = mri_slice_norm.shape
            mri_qimg = QImage(mri_slice_norm.tobytes(), w, h, w, QImage.Format_Grayscale8)
            mri_pixmap = QPixmap.fromImage(mri_qimg).scaled(
                self.mri_preview_label.width(), self.mri_preview_label.height(),
                Qt.KeepAspectRatio)
            self.mri_preview_label.setPixmap(mri_pixmap)
            self.mri_preview_label.setText("")
        else:
            self.mri_preview_label.setText("MRI数据未加载")

        # 更新配准后图像预览 - 灰度融合效果
        if (hasattr(self.registration_handler, 'registered_image') and
                self.registration_handler.registered_image is not None and
                hasattr(self.ct_handler, 'sitk_image') and self.ct_handler.sitk_image is not None):

            # 获取CT图像和配准后的MRI图像
            ct = self.ct_handler.sitk_image
            mri_reg = self.registration_handler.registered_image

            # 将SimpleITK图像转换为numpy数组
            ct_array = sitk.GetArrayFromImage(ct)
            mri_array = sitk.GetArrayFromImage(mri_reg)

            # 使用相同的切片索引
            slice_idx = int(slice_pos * (ct_array.shape[0] - 1))
            slice_idx = max(0, min(slice_idx, ct_array.shape[0] - 1))

            ct_slice = ct_array[slice_idx]
            mri_slice = mri_array[slice_idx]

            # 为融合图像归一化到[0,1]
            ct_norm = (ct_slice - np.min(ct_slice)) / (np.max(ct_slice) - np.min(ct_slice) + 1e-8)
            mri_norm = (mri_slice - np.min(mri_slice)) / (np.max(mri_slice) - np.min(mri_slice) + 1e-8)

            # 创建灰度融合图像 (0.5*CT + 0.5*MRI)
            fused_gray = 0.5 * ct_norm + 0.5 * mri_norm
            fused_slice_norm = (fused_gray * 255).astype(np.uint8)

            # 转换为Qt图像并显示
            h, w = fused_slice_norm.shape
            reg_qimg = QImage(fused_slice_norm.tobytes(), w, h, w, QImage.Format_Grayscale8)
            reg_pixmap = QPixmap.fromImage(reg_qimg).scaled(
                self.registered_preview_label.width(), self.registered_preview_label.height(),
                Qt.KeepAspectRatio)
            self.registered_preview_label.setPixmap(reg_pixmap)
            self.registered_preview_label.setText("")
        else:
            self.registered_preview_label.setText("未执行配准或CT数据未加载")

    def visualize_registration_comparison(self):
        """
        显示配准效果：
          - 左图：CT 图像中间切片
          - 中图：经过配准后重采样的 MRI 图像中间切片
          - 右图：将两幅图像归一化后以红/绿通道叠加显示的融合图像
        改进：确保CT和MRI图像完全对齐，并调整融合方式以更好地显示两者的关系
        """
        if (not hasattr(self.ct_handler, 'sitk_image') or self.ct_handler.sitk_image is None or
                not hasattr(self.registration_handler, 'registered_image') or
                self.registration_handler.registered_image is None):
            QMessageBox.warning(self, "警告", "请先加载CT和MRI数据并执行图像配准")
            return

        # 清除当前图表
        fig = self.visualization_figure
        fig.clear()

        # 获取SimpleITK图像
        ct = self.ct_handler.sitk_image
        mri_reg = self.registration_handler.registered_image

        # 将 SimpleITK 图像转换为 numpy 数组，数组 shape 为 (slices, rows, cols)
        ct_array = sitk.GetArrayFromImage(ct)
        mri_array = sitk.GetArrayFromImage(mri_reg)

        # 确保两个图像具有相同的尺寸
        if ct_array.shape != mri_array.shape:
            print(f"警告：CT图像尺寸 {ct_array.shape} 与配准后的MRI图像尺寸 {mri_array.shape} 不一致")
            # 如果尺寸不一致，将MRI图像重新采样到CT图像的尺寸
            if hasattr(self.registration_handler, 'final_transform') and self.registration_handler.final_transform is not None:
                print("重新应用变换以确保尺寸一致...")
                mri_reg = reg_resample_image(self.mri_handler.sitk_image, ct, self.registration_handler.final_transform)
                mri_array = sitk.GetArrayFromImage(mri_reg)

        # 选择中间切片进行显示
        slice_idx = min(ct_array.shape[0], mri_array.shape[0]) // 2

        ct_slice = ct_array[slice_idx] if slice_idx < ct_array.shape[0] else ct_array[-1]
        mri_slice = mri_array[slice_idx] if slice_idx < mri_array.shape[0] else mri_array[-1]

        # 为融合图像分配红色（CT）和绿色（MRI）通道，先归一化到 [0,1]
        ct_norm = (ct_slice - np.min(ct_slice)) / (np.max(ct_slice) - np.min(ct_slice) + 1e-8)
        mri_norm = (mri_slice - np.min(mri_slice)) / (np.max(mri_slice) - np.min(mri_slice) + 1e-8)

        # 创建 3 通道的融合图像，调整权重以更好地显示两者的关系
        fused_overlay = np.zeros((ct_slice.shape[0], ct_slice.shape[1], 3))
        fused_overlay[:, :, 0] = ct_norm * 0.7  # 红色通道显示 CT，降低权重
        fused_overlay[:, :, 1] = mri_norm * 0.7  # 绿色通道显示 MRI，降低权重
        # 添加亮度增强，使重叠区域更明显
        fused_overlay[:, :, 2] = np.minimum(ct_norm, mri_norm) * 0.3  # 蓝色通道用于增强重叠区域

        # 绘制三个子图
        axes = fig.subplots(1, 3)
        
        # 显示CT图像
        axes[0].imshow(ct_slice, cmap="gray")
        axes[0].set_title("CT")
        axes[0].axis('off')

        # 显示配准后的MRI图像
        axes[1].imshow(mri_slice, cmap="gray")
        axes[1].set_title("MRI (配准后)")
        axes[1].axis('off')

        # 显示融合图像
        axes[2].imshow(fused_overlay)
        axes[2].set_title("配准后融合图")
        axes[2].axis('off')

        fig.tight_layout()

    def export_registration_result(self):
        """导出配准结果"""
        if not hasattr(self.registration_handler,
                       'registered_image') or self.registration_handler.registered_image is None:
            QMessageBox.warning(self, "警告", "请先执行图像配准")
            return

        folder = self.export_reg_path_label.text()
        if folder == "未设置配准结果导出文件夹":
            folder = QFileDialog.getExistingDirectory(self, "选择配准结果导出文件夹")
            if not folder:
                return
            self.export_reg_path_label.setText(folder)

        self.statusBar.showMessage("正在导出配准结果...")

        # 创建输出目录
        os.makedirs(folder, exist_ok=True)

        # 保存配准图像
        sitk.WriteImage(self.registration_handler.registered_image, os.path.join(folder, "registered_image.mha"))

        # 导出校准参数
        with open(os.path.join(folder, "calibration_parameters.txt"), 'w') as f:
            f.write(f"MRI到HU转换参数:\n")
            f.write(
                f"HU = {self.mri_ct_calibrator.mri_to_hu_a:.6f} * MRI + {self.mri_ct_calibrator.mri_to_hu_b:.6f}\n\n")

        # 导出对比图像
        self.visualization_figure.clear()
        self.visualize_registration_comparison()
        self.visualization_figure.savefig(os.path.join(folder, "registration_comparison.png"), dpi=300)

        QMessageBox.information(self, "成功", f"配准结果已导出到: {folder}")
        self.statusBar.showMessage(f"配准结果已导出到: {folder}")



def main():
    """程序入口"""
    app = QApplication(sys.argv)
    app.setStyle('Fusion')  # 使用Fusion样式

    # 设置中文字体
    font = QFont("Microsoft YaHei", 9)
    app.setFont(font)

    window = MainWindow()
    window.show()

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()