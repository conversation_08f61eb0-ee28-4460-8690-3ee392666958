#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Medizinisches Bildregistrierungsprogramm

Dieses Programm wird für die räumliche Registrierung von CT/MRI-Medizinbildern verwendet und unterstützt folgende Funktionen:
1. <PERSON><PERSON> von DICOM-Daten von CT/MRI
2. Automatische Registrierung von CT- und MRI-Daten (räumliche Registrierung und Grauwertkalibrierung)
3. Umwan<PERSON><PERSON> von MRI-<PERSON> in HU-Werte
4. Visualisierung der Registrierungsergebnisse
"""

import os
import sys
import numpy as np
import pydicom
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.backends.backend_qt5agg import NavigationToolbar2QT as NavigationToolbar
from PyQt5.QtWidgets import (QApplication, QMainWindow, QPushButton, QFileDialog,
                             Q<PERSON>abel, QProgressBar, Q<PERSON>ab<PERSON>idget, QVBoxLayout, QHBoxLayout,
                             QWidget, QGroupBox, QFormLayout, QLineEdit, QComboBox,
                             QSpinBox, QMessageBox, QTableWidget, QTableWidgetItem,
                             QHeaderView, QSplitter, QGridLayout, QDoubleSpinBox,
                             QCheckBox, QRadioButton, QButtonGroup, QSlider)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QFont, QPixmap, QImage
import scipy.stats as stats
import SimpleITK as sitk
import tempfile
import shutil
from scipy import ndimage

# Überprüfen, ob elastix installiert ist
ELASTIX_INSTALLED = False
try:
    import SimpleITK.ElastixImageFilter as sitk_elastix

    ELASTIX_INSTALLED = True
except ImportError:
    ELASTIX_INSTALLED = False

plt.rcParams['font.sans-serif'] = ['SimHei']
# Schriftgröße einstellen
plt.rcParams['font.size'] = 14
# Chinesische Anzeige normal einstellen
plt.rcParams['axes.unicode_minus'] = False


# ========== Konsistente Registrierungsfunktion aus reg.py importiert ==========
def reg_load_ct_volume(ct_folder):
    """
    Laden der CT-Bildsequenz (mit pydicom gelesen), angenommen, die Dateireihenfolge ist korrekt.
    Da die Dateien eine zweidimensionale Bildsequenz sind, wird sie schließlich zu einem 3D-Bild zusammengefügt (Form: [slices, rows, cols]).
    """
    # Abrufen der vollständigen Pfade aller Dateien (angenommen, alle Dateien sind Bilder)
    files = [os.path.join(ct_folder, f) for f in os.listdir(ct_folder) if os.path.isfile(os.path.join(ct_folder, f))]
    files.sort()  # Nach Dateinamen sortieren, bei Bedarf kann nach InstanceNumber in DICOM sortiert werden

    slices = []
    for file in files:
        # Wenn die Datei keine DICOM-Erweiterung hat, trotzdem versuchen, mit pydicom zu lesen, mit force=True erzwingen
        ds = pydicom.dcmread(file, force=True)
        pixel_array = ds.pixel_array.astype(np.float32)
        slices.append(pixel_array)
    volume = np.stack(slices, axis=-1)  # Erhaltene Form: (rows, cols, num_slices)
    # SimpleITK's GetImageFromArray erfordert, dass das Array die Form (slices, rows, cols) hat
    volume = np.transpose(volume, (2, 0, 1))
    sitk_image = sitk.GetImageFromArray(volume)

    # Bei Bedarf können auch physikalische Bildinformationen (Abstand, Ursprung, Richtung) eingestellt werden,
    # z.B.: sitk_image.SetSpacing([1.0, 1.0, 1.0])
    return sitk_image


def reg_load_mri_volume(mri_folder):
    """
    Lesen der DICOM-Sequenz von MRI mit SimpleITK.
    """
    reader = sitk.ImageSeriesReader()
    dicom_names = reader.GetGDCMSeriesFileNames(mri_folder)
    reader.SetFileNames(dicom_names)
    image = reader.Execute()
    return image


def reg_register_images(fixed, moving, progress_callback=None):
    """
    Verwendung von SimpleITK zur affinen Registrierung des bewegten Bildes (MRI) und des festen Bildes (CT).
    Verwendung von Mutual Information als Ähnlichkeitsmaß und Gradientenabstieg zur Optimierung.
    Verbesserung: Verwendung einer mehrstufigen Registrierungsstrategie und leistungsstärkerer Optimierungsparameter zur Verbesserung des Ähnlichkeitsmaßes
    """
    # Zuerst in denselben Pixeltyp umwandeln
    fixed_float = sitk.Cast(fixed, sitk.sitkFloat32)
    moving_float = sitk.Cast(moving, sitk.sitkFloat32)

    if progress_callback:
        progress_callback(5)

    # Verbesserte Vorverarbeitungsschritte
    print("Ausführen der verbesserten Vorverarbeitung...")

    # 1. Anwendung der Intensitätsnormalisierung
    fixed_stats = sitk.StatisticsImageFilter()
    fixed_stats.Execute(fixed_float)
    fixed_float = sitk.Normalize(fixed_float)

    moving_stats = sitk.StatisticsImageFilter()
    moving_stats.Execute(moving_float)
    moving_float = sitk.Normalize(moving_float)

    # 2. Anwendung einer vereinfachten Intensitätsanpassung zur Verbesserung der Registrierung
    try:
        # Verwendung einer effizienteren Methode zur Intensitätsanpassung
        fixed_array = sitk.GetArrayFromImage(fixed_float)
        moving_array = sitk.GetArrayFromImage(moving_float)

        # Verwendung einer vereinfachten Intensitätsanpassungsmethode
        # Nur Hauptperzentile berechnen, um den Rechenaufwand zu reduzieren
        fixed_min = np.min(fixed_array)
        fixed_max = np.max(fixed_array)
        moving_min = np.min(moving_array)
        moving_max = np.max(moving_array)

        # Vereinfachte lineare Skalierung
        moving_array_adj = (moving_array - moving_min) * ((fixed_max - fixed_min) / (moving_max - moving_min)) + fixed_min
        moving_float = sitk.GetImageFromArray(moving_array_adj)
        moving_float.CopyInformation(moving)

        print("Vereinfachte Intensitätsanpassung angewendet")
    except Exception as e:
        print(f"Intensitätsanpassung fehlgeschlagen, Verwendung des ursprünglichen normalisierten Bildes: {str(e)}")

    # Überspringen des Kantenschärfeschritts, da dieser rechenintensiv ist und begrenzte Wirkung hat
    print("Überspringen des Kantenschärfeschritts zur Effizienzsteigerung")

    if progress_callback:
        progress_callback(10)

    # Erste Phase: Grobe starre Registrierung (optimiert für höhere Effizienz)
    print("Erste Phase: Grobe starre Registrierung")
    registration_method1 = sitk.ImageRegistrationMethod()

    # Verwendung des Mittleren-Quadrate-Maßes zur anfänglichen Ausrichtung
    registration_method1.SetMetricAsMeanSquares()

    # Optimierung der Stichprobenstrategie zur Effizienzsteigerung
    registration_method1.SetMetricSamplingStrategy(registration_method1.RANDOM)
    registration_method1.SetMetricSamplingPercentage(0.15)  # Verwendung von weniger Stichprobenpunkten

    # Verwendung eines effizienteren regulären Gradientenabstiegs
    registration_method1.SetOptimizerAsRegularStepGradientDescent(
        learningRate=2.0,
        minStep=1e-3,  # Erhöhung der minimalen Schrittweite zur Beschleunigung der Konvergenz
        numberOfIterations=100,  # Reduzierung der Iterationsanzahl
        relaxationFactor=0.7)  # Erhöhung des Entspannungsfaktors zur Beschleunigung der Konvergenz
    registration_method1.SetOptimizerScalesFromPhysicalShift()

    # Verwendung einer vereinfachten Mehrauflösungsstrategie
    registration_method1.SetShrinkFactorsPerLevel(shrinkFactors=[8])
    registration_method1.SetSmoothingSigmasPerLevel(smoothingSigmas=[4])
    registration_method1.SmoothingSigmasAreSpecifiedInPhysicalUnitsOn()

    # Versuch der Verwendung von Parallelverarbeitung
    try:
        import multiprocessing
        num_cores = multiprocessing.cpu_count()
        registration_method1.SetNumberOfThreads(num_cores)
    except:
        pass

    # Initialisierung der Transformation: Verwendung der zentrierten Ausrichtung
    initial_transform = sitk.CenteredTransformInitializer(
        fixed_float, moving_float, sitk.Euler3DTransform(),
        sitk.CenteredTransformInitializerFilter.GEOMETRY)
    registration_method1.SetInitialTransform(initial_transform, inPlace=False)

    # Ausführung der ersten Registrierungsphase
    rigid_transform = registration_method1.Execute(fixed_float, moving_float)

    print("Ähnlichkeitsmaß der ersten Phase: {:.4f}".format(registration_method1.GetMetricValue()))

    if progress_callback:
        progress_callback(40)

    # Zweite Phase: Feine affine Registrierung
    print("Zweite Phase: Feine affine Registrierung")
    registration_method2 = sitk.ImageRegistrationMethod()

    # Verwendung des Mutual-Information-Maßes, um Effekt und Effizienz auszugleichen
    print("Verwendung des optimierten Mutual-Information-Maßes zur Registrierung")
    registration_method2.SetMetricAsMattesMutualInformation(numberOfHistogramBins=150)  # Reduzierung der Bins zur Effizienzsteigerung
    registration_method2.MetricUseFixedImageGradientFilterOff()

    # Anpassung der Stichprobenstrategie zur Ausbalancierung von Effekt und Effizienz
    registration_method2.SetMetricSamplingStrategy(registration_method2.RANDOM)
    registration_method2.SetMetricSamplingPercentage(0.75)  # Reduzierung der Stichprobenrate zur Effizienzsteigerung

    # Versuch der Verwendung von Parallelverarbeitung zur Beschleunigung
    try:
        import multiprocessing
        num_cores = multiprocessing.cpu_count()
        registration_method2.SetNumberOfThreads(num_cores)  # Verwendung aller verfügbaren CPU-Kerne
        print(f"Parallelverarbeitung aktiviert, Verwendung von {num_cores} Kernen")
    except Exception as e:
        print(f"Parallelverarbeitung konnte nicht aktiviert werden: {str(e)}")

    # Verwendung eines effizienten Gradientenabstiegsoptimierers anstelle von Powell
    print("Verwendung eines effizienten Gradientenabstiegsoptimierers")
    registration_method2.SetOptimizerAsGradientDescent(
        learningRate=1.0,
        numberOfIterations=500,  # Deutliche Reduzierung der Iterationsanzahl
        convergenceMinimumValue=1e-7,
        convergenceWindowSize=15)
    registration_method2.SetOptimizerScalesFromPhysicalShift()

    # Vereinfachte Mehrauflösungsstrategie
    registration_method2.SetShrinkFactorsPerLevel(shrinkFactors=[2])
    registration_method2.SetSmoothingSigmasPerLevel(smoothingSigmas=[1])
    registration_method2.SmoothingSigmasAreSpecifiedInPhysicalUnitsOn()

    # Verwendung der affinen Transformation, mit der Transformation der ersten Phase als Initialwert
    # Erstellung einer neuen affinen Transformation
    affine_transform = sitk.AffineTransform(3)

    # Behandlung unterschiedlicher Transformationstypen
    try:
        # Wenn es sich um einen Standardtransformationstyp handelt, direkt Matrix und Translation abrufen
        affine_transform.SetMatrix(rigid_transform.GetMatrix())
        affine_transform.SetTranslation(rigid_transform.GetTranslation())
        affine_transform.SetCenter(rigid_transform.GetCenter())
    except AttributeError:
        # Wenn es sich um eine CompositeTransform oder einen anderen Typ handelt, eine allgemeinere Methode verwenden
        print("Die erste Phase hat eine zusammengesetzte Transformation zurückgegeben, Verwendung einer allgemeineren Initialisierungsmethode")
        # Transformation neu initialisieren
        affine_transform = sitk.CenteredTransformInitializer(
            fixed_float, moving_float, sitk.AffineTransform(3),
            sitk.CenteredTransformInitializerFilter.GEOMETRY)

    registration_method2.SetInitialTransform(affine_transform, inPlace=False)

    if progress_callback:
        progress_callback(60)

    # Ausführung der zweiten Registrierungsphase
    final_transform = registration_method2.Execute(fixed_float, moving_float)

    if progress_callback:
        progress_callback(80)

    print("Endgültiges Ähnlichkeitsmaß: {:.4f}".format(registration_method2.GetMetricValue()))
    print("Beendigungsbedingung des Optimierers: {}".format(registration_method2.GetOptimizerStopConditionDescription()))

    # Resampling
    resampled_image = reg_resample_image(moving_float, fixed_float, final_transform)

    if progress_callback:
        progress_callback(100)

    return final_transform, resampled_image


def reg_resample_image(moving, fixed, transform):
    """
    Resampling des bewegten Bildes (MRI) im Raum des festen Bildes (CT) gemäß der erhaltenen Transformation.
    Verbesserung: Verwendung einer qualitativ hochwertigeren Interpolationsmethode und erweiterter Einstellungen zur Verbesserung der Resampling-Qualität
    """
    # Verwendung erweiterter Resampling-Einstellungen
    resampler = sitk.ResampleImageFilter()
    resampler.SetReferenceImage(fixed)  # Verwendung des festen Bildes als Referenz

    # Auswahl der am besten geeigneten Interpolationsmethode
    # Für B-Spline-Transformationen Verwendung von BSpline-Interpolation
    # Für affine Transformationen Verwendung von Lanczos-Fenster-Interpolation
    if isinstance(transform, sitk.BSplineTransform):
        resampler.SetInterpolator(sitk.sitkBSpline)
    else:
        # Lanczos-Fenster-Interpolation ist für affine Transformationen besser geeignet
        resampler.SetInterpolator(sitk.sitkLanczosWindowedSinc)

    # Einstellung des Standardpixelwerts
    # Für medizinische Bilder Verwendung des Hintergrundwerts anstelle des absoluten Minimalwerts
    moving_stats = sitk.StatisticsImageFilter()
    moving_stats.Execute(moving)

    # Verwendung einer einfacheren Methode zur Berechnung des Hintergrundwerts
    # Einstellung des Hintergrundwerts auf den Minimalwert plus eine kleine Verschiebung, um die Verwendung eines Histogramms zu vermeiden
    background_value = moving_stats.GetMinimum() + (moving_stats.GetMaximum() - moving_stats.GetMinimum()) * 0.05

    # Abrufen des numpy-Arrays des bewegten Bildes zur detaillierteren Analyse
    moving_array = sitk.GetArrayFromImage(moving)
    # Verwendung von numpy-Perzentilen zur Schätzung des Hintergrundwerts
    try:
        # Versuch, Perzentilen zu verwenden, um einen genaueren Hintergrundwert zu erhalten
        background_percentile = np.percentile(moving_array.flatten(), 5)  # Verwendung des 5. Perzentils als Hintergrundschätzung
        background_value = background_percentile
    except:
        # Bei Fehler Verwendung der einfachen Methode
        pass

    resampler.SetDefaultPixelValue(background_value)

    # Einstellung der Transformation
    resampler.SetTransform(transform)

    # Ausführung des Resamplings
    out = resampler.Execute(moving)

    # Sicherstellen, dass das Ausgabebild dieselben physikalischen Eigenschaften wie das feste Bild hat
    out.SetOrigin(fixed.GetOrigin())
    out.SetDirection(fixed.GetDirection())
    out.SetSpacing(fixed.GetSpacing())

    # Anwendung einer leichten Glättung zur Reduzierung von Resampling-Artefakten
    try:
        # Versuch, einen diskreten Gauß-Filter zu verwenden
        out = sitk.DiscreteGaussian(out, variance=0.5)
    except:
        # Bei Nichtunterstützung Versuch, einen normalen Gauß-Filter zu verwenden
        try:
            out = sitk.SmoothingRecursiveGaussian(out, sigma=0.5)
        except:
            # Bei Nichtunterstützung Überspringen des Glättungsschritts
            print("Überspringen des Bildglättungsschritts")

    return out


class CTDataHandler:
    """CT-Datenverarbeitungsklasse"""

    def __init__(self):
        self.ct_data = None
        self.pixel_spacing = None
        self.slice_thickness = None
        self.origin = None
        self.dicom_files = []
        self.shape = None
        self.sitk_image = None  # SimpleITK-Bildobjekt
        self.direction = None  # Richtungsmatrix

    def load_dicom_folder(self, folder_path):
        """
        Laden von CT-Daten mit der Methode aus reg.py, um konsistente Registrierungsergebnisse zu gewährleisten
        Parameter:
            folder_path: Pfad zum DICOM-Ordner
        Rückgabe:
            True bei Erfolg, False bei Misserfolg
        """
        try:
            print("Lade CT-Daten mit reg.py-kompatibler Methode...")

            # Originalen Ordnerpfad speichern
            self.dicom_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path)
                                if os.path.isfile(os.path.join(folder_path, f))]

            # Daten mit der Methode aus reg.py laden
            self.sitk_image = reg_load_ct_volume(folder_path)

            # Metadaten extrahieren
            self.direction = self.sitk_image.GetDirection()
            spacing = self.sitk_image.GetSpacing()
            self.pixel_spacing = [spacing[0], spacing[1]]
            self.slice_thickness = spacing[2]
            self.origin = self.sitk_image.GetOrigin()

            # Array-Daten von SimpleITK abrufen
            self.ct_data = sitk.GetArrayFromImage(self.sitk_image)
            # Hinweis: SimpleITK gibt Arrays mit der Form (slices, rows, cols) zurück
            # Muss für die UI-Erwartung in (rows, cols, slices) transponiert werden
            self.ct_data = np.transpose(self.ct_data, (1, 2, 0))
            self.shape = self.ct_data.shape

            print(f"CT-Daten erfolgreich geladen: Größe {self.shape}")
            return True

        except Exception as e:
            import traceback
            traceback.print_exc()
            print(f"Fehler beim Laden der CT-Daten mit reg-Methode: {str(e)}")
            print("Versuche, mit ursprünglicher Methode zu laden...")

            # Bei Fehler auf ursprüngliche Methode zurückgreifen
            # Alle Dateien im Ordner abrufen
            files = [os.path.join(folder_path, f) for f in os.listdir(folder_path)
                     if os.path.isfile(os.path.join(folder_path, f))]

            if not files:
                print("Keine Dateien im Ordner")
                return False

            # Versuch, alle Dateien als DICOM zu lesen
            self.dicom_files = []
            for file_path in files:
                try:
                    # Versuch, DICOM-Datei zu laden
                    dicom = pydicom.dcmread(file_path)
                    # Überprüfen, ob es Pixeldaten enthält
                    if hasattr(dicom, 'pixel_array') and dicom.pixel_array.size > 0:
                        self.dicom_files.append(file_path)
                        print(f"Gültige CT-DICOM-Datei: {file_path}, Größe: {dicom.pixel_array.shape}, "
                              f"Minimalwert: {np.min(dicom.pixel_array)}, Maximalwert: {np.max(dicom.pixel_array)}")
                except Exception as e:
                    print(f"Ungültige Datei {file_path} ignoriert: {e}")
                    continue

            if not self.dicom_files:
                print("Keine gültigen DICOM-Dateien gefunden")
                return False

            # Lesen der ersten Datei, um grundlegende Informationen zu erhalten
            ref_dicom = pydicom.dcmread(self.dicom_files[0])

            # Abrufen von Pixelabstand und Schichtdicke
            self.pixel_spacing = ref_dicom.PixelSpacing
            self.slice_thickness = ref_dicom.SliceThickness
            self.origin = ref_dicom.ImagePositionPatient

            # Lesen aller Dateien und Sortieren
            slices = [pydicom.dcmread(f) for f in self.dicom_files]
            slices.sort(key=lambda x: float(x.ImagePositionPatient[2]))

            # Umwandlung in ein 3D-numpy-Array
            img_shape = list(slices[0].pixel_array.shape)
            img_shape.append(len(slices))
            self.ct_data = np.zeros(img_shape)
            self.shape = img_shape

            # Füllen des 3D-Arrays und Umwandlung in HU-Werte
            for i, s in enumerate(slices):
                pixel_array = s.pixel_array.astype(np.int16)

                # Umwandlung in HU-Werte (falls erforderlich)
                if hasattr(s, 'RescaleIntercept') and hasattr(s, 'RescaleSlope'):
                    intercept = s.RescaleIntercept
                    slope = s.RescaleSlope
                    pixel_array = pixel_array * slope + intercept

                self.ct_data[:, :, i] = pixel_array

            # Erstellung eines SimpleITK-Bildobjekts zur Registrierung
            try:
                self.sitk_image = sitk.ReadImage(self.dicom_files)
                self.direction = self.sitk_image.GetDirection()
            except Exception as e:
                print(f"Erstellung des SimpleITK-Bildes fehlgeschlagen: {e}")
                # Bei Fehler beim Lesen der DICOM-Sequenz Versuch, aus dem numpy-Array zu erstellen
                self.sitk_image = sitk.GetImageFromArray(self.ct_data)
                self.sitk_image.SetSpacing((self.pixel_spacing[0], self.pixel_spacing[1], self.slice_thickness))
                self.sitk_image.SetOrigin(self.origin)
                # Einstellung der Standardrichtung auf die Einheitsmatrix
                self.sitk_image.SetDirection((1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0))

            print(f"CT-Daten erfolgreich geladen: Größe {self.ct_data.shape}")
            return True

    def get_hu_value(self, x, y, z):
        """
        Abrufen des HU-Werts an den angegebenen Weltkoordinaten
        Parameter:
            x, y, z: Weltkoordinaten (mm)
        Rückgabe:
            HU-Wert an der entsprechenden Position, wenn außerhalb des Bereichs, dann None
        """
        if self.ct_data is None:
            return None

        # Umwandlung der Weltkoordinaten in Pixelkoordinaten
        px = int((x - self.origin[0]) / self.pixel_spacing[0])
        py = int((y - self.origin[1]) / self.pixel_spacing[1])
        pz = int((z - self.origin[2]) / self.slice_thickness)

        # Überprüfen, ob die Koordinaten im Bereich liegen
        if (0 <= px < self.ct_data.shape[0] and
                0 <= py < self.ct_data.shape[1] and
                0 <= pz < self.ct_data.shape[2]):
            return self.ct_data[px, py, pz]
        else:
            return None


class MRIDataHandler:
    """MRI-Datenverarbeitungsklasse"""

    def __init__(self):
        self.mri_data = None
        self.pixel_spacing = None
        self.slice_thickness = None
        self.origin = None
        self.dicom_files = []
        self.shape = None
        self.sitk_image = None  # SimpleITK-Bildobjekt
        self.direction = None  # Richtungsmatrix
        self.transformed_image = None  # Transformiertes Bild

    def load_dicom_folder(self, folder_path):
        """
        Laden von MRI-Daten mit der Methode aus reg.py, um konsistente Registrierungsergebnisse sicherzustellen
        Parameter:
            folder_path: DICOM-Ordnerpfad
        Rückgabe:
            Erfolgreich gibt True zurück, bei Fehler False
        """
        try:
            print("Laden von MRI-Daten mit reg.py-kompatibler Methode...")

            # Speichern des ursprünglichen Ordnerpfads
            self.dicom_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path)
                                if os.path.isfile(os.path.join(folder_path, f))]

            # Laden der Daten mit der Methode aus reg.py
            self.sitk_image = reg_load_mri_volume(folder_path)

            # Extrahieren von Metadaten
            self.direction = self.sitk_image.GetDirection()
            spacing = self.sitk_image.GetSpacing()
            self.pixel_spacing = [spacing[0], spacing[1]]
            self.slice_thickness = spacing[2]
            self.origin = self.sitk_image.GetOrigin()

            # Abrufen von Array-Daten aus SimpleITK
            self.mri_data = sitk.GetArrayFromImage(self.sitk_image)
            # Hinweis: Die von SimpleITK zurückgegebene Array-Form ist (slices, rows, cols)
            # Muss in die von der UI erwartete Form (rows, cols, slices) transponiert werden
            self.mri_data = np.transpose(self.mri_data, (1, 2, 0))
            self.shape = self.mri_data.shape

            print(f"MRI-Daten erfolgreich geladen: Größe {self.shape}")
            return True

        except Exception as e:
            import traceback
            traceback.print_exc()
            print(f"Laden von MRI-Daten mit reg-Methode fehlgeschlagen: {str(e)}")
            print("Versuch, mit der ursprünglichen Methode zu laden...")

            # Bei Fehler Rückfall auf die ursprüngliche Methode
            # Abrufen aller Dateien im Ordner
            files = [os.path.join(folder_path, f) for f in os.listdir(folder_path)
                     if os.path.isfile(os.path.join(folder_path, f))]

            if not files:
                print("Keine Dateien im Ordner")
                return False

            # Nicht nur .dcm-Dateien filtern, versuchen, alle Dateien als DICOM zu laden
            self.dicom_files = []
            for file_path in files:
                try:
                    # Versuch, DICOM-Datei zu laden und ihre Gültigkeit zu überprüfen
                    dicom = pydicom.dcmread(file_path)
                    # Überprüfen, ob es Pixeldaten enthält
                    if hasattr(dicom, 'pixel_array') and dicom.pixel_array.size > 0:
                        self.dicom_files.append(file_path)
                        print(f"Gültige MRI-DICOM-Datei: {file_path}, Größe: {dicom.pixel_array.shape}, "
                              f"Minimalwert: {np.min(dicom.pixel_array)}, Maximalwert: {np.max(dicom.pixel_array)}")
                except Exception as e:
                    print(f"Ungültige Datei {file_path} ignorieren: {e}")
                    continue

            if not self.dicom_files:
                print("Keine gültigen DICOM-Dateien gefunden")
                return False

            # Lesen der ersten Datei, um grundlegende Informationen zu erhalten
            ref_dicom = pydicom.dcmread(self.dicom_files[0])

            # Abrufen von Pixelabstand und Schichtdicke
            self.pixel_spacing = ref_dicom.PixelSpacing
            self.slice_thickness = ref_dicom.SliceThickness
            self.origin = ref_dicom.ImagePositionPatient

            # Lesen aller Dateien und Sortieren
            slices = [pydicom.dcmread(f) for f in self.dicom_files]
            slices.sort(key=lambda x: float(x.ImagePositionPatient[2]))

            # Umwandlung in 3D-Numpy-Array
            img_shape = list(slices[0].pixel_array.shape)
            img_shape.append(len(slices))
            self.mri_data = np.zeros(img_shape)
            self.shape = img_shape

            # Füllen des 3D-Arrays
            for i, s in enumerate(slices):
                pixel_array = s.pixel_array.astype(np.int16)

                # Überprüfen des Datenbereichs, um sicherzustellen, dass ein gültiger Kontrast vorhanden ist
                if np.max(pixel_array) == np.min(pixel_array):
                    print(f"Warnung: Schicht {i} ohne Kontrast, zufälliges Rauschen hinzufügen")
                    pixel_array = pixel_array + np.random.normal(0, 1, pixel_array.shape).astype(np.int16)

                self.mri_data[:, :, i] = pixel_array

            # Validierung der MRI-Daten
            if np.max(self.mri_data) == np.min(self.mri_data):
                print("Warnung: Gesamte MRI-Daten ohne Kontrast, Histogramm-Stretching anwenden")
                # Zufälliges Rauschen hinzufügen, um Kontrast zu erzeugen
                self.mri_data = self.mri_data + np.random.normal(0, 10, self.mri_data.shape).astype(np.int16)

            # Erstellen eines SimpleITK-Bildobjekts zur Registrierung
            try:
                self.sitk_image = sitk.ReadImage(self.dicom_files)
                self.direction = self.sitk_image.GetDirection()

                # Validierung des SimpleITK-Bildes
                stats = sitk.StatisticsImageFilter()
                stats.Execute(self.sitk_image)
                print(f"MRI-SITK-Bild: Minimalwert: {stats.GetMinimum()}, Maximalwert: {stats.GetMaximum()}")

                if stats.GetMinimum() == stats.GetMaximum():
                    print("Warnung: MRI-SITK-Bild ohne Kontrast, neu aus NumPy-Array erstellen")
                    self.sitk_image = sitk.GetImageFromArray(self.mri_data)
                    self.sitk_image.SetSpacing((self.pixel_spacing[0], self.pixel_spacing[1], self.slice_thickness))
                    self.sitk_image.SetOrigin(self.origin)
                    self.sitk_image.SetDirection((1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0))
            except Exception as e:
                print(f"Erstellen des SimpleITK-Bildes fehlgeschlagen: {e}")
                # Bei Fehler beim Lesen der DICOM-Sequenz Versuch, aus dem numpy-Array zu erstellen
                self.sitk_image = sitk.GetImageFromArray(self.mri_data)
                self.sitk_image.SetSpacing((self.pixel_spacing[0], self.pixel_spacing[1], self.slice_thickness))
                self.sitk_image.SetOrigin(self.origin)
                # Einstellung der Standardrichtung auf die Einheitsmatrix
                self.sitk_image.SetDirection((1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0))

            print(f"MRI-Daten erfolgreich geladen: Größe {self.mri_data.shape}")
            print(f"MRI-Datenbereich: {np.min(self.mri_data)} - {np.max(self.mri_data)}")
            return True

    def get_mri_value(self, x, y, z):
        """
        Abrufen des MRI-Werts an den angegebenen Weltkoordinaten
        Parameter:
            x, y, z: Weltkoordinaten (mm)
        Rückgabe:
            MRI-Wert an der entsprechenden Position, wenn außerhalb des Bereichs, dann None
        """
        if self.mri_data is None:
            return None

        # Umwandlung der Weltkoordinaten in Pixelkoordinaten
        px = int((x - self.origin[0]) / self.pixel_spacing[0])
        py = int((y - self.origin[1]) / self.pixel_spacing[1])
        pz = int((z - self.origin[2]) / self.slice_thickness)

        # Überprüfen, ob die Koordinaten im Bereich liegen
        if (0 <= px < self.mri_data.shape[0] and
                0 <= py < self.mri_data.shape[1] and
                0 <= pz < self.mri_data.shape[2]):
            return self.mri_data[px, py, pz]
        else:
            return None

    def update_from_sitk(self, sitk_image):
        """Aktualisieren der MRI-Daten aus einem SimpleITK-Bild"""
        # Speichern des transformierten Bildes
        self.transformed_image = sitk_image

        # Aktualisieren des numpy-Arrays aus dem SimpleITK-Bild
        new_data = sitk.GetArrayFromImage(sitk_image)

        # Die Dimensionen von SimpleITK und numpy sind unterschiedlich, daher muss transponiert werden
        new_data = np.transpose(new_data, (1, 0, 2))

        # Aktualisieren der MRI-Daten
        self.mri_data = new_data
        self.shape = new_data.shape

        # Aktualisieren anderer Attribute
        self.origin = sitk_image.GetOrigin()
        spacing = sitk_image.GetSpacing()
        self.pixel_spacing = [spacing[0], spacing[1]]
        self.slice_thickness = spacing[2]

        # Validierung der aktualisierten Daten
        print(f"Aktualisierte MRI-Daten: Größe {self.shape}, Bereich {np.min(self.mri_data)} - {np.max(self.mri_data)}")

        # Überprüfen, ob die Daten ein Gradientenbild sind
        def is_gradient_image(img, threshold=0.9):
            """Überprüfen, ob das Bild hauptsächlich ein Gradient ist"""
            if len(img.shape) < 2:
                return False

            h, w = img.shape[:2]
            # Berechnen der Unterschiede in Zeilen- und Spaltenrichtung
            row_diff = np.mean(np.abs(np.diff(img, axis=0)))
            col_diff = np.mean(np.abs(np.diff(img, axis=1)))
            total_var = np.var(img)
            # Wenn die Hauptveränderung in eine Richtung erfolgt und die Gesamtvarianz groß ist, könnte es ein Gradientenbild sein
            return (row_diff / (col_diff + 1e-10) > 5 or col_diff / (row_diff + 1e-10) > 5) and total_var > 100

        # Überprüfen der mittleren Schicht
        mid_slice = self.mri_data[:, :, self.mri_data.shape[2] // 2]
        if is_gradient_image(mid_slice):
            print("Warnung: Die aktualisierten MRI-Daten könnten ein Gradientenbild sein, das Registrierungsergebnis könnte nicht korrekt sein")

        return True


class RegistrationHandler:
    """Bildregistrierungsverarbeitungsklasse"""

    def __init__(self, ct_handler=None, mri_handler=None):
        self.fixed_image = None
        self.moving_image = None
        self.transform_parameters_map = None
        self.registered_image = None
        self.registration_type = "rigid"  # Optionen: rigid, affine, bspline
        self.temp_dir = tempfile.mkdtemp()
        self.ct_handler = ct_handler  # Hinzufügen eines Verweises auf den CT-Handler
        self.mri_handler = mri_handler  # Hinzufügen eines Verweises auf den MRI-Handler
        self.final_transform = None  # Speichern der endgültigen Transformation

    def __del__(self):
        """Destruktor, Bereinigung des temporären Ordners"""
        try:
            shutil.rmtree(self.temp_dir)
        except:
            pass

    def set_fixed_image(self, image):
        """Festlegen des festen Bildes (normalerweise das CT-Bild)"""
        self.fixed_image = image

    def set_moving_image(self, image):
        """Festlegen des beweglichen Bildes (normalerweise das MRI-Bild)"""
        self.moving_image = image

    def set_registration_type(self, reg_type):
        """Festlegen des Registrierungstyps"""
        if reg_type in ["rigid", "affine", "bspline"]:
            self.registration_type = reg_type

    def register_images(self, progress_callback=None):
        """
        Ausführen der Bildregistrierung mit der konsistenten Methode aus reg.py
        """
        if self.fixed_image is None or self.moving_image is None:
            print("Registrierung fehlgeschlagen: Festes Bild oder bewegliches Bild nicht festgelegt")
            return None

        try:
            print("Ausführen der Registrierung mit reg.py-kompatibler Methode...")

            # Aufrufen der reg.py-kompatiblen Registrierungsmethode
            self.final_transform, self.registered_image = reg_register_images(
                self.fixed_image,
                self.moving_image,
                progress_callback
            )

            # Überprüfen des Registrierungsergebnisses
            reg_stats = sitk.StatisticsImageFilter()
            reg_stats.Execute(self.registered_image)
            print(f"Registrierungsergebnis: Minimalwert={reg_stats.GetMinimum()}, Maximalwert={reg_stats.GetMaximum()}")

            return self.registered_image

        except Exception as e:
            import traceback
            traceback.print_exc()
            print(f"Registrierung mit reg-Methode fehlgeschlagen: {str(e)}")

            return None

    def resample_image(self, moving, fixed, transform):
        """
        Verwenden der konsistenten Bild-Resampling-Methode aus reg.py
        """
        return reg_resample_image(moving, fixed, transform)

    def save_registration_result(self, output_dir):
        """Speichern des Registrierungsergebnisses und der Transformationsparameter"""
        if self.registered_image is None:
            print("Kein Registrierungsergebnis zum Speichern")
            return False

        # Erstellen des Ausgabeverzeichnisses
        os.makedirs(output_dir, exist_ok=True)

        # Speichern des registrierten Bildes
        sitk.WriteImage(self.registered_image, os.path.join(output_dir, "registered_image.mha"))

        # Speichern der Transformationsparameter
        if self.transform_parameters_map is not None and ELASTIX_INSTALLED:
            sitk.WriteParameterFile(self.transform_parameters_map[0],
                                    os.path.join(output_dir, "transform_parameters.txt"))

        return True


class MRICTCalibrator:
    """Kalibrator von MRI zu CT"""

    def __init__(self):
        self.reference_points = []  # [x, y, z, mri-Wert, hu-Wert]
        self.mri_to_hu_a = 1.0  # Steigung
        self.mri_to_hu_b = 0.0  # Achsenabschnitt

    def add_reference_point(self, x, y, z, mri_value, hu_value):
        """Hinzufügen eines Referenzpunkts"""
        self.reference_points.append([x, y, z, mri_value, hu_value])
        return len(self.reference_points)

    def clear_reference_points(self):
        """Löschen aller Referenzpunkte"""
        self.reference_points = []
        return True

    def perform_calibration(self):
        """Verbesserte Kalibrierung von MRI zu HU"""
        if len(self.reference_points) < 2:
            print("Mindestens zwei Referenzpunkte sind erforderlich, um die Kalibrierung durchzuführen")
            return False

        # Extrahieren von MRI-Werten und HU-Werten
        mri_values = np.array([point[3] for point in self.reference_points])
        hu_values = np.array([point[4] for point in self.reference_points])

        # Ausreißererkennung und -behandlung
        if len(self.reference_points) >= 5:
            print("Ausreißererkennung durchführen...")

            # Ausreißererkennung mit der Interquartilsabstandsmethode
            mri_q1, mri_q3 = np.percentile(mri_values, [25, 75])
            hu_q1, hu_q3 = np.percentile(hu_values, [25, 75])

            mri_iqr = mri_q3 - mri_q1
            hu_iqr = hu_q3 - hu_q1

            # Definition der Ausreißergrenzen
            mri_lower = mri_q1 - 1.5 * mri_iqr
            mri_upper = mri_q3 + 1.5 * mri_iqr
            hu_lower = hu_q1 - 1.5 * hu_iqr
            hu_upper = hu_q3 + 1.5 * hu_iqr

            # Filtern von Ausreißern
            good_indices = []
            for i in range(len(mri_values)):
                if (mri_lower <= mri_values[i] <= mri_upper and
                        hu_lower <= hu_values[i] <= hu_upper):
                    good_indices.append(i)

            if len(good_indices) < 2:
                print("Warnung: Zu viele Ausreißer erkannt. Alle Punkte verwenden.")
            else:
                print(f"{len(mri_values) - len(good_indices)} Ausreißer gefiltert")
                mri_values = mri_values[good_indices]
                hu_values = hu_values[good_indices]

        # Lineare Regression
        slope, intercept, r_value, p_value, std_err = stats.linregress(mri_values, hu_values)
        r_squared = r_value ** 2

        print(f"Lineare Anpassung: HU = {slope:.4f} × MRI + {intercept:.2f} (R² = {r_squared:.4f})")

        # Versuch einer quadratischen Polynom-Anpassung
        try:
            poly2_coeffs = np.polyfit(mri_values, hu_values, 2)
            p2 = np.poly1d(poly2_coeffs)

            # Berechnen der Vorhersagewerte mit dem Polynom
            predicted2 = p2(mri_values)

            # Berechnen des R²-Werts
            ss_total = np.sum((hu_values - np.mean(hu_values)) ** 2)
            ss_residual2 = np.sum((hu_values - predicted2) ** 2)
            r_squared2 = 1 - (ss_residual2 / ss_total)

            print(
                f"Quadratische Polynom-Anpassung: HU = {poly2_coeffs[0]:.6f} × MRI² + {poly2_coeffs[1]:.4f} × MRI + {poly2_coeffs[2]:.2f} (R² = {r_squared2:.4f})")

            # Wenn die quadratische Anpassung deutlich besser ist als die lineare, dann die quadratische verwenden
            if r_squared2 > r_squared + 0.05:  # R² verbessert sich um mindestens 0.05
                print("Verwendung der quadratischen Polynom-Anpassung (bessere Leistung)")
                self.mri_to_hu_model = "polynomial2"
                self.mri_to_hu_coeffs = poly2_coeffs
                self.mri_to_hu_a = slope  # Lineare Parameter zur einfachen Anzeige beibehalten
                self.mri_to_hu_b = intercept
                return True
        except Exception as e:
            print(f"Quadratische Polynom-Anpassung fehlgeschlagen: {e}")
            print("Verwendung der linearen Anpassung")

        # Standardmäßig lineare Anpassung verwenden
        self.mri_to_hu_model = "linear"
        self.mri_to_hu_a = slope
        self.mri_to_hu_b = intercept

        print(f"Kalibrierungsergebnis: HU = {slope:.4f} × MRI + {intercept:.2f} (R² = {r_squared:.4f})")
        return True

    def mri_to_hu(self, mri_value):
        """Umwandlung von MRI-Wert in HU-Wert, unterstützt mehrere Modelle"""
        if hasattr(self, 'mri_to_hu_model') and self.mri_to_hu_model == "polynomial2":
            # Umwandlung mit quadratischem Polynom
            a, b, c = self.mri_to_hu_coeffs
            return a * mri_value ** 2 + b * mri_value + c
        else:
            # Standardmäßig lineare Umwandlung
            return self.mri_to_hu_a * mri_value + self.mri_to_hu_b


class RegistrationThread(QThread):
    """Registrierungs-Thread-Klasse"""
    progress_signal = pyqtSignal(int)  # Fortschrittssignal
    result_signal = pyqtSignal(object)  # Ergebnissignal
    error_signal = pyqtSignal(str)  # Fehlersignal

    def __init__(self, registration_handler):
        super().__init__()
        self.registration_handler = registration_handler

    def run(self):
        try:
            # Ausführen der Registrierung
            result = self.registration_handler.register_images(self.update_progress)

            if result is not None:
                self.result_signal.emit(result)
            else:
                self.error_signal.emit("Registrierung fehlgeschlagen")
        except Exception as e:
            import traceback
            traceback.print_exc()
            self.error_signal.emit(f"Registrierungs-Thread-Fehler: {str(e)}")

    def update_progress(self, value):
        """Aktualisieren des Fortschrittssignals"""
        self.progress_signal.emit(value)


class MainWindow(QMainWindow):
    """Hauptfensterklasse"""

    def __init__(self):
        super().__init__()

        # Initialisieren der Datenverarbeitungsklassen
        self.ct_handler = CTDataHandler()
        self.mri_handler = MRIDataHandler()
        self.registration_handler = RegistrationHandler(self.ct_handler, self.mri_handler)
        self.mri_ct_calibrator = MRICTCalibrator()

        # Datenspeicherung
        self.registration_thread = None  # Registrierungs-Thread

        # Initialisieren der Benutzeroberfläche
        self.init_ui()

        # # Hinzufügen eines reg.py-Kompatibilitätshinweises
        # compat_label = QLabel("reg.py-Kompatibilitätsmodus aktiviert, um konsistente Registrierungsergebnisse sicherzustellen")
        # compat_label.setStyleSheet("color: green; font-weight: bold;")
        # self.statusBar.addPermanentWidget(compat_label)

    def init_ui(self):
        """Initialisieren der Benutzeroberfläche"""
        self.setWindowTitle('Medizinisches Bildregistrierungstool')
        self.setGeometry(100, 100, 1200, 800)  # Fenstergröße erhöhen

        # Erstellen von Tabs
        self.tabs = QTabWidget()
        self.tab_data = QWidget()
        self.tab_registration = QWidget()  # Hinzufügen des Registrierungstabs
        self.tab_calibration = QWidget()
        self.tab_visualization = QWidget()
        self.tab_export = QWidget()

        self.tabs.addTab(self.tab_data, "Daten laden")
        self.tabs.addTab(self.tab_registration, "Bildregistrierung")
        self.tabs.addTab(self.tab_calibration, "MRI-Kalibrierung")
        self.tabs.addTab(self.tab_visualization, "Visualisierung")
        self.tabs.addTab(self.tab_export, "Export")

        # Einrichten der einzelnen Tabs
        self.setup_data_tab()
        self.setup_registration_tab()
        self.setup_calibration_tab()
        self.setup_visualization_tab()
        self.setup_export_tab()

        # Einrichten des Hauptlayouts
        main_layout = QVBoxLayout()
        main_layout.addWidget(self.tabs)

        # Statusleiste
        self.statusBar = self.statusBar()
        self.statusBar.showMessage('Bereit')

        # Einrichten des zentralen Fenster-Widgets
        central_widget = QWidget()
        central_widget.setLayout(main_layout)
        self.setCentralWidget(central_widget)

    def setup_data_tab(self):
        """Einrichten des Datenladetabs"""
        layout = QVBoxLayout()

        # CT-Datenladegruppe
        ct_group = QGroupBox("CT-Daten")
        ct_layout = QVBoxLayout()

        ct_file_layout = QHBoxLayout()
        self.ct_path_label = QLabel("Kein CT-Ordner ausgewählt")
        self.ct_browse_button = QPushButton("Durchsuchen...")
        self.ct_browse_button.clicked.connect(self.browse_ct_folder)
        ct_file_layout.addWidget(self.ct_path_label)
        ct_file_layout.addWidget(self.ct_browse_button)

        self.ct_load_button = QPushButton("CT-Daten laden")
        self.ct_load_button.clicked.connect(self.load_ct_data)

        ct_layout.addLayout(ct_file_layout)
        ct_layout.addWidget(self.ct_load_button)
        ct_group.setLayout(ct_layout)

        # MRI-Datenladegruppe
        mri_group = QGroupBox("MRI-Daten")
        mri_layout = QVBoxLayout()

        mri_file_layout = QHBoxLayout()
        self.mri_path_label = QLabel("Kein MRI-Ordner ausgewählt")
        self.mri_browse_button = QPushButton("Durchsuchen...")
        self.mri_browse_button.clicked.connect(self.browse_mri_folder)
        mri_file_layout.addWidget(self.mri_path_label)
        mri_file_layout.addWidget(self.mri_browse_button)

        self.mri_load_button = QPushButton("MRI-Daten laden")
        self.mri_load_button.clicked.connect(self.load_mri_data)

        # Hinzufügen eines Test-MRI-Daten-Buttons
        self.create_test_mri_button = QPushButton("Test-MRI-Daten erstellen")
        self.create_test_mri_button.clicked.connect(self.create_test_mri_data)

        mri_layout.addLayout(mri_file_layout)
        mri_layout.addWidget(self.mri_load_button)
        mri_layout.addWidget(self.create_test_mri_button)
        mri_group.setLayout(mri_layout)

        # Dateninformationsanzeigebereich
        info_group = QGroupBox("Dateninformationen")
        info_layout = QVBoxLayout()
        self.data_info_label = QLabel("Keine Daten geladen")
        info_layout.addWidget(self.data_info_label)
        info_group.setLayout(info_layout)

        # Hinzufügen aller Gruppen zum Layout
        layout.addWidget(ct_group)
        layout.addWidget(mri_group)
        layout.addWidget(info_group)

        layout.addStretch()

        self.tab_data.setLayout(layout)

    def setup_registration_tab(self):
        """Einrichten des Registrierungstabs"""
        layout = QVBoxLayout()

        # Registrierungsparametergruppe
        params_group = QGroupBox("Registrierungsparameter")
        params_layout = QVBoxLayout()

        # Auswahl des Registrierungstyps
        reg_type_layout = QHBoxLayout()
        reg_type_layout.addWidget(QLabel("Registrierungstyp:"))
        self.reg_type_combo = QComboBox()
        self.reg_type_combo.addItems(["Starre Registrierung", "Affine Transformation", "Nichtstarre B-Spline-Registrierung"])
        reg_type_layout.addWidget(self.reg_type_combo)
        params_layout.addLayout(reg_type_layout)

        # Auswahl der Registrierungsrichtung
        direction_layout = QHBoxLayout()
        direction_layout.addWidget(QLabel("Registrierungsrichtung:"))
        self.direction_combo = QComboBox()
        self.direction_combo.addItems(["CT zu MRI", "MRI zu CT"])
        direction_layout.addWidget(self.direction_combo)
        params_layout.addLayout(direction_layout)

        # Registrierungsoptionen
        options_layout = QFormLayout()

        # Hinzufügen einer Checkbox für erweiterte Optionen
        self.use_advanced_options = QCheckBox("Erweiterte Optionen verwenden")
        self.use_advanced_options.setChecked(False)
        options_layout.addRow(self.use_advanced_options)

        # Erweiterte Optionen
        self.advanced_options_widget = QWidget()
        advanced_layout = QFormLayout(self.advanced_options_widget)

        # Inhalt der erweiterten Optionen
        self.max_iterations_input = QSpinBox()
        self.max_iterations_input.setRange(10, 5000)
        self.max_iterations_input.setValue(200)

        self.sample_percent_input = QDoubleSpinBox()
        self.sample_percent_input.setRange(0.01, 1.0)
        self.sample_percent_input.setSingleStep(0.01)
        self.sample_percent_input.setValue(0.1)

        self.histogram_bins_input = QSpinBox()
        self.histogram_bins_input.setRange(10, 200)
        self.histogram_bins_input.setValue(50)

        advanced_layout.addRow("Maximale Iterationsanzahl:", self.max_iterations_input)
        advanced_layout.addRow("Prozentsatz der Stichprobe:", self.sample_percent_input)
        advanced_layout.addRow("Anzahl der Histogrammbins:", self.histogram_bins_input)

        self.advanced_options_widget.setVisible(False)
        self.use_advanced_options.toggled.connect(self.advanced_options_widget.setVisible)

        params_layout.addLayout(options_layout)
        params_layout.addWidget(self.advanced_options_widget)

        params_group.setLayout(params_layout)

        # Registrierungsoperationsgruppe
        operation_group = QGroupBox("Registrierungsoperationen")
        operation_layout = QVBoxLayout()

        # Registrierungsbutton
        self.run_registration_button = QPushButton("Bildregistrierung ausführen")
        self.run_registration_button.clicked.connect(self.run_registration)

        # Button zum Speichern des Registrierungsergebnisses
        self.save_reg_result_button = QPushButton("Registrierungsergebnis speichern")
        self.save_reg_result_button.clicked.connect(self.save_registration_result)
        self.save_reg_result_button.setEnabled(False)

        # Registrierungsfortschrittsbalken
        self.registration_progress = QProgressBar()
        self.registration_progress.setRange(0, 100)
        self.registration_progress.setValue(0)

        operation_layout.addWidget(self.run_registration_button)
        operation_layout.addWidget(self.save_reg_result_button)
        operation_layout.addWidget(self.registration_progress)

        # Registrierungsstatus
        self.registration_status_label = QLabel("Registrierungsstatus: Nicht gestartet")
        operation_layout.addWidget(self.registration_status_label)

        operation_group.setLayout(operation_layout)

        # Vorschau des Registrierungsergebnisses
        preview_group = QGroupBox("Vorschau des Registrierungsergebnisses")
        preview_layout = QGridLayout()

        # Hinzufügen von drei Bildvorschau-Bereichen
        self.ct_preview_label = QLabel("CT-Bild")
        self.ct_preview_label.setAlignment(Qt.AlignCenter)
        self.ct_preview_label.setFixedSize(300, 300)
        self.ct_preview_label.setStyleSheet("border: 1px solid black")

        self.mri_preview_label = QLabel("MRI-Bild")
        self.mri_preview_label.setAlignment(Qt.AlignCenter)
        self.mri_preview_label.setFixedSize(300, 300)
        self.mri_preview_label.setStyleSheet("border: 1px solid black")

        self.registered_preview_label = QLabel("Registriertes Bild")
        self.registered_preview_label.setAlignment(Qt.AlignCenter)
        self.registered_preview_label.setFixedSize(300, 300)
        self.registered_preview_label.setStyleSheet("border: 1px solid black")

        # Hinzufügen eines Schiebereglers für die Schichtposition
        self.slice_slider = QSlider(Qt.Horizontal)
        self.slice_slider.setRange(0, 100)
        self.slice_slider.setValue(50)
        self.slice_slider.valueChanged.connect(self.update_preview_slices)

        preview_layout.addWidget(self.ct_preview_label, 0, 0)
        preview_layout.addWidget(self.mri_preview_label, 0, 1)
        preview_layout.addWidget(self.registered_preview_label, 0, 2)
        preview_layout.addWidget(QLabel("Schichtposition:"), 1, 0)
        preview_layout.addWidget(self.slice_slider, 1, 1, 1, 2)

        preview_group.setLayout(preview_layout)

        # Hinzufügen aller Gruppen zum Hauptlayout
        layout.addWidget(params_group)
        layout.addWidget(operation_group)
        layout.addWidget(preview_group)

        self.tab_registration.setLayout(layout)

    def setup_calibration_tab(self):
        """Einrichten des MRI-Kalibrierungstabs"""
        layout = QVBoxLayout()

        # Kalibrierungspunktgruppe
        cal_group = QGroupBox("Kalibrierungsreferenzpunkte")
        cal_layout = QVBoxLayout()

        cal_info = QLabel("Hinzufügen von Referenzpunkten mit bekannten MRI- und HU-Werten zur Kalibrierung")
        cal_layout.addWidget(cal_info)

        point_input_layout = QHBoxLayout()
        self.point_x_input = QDoubleSpinBox()
        self.point_x_input.setRange(-1000, 1000)
        self.point_x_input.setDecimals(2)

        self.point_y_input = QDoubleSpinBox()
        self.point_y_input.setRange(-1000, 1000)
        self.point_y_input.setDecimals(2)

        self.point_z_input = QDoubleSpinBox()
        self.point_z_input.setRange(-1000, 1000)
        self.point_z_input.setDecimals(2)

        self.point_mri_input = QSpinBox()
        self.point_mri_input.setRange(-1000, 5000)

        self.point_hu_input = QSpinBox()
        self.point_hu_input.setRange(-1000, 5000)

        point_input_layout.addWidget(QLabel("X:"))
        point_input_layout.addWidget(self.point_x_input)
        point_input_layout.addWidget(QLabel("Y:"))
        point_input_layout.addWidget(self.point_y_input)
        point_input_layout.addWidget(QLabel("Z:"))
        point_input_layout.addWidget(self.point_z_input)
        point_input_layout.addWidget(QLabel("MRI-Wert:"))
        point_input_layout.addWidget(self.point_mri_input)
        point_input_layout.addWidget(QLabel("HU-Wert:"))
        point_input_layout.addWidget(self.point_hu_input)

        self.add_point_button = QPushButton("Referenzpunkt hinzufügen")
        self.add_point_button.clicked.connect(self.add_reference_point)

        # Referenzpunkttabelle
        self.points_table = QTableWidget(0, 5)
        self.points_table.setHorizontalHeaderLabels(["X", "Y", "Z", "MRI-Wert", "HU-Wert"])
        self.points_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)

        point_buttons_layout = QHBoxLayout()
        point_buttons_layout.addWidget(self.add_point_button)

        cal_layout.addLayout(point_input_layout)
        cal_layout.addLayout(point_buttons_layout)
        cal_layout.addWidget(self.points_table)
        cal_group.setLayout(cal_layout)

        # Kalibrierungsoperationsgruppe
        calibrate_group = QGroupBox("Kalibrierung ausführen")
        calibrate_layout = QVBoxLayout()

        self.calibrate_button = QPushButton("Kalibrierung durchführen")
        self.calibrate_button.clicked.connect(self.perform_calibration)
        self.calibrate_button.setEnabled(False)

        self.clear_points_button = QPushButton("Referenzpunkte löschen")
        self.clear_points_button.clicked.connect(self.clear_reference_points)

        self.calibration_result_label = QLabel("Nicht kalibriert")
        self.calibration_result_label.setAlignment(Qt.AlignCenter)
        font = QFont()
        font.setBold(True)
        self.calibration_result_label.setFont(font)

        # Kalibrierungsdiagramm
        self.calibration_figure = plt.figure(figsize=(5, 4))
        self.calibration_canvas = FigureCanvas(self.calibration_figure)

        button_layout = QHBoxLayout()
        button_layout.addWidget(self.calibrate_button)
        button_layout.addWidget(self.clear_points_button)

        calibrate_layout.addLayout(button_layout)
        calibrate_layout.addWidget(self.calibration_result_label)
        calibrate_layout.addWidget(self.calibration_canvas)
        calibrate_group.setLayout(calibrate_layout)

        # Hinzufügen aller Gruppen zum Layout
        splitter = QSplitter(Qt.Horizontal)

        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.addWidget(cal_group)
        left_widget.setLayout(left_layout)

        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_layout.addWidget(calibrate_group)
        right_widget.setLayout(right_layout)

        splitter.addWidget(left_widget)
        splitter.addWidget(right_widget)

        layout.addWidget(splitter)
        self.tab_calibration.setLayout(layout)

    def setup_visualization_tab(self):
        """Visualisierungstab einrichten"""
        layout = QVBoxLayout()

        # Visualisierungsoptionen-Gruppe
        vis_options_group = QGroupBox("Visualisierungsoptionen")
        vis_options_layout = QHBoxLayout()

        self.vis_type_combo = QComboBox()
        self.vis_type_combo.addItems([
            "Schnittbild",
            "Registrierungsergebnisvergleich"
        ])

        self.generate_vis_button = QPushButton("Visualisierung generieren")
        self.generate_vis_button.clicked.connect(self.generate_visualization)

        vis_options_layout.addWidget(QLabel("Visualisierungstyp:"))
        vis_options_layout.addWidget(self.vis_type_combo)
        vis_options_layout.addWidget(self.generate_vis_button)
        vis_options_group.setLayout(vis_options_layout)

        # Visualisierungsdiagramm
        self.visualization_figure = plt.figure(figsize=(8, 6))
        self.visualization_canvas = FigureCanvas(self.visualization_figure)
        self.visualization_toolbar = NavigationToolbar(self.visualization_canvas, self)

        # Visualisierungskomponenten zum Layout hinzufügen
        layout.addWidget(vis_options_group)
        layout.addWidget(self.visualization_toolbar)
        layout.addWidget(self.visualization_canvas)

        self.tab_visualization.setLayout(layout)

    def setup_export_tab(self):
        """Export-Tab einrichten"""
        layout = QVBoxLayout()

        # Export der Registrierungsergebnisse-Gruppe
        export_reg_group = QGroupBox("Registrierungsergebnisse exportieren")
        export_reg_layout = QVBoxLayout()

        export_reg_file_layout = QHBoxLayout()
        self.export_reg_path_label = QLabel("Registrierungsergebnisse-Exportordner nicht festgelegt")
        self.export_reg_browse_button = QPushButton("Durchsuchen...")
        self.export_reg_browse_button.clicked.connect(self.browse_export_reg_folder)
        export_reg_file_layout.addWidget(self.export_reg_path_label)
        export_reg_file_layout.addWidget(self.export_reg_browse_button)

        self.export_reg_button = QPushButton("Registrierungsergebnisse exportieren")
        self.export_reg_button.clicked.connect(self.export_registration_result)
        self.export_reg_button.setEnabled(False)

        export_reg_layout.addLayout(export_reg_file_layout)
        export_reg_layout.addWidget(self.export_reg_button)
        export_reg_group.setLayout(export_reg_layout)

        # Alle Gruppen zum Layout hinzufügen
        layout.addWidget(export_reg_group)  # Export der Registrierungsergebnisse-Gruppe hinzufügen
        layout.addStretch()

        self.tab_export.setLayout(layout)

    # ================= Ereignisbehandlungsmethoden =================

    def browse_ct_folder(self):
        """CT-Datenordner durchsuchen"""
        folder = QFileDialog.getExistingDirectory(self, "CT-Datenordner auswählen")
        if folder:
            self.ct_path_label.setText(folder)
            self.statusBar.showMessage(f"CT-Datenordner ausgewählt: {folder}")

    def browse_mri_folder(self):
        """MRI-Datenordner durchsuchen"""
        folder = QFileDialog.getExistingDirectory(self, "MRI-Datenordner auswählen")
        if folder:
            self.mri_path_label.setText(folder)
            self.statusBar.showMessage(f"MRI-Datenordner ausgewählt: {folder}")

    def browse_export_reg_folder(self):
        """Registrierungsergebnisse-Exportordner durchsuchen"""
        folder = QFileDialog.getExistingDirectory(self, "Registrierungsergebnisse-Exportordner auswählen")
        if folder:
            self.export_reg_path_label.setText(folder)
            self.statusBar.showMessage(f"Registrierungsergebnisse-Exportordner ausgewählt: {folder}")

    def load_ct_data(self):
        """CT-Daten laden"""
        folder = self.ct_path_label.text()
        if folder == "Kein CT-Ordner ausgewählt":
            QMessageBox.warning(self, "Warnung", "Bitte wählen Sie zuerst einen CT-Datenordner aus")
            return

        self.statusBar.showMessage("CT-Daten werden geladen...")
        success = self.ct_handler.load_dicom_folder(folder)

        if success:
            QMessageBox.information(self, "Erfolg", "CT-Daten erfolgreich geladen")
            self.statusBar.showMessage("CT-Daten geladen")

            # Dateninformationen aktualisieren
            ct_info = (f"CT-Daten: Größe {self.ct_handler.shape}, "
                       f"Pixelabstand {self.ct_handler.pixel_spacing}, "
                       f"Schnittdicke {self.ct_handler.slice_thickness}, "
                       f"Bereich {np.min(self.ct_handler.ct_data)} - {np.max(self.ct_handler.ct_data)}")
            self.data_info_label.setText(ct_info)

            # CT-Vorschau aktualisieren
            self.update_preview_slices()
        else:
            QMessageBox.critical(self, "Fehler", "CT-Daten konnten nicht geladen werden")
            self.statusBar.showMessage("CT-Daten konnten nicht geladen werden")

    def load_mri_data(self):
        """MRI-Daten laden und detailliert überprüfen"""
        folder = self.mri_path_label.text()
        if folder == "Kein MRI-Ordner ausgewählt":
            QMessageBox.warning(self, "Warnung", "Bitte wählen Sie zuerst einen MRI-Datenordner aus")
            return

        self.statusBar.showMessage("MRI-Daten werden geladen...")

        # Detaillierte Diagnose hinzufügen
        print("----- MRI-Datenlade-Diagnose -----")
        print(f"Ladeordner: {folder}")

        success = self.mri_handler.load_dicom_folder(folder)

        if success:
            # Datenvalidität überprüfen
            if self.mri_handler.mri_data is None:
                QMessageBox.critical(self, "Fehler", "MRI-Daten sind leer")
                return

            data_range = np.max(self.mri_handler.mri_data) - np.min(self.mri_handler.mri_data)
            print(f"MRI-Datenbereich: {np.min(self.mri_handler.mri_data)} - {np.max(self.mri_handler.mri_data)}")
            print(f"Datenbereichsdifferenz: {data_range}")

            if data_range < 1:
                QMessageBox.warning(self, "Warnung", f"MRI-Datenkontrast ist extrem niedrig (Bereich: {data_range})")

                # Versuch, MRI-Daten zu reparieren
                self.mri_handler.mri_data = self.mri_handler.mri_data + np.random.normal(
                    0, 20, self.mri_handler.mri_data.shape).astype(self.mri_handler.mri_data.dtype)
                print("Zufälliges Rauschen hinzugefügt, um Kontrast zu erzeugen")
                print(f"Reparierter Bereich: {np.min(self.mri_handler.mri_data)} - {np.max(self.mri_handler.mri_data)}")

            QMessageBox.information(self, "Erfolg", "MRI-Daten erfolgreich geladen")
            self.statusBar.showMessage("MRI-Daten geladen")

            # Dateninformationen aktualisieren
            mri_info = (f"MRI-Daten: Größe {self.mri_handler.shape}, "
                        f"Pixelabstand {self.mri_handler.pixel_spacing}, "
                        f"Schnittdicke {self.mri_handler.slice_thickness}, "
                        f"Bereich {np.min(self.mri_handler.mri_data)} - {np.max(self.mri_handler.mri_data)}")

            # Wenn CT-Daten geladen sind, Informationen hinzufügen
            if hasattr(self.ct_handler, 'ct_data') and self.ct_handler.ct_data is not None:
                cur_info = self.data_info_label.text()
                self.data_info_label.setText(f"{cur_info}\n{mri_info}")
            else:
                self.data_info_label.setText(mri_info)

            # MRI-Vorschau aktualisieren
            self.update_preview_slices()
        else:
            QMessageBox.critical(self, "Fehler", "MRI-Daten konnten nicht geladen werden")
            self.statusBar.showMessage("MRI-Daten konnten nicht geladen werden")

    def create_test_mri_data(self):
        """Test-MRI-Daten für Debugging erstellen"""
        # Überprüfen, ob CT-Daten geladen sind
        if not hasattr(self.ct_handler, 'ct_data') or self.ct_handler.ct_data is None:
            QMessageBox.warning(self, "Warnung", "Bitte laden Sie zuerst CT-Daten")
            return False

        # MRI-Daten mit derselben Größe wie CT-Daten erstellen
        ct_shape = self.ct_handler.ct_data.shape
        test_mri = np.zeros(ct_shape, dtype=np.int16)

        # Daten mit Struktur erstellen
        for z in range(ct_shape[2]):
            for y in range(ct_shape[1]):
                for x in range(ct_shape[0]):
                    # Abstand zum Zentrum
                    cx, cy, cz = ct_shape[0] // 2, ct_shape[1] // 2, ct_shape[2] // 2
                    r = np.sqrt((x - cx) ** 2 + (y - cy) ** 2 + (z - cz) ** 2)
                    # Konzentrische Kreise erstellen
                    test_mri[x, y, z] = int(1000 * np.exp(-r / 30)) + np.random.randint(-10, 10)

        # MRI-Daten festlegen
        self.mri_handler.mri_data = test_mri
        self.mri_handler.shape = test_mri.shape

        # Andere Eigenschaften von CT kopieren
        self.mri_handler.pixel_spacing = self.ct_handler.pixel_spacing
        self.mri_handler.slice_thickness = self.ct_handler.slice_thickness
        self.mri_handler.origin = self.ct_handler.origin

        # SimpleITK-Bild erstellen
        self.mri_handler.sitk_image = sitk.GetImageFromArray(test_mri)
        self.mri_handler.sitk_image.SetSpacing((self.mri_handler.pixel_spacing[0],
                                                self.mri_handler.pixel_spacing[1],
                                                self.mri_handler.slice_thickness))
        self.mri_handler.sitk_image.SetOrigin(self.mri_handler.origin)
        self.mri_handler.sitk_image.SetDirection((1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0))

        # Oberfläche aktualisieren
        mri_info = (f"MRI-Daten (Test): Größe {self.mri_handler.shape}, "
                    f"Pixelabstand {self.mri_handler.pixel_spacing}, "
                    f"Schnittdicke {self.mri_handler.slice_thickness}, "
                    f"Bereich {np.min(self.mri_handler.mri_data)} - {np.max(self.mri_handler.mri_data)}")

        # Wenn CT-Daten geladen sind, Informationen hinzufügen
        if hasattr(self.ct_handler, 'ct_data') and self.ct_handler.ct_data is not None:
            cur_info = self.data_info_label.text()
            self.data_info_label.setText(f"{cur_info}\n{mri_info}")
        else:
            self.data_info_label.setText(mri_info)

        QMessageBox.information(self, "Erfolg", "Test-MRI-Daten erstellt")
        self.update_preview_slices()

        return True

    def run_registration(self):
        """Bildregistrierung durchführen"""
        # Überprüfen, ob Daten geladen sind
        if (not hasattr(self.ct_handler, 'sitk_image') or self.ct_handler.sitk_image is None or
                not hasattr(self.mri_handler, 'sitk_image') or self.mri_handler.sitk_image is None):
            QMessageBox.warning(self, "Warnung", "Bitte laden Sie zuerst CT- und MRI-Daten")
            return

        # Überprüfen, ob Daten gültig sind
        ct_stats = sitk.StatisticsImageFilter()
        ct_stats.Execute(self.ct_handler.sitk_image)
        mri_stats = sitk.StatisticsImageFilter()
        mri_stats.Execute(self.mri_handler.sitk_image)

        if ct_stats.GetMinimum() == ct_stats.GetMaximum():
            QMessageBox.warning(self, "Warnung", "CT-Daten ungültig, bitte überprüfen Sie die Daten")
            return

        if mri_stats.GetMinimum() == mri_stats.GetMaximum():
            QMessageBox.warning(self, "Warnung", "MRI-Daten ungültig, bitte überprüfen Sie die Daten")
            return

        # Festgelegte Registrierungsparameter erzwingen (mit reg.py übereinstimmend)
        reg_message = ""
        if self.reg_type_combo.currentIndex() != 0:
            reg_message = "Hinweis: Um konsistente Ergebnisse zu gewährleisten, wird eine starre Registrierung verwendet (Benutzerauswahl ignoriert)\n"
            self.reg_type_combo.setCurrentIndex(0)

        if self.use_advanced_options.isChecked():
            reg_message += "Hinweis: Um konsistente Ergebnisse zu gewährleisten, werden erweiterte Optionen ignoriert\n"

        if reg_message:
            QMessageBox.information(self, "Registrierungsparameteranpassung", reg_message)

        # Festgelegte Richtung "CT zu MRI" verwenden
        self.registration_handler.set_registration_type("rigid")
        self.registration_handler.set_fixed_image(self.ct_handler.sitk_image)
        self.registration_handler.set_moving_image(self.mri_handler.sitk_image)

        # Oberfläche aktualisieren
        self.registration_progress.setValue(0)
        self.registration_status_label.setText("Registrierungsstatus: Wird ausgeführt...")
        self.run_registration_button.setEnabled(False)

        # Registrierungs-Thread erstellen und starten
        self.registration_thread = RegistrationThread(self.registration_handler)
        self.registration_thread.progress_signal.connect(self.update_registration_progress)
        self.registration_thread.result_signal.connect(self.handle_registration_result)
        self.registration_thread.error_signal.connect(self.handle_registration_error)
        self.registration_thread.start()

    def update_registration_progress(self, value):
        """Registrierungsfortschritt aktualisieren"""
        self.registration_progress.setValue(value)

    def handle_registration_error(self, error_msg):
        """Registrierungsfehler behandeln"""
        self.registration_progress.setValue(0)
        self.registration_status_label.setText(f"Registrierungsstatus: Fehlgeschlagen - {error_msg}")
        self.run_registration_button.setEnabled(True)

        QMessageBox.critical(self, "Fehler", f"Registrierung fehlgeschlagen: {error_msg}")
        self.statusBar.showMessage(f"Registrierung fehlgeschlagen: {error_msg}")

    def save_registration_result(self):
        """Registrierungsergebnis speichern"""
        folder = QFileDialog.getExistingDirectory(self, "Registrierungsergebnisspeicherordner auswählen")
        if not folder:
            return

        if self.registration_handler.registered_image is None:
            QMessageBox.warning(self, "Warnung", "Kein Registrierungsergebnis zum Speichern")
            return

        self.statusBar.showMessage("Registrierungsergebnis wird gespeichert...")

        # Ergebnis speichern
        success = self.registration_handler.save_registration_result(folder)

        if success:
            QMessageBox.information(self, "Erfolg", f"Registrierungsergebnis gespeichert in {folder}")
            self.statusBar.showMessage(f"Registrierungsergebnis gespeichert in {folder}")
        else:
            QMessageBox.critical(self, "Fehler", "Registrierungsergebnis konnte nicht gespeichert werden")
            self.statusBar.showMessage("Registrierungsergebnis konnte nicht gespeichert werden")

    def add_reference_point(self):
        """MRI-HU-Kalibrierungsreferenzpunkt hinzufügen"""
        x = self.point_x_input.value()
        y = self.point_y_input.value()
        z = self.point_z_input.value()
        mri_value = self.point_mri_input.value()
        hu_value = self.point_hu_input.value()

        # Zur Referenzpunktliste hinzufügen
        self.mri_ct_calibrator.add_reference_point(x, y, z, mri_value, hu_value)

        # Zur Tabelle hinzufügen
        row = self.points_table.rowCount()
        self.points_table.insertRow(row)
        self.points_table.setItem(row, 0, QTableWidgetItem(f"{x:.2f}"))
        self.points_table.setItem(row, 1, QTableWidgetItem(f"{y:.2f}"))
        self.points_table.setItem(row, 2, QTableWidgetItem(f"{z:.2f}"))
        self.points_table.setItem(row, 3, QTableWidgetItem(f"{mri_value}"))
        self.points_table.setItem(row, 4, QTableWidgetItem(f"{hu_value}"))

        # Wenn zwei oder mehr Punkte vorhanden sind, Kalibrierungsschaltfläche aktivieren
        if self.points_table.rowCount() >= 2:
            self.calibrate_button.setEnabled(True)

        self.statusBar.showMessage(f"Referenzpunkt hinzugefügt: ({x:.2f}, {y:.2f}, {z:.2f}), MRI={mri_value}, HU={hu_value}")

    def clear_reference_points(self):
        """Alle Referenzpunkte löschen"""
        self.mri_ct_calibrator.clear_reference_points()
        self.points_table.setRowCount(0)
        self.calibrate_button.setEnabled(False)
        self.calibration_result_label.setText("Nicht kalibriert")

        # Kalibrierungsdiagramm löschen
        self.calibration_figure.clear()
        self.calibration_canvas.draw()

        self.statusBar.showMessage("Alle Referenzpunkte gelöscht")

    def perform_calibration(self):
        """Kalibrierung von MRI zu HU durchführen"""
        if len(self.mri_ct_calibrator.reference_points) < 2:
            QMessageBox.warning(self, "Warnung", "Mindestens zwei Referenzpunkte sind erforderlich, um die Kalibrierung durchzuführen")
            return

        # Kalibrierung durchführen
        success = self.mri_ct_calibrator.perform_calibration()

        if not success:
            QMessageBox.critical(self, "Fehler", "Kalibrierung fehlgeschlagen")
            return

        # Kalibrierungsparameter abrufen
        slope = self.mri_ct_calibrator.mri_to_hu_a
        intercept = self.mri_ct_calibrator.mri_to_hu_b

        # MRI- und HU-Werte für die Darstellung extrahieren
        mri_values = [point[3] for point in self.mri_ct_calibrator.reference_points]
        hu_values = [point[4] for point in self.mri_ct_calibrator.reference_points]

        # R2-Wert berechnen
        _, _, r_value, _, _ = stats.linregress(mri_values, hu_values)
        r_squared = r_value ** 2

        # Kalibrierungsergebnis anzeigen
        self.calibration_result_label.setText(
            f"Kalibrierungsergebnis: HU = {slope:.4f} × MRI + {intercept:.2f} (R² = {r_squared:.4f})")

        # Kalibrierungsdiagramm zeichnen
        self.calibration_figure.clear()
        ax = self.calibration_figure.add_subplot(111)

        # Datenpunkte zeichnen
        ax.scatter(mri_values, hu_values, color='blue', label='Referenzpunkte')

        # Anpassungslinie zeichnen
        min_mri = min(mri_values)
        max_mri = max(mri_values)
        x_line = np.linspace(min_mri - 100, max_mri + 100, 100)
        y_line = slope * x_line + intercept
        ax.plot(x_line, y_line, color='red', label=f'Anpassungslinie: HU = {slope:.4f} × MRI + {intercept:.2f}')

        ax.set_xlabel('MRI-Wert')
        ax.set_ylabel('HU-Wert')
        ax.set_title('MRI-HU-Kalibrierungskurve')
        ax.grid(True, alpha=0.3)
        ax.legend()

        self.calibration_figure.tight_layout()
        self.calibration_canvas.draw()

        QMessageBox.information(self, "Erfolg", "MRI-Kalibrierung abgeschlossen")
        self.statusBar.showMessage(f"MRI-Kalibrierung abgeschlossen: HU = {slope:.4f} × MRI + {intercept:.2f}")

    def generate_visualization(self):
        """Visualisierungsdiagramm generieren"""
        vis_type = self.vis_type_combo.currentText()
        self.statusBar.showMessage(f"{vis_type} wird generiert...")

        # Aktuelles Diagramm löschen
        self.visualization_figure.clear()

        if vis_type == "Schnittbild":
            self.visualize_section()
        elif vis_type == "Registrierungsergebnisvergleich":
            self.visualize_registration_comparison()

        # Leinwand aktualisieren
        self.visualization_canvas.draw()
        self.statusBar.showMessage(f"{vis_type} generiert")

    def visualize_section(self):
        """CT/MRI-Schnittbild visualisieren"""
        if not hasattr(self.ct_handler, 'ct_data') or self.ct_handler.ct_data is None:
            if not hasattr(self.mri_handler, 'mri_data') or self.mri_handler.mri_data is None:
                QMessageBox.warning(self, "Warnung", "Bitte laden Sie zuerst CT- oder MRI-Daten")
                return

            # MRI-Daten verwenden
            self.visualize_mri_section()
            return

        # CT-Daten verwenden
        ct_data = self.ct_handler.ct_data

        # Unterdiagramme erstellen
        fig = self.visualization_figure
        gs = fig.add_gridspec(2, 2)
        ax1 = fig.add_subplot(gs[0, 0])
        ax2 = fig.add_subplot(gs[0, 1])
        ax3 = fig.add_subplot(gs[1, :])

        # Mittelpunkt ermitteln
        z_mid = ct_data.shape[2] // 2
        y_mid = ct_data.shape[1] // 2
        x_mid = ct_data.shape[0] // 2

        # Drei orthogonale Ebenen anzeigen
        ax1.imshow(ct_data[:, :, z_mid], cmap='bone', aspect='equal',
                   vmin=-1000, vmax=2000)
        ax1.set_title('Axialschnitt')
        ax1.axis('off')

        ax2.imshow(ct_data[:, y_mid, :], cmap='bone', aspect='equal',
                   vmin=-1000, vmax=2000)
        ax2.set_title('Koronalschnitt')
        ax2.axis('off')

        im = ax3.imshow(ct_data[x_mid, :, :], cmap='bone', aspect='equal',
                        vmin=-1000, vmax=2000)
        ax3.set_title('Sagittalschnitt')
        ax3.axis('off')

        # Farbskala hinzufügen
        cbar = fig.colorbar(im, ax=[ax1, ax2, ax3], orientation='horizontal', pad=0.01, shrink=0.7)
        cbar.set_label('HU-Wert')

        fig.tight_layout()

    def visualize_mri_section(self):
        """MRI-Schnittbild visualisieren"""
        if not hasattr(self.mri_handler, 'mri_data') or self.mri_handler.mri_data is None:
            QMessageBox.warning(self, "Warnung", "Bitte laden Sie zuerst MRI-Daten")
            return

        # MRI-Daten verwenden
        mri_data = self.mri_handler.mri_data

        # Unterdiagramme erstellen
        fig = self.visualization_figure
        gs = fig.add_gridspec(2, 2)
        ax1 = fig.add_subplot(gs[0, 0])
        ax2 = fig.add_subplot(gs[0, 1])
        ax3 = fig.add_subplot(gs[1, :])

        # Mittelpunkt ermitteln
        z_mid = mri_data.shape[2] // 2
        y_mid = mri_data.shape[1] // 2
        x_mid = mri_data.shape[0] // 2

        # Drei orthogonale Ebenen anzeigen
        ax1.imshow(mri_data[:, :, z_mid], cmap='gray', aspect='equal')
        ax1.set_title('Axialschnitt')
        ax1.axis('off')

        ax2.imshow(mri_data[:, y_mid, :], cmap='gray', aspect='equal')
        ax2.set_title('Koronalschnitt')
        ax2.axis('off')

        im = ax3.imshow(mri_data[x_mid, :, :], cmap='gray', aspect='equal')
        ax3.set_title('Sagittalschnitt')
        ax3.axis('off')

        # Farbskala hinzufügen
        cbar = fig.colorbar(im, ax=[ax1, ax2, ax3], orientation='horizontal', pad=0.01, shrink=0.7)
        cbar.set_label('MRI-Intensitätswert')

        fig.tight_layout()

    def handle_registration_result(self, result):
        """Registrierungsergebnis behandeln"""
        # Oberfläche aktualisieren
        self.registration_progress.setValue(100)
        self.registration_status_label.setText("Registrierungsstatus: Abgeschlossen")
        self.run_registration_button.setEnabled(True)
        self.save_reg_result_button.setEnabled(True)
        self.export_reg_button.setEnabled(True)

        # Registrierungsergebnis überprüfen
        result_stats = sitk.StatisticsImageFilter()
        result_stats.Execute(result)
        print(f"Registrierungsergebnis: Minimum={result_stats.GetMinimum()}, Maximum={result_stats.GetMaximum()}")

        if result_stats.GetMinimum() == result_stats.GetMaximum():
            QMessageBox.warning(self, "Warnung", "Registrierungsergebnis ungültig, bitte versuchen Sie andere Registrierungsparameter")
            return

        # Vorschau aktualisieren
        self.update_preview_slices()

        # Zum Visualisierungstab wechseln und Registrierungsergebnis anzeigen
        self.tabs.setCurrentWidget(self.tab_visualization)
        self.visualize_registration_comparison()
        self.visualization_canvas.draw()

        QMessageBox.information(self, "Erfolg", "Bildregistrierung abgeschlossen")
        self.statusBar.showMessage("Bildregistrierung abgeschlossen")

    def update_preview_slices(self):
        """Vorschau-Bildschnitt entsprechend der Schiebereglerposition aktualisieren"""
        # Normalisierte Schiebereglerposition abrufen (0-1)
        slice_pos = self.slice_slider.value() / 100.0

        # CT-Vorschau aktualisieren
        if hasattr(self.ct_handler, 'sitk_image') and self.ct_handler.sitk_image is not None:
            # Numpy-Array aus SimpleITK-Bild abrufen
            ct_array = sitk.GetArrayFromImage(self.ct_handler.sitk_image)

            # Schnittindex berechnen
            ct_slice_idx = int(slice_pos * (ct_array.shape[0] - 1))
            ct_slice_idx = max(0, min(ct_slice_idx, ct_array.shape[0] - 1))

            # Schnitt abrufen
            ct_slice = ct_array[ct_slice_idx]

            # Normalisierte Anzeige
            ct_norm = (ct_slice - np.min(ct_slice)) / (np.max(ct_slice) - np.min(ct_slice) + 1e-8)
            ct_slice_norm = (ct_norm * 255).astype(np.uint8)

            h, w = ct_slice_norm.shape
            ct_qimg = QImage(ct_slice_norm.tobytes(), w, h, w, QImage.Format_Grayscale8)
            ct_pixmap = QPixmap.fromImage(ct_qimg).scaled(
                self.ct_preview_label.width(), self.ct_preview_label.height(),
                Qt.KeepAspectRatio)
            self.ct_preview_label.setPixmap(ct_pixmap)
            self.ct_preview_label.setText("")
        else:
            self.ct_preview_label.setText("CT-Daten nicht geladen")

        # MRI-Vorschau aktualisieren
        if hasattr(self.mri_handler, 'sitk_image') and self.mri_handler.sitk_image is not None:
            # Numpy-Array aus SimpleITK-Bild abrufen
            mri_array = sitk.GetArrayFromImage(self.mri_handler.sitk_image)

            # Schnittindex berechnen
            mri_slice_idx = int(slice_pos * (mri_array.shape[0] - 1))
            mri_slice_idx = max(0, min(mri_slice_idx, mri_array.shape[0] - 1))

            # Schnitt abrufen
            mri_slice = mri_array[mri_slice_idx]

            # Normalisierte Anzeige
            mri_norm = (mri_slice - np.min(mri_slice)) / (np.max(mri_slice) - np.min(mri_slice) + 1e-8)
            mri_slice_norm = (mri_norm * 255).astype(np.uint8)

            h, w = mri_slice_norm.shape
            mri_qimg = QImage(mri_slice_norm.tobytes(), w, h, w, QImage.Format_Grayscale8)
            mri_pixmap = QPixmap.fromImage(mri_qimg).scaled(
                self.mri_preview_label.width(), self.mri_preview_label.height(),
                Qt.KeepAspectRatio)
            self.mri_preview_label.setPixmap(mri_pixmap)
            self.mri_preview_label.setText("")
        else:
            self.mri_preview_label.setText("MRI-Daten nicht geladen")

        # Vorschau des registrierten Bildes aktualisieren - Graustufenfusionseffekt
        if (hasattr(self.registration_handler, 'registered_image') and
                self.registration_handler.registered_image is not None and
                hasattr(self.ct_handler, 'sitk_image') and self.ct_handler.sitk_image is not None):

            # CT-Bild und registriertes MRI-Bild abrufen
            ct = self.ct_handler.sitk_image
            mri_reg = self.registration_handler.registered_image

            # SimpleITK-Bild in Numpy-Array umwandeln
            ct_array = sitk.GetArrayFromImage(ct)
            mri_array = sitk.GetArrayFromImage(mri_reg)

            # Gleichen Schnittindex verwenden
            slice_idx = int(slice_pos * (ct_array.shape[0] - 1))
            slice_idx = max(0, min(slice_idx, ct_array.shape[0] - 1))

            ct_slice = ct_array[slice_idx]
            mri_slice = mri_array[slice_idx]

            # Für Fusionsbild auf [0,1] normalisieren
            ct_norm = (ct_slice - np.min(ct_slice)) / (np.max(ct_slice) - np.min(ct_slice) + 1e-8)
            mri_norm = (mri_slice - np.min(mri_slice)) / (np.max(mri_slice) - np.min(mri_slice) + 1e-8)

            # Graustufen-Fusionsbild erstellen (0.5*CT + 0.5*MRI)
            fused_gray = 0.5 * ct_norm + 0.5 * mri_norm
            fused_slice_norm = (fused_gray * 255).astype(np.uint8)

            # In Qt-Bild umwandeln und anzeigen
            h, w = fused_slice_norm.shape
            reg_qimg = QImage(fused_slice_norm.tobytes(), w, h, w, QImage.Format_Grayscale8)
            reg_pixmap = QPixmap.fromImage(reg_qimg).scaled(
                self.registered_preview_label.width(), self.registered_preview_label.height(),
                Qt.KeepAspectRatio)
            self.registered_preview_label.setPixmap(reg_pixmap)
            self.registered_preview_label.setText("")
        else:
            self.registered_preview_label.setText("Registrierung nicht durchgeführt oder CT-Daten nicht geladen")

    def visualize_registration_comparison(self):
        """
        Registrierungseffekt anzeigen:
          - Linkes Bild: Mittlerer Schnitt des CT-Bildes
          - Mittleres Bild: Mittlerer Schnitt des registrierten und neu abgetasteten MRI-Bildes
          - Rechtes Bild: Fusionsbild, das die beiden normalisierten Bilder in Rot/Grün-Kanälen überlagert
        Verbesserung: Sicherstellen, dass CT- und MRI-Bilder vollständig ausgerichtet sind, und die Fusionsmethode anpassen, um die Beziehung besser darzustellen
        """
        if (not hasattr(self.ct_handler, 'sitk_image') or self.ct_handler.sitk_image is None or
                not hasattr(self.registration_handler, 'registered_image') or
                self.registration_handler.registered_image is None):
            QMessageBox.warning(self, "Warnung", "Bitte laden Sie zuerst CT- und MRI-Daten und führen Sie die Bildregistrierung durch")
            return

        # Aktuelles Diagramm löschen
        fig = self.visualization_figure
        fig.clear()

        # SimpleITK-Bild abrufen
        ct = self.ct_handler.sitk_image
        mri_reg = self.registration_handler.registered_image

        # SimpleITK-Bild in Numpy-Array umwandeln, Array-Form ist (Schnitte, Zeilen, Spalten)
        ct_array = sitk.GetArrayFromImage(ct)
        mri_array = sitk.GetArrayFromImage(mri_reg)

        # Sicherstellen, dass beide Bilder die gleiche Größe haben
        if ct_array.shape != mri_array.shape:
            print(f"Warnung: CT-Bildgröße {ct_array.shape} stimmt nicht mit der Größe des registrierten MRI-Bildes {mri_array.shape} überein")
            # Wenn die Größen nicht übereinstimmen, MRI-Bild auf die Größe des CT-Bildes neu abtasten
            if hasattr(self.registration_handler, 'final_transform') and self.registration_handler.final_transform is not None:
                print("Transformation erneut anwenden, um die Größenübereinstimmung sicherzustellen...")
                mri_reg = reg_resample_image(self.mri_handler.sitk_image, ct, self.registration_handler.final_transform)
                mri_array = sitk.GetArrayFromImage(mri_reg)

        # Mittleren Schnitt zur Anzeige auswählen
        slice_idx = min(ct_array.shape[0], mri_array.shape[0]) // 2

        ct_slice = ct_array[slice_idx] if slice_idx < ct_array.shape[0] else ct_array[-1]
        mri_slice = mri_array[slice_idx] if slice_idx < mri_array.shape[0] else mri_array[-1]

        # Für Fusionsbild rote (CT) und grüne (MRI) Kanäle zuweisen, zuerst auf [0,1] normalisieren
        ct_norm = (ct_slice - np.min(ct_slice)) / (np.max(ct_slice) - np.min(ct_slice) + 1e-8)
        mri_norm = (mri_slice - np.min(mri_slice)) / (np.max(mri_slice) - np.min(mri_slice) + 1e-8)

        # 3-Kanal-Fusionsbild erstellen, Gewichtungen anpassen, um die Beziehung besser darzustellen
        fused_overlay = np.zeros((ct_slice.shape[0], ct_slice.shape[1], 3))
        fused_overlay[:, :, 0] = ct_norm * 0.7  # Roter Kanal zeigt CT, Gewichtung reduzieren
        fused_overlay[:, :, 1] = mri_norm * 0.7  # Grüner Kanal zeigt MRI, Gewichtung reduzieren
        # Helligkeitsverstärkung hinzufügen, um überlappende Bereiche deutlicher zu machen
        fused_overlay[:, :, 2] = np.minimum(ct_norm, mri_norm) * 0.3  # Blauer Kanal zur Verstärkung überlappender Bereiche

        # Drei Unterdiagramme zeichnen
        axes = fig.subplots(1, 3)

        # CT-Bild anzeigen
        axes[0].imshow(ct_slice, cmap="gray")
        axes[0].set_title("CT")
        axes[0].axis('off')

        # Registriertes MRI-Bild anzeigen
        axes[1].imshow(mri_slice, cmap="gray")
        axes[1].set_title("MRI (registriert)")
        axes[1].axis('off')

        # Fusionsbild anzeigen
        axes[2].imshow(fused_overlay)
        axes[2].set_title("Registriertes Fusionsbild")
        axes[2].axis('off')

        fig.tight_layout()

    def export_registration_result(self):
        """Registrierungsergebnis exportieren"""
        if not hasattr(self.registration_handler,
                       'registered_image') or self.registration_handler.registered_image is None:
            QMessageBox.warning(self, "Warnung", "Bitte führen Sie zuerst die Bildregistrierung durch")
            return

        folder = self.export_reg_path_label.text()
        if folder == "Registrierungsergebnisse-Exportordner nicht festgelegt":
            folder = QFileDialog.getExistingDirectory(self, "Registrierungsergebnisse-Exportordner auswählen")
            if not folder:
                return
            self.export_reg_path_label.setText(folder)

        self.statusBar.showMessage("Registrierungsergebnis wird exportiert...")

        # Ausgabeverzeichnis erstellen
        os.makedirs(folder, exist_ok=True)

        # Registrierungsbild speichern
        sitk.WriteImage(self.registration_handler.registered_image, os.path.join(folder, "registered_image.mha"))

        # Kalibrierungsparameter exportieren
        with open(os.path.join(folder, "calibration_parameters.txt"), 'w') as f:
            f.write(f"MRI zu HU Umwandlungsparameter:\n")
            f.write(
                f"HU = {self.mri_ct_calibrator.mri_to_hu_a:.6f} * MRI + {self.mri_ct_calibrator.mri_to_hu_b:.6f}\n\n")

        # Vergleichsbild exportieren
        self.visualization_figure.clear()
        self.visualize_registration_comparison()
        self.visualization_figure.savefig(os.path.join(folder, "registration_comparison.png"), dpi=300)

        QMessageBox.information(self, "Erfolg", f"Registrierungsergebnis exportiert nach: {folder}")
        self.statusBar.showMessage(f"Registrierungsergebnis exportiert nach: {folder}")



def main():
    """Programmeinstiegspunkt"""
    app = QApplication(sys.argv)
    app.setStyle('Fusion')  # Fusion-Stil verwenden

    # Chinesische Schriftart einstellen
    font = QFont("Microsoft YaHei", 9)
    app.setFont(font)

    window = MainWindow()
    window.show()

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()