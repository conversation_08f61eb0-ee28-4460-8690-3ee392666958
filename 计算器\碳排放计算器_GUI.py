import tkinter as tk
from tkinter import ttk, messagebox

class CarbonEmissionCalculator:
    def __init__(self, root):
        self.root = root
        self.root.title("碳排放计算器：今天你'低碳'了吗？")
        self.root.geometry("900x600")
        self.root.resizable(False, False)
        
        # 设置背景颜色和样式
        self.root.configure(bg="#d4d0c8")
        self.style = ttk.Style()
        self.style.configure("TFrame", background="#d4d0c8")
        self.style.configure("TLabel", background="#d4d0c8", font=("SimSun", 10))
        self.style.configure("TButton", font=("SimSun", 12))
        self.style.configure("TEntry", font=("SimSun", 10))
        self.style.configure("TRadiobutton", background="#d4d0c8", font=("SimSun", 10))
        self.style.configure("TLabelframe", background="#d4d0c8", font=("SimSun", 10, "bold"))
        self.style.configure("TLabelframe.Label", background="#d4d0c8", font=("SimSun", 10, "bold"))
        
        # 创建主内容区域
        main_container = tk.Frame(root, bg="#d4d0c8")
        main_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建三个主要部分
        self.main_frame = ttk.Frame(main_container)
        self.main_frame.pack(fill=tk.BOTH, expand=True, side=tk.TOP)
        
        self.create_left_section()
        self.create_middle_section()
        self.create_right_section()
        
        # 创建一个灰色的区域放置计算按钮 - 类似图片中的样式
        button_bg = tk.Frame(root, bg="#c0c0c0", height=40)
        button_bg.pack(fill=tk.X, pady=(10, 0))
        
        # 计算按钮放在中间位置
        calc_button = tk.Button(button_bg, text="计算", command=self.calculate_emission, 
                             width=15, font=("SimSun", 12), relief=tk.RAISED)
        calc_button.pack(pady=5)
        
        # 创建结果显示区域 - 标签为"计算结果"
        result_container = tk.Frame(root, bg="#d4d0c8")
        result_container.pack(fill=tk.BOTH, expand=True, padx=10)
        
        result_label_frame = tk.LabelFrame(result_container, text="计算结果", font=("SimSun", 10, "bold"), 
                                        bg="#d4d0c8", padx=10, pady=10)
        result_label_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 显示结果的标签
        self.result_var = tk.StringVar()
        self.result_var.set("点击计算按钮计算您的碳排放")
        self.result_label = tk.Label(result_label_frame, textvariable=self.result_var, 
                                  font=("SimSun", 12), bg="#ffffff", 
                                  padx=20, pady=30, anchor="center")
        self.result_label.pack(fill=tk.BOTH, expand=True)
        
        # 确保底部文字完全显示 - 使用固定位置放在底部
        # 创建一个灰色的条带作为底部背景
        footer_bg = tk.Frame(root, bg="#808080", height=25)
        footer_bg.pack(side=tk.BOTTOM, fill=tk.X)
        footer_bg.pack_propagate(False)  # 防止被压缩
        
        # 底部文字 - 确保字体足够大且可见
        footer_label = tk.Label(footer_bg, text="倡导低碳生活    共建美好家园", 
                             font=("SimSun", 12), bg="#808080", fg="white")
        footer_label.pack(side=tk.BOTTOM, fill=tk.X, pady=3)
    
    def create_left_section(self):
        left_frame = ttk.Frame(self.main_frame, width=280, height=320)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        left_frame.pack_propagate(False)
        
        # 衣 (Clothing) section
        clothing_frame = ttk.LabelFrame(left_frame, text="衣")
        clothing_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(clothing_frame, text="衣服/鞋袜/包").grid(row=0, column=0, padx=5, pady=5)
        self.clothes_entry = ttk.Entry(clothing_frame, width=15)
        self.clothes_entry.grid(row=0, column=1, padx=5, pady=5)
        ttk.Label(clothing_frame, text="元").grid(row=0, column=2, padx=5, pady=5)
        
        # 食 (Food) section
        food_frame = ttk.LabelFrame(left_frame, text="食")
        food_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(food_frame, text="素菜/米饭/粉面").grid(row=0, column=0, padx=5, pady=5)
        self.vegetarian_entry = ttk.Entry(food_frame, width=15)
        self.vegetarian_entry.grid(row=0, column=1, padx=5, pady=5)
        ttk.Label(food_frame, text="元").grid(row=0, column=2, padx=5, pady=5)
        
        ttk.Label(food_frame, text="荤菜/烟酒/零食").grid(row=1, column=0, padx=5, pady=5)
        self.meat_entry = ttk.Entry(food_frame, width=15)
        self.meat_entry.grid(row=1, column=1, padx=5, pady=5)
        ttk.Label(food_frame, text="元").grid(row=1, column=2, padx=5, pady=5)
        
        # 用 (Daily Use) section
        daily_use_frame = ttk.LabelFrame(left_frame, text="用")
        daily_use_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(daily_use_frame, text="塑料袋").grid(row=0, column=0, padx=5, pady=5)
        self.plastic_bags_entry = ttk.Entry(daily_use_frame, width=15)
        self.plastic_bags_entry.grid(row=0, column=1, padx=5, pady=5)
        ttk.Label(daily_use_frame, text="个").grid(row=0, column=2, padx=5, pady=5)
        
        ttk.Label(daily_use_frame, text="书籍/文具").grid(row=1, column=0, padx=5, pady=5)
        self.stationery_entry = ttk.Entry(daily_use_frame, width=15)
        self.stationery_entry.grid(row=1, column=1, padx=5, pady=5)
        ttk.Label(daily_use_frame, text="元").grid(row=1, column=2, padx=5, pady=5)
    
    def create_middle_section(self):
        middle_frame = ttk.Frame(self.main_frame, width=280, height=320)
        middle_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        middle_frame.pack_propagate(False)
        
        # 住 (Housing) section
        housing_frame = ttk.LabelFrame(middle_frame, text="住")
        housing_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Gas usage
        ttk.Label(housing_frame, text="燃气使用量").grid(row=0, column=0, sticky="w", padx=5, pady=5)
        self.gas_entry = ttk.Entry(housing_frame, width=10)
        self.gas_entry.grid(row=0, column=1, padx=5, pady=5)
        ttk.Label(housing_frame, text="m³").grid(row=0, column=2, sticky="w", padx=5, pady=5)
        
        # Water usage
        ttk.Label(housing_frame, text="用水量").grid(row=1, column=0, sticky="w", padx=5, pady=5)
        self.water_entry = ttk.Entry(housing_frame, width=10)
        self.water_entry.grid(row=1, column=1, padx=5, pady=5)
        ttk.Label(housing_frame, text="吨").grid(row=1, column=2, sticky="w", padx=5, pady=5)
        
        # Appliance power
        ttk.Label(housing_frame, text="家用电器总功率").grid(row=2, column=0, sticky="w", padx=5, pady=5)
        self.appliance_power_entry = ttk.Entry(housing_frame, width=10)
        self.appliance_power_entry.grid(row=2, column=1, padx=5, pady=5)
        ttk.Label(housing_frame, text="瓦").grid(row=2, column=2, sticky="w", padx=5, pady=5)
        
        # Usage hours
        ttk.Label(housing_frame, text="每日使用小时数").grid(row=3, column=0, sticky="w", padx=5, pady=5)
        self.usage_hours_entry = ttk.Entry(housing_frame, width=10)
        self.usage_hours_entry.grid(row=3, column=1, padx=5, pady=5)
        ttk.Label(housing_frame, text="小时").grid(row=3, column=2, sticky="w", padx=5, pady=5)
    
    def create_right_section(self):
        right_frame = ttk.Frame(self.main_frame, width=280, height=320)
        right_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        right_frame.pack_propagate(False)
        
        # 行 (Transportation) section
        transportation_frame = ttk.LabelFrame(right_frame, text="行")
        transportation_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Elevator usage
        ttk.Label(transportation_frame, text="是否乘坐电梯").grid(row=0, column=0, sticky="w", padx=5, pady=5)
        
        self.elevator_var = tk.StringVar(value="否")
        ttk.Radiobutton(transportation_frame, text="是", variable=self.elevator_var, value="是", 
                       command=self.toggle_elevator_fields).grid(row=0, column=1, padx=5, pady=5, sticky="w")
        ttk.Radiobutton(transportation_frame, text="否", variable=self.elevator_var, value="否", 
                       command=self.toggle_elevator_fields).grid(row=0, column=2, padx=5, pady=5, sticky="w")
        
        # Weight
        ttk.Label(transportation_frame, text="体重").grid(row=1, column=0, sticky="w", padx=5, pady=5)
        self.weight_entry = ttk.Entry(transportation_frame, width=10)
        self.weight_entry.grid(row=1, column=1, padx=5, pady=5)
        ttk.Label(transportation_frame, text="kg").grid(row=1, column=2, sticky="w", padx=5, pady=5)
        
        # Floors
        ttk.Label(transportation_frame, text="累计层数").grid(row=2, column=0, sticky="w", padx=5, pady=5)
        self.floors_entry = ttk.Entry(transportation_frame, width=10)
        self.floors_entry.grid(row=2, column=1, padx=5, pady=5)
        ttk.Label(transportation_frame, text="层").grid(row=2, column=2, sticky="w", padx=5, pady=5)
        
        # Transportation mode
        ttk.Label(transportation_frame, text="交通工具选择:").grid(row=3, column=0, sticky="w", padx=5, pady=5)
        
        self.transport_var = tk.IntVar(value=1)
        transport_frame = ttk.Frame(transportation_frame)
        transport_frame.grid(row=4, column=0, columnspan=3, sticky="w", padx=5, pady=5)
        
        ttk.Radiobutton(transport_frame, text="1. 步行", variable=self.transport_var, value=1).grid(row=0, column=0, sticky="w", padx=5)
        ttk.Radiobutton(transport_frame, text="2. 骑行", variable=self.transport_var, value=2).grid(row=0, column=1, sticky="w", padx=5)
        ttk.Radiobutton(transport_frame, text="3. 公交", variable=self.transport_var, value=3).grid(row=1, column=0, sticky="w", padx=5)
        ttk.Radiobutton(transport_frame, text="4. 私家车", variable=self.transport_var, value=4).grid(row=1, column=1, sticky="w", padx=5)
        
        # Distance
        ttk.Label(transportation_frame, text="出行距离").grid(row=5, column=0, sticky="w", padx=5, pady=5)
        self.distance_entry = ttk.Entry(transportation_frame, width=10)
        self.distance_entry.grid(row=5, column=1, padx=5, pady=5)
        ttk.Label(transportation_frame, text="km").grid(row=5, column=2, sticky="w", padx=5, pady=5)
        
        # Initial state of elevator fields
        self.toggle_elevator_fields()
    
    def toggle_elevator_fields(self):
        if self.elevator_var.get() == "是":
            self.weight_entry.configure(state="normal")
            self.floors_entry.configure(state="normal")
        else:
            self.weight_entry.delete(0, tk.END)
            self.weight_entry.insert(0, "0")
            self.floors_entry.delete(0, tk.END)
            self.floors_entry.insert(0, "0")
            self.weight_entry.configure(state="disabled")
            self.floors_entry.configure(state="disabled")
    
    def get_float_value(self, entry, default=0.0):
        try:
            return float(entry.get())
        except ValueError:
            messagebox.showwarning("输入错误", f"{entry.get()} 不是有效的数字，将使用默认值 0。")
            entry.delete(0, tk.END)
            entry.insert(0, str(default))
            return default
    
    def get_int_value(self, entry, default=0):
        try:
            return int(entry.get())
        except ValueError:
            messagebox.showwarning("输入错误", f"{entry.get()} 不是有效的数字，将使用默认值 0。")
            entry.delete(0, tk.END)
            entry.insert(0, str(default))
            return default
    
    def calculate_emission(self):
        total = 0
        
        # Clothing
        clothes = self.get_float_value(self.clothes_entry)
        clothing_emission = clothes * 0.3  # 假设每元产生0.3g排放
        total += clothing_emission
        
        # Food
        vegetarian = self.get_float_value(self.vegetarian_entry)
        meat = self.get_float_value(self.meat_entry)
        food_emission = (vegetarian + meat) * 0.5  # 假设每元产生0.5g排放
        total += food_emission
        
        # Daily use
        plastic_bags = self.get_int_value(self.plastic_bags_entry)
        stationery = self.get_float_value(self.stationery_entry)
        daily_use_emission = plastic_bags * 0.1 + stationery * 0.05
        total += daily_use_emission
        
        # Housing
        gas = self.get_float_value(self.gas_entry)
        water = self.get_float_value(self.water_entry)
        appliance_power = self.get_float_value(self.appliance_power_entry)
        usage_hours = self.get_float_value(self.usage_hours_entry)
        
        gas_emission = gas * 2.1
        water_emission = water * 0.9
        appliance_emission = appliance_power * usage_hours * 0.001 * 0.5
        housing_emission = gas_emission + water_emission + appliance_emission
        total += housing_emission
        
        # Transportation
        # Elevator calculation
        elevator_emission = 0
        if self.elevator_var.get() == "是":
            weight = self.get_float_value(self.weight_entry)
            floors = self.get_int_value(self.floors_entry)
            elevator_emission = weight * floors * 0.001
        
        # Transport mode calculation
        transport_data = {"步行": 0, "骑行": 0, "公交": 11.69, "私家车": 32.4}
        transport_modes = ["步行", "骑行", "公交", "私家车"]
        transport_mode = transport_modes[self.transport_var.get() - 1]
        
        distance = self.get_float_value(self.distance_entry)
        transport_emission = transport_data[transport_mode] * distance
        transport_total = elevator_emission + transport_emission
        total += transport_total
        
        # Update the result display
        self.result_var.set(f"总碳排放量: {total:.2f} 克CO₂当量")
        
        return total

def main():
    root = tk.Tk()
    app = CarbonEmissionCalculator(root)
    root.mainloop()

if __name__ == "__main__":
    main()