def print_header():
    print("\n" + "="*50)
    print(" 碳排放计算器：今天你'低碳'了吗？".center(45))
    print("="*50)

def clothing_section():
    print("\n【衣】")
    print("-"*45)
    clothes = float(input("衣服/鞋袜消费金额（元）: "))
    return clothes

def food_section():
    print("\n【食】")
    print("-"*45)
    vegetarian = float(input("素菜/米饭消费金额（元）: "))
    meat = float(input("荤菜/烟酒消费金额（元）: "))
    return vegetarian + meat

def daily_use_section():
    print("\n【用】")
    print("-"*45)
    plastic_bags = int(input("使用塑料袋数量（个）: "))
    stationery = float(input("书籍/文具消费金额（元）: "))
    return plastic_bags * 0.1 + stationery * 0.05  # 假设排放因子

def housing_section():
    print("\n【住】")
    print("-"*45)
    gas = float(input("燃气使用量（m³）: "))
    water = float(input("用水量（吨）: "))
    appliance_power = float(input("家用电器总功率（瓦）: "))
    usage_hours = float(input("每日使用小时数: "))
    return gas*2.1 + water*0.9 + appliance_power*usage_hours*0.001*0.5  # 假设排放因子

def transportation_section():
    print("\n【行】")
    print("-"*45)
    elevator = input("是否乘坐电梯（是/否）: ").lower()
    weight = float(input("体重（kg）: ")) if elevator == "是" else 0
    floors = int(input("累计层数: ")) if elevator == "是" else 0
    elevator_emission = weight * floors * 0.001 if elevator == "是" else 0  # 假设每kg·层排放0.001g
    
    # 交通方式计算（复用原代码逻辑）
    transport_data = {"步行":0, "骑行":0, "公交":11.69, "私家车":32.4}
    print("\n交通工具选择：")
    [print(f"{i+1}. {k}") for i, k in enumerate(transport_data)]
    choice = int(input("请选择交通工具编号: "))-1
    distance = float(input("出行距离（km）: "))
    key = list(transport_data.keys())[choice]
    transport_emission = transport_data[key] * distance
    return elevator_emission + transport_emission

def main():
    print_header()
    
    total = 0
    total += clothing_section() * 0.3     # 假设每元产生0.3g排放
    total += food_section() * 0.5         # 假设每元产生0.5g排放
    total += daily_use_section()
    total += housing_section()
    total += transportation_section()
    
    print("\n" + "="*50)
    print(f" 总碳排放量: {total:.2f} 克CO₂当量 ".center(45))
    print("="*50)
    print("\n倡导低碳生活    共建美好家园".center(50))

if __name__ == "__main__":
    main()
