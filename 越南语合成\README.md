# 越南语文本转语音工具

这是一个基于讯飞开放平台的越南语文本转语音工具，支持将越南语文本转换为MP3格式的语音文件。

## 功能特点

- 支持越南语文本转换为语音
- 提供命令行和API两种使用方式
- 自动将生成的PCM文件转换为MP3格式
- 简单易用的接口

## 安装依赖

```bash
pip install websocket-client pydub flask
```

您还需要安装ffmpeg作为pydub的后端：

- Windows: 下载[ffmpeg](https://ffmpeg.org/download.html)并添加到PATH环境变量
- Linux: `sudo apt install ffmpeg`
- macOS: `brew install ffmpeg`

## 使用方法

### 1. 命令行方式

直接运行main.py文件：

```bash
python main.py
```

按照提示依次输入越南语文本、APPID、APIKey、APISecret和输出文件名。所有参数均为必填。

### 2. 作为Python模块导入

```python
from main import text_to_mp3

# 将越南语文本转换为MP3
result_file = text_to_mp3(
    text="Xin chào, dạo này bạn có khỏe không?", 
    appid="your_appid",
    api_key="your_api_key", 
    api_secret="your_api_secret",
    output_file="output.mp3"
)

print(f"生成的MP3文件: {result_file}")
```

### 3. API服务

启动API服务：

```bash
python api.py
```

API服务将在http://localhost:5000启动，您可以通过以下方式使用：

- 访问主页获取使用说明
- 发送POST请求到/tts接口进行文本转语音

示例请求：

```bash
curl -X POST http://localhost:5000/tts \
  -H "Content-Type: application/json" \
  -d '{
    "text": "Xin chào, dạo này bạn có khỏe không?",
    "appid": "your_appid",
    "api_key": "your_api_key",
    "api_secret": "your_api_secret"
  }'
```

成功后会返回包含下载链接的JSON响应。

## API参数说明

### /tts接口

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| text | string | 是 | 要转换的越南语文本 |
| appid | string | 是 | 讯飞开放平台的APPID |
| api_key | string | 是 | 讯飞开放平台的APIKey |
| api_secret | string | 是 | 讯飞开放平台的APISecret |

## 注意事项

1. 使用前请确保您已在讯飞开放平台开通相关服务
2. 所有参数均为必填，不提供默认值
3. 生成的MP3文件默认保存在当前目录或output目录下
4. API服务默认监听所有网络接口，生产环境请配置安全设置 