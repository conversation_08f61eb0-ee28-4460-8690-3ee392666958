#!/usr/bin/env python
# -*- coding:utf-8 -*-

from flask import Flask, request, jsonify, send_file
import os
import uuid
from main import text_to_mp3

app = Flask(__name__)

# 配置默认API参数
DEFAULT_APPID = "cac92969"
DEFAULT_API_KEY = "edfcb23c17bbc1d5b08bb7e365efb10d"
DEFAULT_API_SECRET = "OGMxNDY0OTEwNzkxMzRkMjdjMWZjNjQ4"

# 创建输出目录
OUTPUT_DIR = "output"
if not os.path.exists(OUTPUT_DIR):
    os.makedirs(OUTPUT_DIR)

@app.route('/tts', methods=['POST'])
def text_to_speech():
    """
    接收POST请求，将文本转换为语音
    
    请求参数:
        text: 要转换的越南语文本 (必填)
        appid: 讯飞API的APPID (必填)
        api_key: 讯飞API的APIKey (必填)
        api_secret: 讯飞API的APISecret (必填)
    
    返回:
        JSON响应，包含生成的MP3文件URL或错误信息
    """
    # 获取请求数据
    data = request.json
    
    # 检查必填参数
    if not data:
        return jsonify({'error': '缺少请求数据'}), 400
        
    missing_params = []
    for param in ['text', 'appid', 'api_key', 'api_secret']:
        if param not in data or not data[param]:
            missing_params.append(param)
    
    if missing_params:
        return jsonify({'error': f'缺少必填参数: {", ".join(missing_params)}'}), 400
    
    # 获取参数值
    text = data.get('text')
    appid = data.get('appid')
    api_key = data.get('api_key')
    api_secret = data.get('api_secret')
    
    # 生成唯一文件名
    unique_id = str(uuid.uuid4())
    output_file = os.path.join(OUTPUT_DIR, f"{unique_id}.mp3")
    
    # 转换文本为MP3
    try:
        result_file = text_to_mp3(text, appid, api_key, api_secret, output_file)
        if result_file:
            # 构建文件URL
            file_url = f"/download/{unique_id}.mp3"
            return jsonify({
                'success': True,
                'message': '语音合成成功',
                'file_url': file_url
            })
        else:
            return jsonify({
                'success': False,
                'message': '语音合成失败'
            }), 500
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'处理请求时出错: {str(e)}'
        }), 500

@app.route('/download/<filename>', methods=['GET'])
def download_file(filename):
    """
    提供MP3文件下载
    """
    file_path = os.path.join(OUTPUT_DIR, filename)
    if os.path.exists(file_path):
        return send_file(file_path, as_attachment=True)
    else:
        return jsonify({'error': '文件不存在'}), 404

@app.route('/', methods=['GET'])
def home():
    """
    API使用说明
    """
    return """
    <html>
    <head>
        <title>越南语文本转语音API</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
            h1 { color: #4a4a4a; }
            pre { background: #f4f4f4; padding: 15px; border-radius: 5px; }
            .container { max-width: 800px; margin: 0 auto; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>越南语文本转语音API</h1>
            <p>使用方法：</p>
            <pre>
POST /tts
Content-Type: application/json

{
    "text": "Xin chào, dạo này bạn có khỏe không?",
    "appid": "您的APPID",
    "api_key": "您的APIKey",
    "api_secret": "您的APISecret"
}
            </pre>
            <p>注意：所有参数均为必填</p>
            <p>成功后会返回下载链接，可通过该链接下载生成的MP3文件。</p>
        </div>
    </body>
    </html>
    """

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True) 