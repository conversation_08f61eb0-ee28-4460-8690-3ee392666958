#!/usr/bin/env python
# -*- coding:utf-8 -*-

import os
import time
from tts_ws_python3_demo import run_tts

def text_to_mp3(text, appid, api_key, api_secret, output_file):
    """
    将文本转换为MP3格式的语音文件
    
    参数:
        text (str): 要转换的越南语文本
        appid (str): 讯飞API的APPID
        api_key (str): 讯飞API的APIKey
        api_secret (str): 讯飞API的APISecret
        output_file (str): 输出MP3文件名
    
    返回:
        str: 生成的MP3文件路径
    """
    # 删除可能存在的旧PCM文件
    if os.path.exists('./demo.pcm'):
        os.remove('./demo.pcm')
    
    # 如果存在旧的demo.mp3文件，删除它
    if os.path.exists('./demo.mp3'):
        os.remove('./demo.mp3')
    
    # 调用TTS转换
    run_tts(APPID=appid, APIKey=api_key, APISecret=api_secret, Text=text)
    
    # 等待一秒确保MP3文件已经生成
    time.sleep(1)
    
    # 如果MP3文件已生成，重命名为指定的输出文件名
    if os.path.exists('./demo.mp3'):
        # 如果指定的输出文件已存在，先删除
        if os.path.exists(output_file) and output_file != './demo.mp3':
            os.remove(output_file)
        
        # 如果输出文件名不是默认的demo.mp3，则重命名
        if output_file != './demo.mp3':
            os.rename('./demo.mp3', output_file)
            print(f"已将MP3文件重命名为: {output_file}")
    else:
        print("警告：MP3文件生成失败")
        return None
    
    # 返回输出文件名
    return output_file

if __name__ == "__main__":
    # 获取用户输入，不使用默认值
    text = input("请输入越南语文本: ")
    if not text:
        print("错误：文本不能为空")
        exit(1)
        
    appid = input("请输入APPID: ")
    if not appid:
        print("错误：APPID不能为空")
        exit(1)
        
    api_key = input("请输入APIKey: ")
    if not api_key:
        print("错误：APIKey不能为空")
        exit(1)
        
    api_secret = input("请输入APISecret: ")
    if not api_secret:
        print("错误：APISecret不能为空")
        exit(1)
    
    output_file = input("请输入输出文件名(包含.mp3后缀): ")
    if not output_file:
        print("错误：输出文件名不能为空")
        exit(1)
    elif not output_file.endswith('.mp3'):
        output_file += '.mp3'
        print(f"已自动添加.mp3后缀，输出文件名为: {output_file}")
    
    # 转换文本为MP3
    result_file = text_to_mp3(text, appid, api_key, api_secret, output_file)
    
    if result_file:
        print(f"\n转换完成! MP3文件已保存为: {result_file}") 